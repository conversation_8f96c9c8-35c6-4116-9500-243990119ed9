module.exports = {
  rootDir: 'src',
  testEnvironment: 'jsdom',
  transform: {
    '^.+\\.vue$': 'babel-jest',
    '.+\\.(css|styl|less|sass|scss|png|jpg|ttf|woff|woff2|svg|gif)$': 'jest-transform-stub',
    '^.+\\.(js|jsx|ts|tsx)?$': 'babel-jest',
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
    '^.+.(styl|png|jpg|ttf|woff|woff2)$': 'jest-transform-stub',
  },
  modulePaths: ['src', 'test'],
  setupFiles: ['<rootDir>/setupMocks.js'],
  setupFilesAfterEnv: ['<rootDir>/setupTests.js'],
  collectCoverage: true,
  collectCoverageFrom: ['**/*.{js,jsx,ts,tsx}'],
  coveragePathIgnorePatterns: [
    'node_modules',
    'src/test',
    'src/service',
    'src/paris2-vessel.js',
    'src/root.component.js',
    'src/set-public-path.js',
    'src/styleGuide.js',
  ],
};
