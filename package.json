{"name": "paris2-vessel", "repository": "*****************:fleetshipteam/paris2-web-vessel.git", "author": "", "license": "ISC", "version": "0.1.0", "description": "", "main": "dist/paris2-vessel.js", "scripts": {"lint": "eslint --fix --ext .js,.jsx,.ts,.tsx src", "start": "webpack-dev-server --mode=development --port 9011 --server-type https", "test": "jest --runInBand", "test:watch": "jest --watch", "build": "webpack --mode=production", "deploy": "aws s3 cp dist s3://$S3_BUCKET/vessel --recursive --acl public-read", "analyze": "webpack --mode=production --env.analyze=true", "prettier": "prettier --write './**'", "sonar": "sonar-scanner -Dsonar.login=$(aws --region ap-southeast-1 ssm get-parameters --with-decryption --name /paris2-sonar-auth-token/dev | jq '.Parameters[0].Value' | tr -d '\"') -X", "prepare": "husky install"}, "dependencies": {"@react-google-maps/api": "^2.19.2", "axios": "^1.4.0", "buffer": "^6.0.3", "downloadjs": "^1.4.7", "formik": "^2.2.9", "immutability-helper": "^3.1.1", "json-loader": "^0.5.7", "jspdf": "^2.5.1", "libphonenumber-js": "^1.10.26", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "react-bootstrap-icons": "^1.10.3", "react-bootstrap-typeahead": "^6.1.2", "react-datepicker": "^4.11.0", "react-dnd": "^15.1.2", "react-dnd-html5-backend": "^15.1.2", "react-dnd-touch-backend": "^15.1.2", "react-dropzone": "^14.2.3", "react-hook-form": "^7.43.9", "react-icons": "^4.11.0", "react-image-file-resizer": "^0.4.8", "react-image-lightbox": "^5.1.4", "react-pdf": "^7.0.1", "react-router-dom": "^5.3.4", "react-table": "^7.8.0", "react-table-sticky": "^1.1.3", "react-toastify": "^11.0.5", "sheetjs-style": "^0.15.8", "single-spa-react": "^4.1.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "use-debounce": "^9.0.4", "uuid": "^9.0.0", "yup": "^0.32.11"}, "devDependencies": {"@babel/eslint-parser": "^7.21.8", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.21.5", "@babel/preset-react": "^7.18.6", "@types/downloadjs": "^1.4.3", "@types/enzyme": "^3.10.15", "@types/google.maps": "^3.54.10", "@types/jest": "^29.5.5", "@types/lodash": "^4.14.194", "@types/react-router-dom": "^5.3.3", "@types/react-table": "^7.7.15", "@wojtekmaj/enzyme-adapter-react-17": "^0.8.0", "ajv": "^8.12.0", "babel-jest": "^29.5.0", "babel-loader": "^9.1.2", "babel-plugin-styled-components": "^2.1.1", "clean-webpack-plugin": "4.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.7.3", "dotenv": "^16.0.3", "enzyme": "^3.11.0", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-config-react-important-stuff": "^3.0.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "file-loader": "^6.2.0", "husky": "^8.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-transform-stub": "^2.0.0", "sass": "^1.86.0", "prettier": "^2.8.8", "pretty-quick": "^3.1.3", "ramda": "^0.29.0", "react": "^17.0.2", "react-bootstrap": "^2.7.4", "react-dom": "^17.0.2", "react-i18next": "^12.2.2", "sass-loader": "^13.3.1", "sonar-scanner": "^3.1.0", "style-loader": "^3.3.2", "styled-components": "^5.3.10", "svg-url-loader": "^8.0.0", "systemjs-webpack-interop": "^2.3.7", "webpack": "^5.82.1", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.0", "webpack-merge": "^5.8.0"}, "browserslist": [">0.2%", "not dead", "not op_mini all"]}