final ENV_GIT_BRANCH = env.BRANCH
final ENV_STAGE_NAME = env.stageName
final ENV_SKIP_TESTS = env.skipTests
final ENV_SKIP_BUILD = env.skipBuild
final ENV_SOURCE_BRANCH = env.sourcebranch
final ENV_TARGET_BRANCH = env.targetbranch
final ENV_STATE = env.state
final ENV_GIT_REPO = env.gitRepo
ENV_ACCOUNT_ID = env.account
final ENV_REGION = env.region

def configBranch = env.configBranch ?: 'master'
def stageName = ENV_STAGE_NAME
def region = ENV_REGION
def autoAnalysisBranch = env.autoAnalysisBranch ?: 'release'
def developBranch =  env.developBranch ?: 'develop'
def nodeLabel = env.nodeLabel ?: 'fleet-slave'
def dockerImage = env.dockerImage ?: '************.dkr.ecr.ap-southeast-1.amazonaws.com/jenkins-build-agent:node18'
def buildCommand = env.buildCommand ?: "./build.sh"
def testCommand = env.testCommand ?: "./test.sh"
def codeAnalysisCommand = env.codeAnalysisCommand ?: "./upload_sonar_report.sh"
def deployCommand = env.deployCommand ?: "./jenkins-deploy.sh"
def hostDeployCommand = env.hostDeployCommand ?: "echo 'do nothing. (hostDeployCommand)'"
def hostTestCommand = env.hostTestCommand ?: "echo 'do nothing. (hostTestCommand)'"
def timeZone = env.timeZone ?: 'Asia/Hong_Kong'
def shouldPullDockerImage = env.shouldPullDockerImage == 'true'
def shouldBuild =  env.shouldSkipBuild != 'true'
def releaseVersionId = env.releaseVersionId

def repoParts = ENV_GIT_REPO.split('/');
for( String values : repoParts ) println(values);
println(repoParts[repoParts.length - 1]);
repoName = (repoParts[repoParts.length - 1]).split(/\./)[0];
def shouldRelease = false
def shouldRunCodeAnalysis = env.shouldRunCodeAnalysis == 'true'
def shouldTest = ENV_SKIP_TESTS != 'true'
echo "ENV_GIT_BRANCH = ${ENV_GIT_BRANCH}"
echo 'pull request info:'
echo "ENV_SOURCE_BRANCH = ${ENV_SOURCE_BRANCH}"
echo "ENV_TARGET_BRANCH = ${ENV_TARGET_BRANCH}"
echo "ENV_STATE = ${ENV_STATE}"

def ssh_private_key = ''


// Release action condition
if (stageName) {
  echo "Triggered to release to stage: ${stageName}"
  shouldRelease = true
} else if (ENV_STATE == 'MERGED' && ENV_TARGET_BRANCH == developBranch) {
  echo "Triggered by pull request merged event, will be released to DEV"
  stageName = 'dev'
  shouldRelease = true
} else {
  stageName = 'dev2'
  echo "No build will be released"
}

if (ENV_STATE == 'MERGED' || env.shouldRunCodeAnalysis == 'true') {
  shouldRunCodeAnalysis = true
}

if ( ENV_TARGET_BRANCH == autoAnalysisBranch)
{
  shouldRunCodeAnalysis = true
}

if (shouldRunCodeAnalysis) {
  shouldTest = true
}

echo "stageName = ${stageName}"
echo "shouldRelease = ${shouldRelease}"
echo "repoName = ${repoName}"
echo "shouldRunCodeAnalysis = ${shouldRunCodeAnalysis}"

def notifyBitbucket(stateName, onStage) {
	try {
		onStage()
    gitCommitId = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
    echo "notifyBitbucket = ${stateName}, ${repoName}, ${gitCommitId} "
    bitbucketStatusNotify(
      buildState: 'INPROGRESS',
      buildKey: stateName,
      buildName: stateName,
      repoSlug: repoName,
      commitId: gitCommitId
    )
	} catch (exc) {
    echo "notifyBitbucket FAILED"
		bitbucketStatusNotify(
			buildState: 'FAILED',
			buildKey: stateName,
			buildName: stateName,
			buildDescription: "${stateName} failed!!",
			repoSlug: repoName,
			commitId: gitCommitId
		)
		/* Rethrow to fail the Pipeline properly */
    throw exc
	}
  
    echo "notifyBitbucket SUCCESSFUL"
    bitbucketStatusNotify(
		buildState: 'SUCCESSFUL',
		buildKey: stateName,
		buildName: stateName,
		repoSlug: repoName,
		commitId: gitCommitId
	)
}

def aws(handle) {
  if (stageName == "live") {
    withAWS(roleAccount: "${ENV_ACCOUNT_ID}", role:'paris2-live-cross-account-access'){
      handle()
    }
  } else {
      withCredentials([[
        $class: 'AmazonWebServicesCredentialsBinding',
        credentialsId: "paris2_jenkins",
        accessKeyVariable: 'AWS_ACCESS_KEY_ID',
        secretKeyVariable: 'AWS_SECRET_ACCESS_KEY'
      ],
        usernamePassword(
          credentialsId: 'paris2-configuration-bitbucket-app-password',
          usernameVariable: 'BITBUCKET_USER_NAME',
          passwordVariable: 'BITBUCKET_APP_PASSWORD'
        )
      ]){
        handle()
    }
  }
}

def silent_sh(cmd) {
    sh('#!/bin/sh -e\n' + cmd)
}

pipeline {
  agent {
    label "${nodeLabel}"
  }

  stages {
    stage('Check Branch Name') {
      steps {
        script {
            if (stageName == "live") {
                // Check if ENV_GIT_BRANCH starts with refs/tags/
                if (!ENV_GIT_BRANCH.startsWith('refs/tags/')) {
                    error("Deployment can only be triggered with tags. Please use tags for deployment.")
                    }
                }
            }
        }
    }

    stage('Checkout') {
      steps {
        timeout(time: 1, unit: 'MINUTES'){
          checkout([
          $class: 'GitSCM',
          branches: [[name: "${ENV_GIT_BRANCH}"]],
          doGenerateSubmoduleConfigurations: false,
          extensions: [], submoduleCfg: [],
          userRemoteConfigs: [[
            name: 'github',
            credentialsId: 'fleet_devops',
            url: "${ENV_GIT_REPO}"
          ]]
          ])
        script {
            aws{
              sh "aws ecr get-login-password --region ${region} | sudo docker login --username AWS --password-stdin ${ENV_ACCOUNT_ID}.dkr.ecr.${region}.amazonaws.com"
              ssh_private_key = readFile(file: '/home/<USER>/.ssh/id_rsa')
            }
          }
        }
      }
    }

    stage('Check Tag on Branch') {
      steps {
          script {
              if (stageName == "live") {
                  sh "sudo git checkout master"
                  def branch = 'master'
                  
                  def result = sh(script: "git branch --contains \$(git rev-parse ${ENV_GIT_BRANCH}) | grep '^\\* ${branch}\$'", returnStatus: true)
                  
                  if (result == 0) {
                      echo "${ENV_GIT_BRANCH} is contained within ${branch}"
                      sh "sudo git checkout ${ENV_GIT_BRANCH}"
                  } else {
                      error("${ENV_GIT_BRANCH} is not contained within ${branch}")
                  }
              } else {
                  echo "Skipping stage check Tag on Branch for ${stageName} "
                }
            }
        }
    }

    stage('Pull docker image') {
      steps {
        script {
          aws{
            sh "sudo docker pull ${dockerImage}"
          }
        }
      }
    }

    stage('Test and deploy in container') {
      agent {
        docker {
          image "${dockerImage}"
          reuseNode true
          args "--net=host -v /run/dbus/system_bus_socket:/run/dbus/system_bus_socket:ro"
        }
      }

      environment {
        NPM_TOKEN = credentials('npmToken')
        SSH_PRIVATE_KEY = readFile(file: '/home/<USER>/.ssh/id_rsa')
        TIME_ZONE = "${timeZone}"
        ENV="${stageName}"
        BRANCH="${BRANCH}"
        TARGET_BRANCH="${ENV_TARGET_BRANCH}"
        CONFIG_BRANCH="${configBranch}"
      }

      stages {

        stage('Load Configuration') {
            steps {
              script {
              withCredentials([usernamePassword(credentialsId: 'paris2-configuration-bitbucket-app-password', usernameVariable: 'BITBUCKET_USER_NAME', passwordVariable: 'BITBUCKET_APP_PASSWORD')]) {
                  sh "ENV=${stageName} CONFIG_BRANCH=${configBranch} ./load-configuration.sh"
                  sh "cat paris2-configuration.json"
              }
            }
          }
        }

        stage('Install Environment and build') {
          when {
            expression {
              return shouldBuild;
            }
          }
          steps {
            timeout(time: 20, unit: 'MINUTES'){
              script {
                sh "whoami"
                sh "cat ~/.npmrc"
                sh "echo ${NPM_TOKEN}"
                silent_sh "echo \"${SSH_PRIVATE_KEY}\" > \$HOME/.ssh/id_rsa"
                sh "chmod 600 \$HOME/.ssh/id_rsa"
                sh "npm ci --legacy-peer-deps"
                aws{
                    sh "./build.sh"
                }
              }
            }
          }
        }
        
        stage('Test') {
          when {
            expression {
              return shouldTest;
            }
          }
          steps {
            timeout(time: 20, unit: 'MINUTES'){
              notifyBitbucket('Test') {
                sh 'npm run test'
              }
            }
          }
        }

        stage('Code Analysis') {
          when {
            expression {
              return shouldRunCodeAnalysis;
            }
          }
          steps {
            script {
                echo 'Running Code Analysis stage'  
                aws {
                    sh "${codeAnalysisCommand}"
                }
            }
          }
        }

        stage('Deploy') {
          when {
            expression {
              return shouldRelease;
          }
        }

          steps {
            script {
              timeout(time: 5, unit: 'MINUTES'){
                script {
                  sh "chmod +x  ./jenkins-deploy.sh"
                  aws{
                    sh "${deployCommand}"
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }
  }
}