sonar.host.url=https://sonar-dev.fleetship.com/
sonar.web.javaOpts=-Xmx1024m -XX:MaxPermSize=256m -XX:+HeapDumpOnOutOfMemoryError
sonar.web.port=9000
sonar.projectKey=paris2-web-vessel
sonar.projectName=paris2-web-vessel
sonar.sourceEncoding=UTF-8
sonar.sources=src/
sonar.exclusions=src/test/*,**/*.test.tsx,**/*.test.js,**/*.test.jsx
sonar.tests = src/
sonar.test.inclusions = src/test/*
sonar.cpd.exclusions= src/__mocks__/**/*, src/test/**/*, src/service/__mocks__/**, src/model/**, src/constants/**, src/component/TechnicalReports/**/*, src/component/EnvironmentalReports/**/*, src/util/** 
sonar.coverage.exclusions=src/service/**/*,src/test/**/*,**/__mocks__/**,src/*
sonar.javascript.coveragePlugin=lcov
sonar.javascript.lcov.reportPaths=src/coverage/lcov.info
sonar.scm.provider=git
