/* eslint-disable no-undef */
const webpack = require('webpack');
const { merge } = require('webpack-merge');
const singleSpaDefaults = require('./webpack/webpack-react.config');
const CopyPlugin = require('copy-webpack-plugin');
const path = require('path');

require('dotenv').config({ path: path.resolve(__dirname, 'paris2-configuration.env') });

const ENV_MAP = Object.entries(process.env).reduce(
  (map, [key, value]) => ({
    ...map,
    [key]: JSON.stringify(value || ''),
  }),
  {},
);

module.exports = (webpackConfigEnv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: 'paris2',
    projectName: 'vessel',
    webpackConfigEnv,
  });

  const i18nextExternals = {
    externals: [/^react-i18next\/?.*$/],
  };

  return merge(
    defaultConfig,
    i18nextExternals,
    {
      module: {
        rules: [
          {
            test: /\.s[ac]ss$/i,
            use: [
              // Creates `style` nodes from JS strings
              {
                loader: 'style-loader',
                options: {
                  insert: function insertStyle(element) {
                    var parent = document.querySelector('#paris2-inline-style');
                    if (parent) {
                      element.setAttribute('nonce', parent.getAttribute('nonce'));
                      parent.appendChild(element);
                    } else {
                      var head = document.querySelector('head');
                      head.appendChild(element);
                    }
                  },
                },
              },
              // Translates CSS into CommonJS
              'css-loader',
              // Compiles Sass to CSS
              'sass-loader',
            ],
          },
          {
            test: /\.svg$/,
            use: [
              {
                loader: 'svg-url-loader',
                options: {
                  limit: 10000,
                },
              },
            ],
          },
          {
            test: /\.(ttf|eot|woff|woff2)$/,
            use: {
              loader: 'file-loader',
              options: {
                name: '[name].[ext]',
              },
            },
          },
          {
            test: /\.(png|jpeg|gif)$/,
            use: {
              loader: 'file-loader',
              options: {
                name: '[name].[ext]',
              },
            },
          },
        ],
      },
      resolve: {
        alias: {
          src: path.resolve(__dirname, './src'),
        },
        extensions: ['.ts', '.js', '.tsx', '.json'],
      },
    },
    {
      plugins: [
        new webpack.DefinePlugin({
          'process.env': ENV_MAP,
        }),
        new CopyPlugin({
          patterns: [{ from: 'public/images' }],
        }),
      ],
    },
    {
      devServer: {
        port: 9001,
      },
    },
  );
};
