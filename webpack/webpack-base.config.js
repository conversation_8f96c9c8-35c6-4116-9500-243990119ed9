const path = require('path');
const CleanWebpackPlugin = require('clean-webpack-plugin').CleanWebpackPlugin;
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = webpackConfigSingleSpa;

function webpackConfigSingleSpa(opts) {
  if (typeof opts !== 'object') {
    throw Error(`webpack-config-single-spa requires an opts object`);
  }

  if (typeof opts.orgName !== 'string') {
    throw Error(`webpack-config-single-spa requires an opts.orgName string`);
  }

  if (typeof opts.projectName !== 'string') {
    throw Error(`webpack-config-single-spa requires an opts.projectName string`);
  }

  let webpackConfigEnv = opts.webpackConfigEnv || {};

  return {
    entry: path.resolve(process.cwd(), `src/${opts.orgName}-${opts.projectName}.js`),
    output: {
      filename: `${opts.orgName}-${opts.projectName}.js`,
      libraryTarget: 'system',
      path: path.resolve(process.cwd(), 'dist'),
      chunkLoadingGlobal: `webpackJsonp_${opts.projectName}`,
    },
    module: {
      rules: [
        {
          test: /\.[tj]s$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
          },
        },
        {
          test: /\.tsx$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
          },
        },
        {
          test: /\.css$/,
          include: /node_modules/,
          use: [
            {
              loader: 'style-loader',
              options: {
                insert: function insertStyle(element) {
                  var parent = document.querySelector('#paris2-inline-style');
                  if (parent) {
                    element.setAttribute('nonce', parent.getAttribute('nonce'));
                    parent.appendChild(element);
                  } else {
                    var head = document.querySelector('head');
                    head.appendChild(element);
                  }
                },
              },
            },
            {
              loader: 'css-loader',
              options: {
                modules: false,
              },
            },
          ],
        },
      ],
    },
    devtool: 'source-map',
    devServer: {
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
      allowedHosts: 'all',
    },
    externals: ['single-spa', new RegExp(`^@${opts.orgName}/`)],
    plugins: [
      new CleanWebpackPlugin(),
      new BundleAnalyzerPlugin({
        analyzerMode: webpackConfigEnv.analyze ? 'server' : 'disabled',
      }),
    ],
  };
}
