#!/bin/bash
# this script is mainly for <PERSON> to download the file
# You can checkout https://bitbucket.org/fleetshipteam/paris2-configuration for local development
COMMIT_ID=$(curl -s --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/commits/$CONFIG_BRANCH?limit=1" | jq -r '.values[0].hash')
curl -s -S --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD -L -O "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/src/${COMMIT_ID}/$ENV/paris2-configuration.json"

add_env() {
  echo $1=$(jq -r ".$2" ./paris2-configuration.json) >>paris2-configuration.env
}

echo '' >paris2-configuration.env

add_env VESSEL_HOST api.base_urls.vessel
add_env KEYCLOAK_HOST api.base_urls.keycloak
add_env REFERENCE_HOST api.base_urls.reference
add_env FILE_HOST api.base_urls.file
add_env PARIS2_URL web_app.base_url
add_env PARIS_ONE_HOST paris1.host
add_env SHIP_PARTY_HOST api.base_urls.ship_party
add_env SEAFARER_HOST api.base_urls.seafarer
add_env NOTIFICATION_HOST api.base_urls.notification
add_env CREW_ASSIGNMENT_HOST api.base_urls.crew_assignment
add_env OWNER_REPORTING_HOST api.base_urls.vessel_accounting_facade
add_env GOOGLE_API_KEY external_services.google_map.api_key
add_env VESSEL_SERVICE api.base_urls.vessel_service
