/* eslint-disable no-console */
import React from 'react';
import { Container } from 'react-bootstrap';
import { BrowserRouter, Route, Switch, Redirect } from 'react-router-dom';
import Takeover from './pages/Takeover';
import DetailsPage from './pages/Details';
import VesselDetails from './pages/VesselDetails';
import ListPage from './pages/List';
import ApprovalPage from './pages/Approval';
import OwnershipRequest from './pages/OwnershipRequest';
import OwnershipApproval from './pages/OwnershipApproval';
import AdvancedSearch from './component/advanced_search/AdvancedSearchMobile';
import { I18nextProvider } from 'react-i18next';
import * as localization from '@paris2/localization';
import './vessel.scss';
import userService from './service/user-service';
import UserRoleController from './controller/user-role-controller';
import Spinner from './component/Spinner';
import ErrorAlert from './component/ErrorAlert';
import { TrialDetails } from './pages/TrialDetails';
import TechnicalReports from './pages/TechnicalReports';
import ViewDocument from './pages/ViewDocument';
import EmergencyDrillHistoryPage from './pages/EmergencyDrillHistory';
import VesselAdmin from './pages/VesselAdmin';
import DetailContextProvider from './context/DetailContext';
import AssignedCertificatesToVessel from './component/certificates/AssignedCertificatesToVessel';
import EnvironmentalReports from './pages/EnvironmentalReports';
import EnvironmentalReportContextProvider from './context/EnvironmentalReportContext';
import TechnicalReportContextProvider from './context/TechnicalReportContext';
import AdminContextProvider from './context/AdminContext';
import VesselContextProvider from './context/VesselContext';
import FinancialReportContextProvider from './context/FinancialReportContext';
import FinancialReports from './pages/FinancialReports';
import ManualsIndividualPage from './component/drawingsAndManuals/ManualsIndividualPage';
import PropTypes from 'prop-types';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const userRoleController = new UserRoleController();
export default class Root extends React.Component {
  state = {
    hasError: false,
    i18n: null,
    languages: [],
    currentLanguage: null,
    roleConfig: null,
  };

  componentDidCatch(error, info) {
    this.setState({ hasError: true });
  }

  onLanguageChanged = async ({ i18n, languages, currentLanguage }) => {
    this.setState((prevState) => ({
      ...prevState,
      languages,
      currentLanguage,
      i18n,
    }));
  };

  prepareRoleConfig = async () => {
    if (!this.state.roleConfig) {
      const roleConfigJson = await userRoleController.getConfig(this.props.kc);
      this.setState((prevState) => ({
        ...prevState,
        roleConfig: roleConfigJson,
      }));
    }
  };

  ga4EventTrigger = (action, category, label) => {
    try {
      this.props.ga4react?.event(action, label, category, false);
    } catch (error) {
      console.log(error);
    }
  };

  checkReturnError = (errorStatus) => {
    if (errorStatus) {
      return (
        <Container>
          <ErrorAlert message="Error occured while loading vessel!" />
        </Container>
      );
    } else {
      return <Spinner />;
    }
  };

  componentDidMount = async () => {
    await userService.init();
    localization.addCallback(this.onLanguageChanged);
    await localization.initialize();
    await this.prepareRoleConfig();
  };

  componentDidUpdate = async () => {
    await userService.init();
    await this.prepareRoleConfig();
  };

  render() {
    const { hasError, i18n, currentLanguage, roleConfig } = this.state;
    return i18n && currentLanguage && roleConfig ? (
      <I18nextProvider i18n={i18n}>
        <VesselContextProvider
          ga4EventTrigger={this.ga4EventTrigger}
          roleConfig={this.state.roleConfig}
        >
          <BrowserRouter>
            <Switch>
              <Route exact path="/vessel/search">
                <AdvancedSearch />
              </Route>

              <Route exact path="/vessel">
                <Redirect to="/vessel/active-vessels" />
              </Route>

              <Route exact path="/vessel/admin">
                <Redirect to="/vessel/admin/certificates" />
              </Route>

              <Route path="/vessel/:ownershipId/assigned-certificates">
                <DetailContextProvider
                  ga4EventTrigger={this.ga4EventTrigger}
                  roleConfig={this.state.roleConfig}
                >
                  <AssignedCertificatesToVessel ga4EventTrigger={this.ga4EventTrigger}/>
                </DetailContextProvider>
              </Route>

              <Route exact path="/vessel/document">
                <ViewDocument />
              </Route>

              <Route exact path="/vessel/emergency-drills/history/:ownershipId">
                <DetailContextProvider
                  ga4EventTrigger={this.ga4EventTrigger}
                  roleConfig={this.state.roleConfig}
                >
                  <EmergencyDrillHistoryPage />
                </DetailContextProvider>
              </Route>

              <Route exact path="/vessel/:tab">
                <ListPage ga4react={this.props.ga4react} />
              </Route>

              <Route path="/vessel/report/technical">
                <TechnicalReportContextProvider
                  roleConfig={this.state.roleConfig}
                  ga4EventTrigger={this.ga4EventTrigger}
                  userEmail={this.props.kc.tokenParsed.email}
                >
                  <TechnicalReports />
                </TechnicalReportContextProvider>
              </Route>

              <Route path="/vessel/report/environmental">
                <EnvironmentalReportContextProvider
                  userEmail={this.props.kc.tokenParsed.email}
                  ga4EventTrigger={this.ga4EventTrigger}
                  roleConfig={this.state.roleConfig}
                >
                  <EnvironmentalReports />
                </EnvironmentalReportContextProvider>
              </Route>
              <Route path="/vessel/report/financial">
                <FinancialReportContextProvider
                  userEmail={this.props.kc.tokenParsed.email}
                  roleConfig={this.state.roleConfig}
                  ga4EventTrigger={this.ga4EventTrigger}
                >
                  <FinancialReports />
                </FinancialReportContextProvider>
              </Route>

              <Route path="/vessel/:ownershipId/manuals/assigned-vessels">
                <DetailContextProvider roleConfig={this.state.roleConfig}>
                  <ManualsIndividualPage />
                </DetailContextProvider>
              </Route>

              <Route exact path="/vessel/admin/:tab">
                <AdminContextProvider
                  ga4EventTrigger={this.ga4EventTrigger}
                  roleConfig={this.state.roleConfig}
                >
                  <VesselAdmin />
                </AdminContextProvider>
              </Route>

              <Route exact path="/vessel/:vesselId?/:ownershipId?/takeover/:step?">
                <Takeover
                  username={this.props.kc.tokenParsed.preferred_username}
                  roleConfig={this.state.roleConfig}
                  ga4react={this.props.ga4react}
                />
              </Route>
              {/*note: check if path is deprecated*/}
              <Route exact path="/vessel/details/:vesselId">
                <VesselDetails ga4react={this.props.ga4react} />
              </Route>

              <Route exact path="/vessel/ownership/details/:ownershipId/:step?">
                <DetailContextProvider
                  ga4EventTrigger={this.ga4EventTrigger}
                  roleConfig={this.state.roleConfig}
                  userEmail={this.props.kc.tokenParsed.email}
                  isOwner={!!this.props.kc.tokenParsed?.ship_party_id}
                >
                  <DetailsPage ga4react={this.props.ga4react} />
                </DetailContextProvider>
              </Route>

              <Route exact path="/vessel/ownership/details/:ownershipId">
                <Redirect to="/vessel/ownership/details/:ownershipId/general" />
              </Route>

              <Route exact path="/vessel/details/:vesselId/approval">
                <ApprovalPage roleConfig={this.state.roleConfig} ga4react={this.props.ga4react} />
              </Route>

              <Route exact path="/vessel/ownership/:vesselId">
                <OwnershipRequest roleConfig={this.state.roleConfig} />
              </Route>
              {/*NOTE: ApprovalPage VS OwnershipApproval ???*/}
              <Route exact path="/vessel/ownership/:vesselId/approval">
                <OwnershipApproval
                  roleConfig={this.state.roleConfig}
                  ga4react={this.props.ga4react}
                />
              </Route>

              <Route exact path="/vessel/details/:ownershipId/trial-data">
                <TrialDetails roleConfig={this.state.roleConfig} />
              </Route>
            </Switch>
          </BrowserRouter>
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
          />
        </VesselContextProvider>
      </I18nextProvider>
    ) : (
      this.checkReturnError(hasError)
    );
  }
}

Root.propTypes = {
  kc: PropTypes.object,
  ga4react: PropTypes.object,
};
