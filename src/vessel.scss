body {
  margin-top: 80px;
  background-color: #ffffff;
}

.toolbar-allignment {
  float: right;
  padding-top: 24px;
}

// TAKEOVER TAB NAVIGATION
.tab_navigation {
  &__title {
    margin-bottom: 20px;
    color: #aaaaaa;
    font-size: 17px;
    font-weight: 500;
    text-align: center;
    height: 20px;
    &.active {
      color: #1f4a70;
      cursor: pointer;
    }
  }
  &__progress_line {
    background-color: #cccccc;
    height: 4px;
    width: 100%;
    &.active {
      background-color: #1f4a70;
      cursor: pointer;
    }
  }
  &__progress_dot {
    background-color: #cccccc;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    margin-left: 45%;
    margin-top: -12px;
    &.active {
      background-color: #1f4a70;
      cursor: pointer;
    }
  }
}

.photo-upload-spinner-container {
  padding: 50px;
}

.takeover_error_list {
  margin-top: 40px;
}

.approval_error_list {
  width: 100%;
  margin: -30px 20px 0px;
}

// DETAILS PAGE STYLING
.details_page {
  margin-bottom: 140px;

  @media screen and (min-width: 1440px) {
    &__section {
      width: 50%;
    }
  }

  @media screen and (max-width: 1439px) {
    &__section {
      width: 100%;
    }
  }

  .paris2-icon {
    margin: 2px 10px 0 0;
    cursor: pointer;
    svg {
      color: #6c757d;
      height: 16px;
      width: 16px;
    }
    &.compare-edit {
      margin: 0 10px 0 10px;
    }
  }
  .icon__draft__circle {
    position: absolute;
    border-radius: 50%;
    top: 38px;
    opacity: 0.8;
    display: block;
    height: 200px;
    width: 200px;
    right: 0;
    left: 0;
    margin-right: auto;
    margin-left: auto;
    background-color: #1f4a70;
  }
  .paris2-icon.vessel_image_draft {
    position: absolute;
    top: 20%;
    opacity: 0.8;
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 50% !important;
    height: 50% !important;

    svg {
      color: white;
    }
  }
  .paris2-icon.vessel_image {
    margin: 0;
    svg {
      color: white;
    }
  }

  &__draft-alert {
    color: #0091b8;
    padding: 5px;
    padding-left: 15px;
    font-weight: 500;
    margin-bottom: 30px;

    &.alert-danger {
      color: #d41b56;
    }
  }

  tr:hover td {
    background: #eff8fa;
  }
  &__edited_label {
    font-size: 14px;
    font-weight: 400;
    margin-left: 30px;
    color: #6c757d;
  }
  &__row-name {
    width: 50%;
    font-size: 14px;
    font-weight: 500;
  }
  &__row-name2 {
    font-size: 14px;
    font-weight: 500;
    padding: 0;
    text-align: left;
    text-decoration: underline;
    color: #1f4a70;
  }
  &__row-value {
    width: 50%;
    font-size: 14px;
    overflow-wrap: anywhere;
  }
  &__row-value2 {
    font-size: 14px;
  }
  &__table_head {
    border-top: 4px solid #1f4a70;
    color: #1f4a70;
    font-size: 16px;
    font-weight: 500;
    th {
      border: none !important;
    }
  }
  &__sub-header {
    border-top: none;
    color: #1f4a70;
    font-size: 14px;
    padding: 1rem 0 0 0.75rem;
  }
  td {
    border-top: none;
    border-bottom: 1px solid #efefef;
  }
  &__texted_ship_report {
    margin-top: 30px;
    color: #343a40;
    font-size: 14px;
    &__bold {
      font-weight: 600;
    }
  }
  &__position-report {
    margin-bottom: 0px;
  }
  &__position-report-filter {
    margin-top: 1rem;
  }
}

.ship-report-wrapper {
  color: #edf3f7;
  font-size: 14px;
  font-weight: 400;
  background-color: #1f4a70;
  margin-top: 40px;
  a {
    color: white;
  }
  h4 {
    color: #1f4a70;
  }
  &__first-row {
    border-bottom: 1px solid white;
    padding-left: 10px;
    padding-top: 20px;
    padding-bottom: 25px;
  }
  &__second-row {
    border-bottom: 1px solid white;
    padding-left: 10px;
    padding-top: 20px;
    padding-bottom: 25px;
  }
  &__third-row {
    padding-left: 10px;
    padding-top: 20px;
    padding-bottom: 25px;
  }
  &__fourth-row {
    border-left: 1px solid white;
    padding-left: 20px;
    padding-top: 20px;
    padding-bottom: 25px;
    display: block;
  }
  &__bold {
    width: 100%;
    font-weight: 600;
  }
  &__underline {
    text-decoration: underline;
  }
  &__location {
    margin-bottom: 25px;
  }
  &__link {
    color: white;
    padding: 6px 0;
    :hover {
      opacity: 70%;
      color: white;
    }
  }
  &__crew-name {
    padding: 1.125rem 0.375rem;
  }
}

.paris2-icon.default {
  svg {
    color: #1f4a70;
  }
}

.carousel-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

hr.dashed_line {
  border-top: 1px dashed #cccccc;
}

.p-0.m-0 {
  a {
    color: inherit;
    text-decoration: none;
    background-color: transparent;
  }
}

.form-label {
  font-size: 14px;
  color: #333333;
}

.form-text {
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 14px;
}

.table {
  width: 100%;
}

.loading {
  display: block;
  width: 300px;
  margin-left: auto;
  margin-right: auto;
}

// List
.search-parameters-wrapper {
  background-color: rgba(0, 145, 184, 0.05);
  margin-top: 20px;
  margin-bottom: 20px;
  &__title {
    color: #1f4a70;
    margin-top: 10px;
    font-size: 14px;
    font-weight: 600;
  }
  &__params {
    color: #1f4a70;
    margin-bottom: 14px;
    font-size: 14px;
    font-weight: 400;
  }
  &__slash {
    font-weight: 600;
    font-size: 16px;
  }
  &__button-wrapper {
    text-align: right;
  }
}

.hidden {
  display: none;
}

.take_over_page {
  margin-bottom: 350px;
}

// VESSEL APPROVAL STYLING
.vessel_approval {
  .row {
    margin-bottom: 35px;
  }

  h4 {
    margin-top: 20px;
  }
  .paris2-icon {
    cursor: pointer;
    float: right;
    color: #1f4a70;
  }
  hr {
    color: #1f4a70;
    border: 1px solid #cccccc;
  }

  &__section-header {
    margin-bottom: 25px;
    color: #1f4a70;
  }
  .table {
    table-layout: fixed;
    font-size: 14px;
    color: #343a40;

    tr {
      word-break: break-all;
    }

    th,
    td {
      padding-left: 0px;
      border-bottom: 1px solid #dee2e6;
    }

    tr span {
      display: block;
      margin-bottom: 20px;
    }

    thead th {
      border-top: none;
    }

    tr > th > span:first-child,
    tr > td:nth-child(4) {
      text-transform: capitalize;
    }

    tr.pending > td:nth-child(4) {
      color: orange;
    }
    tr.approved > td:nth-child(4) {
      color: green;
    }
    tr.rejected > td:nth-child(4) {
      color: red;
    }
  }

  .btn-outline-secondary.disabled {
    background: #cccccc 0% 0% no-repeat padding-box;
    color: #edf3f7;
    border: none;
  }

  img.icon__close {
    cursor: pointer;
    float: right;
  }

  &__moved-tick {
    path {
      fill: #28a747;
    }
    margin-right: 10px;
  }
}

// OWNERSHIP CHANGE STYLING
.ownership_change {
  padding: 15px;
  .row {
    margin: 0;
  }
  hr {
    border: 1px solid #1f4a70;
  }
  h6.capital {
    color: #1f4a70;
    text-transform: uppercase;
  }
  span {
    font-size: 14px;
    font-weight: 600;
    letter-spacing: -0.77px;
    color: #333333;
  }
  label {
    font-size: 14px;
    font-weight: 600;
    letter-spacing: -0.77px;
    color: #333333;
  }
  .vessel_approval {
    margin-left: -30px;
    margin-right: -30px;
  }
  .ownership-btn {
    color: #1f4a70;
    border-color: #1f4a70;
  }
  .ownership-btn:hover:enabled {
    color: #fff;
    background-color: #1f4a70;
    border-color: #1f4a70;
  }
  .ownership-btn:disabled {
    background: #cccccc 0% 0% no-repeat padding-box;
    color: #edf3f7;
    border: none;
  }
}

// MODAL OVERRIDES
.action-modal {
  & .modal-content {
    span {
      display: block;
    }
    word-wrap: break-word;
  }
  & .modal-dialog {
    max-width: 460px;
  }
}
.form-check {
  margin: 0 15px;
}
.modal-header {
  border-bottom: 0px;
}
.modal-footer {
  border-top: 0px;
}

@media print {
  .no-print,
  .no-print * {
    display: none !important;
  }
  html,
  body {
    height: 100vh;
    width: 100vw;
    margin: 0 !important;
    padding: 0 !important;
  }
  @page {
    size: landscape;
  }
  .print-scaling {
    position: absolute;
    float: left;
    visibility: visible;
    transform: scale(0.5);
    top: 0px;
    overflow: visible;
    transform-origin: left;
  }
  .line-text-truncate {
    display: -webkit-box;
    -webkit-line-clamp: unset;
  }
  .affixed-table {
    position: absolute;
    float: left;
    visibility: visible;
    transform: scale(0.7);
    top: 0px;
    overflow: visible;
    transform-origin: left;
  }
}

// danger alert

.alert-heading {
  font-size: 16px;
  font-weight: 500;
  color: #d41b56;
}

.alert-danger {
  background-color: #faf2f5;
  border: none;
}

.alert-danger li {
  color: #333333;
}

.alert-danger-with-border {
  padding: 10px 20px;
  color: #dc3545;
  margin-right: 20px;
}

// ADVANCED SEARCH STYLING
.advanced_search {
  button {
    padding-left: 16px;
    padding-right: 16px;
  }
  hr.line2px {
    border-top: 2px solid #1f4a70;
  }
  hr.line1px {
    border-top: 1px solid #efefef;
    padding-bottom: 0px;
    margin-bottom: 20px;
    margin-top: 20px;
    width: calc(100% + 30px);
    margin-left: -15px;
  }
  &__title {
    margin-top: 16px;
    margin-bottom: 24px;
  }
  &__subtitle {
    color: #1f4a70;
    margin-top: 30px;
    margin-bottom: 24px;
  }
  .paris2-icon {
    cursor: pointer;
    float: right;
    svg {
      color: #1f4a70;
    }
  }
  .paris2-icon.remove {
    margin-top: 4px;
    margin-left: 10px;
    float: none;
  }

  .label-to-on-top {
    margin-top: 6px;
    color: #333333;
  }
  .label-to-in-row {
    margin-top: 8px;
    color: #333333;
  }
  .hidden-label {
    color: #fafafa;
  }
  &__input-disabled {
    color: #333333;
    background-color: #efefef !important;
    border: 1px solid #cccccc;
  }
  &__filter-row {
    padding-top: 20px;
    padding-bottom: 4px;
    border-bottom: 1px solid #efefef;
  }
  &__clear-button {
    margin-top: 10px;
  }
  &__add-button {
    height: 38px;
    margin-top: 20px;
  }
  &__show-results-button {
    height: 38px;
    margin-top: 40px;
  }
  .category {
    padding-left: 5px;
  }
}

// DATE PICKER

.react-datepicker {
  .react-datepicker__month-select,
  .react-datepicker__year-select,
  .react-datepicker__navigation--next,
  .react-datepicker__navigation--previous {
    outline: none;
  }

  .react-datepicker__navigation--next,
  .react-datepicker__navigation--previous {
    padding-left: 0px;
    padding-right: 0px;
    right: 0px;
  }
}
.react-datepicker-wrapper {
  width: 100%;
  height: 32px;
}

.react-datepicker__input-container {
  width: 100%;
  height: 32px;
}

.react-datepicker__input-container input {
  width: 100%;
  height: 32px;
  border-color: #cfd4d9;
  border-radius: 4px;
  border-width: 1px;
  border-style: solid;
  color: #555b60;
  padding-left: 12px;
  font-size: 17px;
}

.react-datepicker__input-container input:disabled {
  background-color: #e9ecef;
}

.react-datepicker__time-container {
  display: inline;
  position: absolute;
}

//SCROLL TO TOP BUTTON
.back-to-top {
  .paris2-icon {
    position: fixed;
    bottom: 2rem;
    right: 5rem;
    color: #1e4a70;
    animation: fadeIn 1ms ease-in-out 1ms both;
    cursor: pointer;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  span {
    position: fixed;
    bottom: 0rem;
    right: 4rem;
    animation: fadeIn 1ms ease-in-out 1ms both;
    cursor: pointer;
    z-index: 4;
  }
}

// bottom button component

.bottom-component {
  height: 58px;
  background-color: #f8f9fa;
  border-top: 1px solid #cccccc;
  text-align: center;
  padding-top: 10px;
  &__button {
    padding-left: 100px;
    padding-right: 100px;
  }
}

//FOR INPUT MEASUREMENT APPEND

.unit-of-measure {
  width: 55px;
  justify-content: center;
}

//FOR TAKEOVER HEADER
.closeIcon {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  .paris2-icon {
    cursor: pointer;
    float: right;
    color: #1f4a70;
  }
}

.remove_icon {
  margin-top: 5px;
  cursor: pointer;
}

.btn:focus {
  box-shadow: none !important;
}

.dropdown-item:hover {
  background-color: #eff8fa;
}
.dropdown-header {
  color: #1e4a70;
  font-weight: 800;
}

.List__PopoverMenu {
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  width: 56px;
  li {
    cursor: pointer;
    margin: 10px;
  }
}

//FOR ADDING ASTERIX
.required {
  input[type='radio'] + label:after {
    content: '';
  }

  label:after {
    content: '*';
  }
}

.vessel-table {
  .page-number-border {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 0px 8px;
  }

  .page-num-active {
    text-decoration: underline;
  }

  .page-num-inactive {
    text-decoration: initial;
  }

  .page-num-disabled {
    color: #cccccc;
  }

  .page-num-enabled {
    color: #1f4a70;
  }

  .page-num {
    display: inline-block;
    cursor: pointer;
    margin: 8px;
    user-select: none;
  }

  .table {
    border-spacing: 0;
    width: 100%;
    padding-top: 20px;
    min-width: 0 !important;

    .heading-border {
      border-bottom: 0px !important;
      border-top: 2px solid #1f4a70;
    }

    .th,
    .itinerary-table-th {
      background-color: #ffffff;
      margin: 0;
      padding: 0.75em 0.7em;
      border-bottom: 2px solid #1f4a70;
      font-weight: bold;
      user-select: none;
    }

    .no-flex {
      flex: none !important;
    }

    .th:first-child {
      padding-left: 1.5em;
    }

    .body > .tr > .td {
      border-bottom: 1px solid #cccccc;
      :hover > .td {
        background-color: #eff8fa;
      }
    }

    .body > .tr > .td.remarksWordWrap {
      word-wrap: break-word;
    }

    .body > .tr:hover > .td {
      background-color: #eff8fa;
    }

    .td {
      background-color: #ffffff;
      margin: 0;
      padding: 0.7em;
    }

    a.td {
      text-decoration: underline !important;
      cursor: pointer;
      color: #1f4a70;
    }

    .td:first-child {
      padding-left: 1.5em;
    }

    &.sticky {
      overflow-x: auto;
      .footer {
        width: fit-content;
        bottom: 0;
      }

      .header {
        top: 0;
      }

      .body {
        position: relative;
      }
    }
    mark {
      padding: 0;
      background: #add0e4 0% 0% no-repeat padding-box;
    }
  }
}

.quick-filters-button {
  color: #1f4a70;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  div {
    float: left;
    margin-right: 4px;
  }
}

//Margin space below table head
.spinner-table {
  margin: 5% 0%;
  justify-content: center;
  div {
    color: #1f4a70 !important;
  }
}

.load-overlay {
  height: 100%;
  width: 100%;
  z-index: 999;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12.5rem;

  div {
    color: #1f4a70 !important;
  }
}

.change-ownership-link {
  text-decoration: underline !important;
  color: #1f4a70;
}

.search-text-highlight {
  background: #add0e4 0% 0% no-repeat padding-box;
}

.advanced-search {
  .advanced-search-menu {
    width: 60vw;
    padding: 1rem;
  }
  .dropdown-menu {
    &[aria-labelledby='dropdown-advanced-search'] {
      margin-left: -25vw !important;
      border: 1px solid #1f4a70;
    }
  }
}

.mobile {
  @media screen and (min-width: 577px) {
    display: none;
  }
  @media screen and (max-width: 576px) {
    display: block;
  }
}

.desktop {
  @media screen and (min-width: 577px) {
    display: block;
  }
  @media screen and (max-width: 576px) {
    display: none;
  }
}

.subtype-field {
  padding: 0 !important;
  margin-bottom: 0;
  .react-datepicker__navigation--next--with-time {
    right: 0px;
    outline: none;
  }
  .react-datepicker__navigation--previous {
    outline: none;
  }
}

//POPOVER ISSUE FIX

.popover {
  z-index: 1050;
  .arrow {
    transform: translate(15px, 0px) !important;
  }
}

.nav-border {
  border-bottom: 4px solid #1f4a70;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  background: #1f4a70;
  color: #fff !important;
  margin-bottom: 5px;
}

.tab-wrapper {
  margin-bottom: 1rem;
}

.button-link {
  color: #1f4a70;
  text-decoration: underline !important;
  padding: 0;
  cursor: pointer;
}

.list-count {
  margin-left: auto;
  margin-bottom: auto;
  margin-top: auto;
}

.no-result-found {
  text-align: center;
}

.alert-icon-no-search {
  width: 45px;
  margin: 10px;
}

.paris1-link-detail-page {
  color: #1f4a70;
  text-decoration: underline !important;
}
.details-breadcrumb {
  ol {
    margin: 0;
    padding: 0;
  }
}

.filterHeading {
  color: #1f4a70;
}
// Alert
.alert-wrapper {
  background: #f8f9fa 0 0 no-repeat padding-box;
  border-radius: 3px;
  opacity: 1;
  padding: 10px;
  margin-top: 10px;
}

.alert-icn {
  width: 24px;
  height: 24px;
  margin-bottom: 10px;
  background: transparent url('../public/icons/alert.svg') 0 0 no-repeat padding-box;
}

.technical-reports {
  color: #1f4a70;
  font-size: 24px;
  margin-left: 3px;
}

.filter-reports {
  color: #1f4a70;
  font-size: 14px;
  margin-left: 1rem;
}

.itinerary-filter {
  margin-bottom: 15px;
}

.rbt-input-multi.form-control {
  .rbt-input-wrapper {
    white-space: nowrap;
    height: 1.5rem;
    overflow: hidden;
    position: relative;
    div {
      display: inline-flex !important;
      width: 100%;
    }
  }
  input {
    display: inline-flex;
    top: 0;
  }
  .rbt-input-hint {
    display: none;
  }
}

.multi-dropdown {
  .dropdown-menu {
    .dropdown-item {
      padding-left: 0.5rem;
    }
  }
  .check-icon {
    display: flex;
    svg {
      color: #0091b8;
      path {
        fill: #0091b8;
      }
    }
  }
  .empty-icon-padding {
    padding-left: 30px;
  }
}

.spantoken {
  position: absolute;
  display: contents !important;
}
.rbt-token-remove-button {
  display: none;
}

.rbt-token {
  margin-right: 3px;
}

.rbt-token-removeable {
  overflow: hidden;
  width: fit-content !important;
}

.reports-filter-textField {
  max-width: 15%;
  margin-left: 1%;
  background: none;
  :focus {
    box-shadow: none;
    outline: none;
  }
  &.col {
    padding-left: 0;
    padding-right: 0;
    .dropdown {
      &.disabled {
        input {
          background-color: #e9ecef;
          pointer-events: none;
        }
      }
    }
  }
}

.startDatePicker {
  margin-left: 1%;
  width: 12%;
  z-index: 120;
}

.endDatePicker {
  width: 12%;
  margin-left: 4px;
  z-index: 120;
}

.z-index-110 {
  z-index: 110;
}

.datePickerRange {
  align-self: center;
  font-size: 14px;
  margin-left: 4px;
}

.vessel_dropdown {
  margin-left: 1rem;
  width: 30%;
  &__vessel-header-dropdown-switch {
    .form-control {
      justify-content: space-between !important;
      display: flex;
      min-width: 12rem;
      align-items: center;
      background: #fff 0% 0% no-repeat padding-box;
      border: 1px solid #1f4a70;
      border-radius: 4px;
      opacity: 1;
      font: normal normal medium 14px/17px Inter, sans-serif;
      letter-spacing: 0px;
      color: #1f4a70;
    }

    #dropdown-item-button {
      justify-content: space-between !important;
      display: flex;
      min-width: 12rem;
      align-items: center;
      background: #fff 0% 0% no-repeat padding-box;
      border: 1px solid #1f4a70;
      border-radius: 4px;
      opacity: 1;
      font: normal normal medium 14px/17px Inter, sans-serif;
      letter-spacing: 0px;
      color: #1f4a70;
    }
  }
  &__dropdown-icon {
    width: 16px;
  }
}

.filterTextField {
  background: none !important;
}

.custom-picker {
  .react-datepicker__day--outside-month {
    color: transparent;
    pointer-events: none;
    background-color: transparent;
  }
  &__highlighted {
    color: #fff;
    background-color: #1f4a70;
    border-radius: 1rem;
    &:hover {
      color: #000;
    }
    &.react-datepicker__day--selected {
      border-radius: 0.3rem;
      background-color: #216ba5;
      color: #fff;
    }
  }
  &__disabled {
    pointer-events: none;
  }
}
.form-group {
  margin-bottom: 1rem;
  &.custom-picker,
  &.vessel_dropdown {
    display: inline-block;
  }
}

.document-continer {
  background-color: #000;
  color: #ffffff;
  padding: 20px;
  .download-btn {
    float: right;
  }
  &__icon-container {
    float: right;
    .paris2-icon {
      margin-right: 30px;
    }
  }
  .paris2-icon {
    cursor: pointer;
  }
  &__page-number-container {
    display: contents;
  }
  &__header-container {
    display: flex;
  }
  &__spinner-document {
    width: 100%;
    height: 200px;
    text-align: center;
    padding-top: 150px;
    .d-flex {
      justify-content: center;
    }
  }
  &__pdf-style {
    margin: 20px;
    canvas {
      margin-left: auto;
      margin-right: auto;
      max-width: 75% !important;
      margin-bottom: 20px;
    }

    img {
      max-width: 75%;
      align-self: center;
      display: block;
      margin-left: auto;
      margin-right: auto;
    }

    .react-pdf__Page__textContent {
      height: 100% !important;
    }
  }
}
.delete-icon {
  svg path {
    fill: red;
  }
}
.line-text-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
}

.tooltip-arrow::after {
  content: '';
  position: absolute;
  left: -10px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  bottom: -10px;
  border-right: 10px solid #000;
}

.tooltip.pointup .tooltip-arrow::after {
  content: '';
  position: absolute;
  left: -15px;
  width: 0;
  height: 0;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  bottom: 0px;
  border-bottom: 10px solid #000;
}

.tooltip {
  width: fit-content;
  div.tooltip-inner {
    background-color: #000;
    padding: 5px 10px;
    text-align: left;
  }
}

.details-field-tooltip {
  width: fit-content;
  div.arrow::before {
    border-bottom-color: #000;
  }
  div.tooltip-inner {
    background-color: #000;
    max-width: 60vw;
    margin: auto;
  }
}

.border-line {
  border-top: 2px solid #1f4a70;
  width: 100%;
}

.modal-border-line {
  border-top: 1px solid lightgrey;
  width: 100%;
}

.emergency-drills {
  color: #1f4a70;
  font-size: 18px;
  margin-left: 3px;
}

.required-field-text {
  font-size: 16px;
  color: #333333;
}

.vessel-dropdown {
  padding-left: 1rem;
  &.disabled {
    input {
      background-color: #e9ecef;
      pointer-events: none;
    }
  }
}

.from-label {
  font-weight: bold;
  color: black;
}

.edit-label {
  font-size: 14px;
  font-weight: 400;
  color: #6c757d;
}

.form-main-control .form-control:valid {
  background-image: none;
  border-color: #ced4da;
  padding-right: 5px;
  :focus {
    border-color: #ced4da;
    box-shadow: none;
  }
  :active {
    border-color: #ced4da;
    box-shadow: none;
  }
}

.form-main-control .form-control:invalid {
  background-image: none;
  padding-right: 5px;
  :focus {
    border-color: #ced4da;
    box-shadow: none;
  }
  :active {
    border-color: #ced4da;
    box-shadow: none;
  }
}
.form-main-control .form-control.is-invalid {
  border-color: #d41b56;
  &:focus {
    box-shadow: none;
  }
}

.validate-error {
  color: #d41b56;
  font-size: 12px;
}

.dropdown-icon {
  position: relative;
}

.interval-angle-icon {
  position: absolute;
  right: 20px;
  padding-left: 5px;
  padding-right: 5px;
  top: 10px;
  color: #1f4a70;
  font-size: 17px;
  background-color: white;
}

.vessel-type-angle-icon {
  position: absolute;
  right: 4px;
  top: 10px;
  color: #1f4a70;
  font-size: 17px;
  background-color: white;
}

.assign-drill-table {
  margin-bottom: 70px;
}

.vessel-cross-icon {
  top: 5px;
}

.underline-link-text {
  color: #1f4a70;
  text-decoration: underline !important;
  cursor: pointer;
}

.text-danger {
  font-weight: bold;
}

.rbt.control-vessel-dropdown {
  .rbt-input-main.form-control.rbt-input {
    padding-right: 30px;
  }
}
.send-email-modal {
  min-width: 40vw;
  max-width: 80vw;

  .send-email-header {
    border-bottom: 0px;
    .send-email-title {
      color: #343a40;
      font-size: 1.1rem;
    }
  }

  .email-form {
    display: flex;
    justify-content: space-between;
  }

  .left-column {
    display: flex;
    flex: 1;
    flex-direction: column;
    .sendEmail-doc {
      max-width: 35vw;
      &__div {
        background: #f2f9fc;
      }
      .doc-div {
        display: inline-flex;
        padding: 5px;
        p {
          white-space: break-spaces;
        }
        .button-link {
          word-break: break-all;
        }
      }
    }
  }

  .right-column {
    display: flex;
    flex: 1.5;
    flex-direction: column;
    margin-left: 4vw;

    .message-input {
      min-height: 25vh;
      height: 50vh;
      font-size: 16px;
    }
  }

  .validate-error {
    padding-left: 1rem;
  }
}

.message-input {
  min-height: 10vh;
  font-size: 16px;
  max-height: 50vh;
}

.vessel-certificate-header {
  .table .th {
    border-top: none;
  }
  .header {
    .tr:first-child {
      .th {
        border-bottom: none;
      }
    }
  }
}

.assign-user-table {
  padding-top: 20px;
  .table {
    padding-top: 0;
  }
  .header {
    position: sticky;
    z-index: 1;
  }
}

.custom-table-scroll {
  .body {
    overflow-y: visible;
    height: 130px;
  }
}

.drill-table-top-border {
  .table .th {
    border-top: none;
    border-bottom: 2px solid #1f4a70;
  }
}

.list-unstyled {
  list-style-type: none;
}

.basic-checkbox label {
  color: #343a40 !important;
  font-weight: unset !important;
}

.filter-background {
  background-color: #f8f9fa;
}
.documentDownloadBtn {
  height: 80vh;
  flex-direction: column;
  margin: auto;
  width: fit-content;
}
.filter-heading {
  color: #1f4a70;
  font-weight: bolder;
}
.movement-button {
  pointer-events: none;
}
.compare-page-header {
  color: #1f4a70;
}

.compare-page-header-text-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.compare-page-header-tooltip {
  width: fit-content;
  div.arrow::before {
    border-bottom-color: #1f4a70;
  }
  div.tooltip-inner {
    background-color: #1f4a70;
    max-width: 18vw;
    margin: auto;
  }
}
div.paris2-tooltip-icon .paris2-icon {
  margin: 0px 10px 0 0;
  svg {
    height: 21px;
    line-height: 21px;
    vertical-align: middle;
  }
}
.update-spinner {
  div {
    margin: auto;
  }
}
.control-parameter-heading-border {
  border-bottom: 1px solid #cccccc;
  padding-bottom: 10px;
}

.highlight-row-blue .td {
  background-color: #1e4a70 !important;
  color: white !important;
}

.highlight-row-light-blue .td {
  background-color: #e6f4f5 !important;
}

.delete-icon-fill svg path {
  fill: white;
}

.disable-area {
  background-color: white;
  opacity: 0.5;
  pointer-events: none;
}
.reports-filter-checkbox {
  margin-left: 1%;
}
.tooltip-header {
  width: fit-content;
  div.arrow::before {
    border-bottom-color: black;
  }
  div.tooltip-inner {
    background-color: black;
    max-width: 60vw;
  }
}

.control-parameter-table {
  .table.sticky {
    overflow-x: unset;
  }
}

.control-parameter-table-item {
  .table .body {
    div.tr:last-child {
      margin-bottom: 200px;
    }
  }
}

.custom-spinner-background {
  background-color: rgba(255, 255, 255, 0.5);
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
}

.flag-change-table {
  .table {
    border: 1px solid #cccccc;
  }
  .table .th {
    border: none !important;
  }
}

.ownership-form {
  margin-bottom: 65px;
}

.flag-change-list {
  .table {
    border: none !important;
  }
  .table .th {
    border: none !important;
  }
}

.width-30 {
  width: 30px;
}

.cursor-pointer {
  cursor: pointer;
}

.accountant-div {
  .row {
    width: 100%;
  }
}

.eorb-href-class {
  text-decoration: underline !important;
  cursor: pointer;
  color: #1f4a70;
}

.eorb-disabled-class {
  color: grey;
}

.financial_info {
  font-size: 14px;
  font-weight: 400;
  color: #6c757d;
}

.text-spacing {
  white-space: pre-wrap;
}

.upload-document {
  width: 2rem;
}

input[type='number']:hover::-webkit-inner-spin-button,
input[type='number']::-webkit-inner-spin-button {
  width: 30px;
  height: 30px;
}

iframe {
  display: none;
}

.vessel-dropdown-menu {
  .dropdown-menu {
    // Overriding inline style by using important
    transform: translate3d(0px, 32px, 0px) !important;
  }
}

.drill-assign-check {
  input {
    position: inherit;
    margin: 0.3rem, 0, 0, 0;
  }
}
.eu-ets {
  [data-sticky-last-left-td~='true'] {
    border-right: 1px solid #cccccc;
  }
  [data-sticky-first-right-td~='true'] {
    border-left: 1px solid #cccccc;
  }
  .heading {
    height: 49px;
    align-items: center;
  }
  .sub-heading {
    font-family: Inter, sans-serif;
    font-size: 12px;
    letter-spacing: 0px;
    color: #343a40;
    opacity: 1;
    padding: 2px 8px;
    background: #edf3f7 0% 0% no-repeat padding-box;
    border-radius: 10px;
    font-weight: 500;
    display: flex;
    align-items: center;
    height: 20px;
  }
  .m-20 {
    margin-top: 20px;
  }
  .fx-v-center {
    display: flex;
    align-items: center;
  }
}
.form-control:disabled {
  background-color: #e5e9ec;
}
.text-success-color {
  color: #28a747;
}
.text-warning-color {
  color: #efb506;
  font-size: 12px;
}
.text-danger-color {
  color: #d41b56;
}
.rate-container {
  height: 41px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  opacity: 1;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  letter-spacing: 0px;
  span {
    font-weight: 500;
    color: #004085;
  }
  margin-left: 12px;
}
.rate-container-parent {
  display: flex;
}

.form-control {
  height: 32px;
}
.delete-voyage-icon {
  font-size: 16px;
  margin-left: 8px;
  cursor: pointer;
  svg {
    height: 20px;
    line-height: 20px;
    vertical-align: middle;
    color: rgb(31, 74, 112);
  }
}

.box {
  width: 20px;
  height: 20px;
  border-radius: 2px;
}

.voyage-compare-stats {
  .card {
    margin-right: 16px;
    &-body {
      padding: 12px;
    }
    &-title,
    .badge {
      font-size: medium;
      font-weight: 500;
    }
  }
}

.edit-voyage-modal {
  .modal-body {
    padding: 0rem;
  }

  .btn {
    font-size: 14px;
    display: flex;
    align-items: center;
    width: 80px;
    height: 32px;
    justify-content: center;
  }

  .edit-voyage-label {
    margin-top: 15px;
    font-weight: 500;
  }
}

.file-drag-and-drop-area {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-flow: column nowrap;
  color: #555555;
  border: 2px #c3c3c3 dashed;
  border-radius: 12px;
  text-align: center;
  svg {
    color: #6c757d;
    transform: scale(0.8);
  }
  .content {
    font-size: 14px;
    p {
      margin-bottom: 0px;
    }
  }

  .content p:nth-child(1) {
    margin-top: 16px;
  }
  .browse-file {
    font-size: 14px;
    text-decoration: underline;
    color: #1f4a70;
    font-weight: 600;
    cursor: pointer;
  }

  input {
    display: none;
  }
}

.file-details-container {
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-between;
  padding: 5px 12px;
  p {
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 400px;
    padding: 0;
    margin: 0;
  }
}

.file-options {
  top: -45px;
  left: 10px;
  .list-popover-menu {
    margin-bottom: 0px;
    padding: 4px 0px 12px 12px;
    list-style: none;
    width: 100px;
    li {
      color: #464b51;
      cursor: pointer;
      padding-top: 8px;
      font-weight: 600;
    }
  }
}

.upload-container {
  margin-bottom: 1rem;
  .error-text {
    color: red;
    font-size: 12px;
  }
}
.verification-status-dropdown {
  font-size: 14px;
}
.proration-note-filter {
  font-style: italic;
  color: #1f4a70;
  font-size: 12px;
}
.compare-reports-inline-text {
  color: #6c757d;
}
.compare-reports-cancel-btn {
  background-color: #1f4a70;
}
.compare-reports-disabled-save-btn {
  background-color: #efefef !important;
  color: #aaaaaa !important;
  border-color: #efefef !important;
}

.tech-group-osc-hint {
  background-color: #fbe8ee;
  margin-bottom: 12px !important;
  border: 1px solid #f0ced2;
  padding: 4px;
  font-size: 13px;
  color: #d41a56;
  border-radius: 4px;
}

.fs-13-label {
  font-size: 13px;
}

div.details_page:has(div.vessel-certificates-v2) {
  margin-bottom: 10px;
}

div.container:has(div.vessel-certificates-v2) .back-to-top {
  display: none;
}

body:has(div.vessel-certificates-v2) {
  .popover {
    z-index: 4;
  }
  .cert-action-popoverMenu {
    list-style-type: none;
    padding-left: 5px;
    padding-right: 5px;
    margin: 0;
    li {
      cursor: pointer;
      margin: 10px 0px 10px 0px;
    }
  }
  .uploader-drag-drop-container {
    border-radius: 4px;
    border: 1px solid #ccc;
  }
  .uploader-browse-files {
    text-decoration: underline;
    color: #1f4a70;
  }

  .uploader-label {
    font-size: 14px;
    color: #6c757d;
  }
  .upload-item-ul {
    list-style-type: none;
    gap: 8px;
    display: grid;
    padding-left: 0px; // necessary
  }

  .upload-item-li {
    background-color: #f5f5f5;
    padding: 12px;
    padding-left: 0px;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
}

.vessel-certificates-v2 {
  .emergency-drills {
    color: #333333;
    font-weight: 500;
  }

  #send-email-btn:hover {
    background-color: #173652;
    border-color: #173652;
  }
  #send-survey-button:hover {
    background-color: #173652;
    border-color: #173652;
  }

  .react-datepicker__close-icon::after {
    display: inline-block;
    margin-right: 0.25rem;
  }
  .cert-loader {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
  .certificate-filter-row {
    margin: 1rem 0 1rem 0;
    display: flex;
    flex-direction: row;
    justify-content: start;

    .cert-group-dropdown {
      padding: 0;
      width: 100%;
      button {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
        width: 100%;
        background: #fff;
        color: #555b60;
        border: 1px solid #ced4da;
        text-align: left;
        position: relative;
        height: 32px;
      }

      button::after {
        position: absolute;
        right: 1rem;
        margin-top: 3px;

        border-top: 0px;
        border-left: 0px;
        width: 8px;
        height: 8px;
        border-right: 2px solid #6c757d;
        border-bottom: 2px solid #6c757d;
        transform: rotate(45deg);
        margin-left: 5px;
      }

      button:active {
        background: #fff;
        color: #1f4a70;
      }

      .dropdown-menu {
        padding-bottom: 0;
        width: 100%;
        .dropdown-item {
          padding: 0.25rem 1rem;
          .form-check {
            cursor: pointer;
            margin: 0;
          }
          .form-check-input {
            transform: scale(1.25);
            border-color: #cccccc;
          }
        }
        .dropdown-item:last-child {
          border-top: 1px solid #cccccc;
        }
      }
    }

    .cert-group-dropdown.show {
      button {
        border: 1px solid #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
      }
    }

    .cert-date-picker {
      width: 100%;
      z-index: 10;
    }

    .col-md-4,
    .col {
      padding-right: 0px;
      padding-left: 12px;
    }
  }
  .certificate-pagination-row {
    display: flex;
    justify-content: space-between;
    margin: 10px 0 10px 0;
    align-items: center;

    .certificate-pagination {
      display: flex;
      align-items: center;

      .page-num {
        margin: 0 8px 0 8px;
        cursor: pointer;
      }

      .page-number-border {
        padding: 0px 8px;
        display: flex;
        flex-direction: row;
      }
      .page-num-disabled {
        color: #cccccc;
        cursor: not-allowed;
      }
      .page-num-enabled {
        color: #1f4a70;
      }
      .page-num-active {
        color: #cccccc;
        text-decoration: underline;
        cursor: not-allowed;
      }
      select {
        height: 32px;
        font-size: 14px;
      }
    }

    .certificate-due-tab button {
      height: 32px !important;
      border-radius: 0 !important;
      border-right: 0px;
      font-size: 14px;
      div {
        font-size: 0.9rem;
        line-height: normal;
        vertical-align: middle;
      }
    }

    .certificate-due-tab button:first-child {
      border-radius: 0.2rem 0 0 0.2rem !important;
    }
    .certificate-due-tab button:last-child {
      border-radius: 0 0.2rem 0.2rem 0 !important;
      border-right: 1px solid;
      border-color: #1f4a70;
    }
  }
  .certificate-table .table {
    padding-top: 0;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 170px);
    background: #fff;
    color: #333333;
    border-color: #ccc;
    text-align: left;
    border-top: 2px solid #1f4a70;
    .cert-due-date-badge {
      min-width: 35px;
    }
    .header {
      z-index: 5;
      position: sticky;
    }
    .body {
      .cert-checkbox .form-check-input {
        transform: scale(1.5);
        right: 0.5rem;
        cursor: pointer;
      }

      .cert-checkbox .form-check-input:checked {
        background-color: #0091b8;
        border-color: #0091b8;
      }
    }
  }
  .cert-right-sticky-header {
    right: 0 !important; //override inline style
  }
  .cert-right-sticky-header-adjustment {
    right: 100px !important; //override inline style
  }

  .reference__tabNavigation .nav-pills .nav-link {
    padding: 20px 10px 10px 10px;
    color: #797373 !important;
    cursor: pointer;
    background-color: inherit;
    border: none;
    outline: none;
    border-radius: 0 !important;
  }

  .reference__tabNavigation {
    display: flex;
    flex-direction: column;
    gap: 5px;
    flex: 1;
  }

  .reference__tabNavigation .nav-pills {
    gap: 8px;
    border-bottom: 1px solid #ccc;
  }

  .reference__tabNavigation .nav-pills .nav-link.active {
    color: #1e4a70 !important;
    background-color: inherit !important;
    border-bottom: 1.5px solid #1e4a70;
    font-weight: 900;
    outline: none;
    margin: 0 !important;
  }

  div.td:has(.border-right-cell) {
    border-right: 1px solid #cccccc;
  }

  div.td:has(.border-left-cell) {
    border-left: 1px solid #cccccc;
  }

  .th.border-left-cell {
    border-left: 1px solid #cccccc;
  }

  .th.border-right-cell {
    border-right: 1px solid #cccccc;
  }

  .vessel-table {
    .crt-table-top-header {
      .text-left {
        text-align: center !important;
        width: 100%;
      }
    }
  }
  .vessel-table .table .body > .tr > .td {
    display: flex;
    align-items: center;

    .cert-due-date-badge {
      height: 20px;
    }
  }

  .ml5 {
    margin-left: 1px !important;
  }

  .badge-light {
    background-color: #edf3f7;
  }

  .badge-pill {
    padding-right: 8px;
    padding-left: 8px;
    border-radius: 10rem;
    padding-top: 2px;
    padding-bottom: 2px;
  }

  .badge {
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
  }

  #send-survey-button:disabled {
    color: #aaaaaa;
    background-color: #efefef;
    border: none;
  }

  #search-bar {
    height: 32px;
  }

  #cert-date-picker-div {
    input {
      height: 32px;
    }
  }

  .vessel-table .table {
    font-size: 14px;
  }
  .custom-table-height {
    height: 64px;
  }

  .cert-checkbox {
    margin-bottom: 20px;
  }

  .checkbox-container input {
    height: 12px;
    width: 12px;
    margin-right: 0.25rem;
    accent-color: #1f4a70;
  }
  input[type='checkbox'] {
    accent-color: #0091b8 !important;
  }

  .tooltip-container {
    position: relative;
    display: inline-block;
  }

  .custom-tooltip {
    position: absolute;
    top: 100%;
    left: 80%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.926);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    z-index: 10;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
    margin-top: 8px;
  }

  .custom-tooltip::before,
  .custom-tooltip::after {
    content: none !important;
  }

  .tooltip-arrow {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.926);
    width: 12px;
    height: 6px;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  }

  div.crt-fallback-main-div {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40vh;
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;

    .info-icon {
      width: 42px;
      height: 42px;
      border: 2px solid #6c757d; /* Border color */
      border-radius: 50%; /* Makes it a circle */
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: Arial, sans-serif;
      font-size: 16px;
      font-weight: bold;
      color: #6c757d;
      position: relative;
    }

    .info-icon::before {
      content: 'i';
      position: absolute;
    }
  }

  .crt-fallback-main-div {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40vh;
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;

    .crt-fallback-child-div {
      display: flex;
      flex-direction: column; /* Stack icon and text vertically */
      align-items: center; /* Center horizontally */
      text-align: center; /* Ensure text is centered */
    }

    .info-icon {
      width: 42px;
      height: 42px;
      border: 2px solid #6c757d; /* Circle border */
      border-radius: 50%; /* Makes it round */
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      color: #6c757d;
      position: relative;
    }

    .info-icon::before {
      content: 'i'; /* Display "i" inside the circle */
      font-size: 16px;
    }

    .text-below {
      margin-top: 5px; /* Adjust spacing */
      font-size: 14px;
      color: #6c757d;
    }
  }

  .crt-wrapper-datepicker {
    position: relative;
    display: flex;
    align-items: center;

    .react-datepicker-ignore-onclickoutside:focus {
      color: #495057;
      background-color: #fff;
      border-color: #80bdff;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
}

ul#cert-action-popover li {
  padding: 5px 5px 5px 10px;
  border-radius: 7px;
}

ul#cert-action-popover li:hover {
  padding: 5px 5px 5px 10px;
  border-radius: 7px;
  background-color: #e9f1f5;
}

#survey-certificate-modal {
  .modal-content {
    height: 85vh;
    overflow-y: scroll;

    .modal-title {
      font-size: 20px;
      font-weight: 500;
      line-height: 28px;
    }

    .from-label {
      font-weight: 500;
    }
  }
  .btn-secondary:disabled {
    color: black;
    background-color: #efefef;
    border-color: #efefef;
  }

  .document-thumbnail {
    width: 165%;
  }

  .box-container input {
    height: 12px;
    width: 12px;
    margin-right: 0.25rem;
    accent-color: #1f4a70;
  }

  input[type='radio'] {
    accent-color: #0091b8 !important;
    width: 16px;
    height: 16px;
  }

  .crt-wrapper-datepicker {
    position: relative;
    display: flex;
    align-items: center;

    .react-datepicker-ignore-onclickoutside {
      border: 1px solid #80bdff;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    input:focus {
      border: 1px solid #80bdff;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }
  }

  .interval-angle-icon {
    margin-right: -15px;
    margin-top: -1px;
  }

  .crt-assign-certificate-remark {
    padding: 0px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    margin-top: -10px;
    textarea {
      height: 20vh;
    }

    span {
      margin-left: auto;
      align-self: flex-end;
      color: #6c757d;
    }
  }

  .crt-file-text-container {
    padding-left: 0px;

    h6 {
      margin-bottom: 0px;
      margin-top: 5px;
      word-wrap: break-word;
    }
  }

  .cert-group-dropdown {
    button {
      padding: 0.25rem 0.5rem;
      font-size: 0.875rem;
      line-height: 1.5;
      border-radius: 0.2rem;
      width: 100%;
      background: #fff;
      color: #555b60;
      border: 1px solid #ced4da;
      text-align: left;
      position: relative;
      height: 32px;
    }

    button::after {
      position: absolute;
      right: 1rem;
      margin-top: 3px;

      border-top: 0px;
      border-left: 0px;
      width: 8px;
      height: 8px;
      border-right: 2px solid #6c757d;
      border-bottom: 2px solid #6c757d;
      transform: rotate(45deg);
      margin-left: 5px;
    }

     button.show {
      color: #495057;
      background-color: #fff;
      border-color: #80bdff;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }
  }

}

#send-email-modal {
  width: 50vw;
  .message-input {
    max-height: 36vh;
  }
  .modal-content {
    overflow-y: scroll;
  }
  .btn-secondary:disabled {
    color: black;
    background-color: #efefef;
    border-color: #efefef;
  }

  .right-column {
    margin-left: 0px;
  }
  .email-form {
    gap: 1rem;
    padding-bottom: 0;
  }

  a {
    font-size: 0.8rem;
    word-break: break-all;
    text-align: center;
  }
  .send-emil-doc-div-wrap {
    background-color: #f5f5f5;
    padding: 5px;
    margin-bottom: 10px;

    .document-thumbnail {
      width: 180%;
    }
  }

  label {
    font-weight: bold;
  }
  .infotext {
    background-color: #e5f4f8;
    margin-left: 1px;
    padding: 10px;
  }

  .infotext b {
    margin-left: 5px;
    margin-right: 5px;
  }
}

#survey-confirm-modal {
  .modal-body {
    margin-top: -15px;
  }
  .model-body-content {
    font-size: 0.9rem;
    padding: 10px;
    background-color: #fff9e8;
    color: #bf7f05;
  }
}

#assigned-cert-to-vessel {
  .cert-right-sticky-header-adjustment {
    right: 100px !important; //override inline style
  }
  div.td:has(.border-right-cell) {
    border-right: 1px solid #cccccc;
  }

  div.td:has(.border-left-cell) {
    border-left: 1px solid #cccccc;
  }

  .th.border-left-cell {
    border-left: 1px solid #cccccc;
  }

  .th.border-right-cell {
    border-right: 1px solid #cccccc;
  }
  .table {
    border-top: 2px solid #1f4a70;
    padding-top: 0px;
  }

  .certificate-filter-row {
    margin: 1rem 0 0.5rem 0;
    display: flex;
    flex-direction: row;
    justify-content: start;
  }

  .cert-group-dropdown {
    padding: 0;
    width: 100%;
    button {
      padding: 0.25rem 0.5rem;
      font-size: 0.875rem;
      line-height: 1.5;
      border-radius: 0.2rem;
      width: 100%;
      background: #fff;
      color: #1f4a70;
      border-color: #ccc;
      text-align: left;
      position: relative;
    }

    button::after {
      position: absolute;
      right: 1rem;
      margin-top: 3px;
    }

    button:active {
      background: #fff;
      color: #1f4a70;
    }

    .dropdown-menu {
      padding-bottom: 0;
      width: 100%;
      .dropdown-item {
        padding: 0.25rem 1rem;
        .form-check {
          cursor: pointer;
          margin: 0;
        }
        .form-check-input {
          transform: scale(1.25);
          border-color: #cccccc;
        }
      }
      .dropdown-item:last-child {
        border-top: 1px solid #cccccc;
      }
    }
  }
}

.sticky-header,
.sticky-footer {
  position: sticky;
  z-index: 1050; /* Ensures the sticky elements are above other content */
}

.sticky-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
  top: 0;
  background-color: #fff; /* Matches the modal's background */
}

.sticky-footer {
  bottom: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background-color: #fff; /* Matches the modal's background */
}

.vessel-certificate-header-vessel-list .page-number-border {
  border: none;
}

.vessel-certificate-header-vessel-list .pagination-count-wrapper {
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  padding-left: 0 !important;
}

.vessel-certificate-header-vessel-list select {
  height: 32px !important;
}

.vessel-certificate-header-vessel-list {
  font-size: 14px;
}

#assigned-cert-to-vessel {
  .certificate-filter-row button,
  input {
    height: 32px !important;
    display: flex !important;
  }
}

#assigned-cert-to-vessel .certificate-filter-row button {
  place-items: center;
  max-width: 33vw;
  overflow: hidden;
}

#assigned-cert-to-vessel input[type='checkbox'] {
  accent-color: #0091b8 !important;
}

#assigned-cert-to-vessel .vessel-table .table .td {
  display: flex;
  align-items: center;
}

#assigned-cert-to-vessel .certificate-filter-row .cert-group-dropdown button::after {
  position: absolute;
  right: 1rem;
  margin-top: -2px;
  border-top: 0px;
  border-left: 0px;
  width: 8px;
  height: 8px;
  border-right: 2px solid #6c757d;
  border-bottom: 2px solid #6c757d;
  transform: rotate(45deg);
  margin-left: 5px;
}

#assigned-cert-to-vessel .cert-group-dropdown button.show {
  color: #495057;
  background-color: #fff;
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

#assigned-cert-to-vessel {
  .certificate-filter-row .col,
  .col-md-4 {
    padding: 0 !important;
    padding-right: 12px !important;
  }
}

#assigned-cert-to-vessel .form-check-input {
  height: fit-content !important;
}

.react-datepicker-popper {
  z-index: 1000;
}

.actions-border-left-cell {
  right: 0 !important;
  border: none !important;
}

.no-border-left-cell {
  left: 0 !important;
}

.annual-header-vessel-certs-table div {
  width: 100%;
  text-align: center !important;
}

.annual-header-vessel-certs-table {
  height: 44px !important;
}

.cert-due-date-badge {
  height: 20px;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 12px;
}

.assigned-certificates-to-vessel-list .table {
  max-height: calc(100vh - 300px);
  overflow-y: scroll;
}

.assigned-certificates-to-vessel-list .table .header {
  z-index: 5;
  position: sticky;
}

.assigned-certificates-to-vessel-list .table.sticky {
  overflow-x: auto;
}

.assigned-certificates-to-vessel-list .table .header:first-child(div) {
  height: 44px !important;
}

.survey-certificate-modal-v2 .form-check {
  display: flex;
  place-items: center;
}

.survey-certificate-modal-v2 .form-check input {
  margin-bottom: 5px;
}

body:has(div.assigned-certificates-to-vessel-list) {
  .popover {
    z-index: 4;
  }
  .cert-action-popoverMenu {
    list-style-type: none;
    padding-left: 5px;
    padding-right: 5px;
    margin: 0;
    li {
      cursor: pointer;
      margin: 10px;
    }
  }
  .uploader-drag-drop-container {
    border-radius: 4px;
    border: 1px solid #ccc;
  }
  .uploader-browse-files {
    text-decoration: underline;
    color: #1f4a70;
  }

  .uploader-label {
    font-size: 14px;
    color: #6c757d;
  }
  .upload-item-ul {
    list-style-type: none;
    gap: 8px;
    display: grid;
    padding-left: 0px;
  }

  .upload-item-li {
    background-color: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
}

.cert-title-badge-pill {
  height: fit-content;
  font-size: 12px;
  margin-left: 10px;
}

.assigned-certificates-to-vessel-list .dropdown-menu {
  max-height: 300px;
  overflow-y: scroll;
}

.form-group {
  .cert-group-dropdown {
    padding: 0;
    width: 100%;
    button {
      padding: 0.25rem 0.5rem;
      font-size: 0.875rem;
      line-height: 1.5;
      border-radius: 0.2rem;
      width: 100%;
      background: #fff;
      color: #1f4a70;
      border-color: #ccc;
      text-align: left;
      position: relative;
    }
    btn-primary {
      background: #fff;
      color: #000;
      border: black;
    }
    button::after {
      position: absolute;
      right: 1rem;
      margin-top: 3px;
    }

    button:active {
      background: #fff;
      color: #1f4a70;
    }

    .dropdown-menu {
      padding-bottom: 0;
      width: 100%;
      .dropdown-item {
        padding: 0.25rem 1rem;
        .form-check {
          cursor: pointer;
          margin: 0;
        }
        .form-check-input {
          transform: scale(1.25);
          border-color: #cccccc;
        }
      }
      .dropdown-item:last-child {
        border-top: 1px solid #cccccc;
      }
    }
  }
}
.certificate-filter-row .react-datepicker__close-icon::after {
  margin-right: 30px;
}

.vessel-type-info-wrapper {
  background-color: #e5f4f8;
  padding: 10px;
  color: #127392;
}

.applicable-vessel-error {
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #dc3545;
  display: block;
  margin-left: -20px;
}

.mr12 {
  margin-right: 12px;
}
.verification-status {
  width: 71px;
  height: 24px;
  padding: {
    top: 2px;
    right: 8px;
    bottom: 2px;
    left: 8px;
  }
  gap: 10px;
  span {
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    text-align: center;
  }
  border-radius: 4px;
}
.verification-status-pending {
  background-color: #fff9e8;
  span {
    color: #f08100;
  }
}
.verification-status-verified {
  background-color: #e8f5eb;
  span {
    color: #218838;
  }
}
.verification-status-correction {
  background-color: #faf2f5;
  span {
    color: #c82333;
  }
}

.crt-admin-survey-certificate-modal {
  .modal-title {
    font-size: 20px;
    font-weight: 500;
  }

  .modal-content {
    height: 100% !important;
  }

  .crt-admin-top-body {
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;

    select {
      height: 32px;
      font-size: 14px;
    }

    .cert-group-dropdown {
      button {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
        width: 100%;
        background: #fff;
        color: #555b60;
        border: 1px solid #ced4da;
        text-align: left;
        position: relative;
        height: 32px;
      }

      button::after {
        position: absolute;
        right: 1rem;
        margin-top: 3px;

        border-top: 0px;
        border-left: 0px;
        width: 8px;
        height: 8px;
        border-right: 2px solid #6c757d;
        border-bottom: 2px solid #6c757d;
        transform: rotate(45deg);
        margin-left: 5px;
      }

      button.show {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
      }
    }
  }

  .form-label {
    font-weight: 500;
  }

  .basic-checkbox input {
    height: 12px;
    width: 12px;
    margin-right: 0.25rem;
    accent-color: #1f4a70;
  }
  input[type='checkbox'] {
    accent-color: #0091b8 !important;
  }

  .admin-tech-group {
    margin-bottom: 0px;
    button {
      height: 32px;
      font-size: 14px;
    }
  }

  span.admin-helper-text {
    font-size: 12px;
  }

  .crt-admin-bottom-body {
    label {
      padding: 4px 4px 0px 4px;
      margin-bottom: 0px;
      font-size: 14px;
      font-weight: 500;
    }

    p {
      font-size: 14px;
      font-weight: 400;
      color: #6c757d;
    }

    input[type='checkbox'] {
      width: 20px;
      height: 20px;
    }
  }

  .ml6 {
    margin-left: 4rem;
  }
}

.custom-tooltip-vessel-list .tooltip-arrow {
  transform: rotate(90deg) !important;
  left: 50% !important;
}

.crt-pdf-page-number {
  display: flex;
  justify-content: flex-end;
  justify-content: center;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 5px;
  font-weight: bold;
  pointer-events: none;
  color: black;
  margin-left: 50%;
  width: 100px;
}

.cert-group-dropdown label {
  color: #212529;
}
