import React, { useContext, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import moment from 'moment';
import { ChatRightTextFill } from 'react-bootstrap-icons';
import classNames from 'classnames';
import '../../pages/styles/monthly-financial.scss';
import CustomTable from '../customComponent/CustomTable';
import {
  ONGOING_AND_FINALIZED_UNDER_REVIEW_RE_OPENED_APPROVED_STATUSES,
  STATUS,
  SUBMITTED_AND_RE_OPENED_STATUSES,
  VESSEL_STATUS_PENDING_HANDED_OVER,
} from '../../constants/montlyFinancialReprtStatus';
import { DetailContext } from '../../context/DetailContext';
import { OverlayTrigger, Popover } from 'react-bootstrap';
import styleGuide from '../../styleGuide';
const { Icon } = styleGuide;

const commentsBifurcationGenrator = (value) => {
  const countOrNullGenrator = (val) =>
    val?.resolved === 0 && val?.total === 0 ? '-' : `${val?.resolved}/${val?.total}`;
  return `Vessel Accountant: ${countOrNullGenrator(
    value?.vesselAccountant,
  )} \nSuperintendent: ${countOrNullGenrator(value?.superintendent)} \nOwner: ${countOrNullGenrator(
    value?.owner,
  )} \nOthers: ${countOrNullGenrator(value?.others)}`;
};

const MonthlyFinancialReportTable = ({
  data,
  isLoading,
  setData,
  vesselId,
  setReopenId,
  isCurrentVa,
  isOwner,
  bifercationData,
  setOpenGenerateModal,
  handleDeleteModal,
}) => {
  const { roleConfig } = useContext(DetailContext);
  const history = useHistory();
  const FinancialSheetColumn = useMemo(
    () => [
      {
        Header: 'Month',
        accessor: 'name',
        Cell: (props) => {//NOSONAR
          const {
            value,
            row: { original },
          } = props;
          return (
            <div
              className={`d-flex gap-1 cursor-pointer`}
              dataTestId={`fml-monthly-financial-report-${value}`}//NOSONAR
              onClick={() => {
                history.push(`/owner-reporting/${vesselId}/reports/${original.id}`);
              }}
              aria-hidden="true"
            >
              {value}
              {original?.version && !isOwner ? ` V.${original?.version}` : null}
            </div>
          );
        },
      },
      {
        Header: 'Status',
        accessor: '',
        disableSortBy: true,
        Cell: (props) => {//NOSONAR
          const {
            row: { original },
          } = props;
          return (
            <div
              className={`report-status-tag report-status-tag__${
                STATUS[original.status]?.className
              }`}
            >
              {STATUS[original.status]?.label}
            </div>
          );
        },
      },
      {
        Header: 'Accountant',
        accessor: 'accountantName',
        disableSortBy: true,
      },
      {
        Header: 'Updated On',
        accessor: 'modifiedOn',
        Cell: ({ value }) => (//NOSONAR
          <div className="d-flex align-items-center gap-1">
            <span>
              {moment(value).isValid()
                ? moment(value).tz('Asia/Hong_Kong').format('DD MMM YYYY, HH:mm')
                : null}
            </span>
          </div>
        ),
        disableSortBy: true,
      },
      {
        Header: '# of Comments',
        accessor: 'comments',
        disableSortBy: true,
        Cell: ({ row: { original }, value }) => (//NOSONAR
          <div
            title={
              bifercationData &&
              value.total !== 0 &&
              commentsBifurcationGenrator(
                bifercationData?.filter((i) => i?.reportId === original?.id)?.[0]
                  ?.commentsBifurcation,
              )
            }
            dataTestId={`fml-monthly-financial-report-comments-${original?.id}`}//NOSONAR
            className={classNames('d-flex comments-text-gap1 align-items-center', {
              'comments-text': value.total !== 0,
              'cursor-pointer': value.total > 0,
              'comments-text__disable': value.total === 0,
            })}
            onClick={() => {
              value.total > 0 &&
                history.push(
                  `/owner-reporting/${vesselId}/reports/${original.id}?openViewComments=true`,
                );
            }}
            aria-hidden="true"
          >
            <ChatRightTextFill width={15} height={15} color="#1e4a70" fill="inherit" />
            <span>
              {value.resolved}/{value.total}
            </span>
          </div>
        ),
      },
    ],
    [bifercationData],
  );
  const reportFormatVersionColumn = {
    Header: 'Format',
    accessor: '',
    Cell: ({ row: { original } }) => (//NOSONAR
      <div>{original?.reportFormatVersion === 2 ? 'New' : 'Old'}</div>
    ),
    disableSortBy: true,
  };
  const ReopenColumn = () => {
    const actionsCoulumn = {
      Header: 'Actions',
      id: 'actions',
      classNames: 'actions-column-test',
      Cell: ({ row: { original } }) => {//NOSONAR
        const [showActions, setShowActions] = useState(false);
        const canGenrateNewReportButton =
          original?.canCreateNextReport &&
          original?.canGenrateNewReport &&
          original?.vesselStatus &&
          VESSEL_STATUS_PENDING_HANDED_OVER.includes(original?.vesselStatus) &&
          ((original?.status === 'NOT_APPROVED' && original?.submittedOn) ||
            SUBMITTED_AND_RE_OPENED_STATUSES.includes(original.status));
        const canDeleteReportButton =
          original?.canDeleteManually &&
          roleConfig?.ownerReporting?.candeleteReport &&
          original?.version === 0 &&
          ((original?.status === 'NOT_APPROVED' && !original?.submittedOn) ||
            ONGOING_AND_FINALIZED_UNDER_REVIEW_RE_OPENED_APPROVED_STATUSES.includes(original?.status));
        const isDisabled = !canGenrateNewReportButton && !canDeleteReportButton;
        return (
          <div
            className={classNames('ofr-actions-icon', {
              'ofr-disabled-pointer': isDisabled,
            })}
            data-testid="ofr-tableAction-list"
            onClick={(e) => {
              setShowActions(!showActions);
              e.stopPropagation();
            }}
            aria-hidden="true"
          >
            {!isDisabled && <OverlayTrigger
              trigger="click"
              key="bottom"
              placement="bottom"
              data-testid="ofr-tableAction-list"
              show={showActions && !isDisabled}
              overlay={
                <Popover>
                  <Popover.Body>
                    <ul className="ofr-actions-list">
                      {canGenrateNewReportButton && (
                        <li
                          data-testid="fml-vesselList-editLink"
                          onClick={() => setOpenGenerateModal(`01 ${original?.name}`)}
                          aria-hidden="true"
                        >
                          Generate New Report
                        </li>
                      )}
                      {canDeleteReportButton && (
                        <li
                          data-testid="fml-vesselList-editLink"
                          title="Delete Report"
                          onClick={() => handleDeleteModal(original?.id)}
                          aria-hidden="true"
                        >
                          Delete Report
                        </li>
                      )}
                    </ul>
                  </Popover.Body>
                </Popover>
              }
            >
              <div data-testid="ofr-tableAction-list">
                <Icon
                  icon="more"
                  data-testid="ofr-tableAction-list"
                  size={20}
                  className={classNames('cursor-pointer', {
                    'ofr-disabled-pointer': isDisabled,
                  })}
                />
              </div>
            </OverlayTrigger>}
          </div>
        );
      },
      disableSortBy: true,
    };
    return actionsCoulumn;
  };
  if(!isOwner){
    FinancialSheetColumn.splice(2, 0, reportFormatVersionColumn);
  }
  if (isCurrentVa) {
    FinancialSheetColumn.push(ReopenColumn());
  }
  const sortHandler = (value) => {
    const { id } = value[0];
    if (id === 'name') {
      data.reverse();
      setData([...data]);
    }
  };
  return (
    <div className="vessel-table">
      <CustomTable
        column={FinancialSheetColumn}
        reportData={data}
        tableRef={null}
        setSortData={sortHandler}
        isLoading={isLoading}
        name="fml-owner-reporting-list"
      />
    </div>
  );
};
export default MonthlyFinancialReportTable;
