/* eslint-disable react/display-name */
/* eslint-disable react/jsx-key */
import React, { useContext, useMemo } from 'react';
import { useSticky } from 'react-table-sticky';
import { useTable, useFlexLayout } from 'react-table';
import Spinner from '../Spinner';
import { Link } from 'react-router-dom';
import { formatValue, formatInternationalPhoneNumber } from '../../util/view-utils';
import { setLocalStorage } from '../../util/local-storage-helper';
import { LOCAL_STORAGE_FIELDS } from '../../model/constants';
import NoResult from '../NoResult';
import { base64_encode } from '../../util/getURLParams';
import { DetailContext } from '../../context/DetailContext';

const { PARIS2_URL } = process.env;

const ShipPartyTable = ({ data, tableRef, isLoading, ga4EventTrigger }) => {
  const { roleConfig } = useContext(DetailContext);
  const defaultColumn = useMemo(
    () => ({
      minWidth: 30,
      width: 150,
      maxWidth: 90,
    }),
    [],
  );

  const columns = useMemo(
    () => [
      {
        Header: 'Party Type',
        id: 'party_type',
        accessor: (row) => formatValue(row.ship_party_type.name),
        name: 'party_type',
      },
      {
        Header: 'Party Name',
        id: 'party_name',
        Cell: ({ row: { original } }) => (
          roleConfig.shipPartyViewGeneral ? <Link
            className="button-link"
            to={`/ship-party/details/${original.id}`}
            onClick={() =>
              ga4EventTrigger('View ship party', 'Vessel Contingency - Link', 'Vessel Contingency')
            }
          >
            {original.name}
          </Link> : formatValue(original.name)
        ),
        name: 'party_name',
      },
      {
        Header: 'Contact Person',
        id: 'contact_person',
        accessor: (row) => formatValue(row.ship_party_contact_person[0]?.contact_person_name),
        name: 'contact_person',
      },
      {
        Header: 'Phone',
        id: 'phone',
        accessor: (row) => formatInternationalPhoneNumber(row.telephone_office),
        name: 'phone',
      },
      {
        Header: 'Phone (After Hours)',
        id: 'phone_after_hrs',
        accessor: (row) => formatInternationalPhoneNumber(row.telephone_office_after_hours),
        name: 'phone_after_hrs',
      },
      {
        Header: 'Mobile',
        id: 'mobile',
        accessor: (row) =>
          formatInternationalPhoneNumber(row?.ship_party_contact_person[0]?.mobile_phone),
        name: 'mobile',
      },
      {
        Header: 'Email',
        id: 'email',
        Cell: ({ row: { original } }) =>
          original.ship_party_office_emails.length > 0
            ? original.ship_party_office_emails.map((email, index) => (
                <>
                  {index > 0 && ', '}
                  <Link
                    className="button-link"
                    to={{ pathname: `mailto:${email.office_email}` }}
                    target="_blank"
                    onClick={() =>
                      ga4EventTrigger(
                        'Email ship party',
                        'Vessel Contingency - Link',
                        'Vessel Contingency',
                      )
                    }
                  >
                    {email.office_email}
                  </Link>
                </>
              ))
            : formatValue(original.ship_party_office_emails[0]?.office_email),
        name: 'email',
      },
      {
        Header: 'Contingency Plan',
        id: 'plan',
        Cell: ({ row: { original } }) =>
          original.doc_url ? (
            <Link
              to={{
                pathname: `${PARIS2_URL}/vessel/document?id=${
                  original.id
                }&source=contingency&path=${base64_encode(original?.doc_url)}`,
              }}
              target="_blank"
              className="button-link"
              onClick={() =>
                ga4EventTrigger(
                  'View ship party file',
                  'Vessel Contingency - Link',
                  'Vessel Contingency',
                )
              }
            >
              View
            </Link>
          ) : (
            '- - -'
          ),
      },
    ],
    [],
  );

  setLocalStorage(LOCAL_STORAGE_FIELDS.contingencyContactDataKey, {
    jsonData: data,
    columns: columns,
    title: 'CONTACT',
  });

  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows } = useTable(
    {
      columns,
      data,
      defaultColumn,
    },
    useSticky,
    useFlexLayout,
  );

  return (
    <div className="vessel-table">
      <div {...getTableProps()} className="table sticky" ref={tableRef}>
        <div className="header">
          {headerGroups.map((headerGroup, i) => (
            <div key={i} {...headerGroup.getHeaderGroupProps()} className="tr">
              {headerGroup.headers.map((column, j) => (
                <div
                  key={j}
                  {...column.getHeaderProps()}
                  className={`th ${column.customClass}`}
                  data-testid={`fml-contingency-ship-party-table-column-header-${column.render(
                    'Header',
                  )}`}
                >
                  {column.render('Header')}
                </div>
              ))}
            </div>
          ))}
        </div>
        {isLoading && <Spinner alignClass={'spinner-table'} />}
        <div {...getTableBodyProps()} className="body">
          {rows.length > 0
            ? rows.map((row, i) => {
                prepareRow(row);
                return (
                  <div key={i} {...row.getRowProps()} className="tr">
                    {row.cells.map((cell, j) => (
                      <div
                        key={j}
                        {...cell.getCellProps()}
                        className="td"
                        data-testid={`fml-contingency-ship-party-row-${i}-${cell.column.Header}`}
                      >
                        {cell.render('Cell')}
                      </div>
                    ))}
                  </div>
                );
              })
            : !isLoading && <NoResult />}
        </div>
      </div>
    </div>
  );
};

export default ShipPartyTable;
