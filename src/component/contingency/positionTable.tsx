import React, { useMemo } from 'react';
import { useSticky } from 'react-table-sticky';
import { useTable, useFlexLayout } from 'react-table';
import Spinner from '../Spinner';
import { Link } from 'react-router-dom';
import { formatValue, formatDate, parseDMS } from '../../util/view-utils';
import { setLocalStorage } from '../../util/local-storage-helper';
import { LOCAL_STORAGE_FIELDS } from '../../model/constants';
import NoResult from '../NoResult';

const PositionTable = ({ data, tableRef, isLoading, ownershipId, ga4EventTrigger }) => {
  const defaultColumn = useMemo(
    () => ({
      minWidth: 30,
      width: 150,
      maxWidth: 90,
    }),
    [],
  );
  const reportType = data[0]?.report_type;
  const columns = useMemo(() => {
    const result = [
      {
        Header: 'Report Date',
        accessor: (row) => formatDate(row.gmt, 'DD MMM YYYY'),
        id: 'report_date',
        name: 'report_date',
      },
      {
        Header: 'View Report',
        id: 'view_report',
        accessor: (row) => (
          <div className="ship-report-wrapper__underline">
            <Link
              className="button-link"
              to={`/vessel/report/technical/position/${
                row.vessel_ownership_id
              }/compare?gmt=${formatDate(row.gmt, 'YYYY-MM-DD')}`}
              onClick={() =>
                ga4EventTrigger(
                  'View position report',
                  'Vessel Contingency - Link',
                  'Vessel Contingency',
                )
              }
            >
              View report
            </Link>
          </div>
        ),
      },
      {
        Header: 'Time (SMT)',
        id: 'smt',
        accessor: (row) => formatDate(row.smt, 'HH:mm'),
        name: 'smt',
      },
      {
        Header: 'Time (GMT)',
        id: 'gmt',
        accessor: (row) => formatDate(row.gmt, 'HH:mm'),
        name: 'gmt',
      },
      {
        Header: 'Location',
        id: 'location',
        accessor: (row) => row.report_type,
        name: 'location',
      },
      {
        Header: 'Position',
        id: 'position',
        accessor: (row) => (
          <div className="ship-report-wrapper__underline">
            {row.report_json.general.latitude && row.report_json.general.longitude ? (
              <a
                className="button-link"
                target="_blank"
                href={`http://maps.google.com/maps?q=${parseDMS({
                  latitude: row.report_json.general.latitude,
                  longitude: row.report_json.general.longitude,
                })}`}
                rel="noreferrer"
                onClick={() =>
                  ga4EventTrigger(
                    'View position report coordinates',
                    'Vessel Contingency - Link',
                    'Vessel Contingency',
                  )
                }
              >
                {row.report_json.general.latitude + row.report_json.general.longitude}
              </a>
            ) : (
              '- - -'
            )}
          </div>
        ),
        name: 'position',
      },
      {
        Header: 'Speed',
        id: 'speed',
        accessor: (row) => formatValue(row.report_json.general.average_speed),
        name: 'speed',
        type: 'number',
      },
    ];
    if (reportType === 'PORT') {
      result.push(
        {
          Header: 'ETC',
          id: 'etc',
          accessor: (row) => formatDate(row.report_json.general.etd, 'DD MMM YYYY HH:mm'),
          name: 'etc',
        },
        {
          Header: 'Port',
          id: 'port',
          accessor: (row) => formatValue(row.report_json.general.port),
          name: 'port',
        },
      );
    } else if (reportType === 'SEA') {
      result.push(
        {
          Header: 'ETA',
          id: 'eta',
          accessor: (row) => formatDate(row.report_json.general.eta, 'DD MMM YYYY HH:mm'),
          name: 'eta',
        },
        {
          Header: 'Next Port',
          id: 'next_port',
          accessor: (row) => formatValue(row.report_json.general.next_port),
          name: 'next_port',
        },
      );
    }
    return result;
  }, [data[0]]);

  setLocalStorage(LOCAL_STORAGE_FIELDS.contingencyPositionDataKey, {
    jsonData: data,
    columns: columns,
    title: 'POSITION REPORT',
  });

  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows } = useTable(
    {
      columns,
      data,
      defaultColumn,
    },
    useSticky,
    useFlexLayout,
  );

  return (
    <div className="vessel-table">
      <div {...getTableProps()} className="table sticky" ref={tableRef}>
        <div className="header">
          {headerGroups.map((headerGroup, i) => (
            <div key={i} {...headerGroup.getHeaderGroupProps()} className="tr">
              {headerGroup.headers.map((column, j) => (
                <div
                  key={j}
                  {...column.getHeaderProps()}
                  className={`th ${column.customClass}`}
                  data-testid={`fml-contingency-position-table-column-header-${column.render(
                    'Header',
                  )}`}
                >
                  {column.render('Header')}
                </div>
              ))}
            </div>
          ))}
        </div>
        {isLoading && <Spinner alignClass={'spinner-table'} />}
        <div {...getTableBodyProps()} className="body">
          {rows.length > 0
            ? rows.map((row, i) => {
                prepareRow(row);
                return (
                  <div key={i} {...row.getRowProps()} className="tr">
                    {row.cells.map((cell, j) => (
                      <div
                        key={j}
                        {...cell.getCellProps()}
                        data-testid={`fml-contingency-position-row-${i}-${cell.column.Header}`}
                        className="td"
                      >
                        {cell.render('Cell')}
                      </div>
                    ))}
                  </div>
                );
              })
            : !isLoading && <NoResult />}
        </div>
      </div>
    </div>
  );
};

export default PositionTable;
