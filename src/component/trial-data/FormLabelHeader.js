/* eslint-disable react/prop-types */
import React from 'react';
import { Col, Form, Row } from 'react-bootstrap';
import PropTypes from 'prop-types';

const FormLabelHeader = ({ labels = [], gridOption = {}, extraCol }) => {
  const { span, offset } = gridOption;
  return (
    <Row className="form-row">
      <Col md="1" style={{ maxWidth: '5rem' }} />
      {labels.map((label, index) => (
        <Form.Label
          key={`${label}-${span}-${offset}`}
          className="mb-2"
          as={Col}
          md={{ span, offset: index !== 0 ? offset : 0 }}
        >
          {label}
        </Form.Label>
      ))}
      {extraCol ?? null}
    </Row>
  );
};

FormLabelHeader.propTypes = {
  labels: PropTypes.arrayOf(PropTypes.string),
  gridOption: PropTypes.object,
  extraCol: PropTypes.element,
};

const FormTrialSectionHeader = ({ title }) => (
  <Row className="mb-3 ml-0">
    <h6 style={{ textTransform: 'uppercase', color: '#1F4A70' }}>{title}</h6>
  </Row>
);

export { FormLabelHeader, FormTrialSectionHeader };
