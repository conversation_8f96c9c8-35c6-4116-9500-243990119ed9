import React, {
  createRef,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useHistory } from 'react-router-dom';
import {
  Col,
  Row,
  Tab,
  Form,
  Container,
  Button,
  ButtonToolbar,
  Modal,
  Alert,
} from 'react-bootstrap';
import { ErrorPage, Icon } from '../../styleGuide';
import TabWrapper from '../TabWrapper';
import { BreadcrumbHeader } from '../BreadcrumpHeader';
import _ from 'lodash';
import Spinner from '../Spinner';
import { Typeahead } from 'react-bootstrap-typeahead';
import vesselService from '../../service/vessel-service';
import { formatValue, isFloatValue } from '../../util/view-utils';
import ControlParameterReports from './ControlParameterReports';
import { ChevronExpand } from 'react-bootstrap-icons';
import getURLParams from '../../util/getURLParams';
import { TechnicalReportContext } from '../../context/TechnicalReportContext';
import { errorMessageList, maxValue, minValue, REQUIREMENT_OPTIONS } from '../../model/constants';
import CustomCopyModal from '../customComponent/CustomCopyModal';
import ConfirmModal from '../customComponent/CustomConfirmationModal';
import ErrorAlert from '../ErrorAlert';

const { PARIS2_URL } = process.env;

const controlParametersPageTabData = [
  {
    eventKey: 'position',
    tabName: 'Position Report',
  },
  {
    eventKey: 'performance',
    tabName: 'ME Performance',
  },
  {
    eventKey: 'monthly',
    tabName: 'Monthly Report',
  },
  {
    eventKey: 'quarterly',
    tabName: 'Quarterly Report',
  },
  {
    eventKey: 'voyage',
    tabName: 'Voyage Report',
  },
];

const ControlParameters = () => {
  const {
    vesselList,
    filterData,
    roleConfig,
    ga4EventTrigger = () => {},
  } = useContext(TechnicalReportContext);
  const history = useHistory();
  const tab = getURLParams('tab', history.location.search);
  const action = getURLParams('action', history.location.search);
  const ownershipId = getURLParams('ownershipId', history.location.search);
  const [selectedVessel, setSelectedVessel] = useState(null);
  const [controlParameters, setControlParameters] = useState([]);
  const [controlParametersCopy, setControlParametersCopy] = useState([]);
  const [loading, setLoading] = useState(!!ownershipId);
  const [activeTab, setActiveTab] = useState(tab);
  const [error, setError] = useState([]);
  const [isEdit, setIsEdit] = useState(action === 'edit');
  const [showSaveUnsavedControlModal, setShowSaveUnsavedControlModal] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [isDisabled, setIsDisabled] = useState(true);
  const [modifiedParameter, setModifiedParameter] = useState([]);
  const [showCopyControlParametersModal, setShowCopyControlParametersModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedCopyVessel, setSelectedCopyVessel] = useState([]);
  const [errorMsg, setErrorMsg] = useState('');
  const [showEditCopyButtons, setShowEditCopyButtons] = useState(false);
  const vesselRef = useRef(null);
  const requirementRef = useRef([]);

  const insertUpdate = (
    array,
    element,
    min_value,
    max_value = null,
    requirement_of = null,
    errorMessage = {},
  ) => {
    const i = array.findIndex((_element) => _element.attribute_id === element.attribute_id);
    if (i > -1)
      array[i] = {
        ...element,
        min_value: min_value,
        max_value: max_value,
        requirement_of: requirement_of,
        ...errorMessage,
      };
    else
      array.push({
        ...element,
        min_value: min_value,
        max_value: max_value,
        requirement_of: requirement_of,
        ...errorMessage,
      });
  };

  useEffect(() => {
    if (tab === null) {
      setSelectedVessel(null);
      vesselRef.current.clear();
    }
  }, [tab]);

  const getErrorMessage = (error, type, value, comparedValue) => {
    let minMessage = null;
    let maxMessage = null;
    if (error) {
      if (value != '' && comparedValue != '' && !isNaN(value) && !isNaN(comparedValue)) {
        if (type === 'min_value') {
          if (!_.inRange(value, minValue, maxValue)) {
            minMessage = errorMessageList.invalidRangeMessage;
          } else if (value === comparedValue) {
            minMessage = errorMessageList.invalidEqualityMessage;
          } else if (value > comparedValue) {
            minMessage = errorMessageList.invalidMessge;
          }
        }

        if (type === 'max_value') {
          if (!_.inRange(value, minValue, maxValue)) {
            maxMessage = errorMessageList.invalidRangeMessage;
          } else if (value === comparedValue) {
            maxMessage = errorMessageList.invalidEqualityMessage;
          } else if (value < comparedValue) {
            maxMessage = errorMessageList.invalidMessge;
          }
        }
      } else {
        if (comparedValue === '' || isNaN(comparedValue)) {
          if (type === 'min_value') {
            maxMessage = errorMessageList.invalidBlankMessage;
          } else {
            minMessage = errorMessageList.invalidBlankMessage;
          }
        }
        if (value === '' || isNaN(value)) {
          if (type === 'min_value') {
            minMessage = errorMessageList.invalidBlankMessage;
          } else {
            maxMessage = errorMessageList.invalidBlankMessage;
          }
        }
      }
    }
    return { minMessage, maxMessage };
  };

  const removeItem = (errorData, key) => {
    !_.isEmpty(errorData) &&
      errorData.map((erroritem, index) => {
        if (erroritem.name === key) {
          errorData.splice(index, 1);
          setShowAlert(false);
        }
      });
  };

  const onFieldChange = (report, type, key, value, compareValue = null) => {
    if (controlParametersCopy[report]) {
      const data = [];
      const errorData = [...error];
      const updatedData = [...modifiedParameter];
      const parsedValue =
        type === 'requirement_of' || value === '-' || value === '' ? value : parseFloat(value);
      const parsedCompareValue = parseFloat(compareValue);
      controlParametersCopy[report]?.map((item) => {
        if (item.name === key) {
          switch (type) {
            case 'min_value':
              if (
                value &&
                _.inRange(parsedValue, minValue, maxValue) &&
                parsedCompareValue > parsedValue &&
                (!item.maxMessage || item.maxMessage === errorMessageList.invalidMinimumMessge)
              ) {
                data.push({ ...item, [type]: parsedValue, ...getErrorMessage(false) });
                insertUpdate(
                  updatedData,
                  item,
                  parsedValue,
                  parseFloat(item.max_value),
                  item.requirement_of,
                );
                removeItem(errorData, key);
              } else {
                const errorMessageData = getErrorMessage(
                  true,
                  type,
                  parsedValue,
                  parsedCompareValue,
                );
                data.push({
                  ...item,
                  [type]: parsedValue,
                  ...errorMessageData,
                });
                updatedData.forEach((updatedItem, index) => {
                  if (updatedItem.name === key) {
                    updatedData.splice(index, 1);
                  }
                });
                insertUpdate(
                  errorData,
                  item,
                  parsedValue,
                  parseFloat(item.max_value),
                  item.requirement_of,
                  errorMessageData,
                );
                setError(errorData);
              }
              break;
            case 'max_value':
              if (
                value &&
                _.inRange(parsedValue, minValue, maxValue) &&
                parsedCompareValue < parsedValue &&
                (!item.minMessage || item.minMessage === errorMessageList.invalidMinimumMessge)
              ) {
                data.push({ ...item, max_value: parsedValue, ...getErrorMessage(false) });
                insertUpdate(
                  updatedData,
                  item,
                  parseFloat(item.min_value),
                  parsedValue,
                  item.requirement_of,
                );
                removeItem(errorData, key);
              } else {
                const errorMessageData = getErrorMessage(
                  true,
                  type,
                  parsedValue,
                  parsedCompareValue,
                );
                data.push({
                  ...item,
                  [type]: parsedValue,
                  ...errorMessageData,
                });
                updatedData.forEach((updatedItem, index) => {
                  if (updatedItem.name === key) {
                    updatedData.splice(index, 1);
                  }
                });
                insertUpdate(
                  errorData,
                  item,
                  parseFloat(item.min_value),
                  parsedValue,
                  item.requirement_of,
                  errorMessageData,
                );
              }
              break;

            case 'requirement_of':
              data.push({ ...item, requirement_of: _.get(value[0], 'value', null) });
              insertUpdate(
                updatedData,
                item,
                parseFloat(item.min_value),
                parseFloat(item.max_value),
                _.get(value[0], 'value', null),
              );
              break;

            default:
              console.log('invalid type');
          }
        } else return data.push(item);
      });
      setError(errorData);
      const valueData = {
        ...controlParametersCopy,
      };
      valueData[report] = data;

      setControlParametersCopy(valueData);
      setModifiedParameter(updatedData);
      setIsDisabled(false);
    }
  };

  useEffect(() => {
    if (tab && !_.isEmpty(controlParametersCopy[tab])) {
      requirementRef.current = controlParametersCopy[tab].map(
        (item, index) => requirementRef.current[index] ?? createRef(),
      );
    }
  }, [tab, controlParametersCopy]);

  const columns = useMemo(
    () => [
      {
        Header: 'No.',
        accessor: (row, index) => index + 1,
        sticky: 'left',
        id: 'id',
        name: 'no',
        type: 'text',
        maxWidth: 50,
        disableSortBy: true,
      },
      {
        Header: 'Parameter',
        accessor: (row) => (
          <div>
            <div id={row.name} /> <p>{formatValue(row.control_label)}</p>
          </div>
        ),
        id: 'name',
        name: 'parameter',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Minimum Value',
        accessor: (row) =>
          isEdit ? (
            <div>
              <input
                type="number"
                min={minValue}
                max={maxValue}
                step={0.01}
                value={row.min_value}
                onChange={(e) =>
                  onFieldChange(tab, 'min_value', row.name, e.target.value, row.max_value)
                }
              />
              <p className="text-danger">{row.minMessage}</p>
            </div>
          ) : (
            row.min_value
          ),
        id: 'min_value',
        name: 'minimum',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Maximum Value',
        accessor: (row) =>
          isEdit ? (
            <div>
              <input
                type="number"
                min={minValue}
                max={maxValue}
                step={0.01}
                value={row.max_value}
                onChange={(e) =>
                  onFieldChange(tab, 'max_value', row.name, e.target.value, row.min_value)
                }
              />
              <p className="text-danger">{row.maxMessage}</p>
            </div>
          ) : (
            row.max_value
          ),
        id: 'max_value',
        name: 'maximum',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Requirement of',
        accessor: (row, index) =>
          isEdit ? (
            <div className="position-relative">
              <Typeahead
                ref={requirementRef.current[index]}
                id="basic-typeahead-single"
                labelKey="value"
                name="requirement_of"
                placeholder="Please select"
                onChange={(event) => onFieldChange(tab, 'requirement_of', row.name, event)}
                options={REQUIREMENT_OPTIONS}
                selected={checkRequirementSelectDropdown(row.requirement_of)}
                onFocus={() => {
                  onFieldChange(tab, 'requirement_of', row.name, '');
                  requirementRef.current[index].current.clear();
                  requirementRef.current[index].current.toggleMenu();
                }}
              />
              <ButtonToolbar className="interval-angle-icon">
                <ChevronExpand
                  onClick={() => {
                    onFieldChange(tab, 'requirement_of', row.name, '');
                    requirementRef.current[index].current.clear();
                    requirementRef.current[index].current.toggleMenu();
                  }}
                />
              </ButtonToolbar>
            </div>
          ) : (
            formatValue(row.requirement_of)
          ),
        id: 'requirement_of',
        name: 'requirement',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
    ],
    [isEdit, controlParametersCopy],
  );

  const checkRequirementSelectDropdown = (requirement_of) => {
    if (requirement_of) {
      const data = REQUIREMENT_OPTIONS.find((options) => options.value === requirement_of);
      return [data];
    } else {
      return null;
    }
  };

  const handleTabSelect = (key) => {
    ga4EventTrigger('Anchor Link', 'Control Parameters - Menu', 'Anchor Link');
    if (key !== activeTab) {
      setActiveTab('');
    }
    history.push(
      `/vessel/report/technical/control-parameter?tab=${key}&ownershipId=${ownershipId}&action=${action}`,
    );
    setActiveTab(key);
  };

  const handleVesselClear = () => {
    vesselRef.current.clear();
    setSelectedVessel(null);
    setErrorMsg('');
    setIsEdit(false);
    history.push(`/vessel/report/technical/control-parameter`);
  };

  const handleEditButtonClick = () => {
    ga4EventTrigger(
      'Edit Control Parameters',
      'Control Parameters - Menu',
      !_.isEmpty(selectedVessel) ? selectedVessel[0]?.value : '- - -',
    );
    setIsEdit(true);
    setShowEditCopyButtons(false);
    return history.push(
      `/vessel/report/technical/control-parameter?tab=${tab ?? 'position'}&ownershipId=${
        ownershipId ?? selectedVessel[0]?.id
      }&action=edit`,
    );
  };

  const handleCrossButtonClick = () => {
    if (isEdit && !isDisabled) {
      setShowSaveUnsavedControlModal(true);
    } else if (isEdit && isDisabled) {
      setIsEdit(false);
    } else {
      history.push(`/vessel/report/technical/position`);
    }
  };

  const handleSaveButton = async () => {
    ga4EventTrigger(
      'Confirm Edit',
      'Control Parameters Edit - Menu',
      !_.isEmpty(selectedVessel) ? selectedVessel[0]?.value : '- - -',
    );
    if (error.length > 0) {
      setShowAlert(true);
      scrollTop();
    } else {
      try {
        const data = modifiedParameter?.map((item) => {
          return _.pick(item, ['attribute_id', 'requirement_of', 'min_value', 'max_value']);
        });

        await vesselService.patchControlParameters(ownershipId, data);
        setModifiedParameter([]);
        setError([]);
        handleVesselDropdownChange(selectedVessel, true);
        setIsEdit(false);
        setShowSaveUnsavedControlModal(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const scrollTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToRow = (id) => {
    const element = document.getElementById(id);
    window.scrollTo({
      top: element.getBoundingClientRect().top - 80 + window.scrollY,
      behavior: 'smooth',
    });
  };

  const handleNoSaveButton = () => {
    history.push(
      `/vessel/report/technical/control-parameter?tab=${tab}&ownershipId=${ownershipId}`,
    );
    setIsEdit(false);
    setShowSaveUnsavedControlModal(false);
    setControlParametersCopy(controlParameters);
  };

  const getReportTypeControlParameters = (type, data, forEdit) => {
    return data?.result
      .filter((item) =>
        forEdit ? item.report_type === type : item.id !== null && item.report_type === type,
      )
      .map((eachItem) => {
        return {
          ...eachItem,
          min_value: isFloatValue(parseFloat(eachItem?.min_value))
            ? eachItem.min_value
            : parseFloat(eachItem.min_value).toFixed(2),
          max_value: isFloatValue(parseFloat(eachItem?.max_value))
            ? eachItem.max_value
            : parseFloat(eachItem.max_value).toFixed(2),
        };
      });
  };

  const getControlParametersFromResponse = (data, forEdit) => ({
    performance: getReportTypeControlParameters('performance', data, forEdit),
    monthly: getReportTypeControlParameters('monthly', data, forEdit),
    quarterly: getReportTypeControlParameters('quarterly', data, forEdit),
    voyage: getReportTypeControlParameters('voyage', data, forEdit),
    position: getReportTypeControlParameters('position', data, forEdit),
  });

  const handleVesselDropdownChange = async (event, updated = false) => {
    setSelectedVessel(event);
    setErrorMsg('');
    try {
      if (event[0]?.id) {
        setLoading(true);
        const { data } = await vesselService.getContolParameters(event[0].id);
        setControlParameters(getControlParametersFromResponse(data, false));
        setControlParametersCopy(getControlParametersFromResponse(data, true));
        const filteredData = data.result.filter((i) => i.id !== null);
        if (!_.isEmpty(filteredData)) {
          history.push(
            `/vessel/report/technical/control-parameter?tab=${tab ?? 'position'}&ownershipId=${
              event[0]?.id
            }&action=${updated ? null : action}`,
          );
          setShowEditCopyButtons(false);
        } else {
          setShowEditCopyButtons(true);
        }
      } else {
        setControlParameters({});
        setControlParametersCopy({});
      }
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const handleConfirmCopyControlParams = useCallback(async () => {
    setShowConfirmModal(false);
    setLoading(true);
    try {
      await vesselService.assignControlParameters(selectedVessel[0]?.id, {
        vessel_id: selectedCopyVessel[0]?.id,
      });
      handleVesselDropdownChange(selectedVessel);
    } catch (error) {
      const { status, data } = error.response;
      if (status === 400) {
        setErrorMsg(data.error.response.error);
      } else {
        console.log('Unable to copy control parameters', error);
      }
      setLoading(false);
    }
  }, [selectedVessel, selectedCopyVessel]);

  useEffect(() => {
    if (!_.isEmpty(vesselList)) {
      if (ownershipId) {
        const selectedValue = vesselList.find((item) => item.id === Number(ownershipId));
        handleVesselDropdownChange([selectedValue]);
      } else if (!_.isEmpty(filterData?.vessel)) {
        handleVesselDropdownChange(filterData.vessel);
      }
    }
  }, [vesselList, filterData]);

  const breadCrumbsItems = useMemo(
    () => [
      {
        title: 'Technical Reports',
        label: 'To Technical Report Page',
        link: `${PARIS2_URL}/vessel/report/technical/position`,
      },
      {
        title: `Control Parameters •  ${
          !_.isEmpty(selectedVessel) ? selectedVessel[0]?.value : '- - -'
        }`,
        label: 'To Control Parameters Page',
        link: isEdit
          ? `${PARIS2_URL}/vessel/report/technical/control-parameter?tab=${tab}&ownershipId=${ownershipId}`
          : `#`,
      },
      isEdit && {
        title: 'Edit',
        label: 'Edit',
        link: '#',
      },
    ],
    [selectedVessel, isEdit, tab, ownershipId],
  );

  const showAlertBar = useMemo(() => {
    return (
      <Alert className="approval_error_list" variant="danger">
        <Alert.Heading>correct the following highlighted in red:</Alert.Heading>
        <ul>
          {error?.map((item) => {
            if (
              item.minMessage === errorMessageList.invalidRangeMessage ||
              item.maxMessage === errorMessageList.invalidRangeMessage
            ) {
              return (
                <li key={item.name} onClick={() => scrollToRow(item.name)} aria-hidden="true">
                  <a className="underline-link-text">
                    value of {item.label} must be between {minValue} to {maxValue - 1}
                  </a>
                </li>
              );
            } else if (item.min_value > item.max_value) {
              return (
                <li key={item.name} onClick={() => scrollToRow(item.name)} aria-hidden="true">
                  <a className="underline-link-text">
                    Minimum value of {item.label} must be less than Maximum value
                  </a>
                </li>
              );
            } else if (item.max_value === item.min_value) {
              return (
                <li key={item.name} onClick={() => scrollToRow(item.name)} aria-hidden="true">
                  <a className="underline-link-text">
                    Maximum value of {item.label} must not be equal to Minimum value
                  </a>
                </li>
              );
            }
          })}
        </ul>
      </Alert>
    );
  }, [error]);

  const SaveUnsavedControlModal = useMemo(() => {
    return (
      <Modal
        aria-labelledby="contained-modal-title-vcenter"
        centered
        show={showSaveUnsavedControlModal}
        onHide={() => setShowSaveUnsavedControlModal(false)}
      >
        <Modal.Header>
          <Modal.Title>There is Unsaved Input Data</Modal.Title>
        </Modal.Header>
        <Modal.Body>Do you want to save before visiting the document page?</Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={() => handleNoSaveButton()}>
            No
          </Button>
          <Button variant="secondary" onClick={() => handleSaveButton()}>
            Yes
          </Button>
        </Modal.Footer>
      </Modal>
    );
  }, [showSaveUnsavedControlModal, tab]);

  const handleCopy = () => {
    setShowCopyControlParametersModal(false);
    setShowConfirmModal(true);
  };

  const handleCancel = () => {
    setSelectedCopyVessel([]);
    setShowConfirmModal(false);
  };

  const onBackToTopClick = () =>
    ga4EventTrigger(
      'Back to Top',
      'Control Parameters - Menu',
      !_.isEmpty(selectedVessel) ? selectedVessel[0]?.value : '- - -',
    );

  return (
    <Container>
      {roleConfig.params.view ? (
        <div>
          <Row className="align-header">
            <BreadcrumbHeader
              onClick={() =>
                ga4EventTrigger(
                  'Breadcrumb',
                  'Control Parameters - Menu',
                  !_.isEmpty(selectedVessel) ? selectedVessel[0]?.value : '- - -',
                )
              }
              items={breadCrumbsItems}
              activeItem={
                !isEdit ? `Control Parameters •  ${selectedVessel?.[0]?.value || '- - -'}` : 'Edit'
              }
            />
            <Col className="d-flex justify-content-end mt-4 mr-4">
              <Icon
                icon="close"
                className="cursor-pointer"
                size={30}
                onClick={() => handleCrossButtonClick()}
                disabled={loading}
              />
            </Col>
          </Row>
          {showAlert && <Row className="pt-4 pb-3"> {showAlertBar}</Row>}

          <Row>
            <Col className="col-auto">
              <Form.Group className="position-relative form-group">
                <Typeahead
                  ref={vesselRef}
                  data-testid="fml-control-parameter-vessel"
                  id="basic-typeahead-single"
                  className="control-vessel-dropdown"
                  labelKey="value"
                  inputProps={{ 'data-testid': 'fml-control-parameter-vessel' }}
                  onChange={(event) => {
                    ga4EventTrigger('Select Vessel', 'Control Parameters - Menu', event[0]?.value);
                    handleVesselDropdownChange(event);
                  }}
                  options={vesselList}
                  placeholder="Please select"
                  selected={selectedVessel}
                  disabled={loading}
                />
                {!selectedVessel ? (
                  <ButtonToolbar className="interval-angle-icon">
                    <ChevronExpand
                      onClick={() => vesselRef.current.toggleMenu()}
                      data-testid="fml-control-parameter-dropdown-btn"
                    />
                  </ButtonToolbar>
                ) : (
                  <ButtonToolbar className="interval-angle-icon vessel-cross-icon">
                    <Icon
                      icon="remove"
                      size={20}
                      className="remove"
                      onClick={() => handleVesselClear()}
                      data-testid="fml-control-parameter-remove-btn"
                    />
                  </ButtonToolbar>
                )}
              </Form.Group>
            </Col>
            <Col className="d-flex justify-content-end">
              {!isEdit && (
                <Button
                  variant="outline-primary"
                  data-testid="fml-control-parameter-edit-btn"
                  className={!selectedVessel ? `d-none` : `mr-2 my-1`}
                  hidden={!roleConfig.params.edit}
                  onClick={() => handleEditButtonClick()}
                >
                  Edit
                </Button>
              )}
            </Col>
          </Row>
          {loading && (
            <div>
              <Spinner alignClass={'spinner-table'} />
            </div>
          )}
          {!loading && !showEditCopyButtons && selectedVessel ? (
            <>
              <Tab.Container activeKey={activeTab} defaultActiveKey="all">
                <Row className="no-print tab-wrapper">
                  <Col>
                    <TabWrapper
                      handleTabSelect={handleTabSelect}
                      data={controlParametersPageTabData}
                      step={tab}
                      setActiveTab={setActiveTab}
                      activeTab={activeTab}
                    />
                  </Col>
                </Row>

                {tab === 'position' && (
                  <ControlParameterReports
                    columns={columns}
                    controlParameters={
                      isEdit ? controlParametersCopy?.position : controlParameters?.position
                    }
                    title={'POSITION REPORT'}
                    onBackToTopClick={onBackToTopClick}
                  />
                )}
                {tab === 'performance' && (
                  <ControlParameterReports
                    columns={columns}
                    controlParameters={
                      isEdit ? controlParametersCopy?.performance : controlParameters?.performance
                    }
                    title={'ME PERFORMANCE REPORT'}
                    onBackToTopClick={onBackToTopClick}
                  />
                )}
                {tab === 'monthly' && (
                  <ControlParameterReports
                    columns={columns}
                    controlParameters={
                      isEdit ? controlParametersCopy?.monthly : controlParameters?.monthly
                    }
                    title={'MONTHLY REPORT'}
                    onBackToTopClick={onBackToTopClick}
                  />
                )}
                {tab === 'quarterly' && (
                  <ControlParameterReports
                    columns={columns}
                    controlParameters={
                      isEdit ? controlParametersCopy?.quarterly : controlParameters?.quarterly
                    }
                    title={'QUARTERLY REPORT'}
                    onBackToTopClick={onBackToTopClick}
                  />
                )}
                {tab === 'voyage' && (
                  <ControlParameterReports
                    columns={columns}
                    controlParameters={
                      isEdit ? controlParametersCopy?.voyage : controlParameters?.voyage
                    }
                    title={'VOYAGE REPORT'}
                    onBackToTopClick={onBackToTopClick}
                  />
                )}
              </Tab.Container>
              {isEdit && (
                <div className="fixed-bottom d-flex justify-content-center w-100 p-3 bg-light ">
                  <Button
                    variant="secondary"
                    disabled={isDisabled}
                    onClick={() => handleSaveButton()}
                    data-testid="fml-controlParameters-save-button"
                  >
                    Save
                  </Button>
                </div>
              )}
              {SaveUnsavedControlModal}
            </>
          ) : (
            <div>
              <hr className="border-line" />
              {errorMsg && <ErrorAlert message={errorMsg} />}
              <div className="text-center mt-5">
                <Icon icon="alert" className="alert-icon-no-search" />
                <div
                  className="font-weight-bold"
                  data-testid="fml-control-parameter-no-vessel-selected"
                >
                  {showEditCopyButtons ? (
                    <>
                      No records found.
                      <p>You may also copy from other vessels</p>
                    </>
                  ) : (
                    'No Vessel Selected. Please select a vessel'
                  )}
                </div>

                {showEditCopyButtons && (
                  <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={() => {
                      setShowCopyControlParametersModal(true);
                      setErrorMsg('');
                      setSelectedCopyVessel([]);
                    }}
                    data-testid="fml-control-parameter-copyVessel"
                  >
                    Copy from Other Vessel
                  </Button>
                )}

                <CustomCopyModal
                  showCopyModal={showCopyControlParametersModal}
                  setShowCopyModal={setShowCopyControlParametersModal}
                  handleCopy={handleCopy}
                  selectedCopyVessel={selectedCopyVessel}
                  setSelectedCopyVessel={setSelectedCopyVessel}
                  vesselLists={vesselList}
                  modalHeading="Control Parameters"
                />
                <ConfirmModal
                  showConfirmModal={showConfirmModal}
                  setShowConfirmModal={setShowConfirmModal}
                  title={
                    <>
                      Confirm Copying Control Parameters from <b>{selectedCopyVessel[0]?.name}?</b>
                    </>
                  }
                  confirmText={'Confirm'}
                  handleCancel={handleCancel}
                  handleConfirm={handleConfirmCopyControlParams}
                />
              </div>
            </div>
          )}
        </div>
      ) : (
        <ErrorPage errorCode={403} />
      )}
    </Container>
  );
};

export default ControlParameters;
