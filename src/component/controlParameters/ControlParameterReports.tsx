import React from 'react';
import { Col, Row, Form } from 'react-bootstrap';
import ScrollArrow from '../BackToTopButton';
import CustomTable from '../customComponent/CustomTable';

const ControlParameterReports = ({ columns, controlParameters, title, onBackToTopClick }) => {
  return (
    <div>
      <Row>
        <Form.Label className="technical-reports col-sm-3">
          <b>{title}</b>
        </Form.Label>
      </Row>

      <Row className="no-print">
        <Col>
          <CustomTable
            className="control-parameter-table-item"
            column={columns}
            reportData={controlParameters}
            tableRef={null}
          />
        </Col>
      </Row>
      <ScrollArrow onClick={onBackToTopClick} />
    </div>
  );
};

export default ControlParameterReports;
