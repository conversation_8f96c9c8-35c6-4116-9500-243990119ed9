import React from 'react';
import { useHistory } from 'react-router-dom';
import { Container, Col, Button, Row } from 'react-bootstrap';
import moment from 'moment';
import PropTypes from 'prop-types';

const SearchParametersView = (props) => {
  const history = useHistory();

  const handleEditClick = () => {
    history.push(`/vessel/search?${props.query}`);
  };

  function getParameters() {
    const data = props.criteria;
    const result = data.map((item) => {
      const title = item.type.name;
      let value = '';
      switch (item.type.inputType) {
        case 'dropdown':
          value = item.subtype.value;
          break;
        case 'text':
          value = item.subtype;
          break;
        case 'number_range': {
          const min = item.subtype.min;
          const max = item.subtype.max;
          value = `${min} to ${max}`;
          break;
        }
        case 'date': {
          const start = item.subtype.startDate
            ? moment(item.subtype.startDate).format('DD MMM YYYY')
            : '';
          const end = item.subtype.endDate
            ? moment(item.subtype.endDate).format('DD MMM YYYY')
            : '';
          value = `from ${start} to ${end}`;
          break;
        }
        case 'year':
          value = moment(item.subtype).format('YYYY');
          break;
        default:
          value = item.subtype ?? '';
      }

      return `${title}: ${value}`;
    });

    return result;
  }

  return (
    <Container className="search-parameters-wrapper">
      <Row className={getParameters().length === 0 ? 'hidden' : null}>
        <Col md={9}>
          <div className="search-parameters-wrapper__title">Search by</div>
          <div className="search-parameters-wrapper__params">
            {getParameters().map((element, index) => (
              <span key={index}>
                <span>{element}</span>
                {index != getParameters().length - 1 ? (
                  <span className="search-parameters-wrapper__slash"> / </span>
                ) : null}
              </span>
            ))}
          </div>
        </Col>
        <Col md={3} className="my-auto search-parameters-wrapper__button-wrapper">
          <Button variant="primary" size="sm" onClick={handleEditClick}>
            Edit Search Criteria
          </Button>
        </Col>
      </Row>
    </Container>
  );
};

SearchParametersView.propTypes = {
  query: PropTypes.string,
  criteria: PropTypes.object,
};

export default SearchParametersView;
