import React, { useState, useEffect } from 'react';
import { confirmOwnershipChange } from '../../service/ownership-service';
import styleGuide from '../../styleGuide';
const { Icon } = styleGuide;
import { Button, Modal, Form, Col, Row } from 'react-bootstrap';
import Spinner from '../Spinner';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import moment from 'moment';
import { BUSINESS } from '../../model/constants';
import PropTypes from 'prop-types';

export const OwnerShipConfirmation = (props) => {
  const [confirmModelShow, setConfirmModelShow] = useState(false);
  const [handOverDate, setHandOverDate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [ownershipChanged, setOwnershipChanged] = useState(false);

  useEffect(() => {
    if (props.vessel) {
      const handOverDate = getHandoverDate(props.vessel);
      if (handOverDate) {
        setHandOverDate(new Date(handOverDate));
        setOwnershipChanged(true);
      }
    }
  }, []);

  const getHandoverDate = (vessel) => {
    switch (vessel.ownership_change_request.request_for) {
      case 'owner':
        return vessel.owner_start_date;
      case 'registered_owner':
        return vessel.registered_owner_start_date;
      default:
        return vessel.owner_start_date;
    }
  };

  async function applyPendingStatus() {
    setConfirmModelShow(false);
    setLoading(true);
    try {
      let payload = {
        ownership_change_request_id: props.vessel.ownership_change_request_id,
        takeover_date: moment(handOverDate).format('YYYY-MM-DD'),
      };
      const response = await confirmOwnershipChange(payload);
      if (response.status === 200) setOwnershipChanged(true);
      props.renderTable();
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }

  const handleModalOpen = () => {
    props.eventTracker('buttonClick', 'Confirm Ownership Change');
    setConfirmModelShow(true);
  };

  const OwnershipSuccessView = () => {
    return (
      <div className="mt-3">
        <Icon icon="checked" size={20} className="float-left vessel_approval__moved-tick" />
        <span className="font-weight-normal">
          This vessel has been changed ownership successfully.
        </span>
      </div>
    );
  };

  const ConfirmActiveModel = () => {
    return (
      <Modal
        className="action-modal"
        show={confirmModelShow}
        onHide={() => setConfirmModelShow(false)}
        centered
      >
        <Modal.Header className="mb-5">
          <Modal.Title className="h5">
            Final confirmation of ownership change with the selected hand over date?
          </Modal.Title>
        </Modal.Header>
        <Modal.Footer className="mt-5">
          <Button variant="primary" onClick={() => setConfirmModelShow(false)}>
            Cancel
          </Button>
          <Button variant="secondary" onClick={applyPendingStatus}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    );
  };

  const isBusinessUser = () => {
    return props.roleConfig.approvalGroups.includes(BUSINESS);
  };

  const OwnerShipConfirmationView = () => {
    return (
      <>
        <Row>
          <Col>
            <hr></hr>
            <h6 className="vessel_approval__section-header">
              4. FINAL CONFIRMATION OF OWNERSHIP CHANGE{' '}
            </h6>
          </Col>
        </Row>
        <Row>
          <Col md="4">
            <Form.Label>Hand Over Date*</Form.Label>
            <DatePicker
              id="hand-over-date-picker"
              customInput={<input data-testid="test-id-hand-over-datepicker" />}
              isClearable={!ownershipChanged}
              selected={handOverDate}
              onChange={(date) => setHandOverDate(date)}
              placeholderText="Please select"
              dateFormat="d MMM yyyy"
              disabled={!isBusinessUser() || !props.isFinalApproverApproved() || ownershipChanged}
              maxDate={new Date()}
            />
          </Col>
        </Row>
        <Row>
          <Col md="auto">
            {ownershipChanged ? (
              <OwnershipSuccessView />
            ) : (
              <Button
                onClick={handleModalOpen}
                className="btn-sm mt-3 ownership-btn"
                variant="outline-secondary"
                data-testid="test-id-conform-owner"
                disabled={!handOverDate}
              >
                Confirm Ownership Change
              </Button>
            )}
          </Col>
          <Col className="mt-3">{loading && <Spinner alignClass="align-left" />}</Col>
        </Row>
        <ConfirmActiveModel />
        <div style={{ height: '60px' }}></div>
      </>
    );
  };

  return <OwnerShipConfirmationView />;
};

OwnerShipConfirmation.propTypes = {
  vessel: PropTypes.object,
  renderTable: PropTypes.func,
  eventTracker: PropTypes.func,
  isFinalApproverApproved: PropTypes.func, //NOSONAR
  roleConfig: PropTypes.object,
};

export default OwnerShipConfirmation;
