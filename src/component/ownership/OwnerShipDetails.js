import React, { useState, useEffect } from 'react';
import { Form, Col, Row } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import TakeoverDropDownSearchControl from '../takeover/TakeoverDropDownSearchControl';
import moment from 'moment';
import PropTypes from 'prop-types';

import { BUSINESS } from '../../model/constants';

const OwnerShipDetails = (props) => {
  const {
    vessel,
    onInputChange,
    dropDownData,
    isOwnershipRequestCompleted,
    handleBlur,
    onDateChange,
    isFinalApproverApproved,
    roleConfig,
  } = props;

  const { owners, miscRegisteredOwners } = dropDownData;

  const [expectedTakeoverDate, setExpectedTakeoverDate] = useState(null);

  useEffect(() => {
    setupDates();
  }, []);

  const onTakeoverDateChange = (date) => {
    setExpectedTakeoverDate(date);
    changeDateForKey(date, 'expected_takeover_date');
  };

  function changeDateForKey(date, key) {
    let dateValue = null;
    if (date) {
      // dateValue = date.toISOString();
      // just extract the date
      // use the same date str ('DD MMM YYYY') format as seafarer to avoid confusion
      dateValue = moment(date).format('DD MMM YYYY');
    }
    onDateChange(key, dateValue);
  }

  function setupDates() {
    const dateString = vessel.expected_takeover_date;
    if (dateString) {
      const date = new Date(dateString);
      setExpectedTakeoverDate(date);
    }
  }

  const shouldDisableField = () => {
    return (
      isOwnershipRequestCompleted ||
      isFinalApproverApproved ||
      !roleConfig.approvalGroups.includes(BUSINESS)
    );
  };

  return (
    <div className="ownership-form">
      <Row className="mb-3 ml-0">
        <h6 className="capital">{isOwnershipRequestCompleted ? '1. ' : ''}NEW OWNERSHIP DETAILS</h6>
      </Row>
      <span> * Required fields </span>
      <br />
      <span> ** At lease one of them is required </span>

      <Row className="mt-4">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>New Owner Name**</Form.Label>
          <TakeoverDropDownSearchControl
            name={'owner_id'}
            dropDownValues={owners}
            onInputChange={onInputChange}
            selectedValue={vessel.owner_id}
            onBlur={handleBlur}
            disabled={shouldDisableField()}
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>New Registered Owner Name**</Form.Label>
          <TakeoverDropDownSearchControl
            name={'reg_owner_id'}
            dropDownValues={miscRegisteredOwners}
            onInputChange={onInputChange}
            selectedValue={vessel.reg_owner_id}
            onBlur={handleBlur}
            disabled={shouldDisableField()}
          />
        </Form.Group>
      </Row>
      <Row>
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Expected Take Over Date</Form.Label>
          <DatePicker
            isClearable={!isOwnershipRequestCompleted}
            selected={expectedTakeoverDate}
            customInput={<input data-testid="test-id-expected-take-over" />}
            onChange={onTakeoverDateChange}
            placeholderText="Please select"
            dateFormat="d MMM yyyy"
            disabled={shouldDisableField()}
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Split of Accounting Books*</Form.Label>
          <Form.Control
            as="select"
            name="split_accounting_books"
            value={vessel.split_accounting_books}
            data-testid="test-id-split-acc-book"
            onChange={onInputChange}
            onBlur={handleBlur}
            disabled={shouldDisableField()}
            defaultValue="no"
          >
            <option value="yes">Yes</option>
            <option value="no">No</option>
          </Form.Control>
        </Form.Group>
      </Row>

      <Row>
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>
            Vessel Short Code (NEW){vessel.split_accounting_books === 'yes' ? '*' : ''}
          </Form.Label>
          <Form.Control
            type="text"
            name="vessel_short_code"
            data-testid="test-id-vessel-short-code"
            value={vessel.vessel_short_code}
            onChange={onInputChange}
            disabled={vessel.split_accounting_books !== 'yes' || shouldDisableField()}
            onBlur={handleBlur}
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>
            Vessel Account Code{vessel.split_accounting_books === 'yes' ? '*' : ''}
          </Form.Label>
          <Form.Control
            type="text"
            name="vessel_account_code"
            data-testid="test-id-vessel-account-code"
            value={vessel.vessel_account_code}
            onChange={onInputChange}
            disabled={vessel.split_accounting_books !== 'yes' || shouldDisableField()}
            onBlur={handleBlur}
          />
        </Form.Group>
      </Row>

      <Row>
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>
            Vessel Tel FAC Code (NEW){vessel.split_accounting_books === 'yes' ? '*' : ''}
          </Form.Label>
          <Form.Control
            type="text"
            name="vessel_tel_fac_code"
            data-testid="test-id-vessel-fac-code"
            value={vessel.vessel_tel_fac_code}
            onChange={onInputChange}
            disabled={vessel.split_accounting_books !== 'yes' || shouldDisableField()}
            onBlur={handleBlur}
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Vessel Name (NEW)</Form.Label>
          <Form.Control
            type="text"
            name="vessel_name"
            data-testid="test-id-vessel-name"
            value={vessel.vessel_name}
            onChange={onInputChange}
            disabled={vessel.split_accounting_books !== 'yes' || shouldDisableField()}
            onBlur={handleBlur}
          />
        </Form.Group>
      </Row>
    </div>
  );
};

OwnerShipDetails.propTypes = {
  vessel: PropTypes.object,
  onInputChange: PropTypes.func,
  dropDownData: PropTypes.object,
  isOwnershipRequestCompleted: PropTypes.bool,
  handleBlur: PropTypes.node,
  onDateChange: PropTypes.func,
  isFinalApproverApproved: PropTypes.bool,
  roleConfig: PropTypes.object,
};

export default OwnerShipDetails;
