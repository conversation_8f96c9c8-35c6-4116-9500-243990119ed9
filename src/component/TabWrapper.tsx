import React from 'react';
import { Nav } from 'react-bootstrap';

export const TabWrapper = (props) => {
  if (props?.step !== props?.activeTab) {
    props.setActiveTab('');
    props.setActiveTab(props.step);
  }
  return (
    <Nav
      variant="pills"
      onSelect={(k) => props.handleTabSelect(k)}
      className="nav-justified nav-border"
    >
      <hr />
      {props?.data?.map((data) => {
        return data.hideTab ? null : (
          <Nav.Item key={data.eventKey}>
            <Nav.Link
              data-testid={`fml-vessel-details-${data.eventKey}-tab-link`}
              eventKey={data.eventKey}
              className={`${data.eventKey === props?.activeTab && 'active'}`}
              href="#"
            >
              {data.tabName}
            </Nav.Link>
          </Nav.Item>
        );
      })}
    </Nav>
  );
};

export default TabWrapper;
