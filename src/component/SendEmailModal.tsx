import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Col, Form, Modal, Row, Spinner } from 'react-bootstrap';
import { useForm } from 'react-hook-form';
import { Link } from 'react-router-dom';
import * as yup from 'yup';
import { useYupValidationResolver } from '../model/useYupValidationResolver';
import vesselService, { sendEmail } from '../service/vessel-service';
import * as Loader from './Spinner';
import { base64_encode } from '../util/getURLParams';
import FileSvg from './customComponent/FileSvg';

const { PARIS2_URL } = process.env;

const parseMessageToHTML = (message) => {
  const words = message.split('\n');
  return `
  <html>
    <body>
      ${words.join('<br/>')}
      <br/>
    </body>
  </html>
  `;
};

const validationSchema = yup.object().shape({
  recipient: yup.string().required(),
  message: yup.string(),
  cc: yup.string().nullable(),
  subject: yup.string().nullable(),
});

const SendEmailModal = ({
  title = 'Send Email to Vessel',
  isVisible,
  onClose,
  onSuccess = () => {},
  emailContent,
  showDocuments = false,
  onSubmitButtonClick = () => {},
  loading = false,
  disabledEmail = false,
  isMissingAccountant = false,
  modalid = '',
  infoText = () => {},
  headerClassName = "",
  footerClassName = "",
  onError = () => {}
}) => {
  const [isSending, setSending] = useState(false);
  const resolver = useYupValidationResolver(validationSchema);
  const defaultValues = useMemo(() => emailContent, [emailContent]);
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm({ resolver, defaultValues, mode: 'onChange' });

  useEffect(() => {
    if (defaultValues) {
      reset({
        ...defaultValues,
      });
    }
  }, [defaultValues, reset]);

  useEffect(() => {
    setError(isMissingAccountant ? 'Email can not be send. Vessel does not have any assigned vessel accountant.' : '');
  }, [loading, isMissingAccountant])

  const appendName = (documentsData, documents) => {
    const modifiedData = [];
    documentsData.map((docs, index) => {
      const url = documents[index].split('/');
      modifiedData.push({
        data: docs.data,
        name: url[url.length - 1],
        type: docs.headers['content-type'],
      });
    });
    return modifiedData;
  };

  const onSubmit = useCallback(
    async (data) => {
      try {
        if (data?.type == 'cash-call-email' && _.isEmpty(data?.cc)) {
          setError('Email cannot be send. Cc is empty, please modify recipient list or contact IT support.');
        } else {
          setSending(true);
          setError('');
          const { from, recipient, subject, message, documents } = data;
          const recipients = recipient.split(',');
          const ccAddress = data.cc?.length > 0 ? data.cc.split(',') : [];
          let updatedDoc = [];
          if (showDocuments && documents.length > 0) {
            const documentsData = await vesselService.getDocument({ paths: documents });
            updatedDoc = appendName(documentsData, documents);
          }
          await sendEmail(
            {
              from,
              recipients,
              subject,
              emailContent: parseMessageToHTML(message),
              ccAddress,
            },
            updatedDoc,
          );
          onSubmitButtonClick();
          onSuccess();
          reset();
        }
      } catch (error) {
        console.log('Something went wrong on sending email of report. Please try later.', error);
        setError('Something went wrong on sending email of report. Please try later.');
        onError(error)
      } finally {
        setSending(false);
      }
    },
    [onClose, reset],
  );

  return (
    <Modal id={modalid} show={isVisible} onHide={onClose} centered scrollable dialogClassName="send-email-modal">
      <Form onSubmit={handleSubmit(onSubmit)}>
        <Modal.Header className={`send-email-header ${headerClassName}`}>
          <Modal.Title className="send-email-title">{title}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {loading ? (
            <Loader.default alignClass={'spinner-table'} />
          ) : (
            <>
            {infoText()}
            <div className="email-form">
              <div className="left-column">
                <div>
                  <Form.Label className="form-label">From</Form.Label>
                  <Form.Control
                    as="input"
                    type="email"
                    data-testid="fml-sendEmail-from"
                    {...register('from')}
                    disabled
                  />
                </div>
                <div>
                  <Form.Label className="form-label">Subject</Form.Label>
                  <Form.Control
                    data-testid="fml-sendEmail-subject"
                    type="text"
                    {...register('subject')}
                    isInvalid={!!errors.subject}
                  />
                  <Form.Control.Feedback type="invalid">
                    Please enter subject.
                  </Form.Control.Feedback>
                </div>
                <div>
                  <Form.Label className="form-label">Send To</Form.Label>
                  <Form.Control
                    as="textarea"
                    min-height="2"
                    type="email"
                    data-testid="fml-sendEmail-recipient"
                    {...register('recipient')}
                    isInvalid={!!errors.recipient}
                    multiple
                    disabled={disabledEmail}
                  />
                  <Form.Control.Feedback type="invalid">
                    Please enter recipient.
                  </Form.Control.Feedback>
                </div>
                <div>
                  <Form.Label className="form-label">Cc</Form.Label>
                  <Form.Control
                    as="textarea"
                    min-height="2"
                    data-testid="fml-sendEmail-cc"
                    type="email"
                    multiple
                    {...register('cc')}
                    disabled={disabledEmail}
                  />
                </div>
                {showDocuments && !modalid ? (
                  <div className="sendEmail-doc">
                    <Form.Label className="form-label">Documents</Form.Label>
                    <div className="sendEmail-doc__div">
                      {emailContent.documents.map((docs, index) => {
                        const url = docs.split('/');
                        const name = url[url.length - 1];
                        return (
                          <div className="doc-div" key={`email-doc-${index}`}>
                            <p>{`${index + 1}. `}</p>
                            <Link
                              data-testid={`fml-sendEmail-doc-${index}`}
                              className="button-link"
                              to={{
                                pathname: `${PARIS2_URL}/vessel/document?source=certificates&path=${base64_encode(
                                  docs,
                                )}`,
                              }}
                              target="_blank"
                            >
                              {name}
                            </Link>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <></>
                )}
              </div>
              <div className="right-column">
                <Form.Label className="form-label">Message Content</Form.Label>
                <Form.Control
                  type="text"
                  as="textarea"
                  data-testid="fml-sendEmail-message"
                  className="message-input"
                  {...register('message')}
                  isInvalid={!!errors.message}
                />
                <Form.Control.Feedback type="invalid">
                  Please enter email content.
                </Form.Control.Feedback>
              </div>
            </div>

                {(showDocuments && modalid) && (
                  <div className="sendEmail-doc">
                    <Form.Label className="form-label">Uploaded Document</Form.Label>
                    <Row className="sendEmail-doc__div">
                      {emailContent.documents.map((docs, index) => {
                        const url = docs.split('/');
                        const name = url[url.length - 1];
                        return (
                          <Col sm={6} key={`email-doc-${index} crt-file-doc-div`}>
                            <div className='send-emil-doc-div-wrap'>
                            <Link
                              data-testid={`fml-sendEmail-doc-${index}`}
                              className="button-link"
                              to={{
                                pathname: `${PARIS2_URL}/vessel/document?source=certificates&path=${base64_encode(
                                  docs,
                                )}`,
                              }}
                              target="_blank"
                            >
                              <Row><Col sm={2}> <FileSvg fill={'#0091B8'} /> </Col> <Col style={{textAlign:'left'}} sm={10}>{name}</Col></Row>
                            </Link>
                            </div>
                          </Col>
                        );
                      })}
                    </Row>
                  </div>
                )}
                </>
              )} 
        </Modal.Body>
        <Modal.Footer className={`${footerClassName}`}>
          <Button
            style={{ width: '5em' }}
            data-testid="fml-sendEmail-cancel"
            variant="primary"
            onClick={() => {
              setError('');
              onClose();
            }}
          >
            Cancel
          </Button>
          <Button
            disabled={isSending || !isValid || isMissingAccountant}
            style={{ width: '5em' }}
            data-testid="fml-sendEmail-send"
            variant="secondary"
            type="submit"
          >
            {isSending ? <Spinner animation="border" role="status" /> : 'Send'}
          </Button>
        </Modal.Footer>
      </Form>
      {!loading && error && <p data-testid={"fml-sendEmail-error"} className="validate-error">{error}</p>}
    </Modal>
  );
};

export { SendEmailModal };
