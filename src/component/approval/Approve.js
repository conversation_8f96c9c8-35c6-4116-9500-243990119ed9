import React, { useState } from 'react';
import { Button, Modal } from 'react-bootstrap';
import { approvalStatuses } from '../../model/constants';
import { performAction, getModalActionData } from './action';
import PropTypes from 'prop-types';

export const Approve = (props) => {
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const actionText = props.isFinalApprover ? 'Approve' : 'Review';
  const handleShow = () => {
    props.eventTracker('buttonClick', actionText);
    setShow(true);
  };
  const [remarks, setRemarks] = useState(null);

  const confirmApproval = () => {
    performAction(props, approvalStatuses.APPROVED, handleClose, remarks);
    props.eventTracker('createVessel', 'Approved in ');
  };

  return (
    <>
      <Button
        onClick={handleShow}
        className="btn-sm"
        data-testid="test-id-review-btn"
        variant="outline-secondary"
      >
        {actionText}
      </Button>
      <Modal className="action-modal" show={show} onHide={handleClose} centered>
        <Modal.Header>
          <Modal.Title className="h5">{`Confirm ${actionText}?`}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <span>
            {getModalActionData(
              props.isOwnershipApprovalPending ? 'ownership' : props.vessel.pending_status,
              actionText,
            )}
          </span>
          <span>You may leave any remarks below:</span>
          <form className="mt-2">
            <div className="form-group">
              <textarea
                className="form-control"
                data-testid="test-id-approve-comment"
                rows="3"
                onChange={(e) => setRemarks(e.target.value)}
              ></textarea>
            </div>
          </form>
        </Modal.Body>
        <Modal.Footer>
          <Button data-testid="fml-confirmApprove-cancel" variant="primary" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="secondary" data-test-id="test-id-confirm-btn" onClick={confirmApproval}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

Approve.propTypes = {
  vessel: PropTypes.object,
  isFinalApprover: PropTypes.string,
  eventTracker: PropTypes.func,
  isOwnershipApprovalPending: PropTypes.bool,
};

export default Approve;
