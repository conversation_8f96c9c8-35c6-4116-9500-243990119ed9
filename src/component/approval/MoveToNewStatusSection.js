import React, { useState, useMemo } from 'react';
import vesselService from '../../service/vessel-service';
import styleGuide from '../../styleGuide';
const { Icon } = styleGuide;
import { <PERSON><PERSON>, Col, Form, Modal } from 'react-bootstrap';
import { vesselStatuses } from '../../model/constants';
import Spinner from '../Spinner';
import CustomDatePicker from '../customComponent/CustomDatePicker';
import PropTypes from 'prop-types';

export const MoveToNewStatusSection = (props) => {
  const [confirmModelShow, setConfirmModelShow] = useState(false);
  const [missingFieldsModelShow, setMissingFieldsModelShow] = useState(false);
  const [loading, setLoading] = useState(false);
  const [approvalDate, setApprovalDate] = useState();

  async function applyPendingStatus() {
    setConfirmModelShow(false);
    setLoading(true);
    props.eventTracker('buttonClick', 'Move to Active');
    try {
      const payload = { vessel_id: props.vessel.id, [props.uiProps.dateKey]: approvalDate };
      const response = await vesselService.applyPendingStatus(payload);
      if (response && response.status == 200) {
        setLoading(false);
        props.renderTable();
      } else {
        throw Error('apply pending status vessel action not performed correctly!');
      }
    } catch (error) {
      console.error(`apply pending status service error for ${props.vessel.id}. Error: ${error}`);
    }
  }

  const AlreadyMovedView = () => {
    return (
      <div>
        <Icon icon="checked" size={20} className="float-left vessel_approval__moved-tick" />
        <span>{props.uiProps.confirmationText}</span>
      </div>
    );
  };

  AlreadyMovedView.propTypes = {
    uiProps: PropTypes.object,
  };

  const ConfirmActiveModel = () => {
    return (
      <Modal
        className="action-modal"
        show={confirmModelShow}
        onHide={() => setConfirmModelShow(false)}
        centered
      >
        <Modal.Header>
          <Modal.Title className="h5">{props.uiProps.modalTitle}</Modal.Title>
        </Modal.Header>

        <Modal.Footer>
          <Button
            data-testid="fml-moveToNewStatusSection-cancel"
            variant="primary"
            onClick={() => setConfirmModelShow(false)}
          >
            Cancel
          </Button>
          <Button
            data-testid="fml-moveToNewStatusSection-confirm"
            variant="secondary"
            onClick={applyPendingStatus}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    );
  };

  ConfirmActiveModel.propTypes = {
    uiProps: PropTypes.object,
  };

  const MissingFieldsModel = () => {
    missingFieldsModelShow && window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
    return (
      <Modal
        className="action-modal"
        show={missingFieldsModelShow}
        onHide={() => setMissingFieldsModelShow(false)}
        centered
      >
        <Modal.Header>
          <Modal.Title className="h5">
            Some required fields are not completed yet in the vessel detail page.
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Please complete all the required fields in the vessel detail page first.
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setMissingFieldsModelShow(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    );
  };

  async function handleMove() {
    props.eventTracker('buttonClick', 'Move to Active');
    if (approvalDate) {
      if (props.vessel.pending_status == vesselStatuses.ACTIVE) {
        setLoading(true);
        try {
          const response = await vesselService.isAnyInputPending(props.vessel.id);
          setLoading(false);
          return response.data ? setMissingFieldsModelShow(true) : setConfirmModelShow(true);
        } catch (error) {
          console.error(
            `input missing query error from vessel service for ${props.vessel.id}. Error: ${error}`,
          );
        }
      } else {
        setConfirmModelShow(true);
      }
    } else {
      props.setShowDateRequiredError(true);
    }
  }

  const disableSubmitButton = useMemo(
    () => (props.isFinalApprovalPending ? true : !approvalDate),
    [props.isFinalApprovalPending, approvalDate],
  );

  const MoveToNewStatusView = () => {
    return (
      <Col md="4">
        {props.uiProps.showDate && (
          <Form.Group className="form-group">
            <Form.Label className="from-label">{props.uiProps.dateLabel}</Form.Label>
            <CustomDatePicker
              dataTestId="fml-move-to-new-status-date"
              value={approvalDate}
              onChange={(e) => {
                setApprovalDate(e);
                props.setShowDateRequiredError(false);
              }}
              disabled={props.disableDate}
              customProps={{ maxDate: new Date() }}
              placeholder="Please select after final approval"
            />
          </Form.Group>
        )}
        <Button
          data-testid="fml-Approval-moveToNewStatusSection"
          onClick={handleMove}
          className="btn-sm"
          variant="outline-secondary"
          {...(disableSubmitButton && { className: 'disabled', disabled: 'disabled' })}
        >
          {props.uiProps.buttonText}
        </Button>
        <ConfirmActiveModel />
        <MissingFieldsModel />
      </Col>
    );
  };

  MoveToNewStatusView.propTypes = {
    uiProps: PropTypes.object,
    setShowDateRequiredError: PropTypes.func,
    disableDate: PropTypes.bool,
  };

  const renderView = () => {
    if (loading) {
      return <Spinner />;
    } else if (props.vessel.pending_status) {
      return <MoveToNewStatusView />;
    }
    return <AlreadyMovedView />;
  };

  return renderView();
};

export default MoveToNewStatusSection;
