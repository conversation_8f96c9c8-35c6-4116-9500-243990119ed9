import React, { useState } from 'react';
import { But<PERSON>, Modal } from 'react-bootstrap';
import { approvalStatuses } from '../../model/constants';
import { performAction } from './action';
import PropTypes from 'prop-types';

export const Rework = (props) => {
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => {
    props.eventTracker('buttonClick', 'Rework');
    setShow(true);
  };

  return (
    <>
      <Button
        data-testid="fml-approval-rework"
        onClick={handleShow}
        className="btn-sm"
        variant="outline-secondary"
      >
        Rework
      </Button>
      <Modal className="action-modal" show={show} onHide={handleClose} centered>
        <Modal.Header>
          <Modal.Title className="h5">Confirm Rework?</Modal.Title>
        </Modal.Header>
        <Modal.Body>Are you sure to rework the approval status for {props.group}?</Modal.Body>
        <Modal.Footer>
          <Button data-testid="fml-confirmRework-cancel" variant="primary" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            data-testid="fml-confirmRework-confirm"
            variant="secondary"
            onClick={() => performAction(props, approvalStatuses.PENDING, handleClose)}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

Rework.propTypes = {
  eventTracker: PropTypes.func,
  group: PropTypes.string,
};

function throwError(message) {
  throw message;
}

export default Rework;
