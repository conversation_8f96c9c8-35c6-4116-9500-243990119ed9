import vesselApprovalService from '../../service/vessel-approval-service';
import { updateOwnershipApprovalStatus } from '../../service/ownership-service';
import { vesselStatuses, OWNERSHIP } from '../../model/constants';

const modalActionData = [
  {
    action: vesselStatuses.ACTIVE,
    text: 'takeover',
  },
  {
    action: vesselStatuses.HANDED_OVER,
    text: 'hand over',
  },
  {
    action: vesselStatuses.ARCHIVED,
    text: 'archive',
  },
  {
    action: OWNERSHIP,
    text: 'ownership',
  },
];

export const getModalActionData = (pendingStatus, actionText) => {
  const actionProps = modalActionData.find((item) => item.action === pendingStatus);
  return `Are you sure to ${actionText.toLowerCase()} the ${
    actionProps ? actionProps.text : null
  } of this vessel?`;
};

export async function performAction(parentProps, approvalStatus, handleClose, remarks = null) {
  handleClose();
  parentProps.setLoadingFor({ rowId: parentProps.approvalId });
  try {
    let response;
    if (parentProps.isOwnershipApprovalPending) {
      response = await updateOwnershipApprovalStatus(
        parentProps.approvalId,
        approvalStatus,
        remarks,
      );
    } else {
      response = await vesselApprovalService.updateVesselApprovalStatus(
        parentProps.approvalId,
        approvalStatus,
        remarks,
      );
    }
    if (response) {
      parentProps.setLoadingFor({ rowId: null });
      if (response.status == 200) parentProps.renderTable();
      else throw Error('Approval action not performed correctly!');
    }
  } catch (error) {
    console.log(error);
  }
}

export default { performAction, getModalActionData };
