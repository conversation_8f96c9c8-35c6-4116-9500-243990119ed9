/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import { Button, Modal } from 'react-bootstrap';
import { approvalStatuses } from '../../model/constants';
import { performAction, getModalActionData } from './action';
import ErrorAlert from '../ErrorAlert';
import PropTypes from 'prop-types';

export const Reject = (props) => {
  const [show, setShow] = useState(false);
  const [error, setError] = useState('');
  const handleClose = () => setShow(false);
  const handleShow = () => {
    props.eventTracker('buttonClick', 'Reject');
    setShow(true);
  };
  const [remarks, setRemarks] = useState(null);

  const handleReject = () => {
    const trimmedRemarks = remarks?.trim();

    // Regular expression to check for at least one alphabet or number and allow selected special characters
    const isValidRemarks = /^(?=.*[a-zA-Z0-9])[a-zA-Z0-9\s.,!?-]+$/.test(trimmedRemarks);

    if (trimmedRemarks && isValidRemarks) {
      performAction(props, approvalStatuses.REJECTED, handleClose, trimmedRemarks);
    } else {
      setError('Remark is mandatory, please provide the reason.');
    }
    props.eventTracker('createVessel', `Rejected in `);
  };

  return (
    <>
      <Button
        data-testid={props.dataTestId}
        onClick={handleShow}
        className="btn-sm"
        variant="outline-danger"
      >
        Reject
      </Button>
      <Modal className="action-modal" show={show} onHide={handleClose} centered>
        <Modal.Header>
          <Modal.Title className="h5">Confirm Reject?</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <ErrorAlert message={error} />}
          <span>
            {getModalActionData(
              props.isOwnershipApprovalPending ? 'ownership' : props.vessel.pending_status,
              'reject',
            )}
          </span>
          <span> State reason below:</span>
          <form className="mt-2">
            <div className="form-group">
              <textarea
                data-testid="fml-confirmReject-reason"
                className="form-control"
                rows="3"
                onChange={(e) => {
                  setRemarks(e.target.value);
                  setError('');
                }}
                required
              ></textarea>
            </div>
          </form>
        </Modal.Body>
        <Modal.Footer>
          <Button data-testid="fml-confirmReject-Cancel" variant="primary" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            data-testid="fml-confirmReject-Confirm"
            variant="secondary"
            onClick={handleReject}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

Reject.propTypes = {
  eventTracker: PropTypes.func,
  dataTestId: PropTypes.string,
  isOwnershipApprovalPending: PropTypes.bool,
  vessel: PropTypes.object,
};

export default Reject;
