import _ from 'lodash';
import React, { useContext, useState } from 'react';
import { Container } from 'react-bootstrap';
import { useParams, useHistory } from 'react-router-dom';
import { FinancialReportContext } from '../../context/FinancialReportContext';
import CashCallReportList from './ListReports/CashCallReportList';
import FinancialReportList from './ListReports/FinancialReportList';
import { ErrorPage } from '../../styleGuide';

const FinancialList = () => {
  const { roleConfig } = useContext(FinancialReportContext);
  const { tab = 'accounts' } = useParams();
  const history = useHistory();
  const [error, setError] = useState(null);

  const updateQuery = (vessel) => {
    if (!_.isEmpty(vessel)) {
      let queryParam = '';
      if (vessel[0]?.id) {
        queryParam += `&vessel_ownership_id=${vessel[0]?.id}`;
      }
      history.push(`${history.location.pathname}?${queryParam}`);
    } else {
      history.push(`${history.location.pathname}`);
    }
  };

  const financialReport = () => (
    <FinancialReportList
      error={error}
      setError={setError}
      reportType={tab}
      updateQuery={updateQuery}
    />
  );

  return (
    <Container>
      {tab == 'accounts' &&
        (roleConfig.financial.view ? (
          <>
            <div className="d-flex justify-content-between mb-4">
              <div className="font-weight-bold technical-reports">Financial Reports Library</div>
            </div>
            {financialReport()}
          </>
        ) : (
          <ErrorPage errorCode={403} />
        ))}
      {tab == 'cash-call' &&
        (roleConfig.cashCall.view ? (
          <>
            <div className="d-flex justify-content-between mb-4">
              <div className="font-weight-bold technical-reports">Cash Call Reports</div>
            </div>

            <CashCallReportList
              error={error}
              setError={setError}
              reportType={tab}
              updateQuery={updateQuery}
            />
          </>
        ) : (
          <ErrorPage errorCode={403} />
        ))}
      {tab == 'technical' &&
        (roleConfig.owner.view ? (
          <>
            <div className="d-flex justify-content-between mb-4">
              <div className="font-weight-bold technical-reports">Owner Reports</div>
            </div>
            {financialReport()}
          </>
        ) : (
          <ErrorPage errorCode={403} />
        ))}
    </Container>
  );
};

export default FinancialList;
