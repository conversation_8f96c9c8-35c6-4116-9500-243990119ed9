import React, { useContext, useState, useEffect, useMemo } from 'react';
import { formatDate, formatValue } from '../../../util/view-utils';
import vesselService from '../../../service/vessel-service';
import ErrorAlert from '../../ErrorAlert';
import { Link, useHistory } from 'react-router-dom';
import { FinancialReportContext } from '../../../context/FinancialReportContext';
import _ from 'lodash';
import CustomTable from '../../customComponent/CustomTable';
import { Icon } from '../../../styleGuide';
import { Button, ButtonToolbar, Dropdown } from 'react-bootstrap';
import { SUBJECTS } from '../../../constants/recipient-subject';
import AddCashCallReportModal from '../AddCashCallReportModal';
import ConfirmModal from '../../customComponent/CustomConfirmationModal';
import { SendEmailModal } from '../../SendEmailModal';
import { exportTableToExcel } from '../../../util/excel-export';
import FinancialReportFilter from '../FinancialReportFilter';
import getURLParams, { base64_encode } from '../../../util/getURLParams';
import moment from 'moment';
import httpService from '../../../service/http-service';

const { PARIS2_URL } = process.env;

const CashCallReportList = ({ error, setError, reportType, updateQuery }) => {
  const {
    vesselList,
    roleConfig,
    ga4EventTrigger = () => {},
    userEmail,
  } = useContext(FinancialReportContext);
  const history = useHistory();
  const ownershipId = getURLParams('vessel_ownership_id', history.location.search);
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([{ id: 'report_date', desc: true }]);
  const [showFinancialReportModal, setShowFinancialReportModal] = useState(false);
  const [cashCallReports, setCashCallReports] = useState([]);
  const [isSendEmailModalVisible, setIsSendEmailModalVisible] = useState(false);
  const [emailContentLoading, setEmailContentLoading] = useState(false);
  const [showEmailSuccessModal, setShowEmailSuccessModal] = useState(false);
  const [emailContent, setEmailContent] = useState({});
  const [rowData, setRowData] = useState({});
  const [sendEmailReportIds, setSendEmailReportIds] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filterData, setFilterData] = useState({ vessel: [], reportType: [] });
  const [isMissingAccountant, setIsMissingAccountant] = useState(false);

  const columns = useMemo(() => {
    const defaultColumns = [
      {
        Header: 'No.',
        accessor: (row, index) => index + 1,
        sticky: 'left',
        id: 'id',
        name: 'index',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link
            onClick={() => {
              ga4EventTrigger(
                'Link to Vessel Details',
                'Cash Call Report - link',
                _.get(row, 'vessel_name'),
              );
            }}
            to={`/vessel/ownership/details/${row.vessel_ownership_id}`}
            className="button-link"
          >
            {formatValue(row.vessel_name)}
          </Link>
        ),
        id: 'vessel_name',
        name: 'vessel_name',
        maxWidth: 180,
        width: 180,
        type: 'text',
      },
      {
        Header: 'Booking Month',
        id: 'book_month_year',
        name: 'book_month_year',
        type: 'date',
        maxWidth: 180,
        width: 180,
        accessor: (row) => formatDate(row.book_month_year, 'MMM YYYY'),
      },
      {
        Header: 'Type of Fund',
        accessor: (row) => formatValue(row.fund_type),
        id: 'fund_type',
        name: 'fund_type',
        maxWidth: 180,
        width: 180,
        type: 'text',
      },
      {
        Header: 'Report Submit Date',
        accessor: (row) => formatDate(row.report_date, 'DD MMM YYYY'),
        id: 'report_date',
        name: 'report_date',
        maxWidth: 180,
        width: 180,
        type: 'date',
      },
      {
        Header: 'Fund Request Number',
        accessor: (row) => formatValue(row.request_no),
        id: 'request_no',
        name: 'request_no',
        maxWidth: 120,
        type: 'text',
      },
      {
        Header: 'Reference Number',
        accessor: (row) => formatValue(row.reference_no),
        id: 'reference_no',
        name: 'reference_no',
        maxWidth: 180,
        width: 180,
        type: 'text',
      },
      {
        Header: 'Subject',
        accessor: (row) => formatValue(_.find(SUBJECTS, ['id', row.subject])?.value),
        id: 'subject',
        name: 'subject',
        maxWidth: 180,
        width: 180,
        type: 'text',
      },
      {
        Header: 'Fund Amount Currency',
        accessor: (row) => formatValue(row.currency),
        id: 'currency',
        name: 'currency',
        maxWidth: 120,
        type: 'text',
      },
      {
        Header: 'Fund Amount Requested',
        accessor: (row) =>
          new Intl.NumberFormat('en-US', { minimumFractionDigits: 2 }).format(row.amount),
        id: 'amount',
        name: 'amount',
        headerClassName: 'justify-content-left',
        maxWidth: 220,
        width: 220,
        type: 'text',
      },
      {
        Header: 'Fund Receipt Status',
        accessor: (row) => (
          <div className="text-center">
            {row.receipt_date && <Icon icon="checked" size={20} color="green" />}
          </div>
        ),
        id: 'is_sent',
        name: 'is_sent',
        maxWidth: 120,
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Fund Receipt Date',
        accessor: (row) => formatDate(row.receipt_date, 'DD MMM YYYY'),
        id: 'receipt_date',
        name: 'receipt_date',
        maxWidth: 120,
        type: 'text',
      },
      {
        Header: 'Apply to other ships of the same owner?',
        accessor: (row) => (
          <div className="text-center">
            {row.is_same_owner_applied && <Icon icon="checked" size={20} color="green" />}
          </div>
        ),
        id: 'is_same_owner_applied',
        name: 'is_same_owner_applied',
        maxWidth: 120,
        type: 'text',
      },
      {
        Header: 'Document',
        accessor: (row, index) => (
          <div>
            <Link
              className="button-link"
              onClick={() => {
                ga4EventTrigger(
                  'View Document',
                  'Cash Call Report - link',
                  _.get(row, 'vessel_name'),
                );
              }}
              to={{
                pathname: `${PARIS2_URL}/vessel/document?source=cash-call&path=${base64_encode(
                  row.url,
                )}&id=${row.id}`,
              }}
              target="_blank"
            >
              View
            </Link>
            {!sendEmailReportIds.includes(row.id) &&
              row.is_sent === false &&
              !(row.receipt_date && moment(row.receipt_date).isValid()) &&
              roleConfig.cashCall.manage && (
                <div
                  aria-hidden="true"
                  className="button-link d-inline ml-3"
                  data-testid={`fml-cash-call-list-send-button-${index}`}
                  onClick={() => {
                    setIsSendEmailModalVisible(true);
                    ga4EventTrigger(
                      'Email Document',
                      'Cash Call Report - Menu',
                      _.get(row, 'vessel_name'),
                    );
                    setRowData(row);
                    getEmailMessage(
                      row.vessel_ownership_id,
                      row.vessel_name,
                      row.book_month_year ? formatDate(row.book_month_year, 'MMM YYYY') : '',
                      row.id,
                      row.value,
                      row.subject,
                    );
                  }}
                >
                  Send
                </div>
              )}
          </div>
        ),
        id: 'document',
        type: 'text',
        customClass: 'no-print',
        maxWidth: 180,
        disableSortBy: true,
      },
    ];

    if (roleConfig.cashCall.manage) {
      defaultColumns.push({
        Header: 'Action',
        accessor: (row, index) => (
          <div className="text-center">
            <Icon
              icon="Edit"
              size={20}
              className="edit-icon"
              data-testid={`fml-cash-call-list-edit-button-${index}`}
              onClick={() => {
                ga4EventTrigger(
                  'Edit Financial Report',
                  'Cash Call Report - Menu',
                  _.get(row, 'vessel_name'),
                );
                setShowFinancialReportModal(true);
                setRowData(row);
              }}
              hidden={!roleConfig.cashCall.manage}
            />
          </div>
        ),
        id: 'action',
        type: 'text',
        customClass: 'no-print',
        maxWidth: 120,
        disableSortBy: true,
      });
    }
    return defaultColumns;
  }, [sendEmailReportIds]);

  const getEmailMessage = async (
    vesselId,
    vesselName,
    bookingMonth,
    id,
    registeredOwner,
    subject,
  ) => {
    try {
      setEmailContentLoading(true);
      const response = await vesselService.getRecipientList(vesselId, {}, '');
      const vesselResponse = await vesselService.getAllVessels(
        `f=vessel.id&f=misc_manager.value&flatten=true&vessel_ownership_id=${vesselId}`,
      );
      const staffResponse = await vesselService.getOwnershipVessel(vesselId);
      const accountants = { name: [], email: [] };
      const mailSignature = { name: '', email: '' };
      const isVesselHaveAccountant =
        staffResponse?.data?.fleet_staff?.primary_accountant ||
        staffResponse?.data?.fleet_staff?.secondary_accountant;
      setIsMissingAccountant(!isVesselHaveAccountant);
      const recipientSubjectList = response.data.results?.map((item) => item.subject);
      const recipient = recipientSubjectList?.includes(subject)
        ? response.data.results.find((item) => item.subject === subject)
        : response.data.results.find((item) => item.subject === null);
      if (staffResponse?.data?.fleet_staff?.primary_accountant) {
        accountants.email.push(staffResponse?.data?.fleet_staff?.primary_accountant.email);
        accountants.name.push(staffResponse?.data?.fleet_staff?.primary_accountant.full_name);
        mailSignature.name = staffResponse?.data?.fleet_staff?.primary_accountant.full_name;
        mailSignature.email = staffResponse?.data?.fleet_staff?.primary_accountant.email;
      }
      if (staffResponse?.data?.fleet_staff?.secondary_accountant) {
        if (
          _.isEmpty(accountants.email) ||
          !accountants.email.includes(staffResponse?.data?.fleet_staff?.secondary_accountant.email)
        ) {
          accountants.email.push(staffResponse?.data?.fleet_staff?.secondary_accountant.email);
          accountants.name.push(staffResponse?.data?.fleet_staff?.secondary_accountant.full_name);
        }
      }
      const subjectValue = _.find(SUBJECTS, ['id', subject])?.value;
      const managerData = vesselResponse.data.results[0].misc_manager;
      setEmailContent({
        id: id,
        type: 'cash-call-email',
        from: 'Fleet Management Limited <<EMAIL>>',
        subject: `${subjectValue} ${vesselName ?? ''} (${
          bookingMonth ?? ''
        })`,
        cc: [
          userEmail,
          '<EMAIL>',
          ...(accountants?.email || []),
          ...(recipient?.cc_email_array || []),
        ].join(','),
        recipient: recipient?.email_array?.join(',') || '',
        message: `${
          registeredOwner ? registeredOwner + '. \n\n' : ''
        }Dear Sir/Madam, \n\nPlease be noted that the ${subjectValue} (${bookingMonth}) has been uploaded on PARIS. It is now available for download.
        \nIf you have any queries please feel free to contact ${
          mailSignature.email
        } for clarification.
        \nThanks & regards. \n\nYours faithfully, \n${mailSignature.name} \n${
          managerData?.value || ''
        } \n\nImportant Notes: This email notification will be sent to your designated email address as soon as the Fund request is uploaded on PARIS.`,
      });
    } catch (error) {
      setIsSendEmailModalVisible(false);
      setError('Oops, something went wrong with getting email content. Please try again.');
    }
    setEmailContentLoading(false);
  };

  const fetchCashCallReports = async (
    sortData = [{ id: 'report_date', desc: true }],
    pageIndex = 0,
    pageSize = 10,
  ) => {
    setLoading(true);
    setCashCallReports([]);
    try {
      const response = await vesselService.getCashCallReports(
        !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
        {
          sortBy: sortData,
          pageSize: pageSize,
          pageIndex: pageIndex,
          pageMode: 'list',
        },
        '',
      );
      setCashCallReports(response.data.results);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
      setError(null);
      setLoading(false);
    } catch (error) {
      if (httpService.axios.isCancel(error)) {
        setLoading(true);
      } else {
        setError('Oops, something went wrong. Please try again.');
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    setPageIndex(0);
    fetchCashCallReports(sortData, 0, pageSize);
    if (!_.isEmpty(filterData?.vessel)) updateQuery(filterData?.vessel);
  }, [filterData]);

  useEffect(() => {
    if (!_.isEmpty(vesselList)) {
      const info = vesselList.find((i) => i.id === Number(ownershipId));
      if (info) setFilterData({ vessel: [info] });
    }
  }, [vesselList]);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchCashCallReports(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', 'Cash Call Report - List', value);
    setPageSize(value);
    setPageIndex(0);
    fetchCashCallReports(sortData, 0, value);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Cash Call Report - List', value[0]?.id);
    setSortData(value);
    fetchCashCallReports(value, pageIndex, pageSize);
  };

  const cashCallReportButtons = useMemo(() => {
    return (
      <div className="d-flex justify-content-end no-print">
        <Button
          size="sm"
          variant="outline-primary"
          className="mr-2"
          data-testid="fml-cash-call-list-add-report-Button"
          onClick={() => {
            ga4EventTrigger(
              'Add Financial Report',
              'Cash Call Report - Menu',
              !_.isEmpty(filterData) ? filterData?.vessel[0]?.value : 'Add Financial Report',
            );
            setShowFinancialReportModal(true);
          }}
          hidden={!roleConfig.cashCall.manage}
        >
          Add Report
        </Button>
        <Button
          size="sm"
          variant="outline-primary"
          className="mr-2"
          data-testid="fml-cash-call-list-recipient-Button"
          onClick={() => {
            ga4EventTrigger(
              'View Recipients',
              'Cash Call Report - Menu',
              !_.isEmpty(filterData) ? filterData?.vessel[0]?.value : 'View Recipients',
            );
            history.push('/vessel/report/financial/recipients');
          }}
          hidden={!roleConfig.cashCall.manage}
        >
          Recipients
        </Button>

        <ButtonToolbar>
          <Dropdown>
            <Dropdown.Toggle
              variant="outline-primary"
              id="dropdown-more"
              data-testid="fml-cash-call-list-more-button"
            >
              More
            </Dropdown.Toggle>

            <Dropdown.Menu>
              <Dropdown.Item
                data-testid="fml-cash-call-list-export-excel"
                onClick={() => {
                  ga4EventTrigger(
                    'Export to Excel',
                    'Cash Call Report - Menu',
                    !_.isEmpty(filterData) ? filterData?.vessel[0]?.value : 'Export to Excel',
                  );
                  exportTableToExcel(
                    {
                      cash_call: { jsonData: cashCallReports, columns: columns },
                    },
                    `Cash Call Report`,
                  );
                }}
              >
                Export to Excel
              </Dropdown.Item>
              <Dropdown.Item
                data-testid="fml-cash-call-list-print"
                onClick={() => {
                  ga4EventTrigger(
                    'Print',
                    'Cash Call Report - Menu',
                    !_.isEmpty(filterData) ? filterData?.vessel[0]?.value : 'Print',
                  );
                  window.print();
                }}
              >
                Print
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </ButtonToolbar>
      </div>
    );
  }, [cashCallReports]);

  const handleModalCancel = () => {
    setShowFinancialReportModal(false);
    setRowData({});
  };

  const handleSubmit = () => {
    fetchCashCallReports(sortData, pageIndex, pageSize);
    setRowData({});
  };

  const updateIsSentFlag = async () => {
    const submitFormData = {
      id: rowData.id,
      vessel_ownership_ids: rowData.vessel_ownership_ids,
      is_sent: true,
    };
    await vesselService.editCashCallReport(submitFormData);
  };

  return (
    <>
      <FinancialReportFilter
        loading={loading}
        activeTab={reportType}
        filterData={filterData}
        setFilterData={setFilterData}
      />
      <div>
        {cashCallReportButtons}
        {error && <ErrorAlert message={error} />}
        <CustomTable
          column={columns}
          reportData={cashCallReports}
          tableRef={null}
          isLoading={loading}
          pagination={true}
          pageCount={pageCount}
          totalCount={totalCount}
          setPageNo={onPageIndexChange}
          setPageListSize={onPageSizeChange}
          setSortData={onSortChange}
          pageNo={pageIndex}
          pageSize={pageSize}
          className="drill-table-top-border print-scaling"
        />

        <ConfirmModal
          showConfirmModal={showEmailSuccessModal}
          setShowConfirmModal={setShowEmailSuccessModal}
          content={'Email has been sent'}
          confirmText={'Close'}
          handleConfirm={() => setShowEmailSuccessModal(false)}
          hideCancel={true}
        />

        <SendEmailModal
          title="Send Cash Call Report"
          isVisible={isSendEmailModalVisible}
          onClose={() => {
            setIsSendEmailModalVisible(false);
          }}
          onSuccess={() => {
            ga4EventTrigger(
              'Confirm Email Document',
              'Cash Call Report - Menu',
              !_.isEmpty(filterData) ? filterData?.vessel[0]?.value : 'Confirm Email Document',
            );
            setIsSendEmailModalVisible(false);
            updateIsSentFlag();
            setShowEmailSuccessModal(true);
          }}
          loading={emailContentLoading}
          emailContent={emailContent}
          disabledEmail={true}
          isMissingAccountant={isMissingAccountant}
          showDocuments={emailContent.documents}
          onSubmitButtonClick={() => {
            setSendEmailReportIds([...sendEmailReportIds, emailContent?.id]);
          }}
          className="drill-table-top-border"
          name="cash-call-list"
        />
        <AddCashCallReportModal
          showModal={showFinancialReportModal}
          handleModalCancel={handleModalCancel}
          handleModalSubmit={handleSubmit}
          setPageError={setError}
          editData={rowData}
          reportType={reportType}
        />
      </div>
    </>
  );
};

export default CashCallReportList;
