import React, { useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { formatDate, formatValue } from '../../../util/view-utils';
import vesselService from '../../../service/vessel-service';
import ErrorAlert from '../../ErrorAlert';
import { Link, useHistory } from 'react-router-dom';
import { FinancialReportContext } from '../../../context/FinancialReportContext';
import _ from 'lodash';
import CustomTable from '../../customComponent/CustomTable';
import { Icon } from '../../../styleGuide';
import { Button } from 'react-bootstrap';
import FinancialReportDialog from '../FinancialReportDialog';
import FinancialReportFilter from '../FinancialReportFilter';
import httpService from '../../../service/http-service';
import getURLParams, { base64_encode } from '../../../util/getURLParams';

const { PARIS2_URL } = process.env;

const FinancialReportList = ({ error, setError, reportType, updateQuery }) => {
  const {
    vesselList,
    financialReportTypes,
    roleConfig,
    ga4EventTrigger = () => {},
  } = useContext(FinancialReportContext);
  const history = useHistory();
  const ownershipId = getURLParams('vessel_ownership_id', history.location.search);
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([{ id: 'report_date', desc: true }]);
  const [financialReports, setFinancialReports] = useState([]);
  const [showFinancialReportModal, setShowFinancialReportModal] = useState(false);
  const [rowData, setRowData] = useState({});
  const [loading, setLoading] = useState(true);
  const [filterData, setFilterData] = useState({
    vessel: [],
    reportType: [],
  });

  const columns = useMemo(() => {
    const defaultColumns = [
      {
        Header: 'No.',
        accessor: (row, index) => index + 1,
        sticky: 'left',
        id: 'id',
        name: 'id',
        type: 'number',
        maxWidth: 50,
        disableSortBy: true,
      },
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link
            onClick={() => {
              ga4EventTrigger(
                'Link to Vessel Details',
                `${reportType === 'accounts' ? 'Financial' : 'Owner'} Report - link`,
                _.get(row, 'vessel_name'),
              );
            }}
            to={`/vessel/ownership/details/${row.vessel_ownership_id}`}
            className="button-link"
          >
            {formatValue(row.vessel_name)}
          </Link>
        ),
        id: 'vessel_name',
        name: 'vessel_name',
        type: 'text',
      },
      {
        Header: 'Booking Month',
        accessor: (row) => formatDate(row.book_month_year, 'MMM YYYY'),
        id: 'book_month_year',
        name: 'book_month',
        type: 'text',
        maxWidth: 120,
      },
      {
        Header: 'Name of Report',
        id: 'report_type',
        type: 'text',
        accessor: (row) => (
          <div
            className="button-link"
            onClick={() => {
              ga4EventTrigger(
                'Link to Name of Report',
                `${reportType === 'accounts' ? 'Financial' : 'Owner'} Report - link`,
                _.get(row, 'report_type'),
              );
              setFilterData({
                ...filterData,
                reportType: [checkReportType(row?.report_type)],
              });
            }}
            aria-hidden={true}
          >
            {formatValue(row.report_type)}
          </div>
        ),
      },
      {
        Header: 'Report Submit Date',
        accessor: (row) => formatDate(row.report_date, 'DD MMM YYYY'),
        id: 'report_date',
        name: 'report_date',
        type: 'date',
        maxWidth: 120,
      },
      {
        Header: 'Remarks',
        accessor: (row) => formatValue(row.remark),
        id: 'remark',
        name: 'remark',
        type: 'text',
        disableSortBy: true,
        customClass: 'remarksWordWrap',
      },
      {
        Header: 'Document',
        id: 'document',
        type: 'item',
        accessor: (row) =>
          row.url ? (
            <Link
              className="button-link"
              onClick={() => {
                ga4EventTrigger(
                  'View Document',
                  `${reportType === 'accounts' ? 'Financial' : 'Owner'} Report - link`,
                  _.get(row, 'vessel_name'),
                );
              }}
              to={{
                pathname: `${PARIS2_URL}/vessel/document?source=financial&path=${base64_encode(
                  row.url,
                )}&id=${row.id}`,
              }}
              target="_blank"
            >
              View
            </Link>
          ) : (
            '---'
          ),
        disableSortBy: true,
      },
    ];
    reportType; //NOSONAR
    //no sonarqube scanning as it will break current logic
    if (
      (roleConfig.financial.manage && reportType === 'accounts') ||
      (roleConfig.owner.manage && reportType === 'technical')
    ) {
      defaultColumns.push({
        Header: 'Action',
        accessor: (row, index) => (
          <div className="text-center">
            <Icon
              icon="Edit"
              data-testid={`fml-financial-report-list-edit-${index}`}
              size={20}
              style={{ color: 'black' }}
              onClick={() => {
                ga4EventTrigger(
                  'Edit Financial Report',
                  `${reportType === 'accounts' ? 'Financial' : 'Owner'} Report - Menu`,
                  _.get(row, 'vessel_name'),
                );
                setRowData(row);
                setShowFinancialReportModal(true);
              }}
              hidden={!hasRequiredRoleConfig()}
            />
          </div>
        ),
        id: 'action',
        name: 'action',
        type: 'text',
        disableSortBy: true,
        maxWidth: 60,
      });
    }
    return defaultColumns;
  }, [filterData, financialReportTypes]);

  const checkReportType = (value) => {
    const reportData = financialReportTypes?.find((i) => i.value === value);
    if (reportData) {
      return reportData;
    }
  };

  const fetchFinancialReports = useCallback(
    async (sortData = [{ id: 'report_date', desc: true }], pageIndex = 0, pageSize = 10) => {
      setLoading(true);
      setFinancialReports([]);
      try {
        let pageFilterData = !_.isEmpty(filterData.reportType)
          ? `reportTypeId=${filterData?.reportType[0]?.id}`
          : '';
        const response = await vesselService.getFinancialReports(
          !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
          reportType,
          {
            sortBy: sortData,
            pageSize: pageSize,
            pageIndex: pageIndex,
          },
          pageFilterData,
        );
        setFinancialReports(response.data.results);
        const total = response.data.total;
        setPageCount(Math.ceil(total / pageSize));
        setTotalCount(total);
        setError(null);
        setLoading(false);
      } catch (error) {
        if (httpService.axios.isCancel(error)) {
          setLoading(true);
        } else {
          setError('Oops, something went wrong. Please try again.');
          setLoading(false);
        }
      }
    },
    [filterData],
  );

  useEffect(() => {
    setPageIndex(0);
    fetchFinancialReports(sortData, 0, pageSize);
    if (!_.isEmpty(filterData?.vessel)) updateQuery(filterData?.vessel);
  }, [filterData]);

  useEffect(() => {
    if (!_.isEmpty(vesselList)) {
      const info = vesselList.find((i) => i.id === Number(ownershipId));
      if (info) setFilterData({ ...filterData, vessel: [info] });
    }
  }, [vesselList]);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchFinancialReports(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger(
      'Number of Rows',
      `${reportType === 'accounts' ? 'Financial' : 'Owner'} Report - List`,
      value,
    );
    setPageSize(value);
    setPageIndex(0);
    fetchFinancialReports(sortData, 0, value);
  };

  const onSortChange = (value) => {
    ga4EventTrigger(
      'Sorting',
      `${reportType === 'accounts' ? 'Financial' : 'Owner'} Report - List`,
      value[0]?.id,
    );
    setSortData(value);
    fetchFinancialReports(value, pageIndex, pageSize);
  };

  const handleModalCancel = () => {
    setShowFinancialReportModal(false);
    setRowData({});
  };

  const handleSubmit = () => {
    fetchFinancialReports(sortData, pageIndex, pageSize);
    setRowData({});
  };

  const hasRequiredRoleConfig = () => {
    const map = {
      accounts: [roleConfig.financial.manage],
      technical: [roleConfig.owner.manage],
    };
    return map[reportType]?.some((roleConfig) => roleConfig);
  };

  return (
    <>
      <FinancialReportFilter
        loading={loading}
        activeTab={reportType}
        filterData={filterData}
        setFilterData={setFilterData}
      />

      <div>
        {hasRequiredRoleConfig() && (
          <div className="d-flex justify-content-end no-print">
            <Button
              variant="outline-primary"
              className="mr-2"
              data-testid="fml-financial-report-list-add-report-Button"
              onClick={() => {
                ga4EventTrigger(
                  'Add Financial Report',
                  `${reportType === 'accounts' ? 'Financial' : 'Owner'} Report - Menu`,
                  'Add Financial Report',
                );
                setShowFinancialReportModal(true);
              }}
            >
              Add Report
            </Button>
          </div>
        )}
        {error && <ErrorAlert message={error} />}
        <CustomTable
          column={columns}
          reportData={financialReports}
          tableRef={null}
          isLoading={loading}
          pagination={true}
          pageCount={pageCount}
          totalCount={totalCount}
          setPageNo={onPageIndexChange}
          setPageListSize={onPageSizeChange}
          setSortData={onSortChange}
          pageNo={pageIndex}
          pageSize={pageSize}
          className="drill-table-top-border"
          name="financial-report-list"
        />

        <FinancialReportDialog
          showModal={showFinancialReportModal}
          handleModalCancel={handleModalCancel}
          handleModalSubmit={handleSubmit}
          setPageError={setError}
          editData={rowData}
          reportType={reportType}
        />
      </div>
    </>
  );
};

export default FinancialReportList;
