import React, { useEffect, useMemo, useState, useContext } from 'react';
import { Form, Button, Container, Row } from 'react-bootstrap';
import vesselService from '../../../service/vessel-service';
import { formatValue } from '../../../util/view-utils';
import CustomTable from '../../customComponent/CustomTable';
import ErrorAlert from '../../ErrorAlert';
import RecipientDialog from '../RecipientDialog';
import { BreadcrumbHeader } from '../../BreadcrumpHeader';
import { ErrorPage, Icon } from '../../../styleGuide';
import CustomTypeAhead from '../../customComponent/CustomTypeAhead';
import { FinancialReportContext } from '../../../context/FinancialReportContext';
import { SUBJECTS } from '../../../constants/recipient-subject';
import { Link, useHistory } from 'react-router-dom';
import ListViewModal from '../ListViewModal';
import _ from 'lodash';

const { PARIS2_URL } = process.env;

const Recipients = () => {
  const { vesselList, roleConfig, ga4EventTrigger = () => {} } = useContext(FinancialReportContext);
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState();
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([]);
  const [recipientList, setRecipientList] = useState();
  const [showRecipientModal, setShowRecipientModal] = useState(false);
  const [editRecipientData, setEditRecipientData] = useState({});
  const [recipientFilter, setRecipientFilter] = useState({ vessel: [], subject: [] });
  const [showViewModal, setShowViewModal] = useState(false);
  const [emailList, setEmailList] = useState([]);
  const columns = useMemo(
    () => [
      {
        Header: 'No',
        accessor: (row, index) => index + 1,
        sticky: 'left',
        id: 'no',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link to={`/vessel/ownership/details/${row.vessel_ownership_id}`} className="button-link">
            {formatValue(row.vessel_name)}
          </Link>
        ),
        id: 'vessel_name',
        type: 'text',
      },
      {
        Header: 'Subject',
        id: 'subject',
        type: 'text',
        accessor: (row) => formatValue(_.find(SUBJECTS, ['id', row.subject])?.value),
      },
      {
        Header: 'Vessel Accountant',
        id: 'vessel_accountants',
        type: 'text',
        accessor: (row) => (
          <>
            <div>{_.get(row?.vessel_accountants.primary, 'name')}</div>
            <div> {_.get(row?.vessel_accountants.secondary, 'name')}</div>
          </>
        ),
        maxWidth: 200,
        disableSortBy: true,
      },
      {
        Header: 'Email',
        id: 'email_array',
        type: 'text',
        accessor: (row) =>
          row?.email_array?.slice(0, 3)?.map((item, index) => (
            <div key={index}>
              {formatValue(item)}{' '}
              {index === 2 && row?.email_array.length > 3 && (
                <span
                  className="cursor-pointer"
                  onClick={() => {
                    setShowViewModal(true);
                    setEmailList(row.email_array);
                  }}
                  aria-hidden="true"
                >
                  ....
                </span>
              )}
            </div>
          )),
        maxWidth: 200,
        disableSortBy: true,
      },
      {
        Header: 'Action',
        accessor: (row, index) => (
          <div className="text-center">
            <Icon
              icon="Edit"
              data-testid={`fml-recipients-list-edit-${index}`}
              className="cursor-pointer"
              size={20}
              style={{ color: 'black' }}
              onClick={() => {
                ga4EventTrigger(
                  'Edit Recipient List',
                  'Cash Call Report Recipients - Menu',
                  !_.isEmpty(recipientFilter)
                    ? recipientFilter?.vessel[0]?.value
                    : 'Edit Recipient List',
                );
                setEditRecipientData(row);
                setShowRecipientModal(true);
              }}
            />
          </div>
        ),
        id: 'action',
        type: 'text',
        disableSortBy: true,
      },
    ],
    [],
  );
  const fetchRecipientList = async (sortData = [], pageIndex = 0, pageSize = 10) => {
    setError(null);
    setLoading(true);
    let pageFilterData = !_.isEmpty(recipientFilter.subject)
      ? `subject=${recipientFilter?.subject[0]?.id}`
      : '';
    if (roleConfig.cashCall.view) {
      try {
        const response = await vesselService.getRecipientList(
          !_.isEmpty(recipientFilter) && recipientFilter?.vessel[0]?.id,
          {
            sortBy: sortData,
            pageSize: pageSize,
            pageIndex: pageIndex,
          },
          pageFilterData,
        );
        setRecipientList(response.data.results);
        const total = response.data.total;
        setPageCount(Math.ceil(total / pageSize));
        setTotalCount(total);
      } catch (error) {
        setError('Oops, something went wrong. Please try again.');
      }
    }
    setLoading(false);
  };

  useEffect(() => {
    setPageIndex(0);
    fetchRecipientList(sortData, 0, pageSize);
  }, [recipientFilter]);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchRecipientList(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', 'Cash Call Report Recipients - List', value);
    setPageSize(value);
    setPageIndex(0);
    fetchRecipientList(sortData, 0, value);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Cash Call Report Recipients - List', value[0]?.id);
    setSortData(value);
    fetchRecipientList(value, pageIndex, pageSize);
  };

  const handleModalCancel = () => {
    setShowRecipientModal(false);
    setEditRecipientData({});
  };

  const handleSubmit = () => {
    fetchRecipientList(sortData, pageIndex, pageSize);
    setEditRecipientData({});
  };

  const FilterComponent = () => {
    return (
      <>
        <Row>
          <Form.Label className="filter-reports">
            <b>Filter Reports</b>
          </Form.Label>
        </Row>
        <Row>
          <Form.Group className="reports-filter-textField form-group">
            <Form.Control type="text" placeholder="Vessel Name" disabled />
          </Form.Group>
          <Form.Group className={`vessel-dropdown ${loading && 'disabled'} form-group`}>
            <CustomTypeAhead
              id="basic-typeahead-single"
              labelKey="value"
              name="vessel_list"
              inputProps={{ 'data-testid': 'fml-recipient-filter-vessel' }}
              multiple={false}
              placeholder="All Vessels"
              options={vesselList}
              selected={recipientFilter.vessel}
              disabled={loading}
              showDropDownIcon={true}
              clearOnFocus={true}
              onChange={(e) => {
                setRecipientFilter({ ...recipientFilter, vessel: e });
              }}
              handleClear={() => {
                setRecipientFilter({ ...recipientFilter, vessel: [] });
              }}
            />
          </Form.Group>
          <Form.Group className="reports-filter-textField form-group">
            <Form.Control type="text" placeholder="Subject" disabled />
          </Form.Group>
          <Form.Group className={`vessel-dropdown ${loading && 'disabled'} form-group`}>
            <CustomTypeAhead
              id="basic-typeahead-single"
              labelKey="value"
              name="subject"
              inputProps={{ 'data-testid': 'fml-recipient-filter-subject' }}
              multiple={false}
              placeholder="All Subjects"
              options={SUBJECTS}
              selected={recipientFilter.subject}
              showDropDownIcon={true}
              disabled={loading}
              clearOnFocus={true}
              onChange={(e) => {
                ga4EventTrigger(
                  'Recipient Subject',
                  'Cash Call Report Recipients - Filter',
                  e[0].value,
                );
                setRecipientFilter({ ...recipientFilter, subject: e });
              }}
              handleClear={() => {
                setRecipientFilter({ ...recipientFilter, subject: [] });
              }}
            />
          </Form.Group>
        </Row>
      </>
    );
  };

  const breadCrumbsItems = [
    {
      title: 'Financial Reports',
      label: 'To Financial Report Page',
      link: `${PARIS2_URL}/vessel/report/financial`,
    },
    {
      title: `Cash Call Reports`,
      label: 'To Cash Call Reports Page',
      link: `${PARIS2_URL}/vessel/report/financial/cash-call`,
    },
    {
      title: 'Recipients',
      label: 'Recipients',
      link: '#',
    },
  ];

  return (
    <Container className="pt-4">
      {roleConfig.cashCall.view ? (
        <>
          <div className="d-flex justify-content-between align-items-center">
            <BreadcrumbHeader
              onClick={() =>
                ga4EventTrigger(
                  'Breadcrumb',
                  'Cash Call Report Recipients - Menu ',
                  !_.isEmpty(recipientFilter) ? recipientFilter?.vessel[0]?.value : 'Breadcrumb',
                )
              }
              items={breadCrumbsItems}
              activeItem={'Recipients'}
            />
            <div className="d-flex h-50">
              <Button
                size="sm"
                variant="outline-primary"
                className="mr-2"
                data-testid="fml-recipient-list-add-button"
                onClick={() => {
                  ga4EventTrigger(
                    'Add Recipient List',
                    'Cash Call Report Recipients - Menu',
                    !_.isEmpty(recipientFilter)
                      ? recipientFilter?.vessel[0]?.value
                      : 'Add Financial Report',
                  );
                  setShowRecipientModal(true);
                }}
                hidden={!roleConfig.cashCall.manage}
              >
                Add Recipient List
              </Button>
              <Icon
                icon="close"
                size={30}
                className="ml-4 cursor-pointer"
                onClick={() => history.push('/vessel/report/financial/cash-call')}
              />
            </div>
          </div>

          <FilterComponent />
          {error && <ErrorAlert message={error} />}

          <CustomTable
            column={columns}
            reportData={recipientList}
            tableRef={null}
            isLoading={loading}
            pagination={true}
            pageCount={pageCount}
            totalCount={totalCount}
            setPageNo={onPageIndexChange}
            setPageListSize={onPageSizeChange}
            setSortData={onSortChange}
            pageNo={pageIndex}
            pageSize={pageSize}
          />
          <RecipientDialog
            showModal={showRecipientModal}
            editData={editRecipientData}
            handleModalCancel={handleModalCancel}
            handleModalSubmit={handleSubmit}
            setPageError={setError}
          />

          <ListViewModal
            showModal={showViewModal}
            handleModalClose={() => {
              setShowViewModal(false);
            }}
            data={emailList}
          />
        </>
      ) : (
        <ErrorPage errorCode={403} />
      )}
    </Container>
  );
};
export default Recipients;
