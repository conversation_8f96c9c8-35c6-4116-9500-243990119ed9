import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import { Button, Col, Form, Modal, Row } from 'react-bootstrap';
import { Icon } from '../../styleGuide';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';
import { FinancialReportContext } from '../../context/FinancialReportContext';
import { SUBJECTS } from '../../constants/recipient-subject';
import ErrorAlert from '../ErrorAlert';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import vesselService from '../../service/vessel-service';
import { EMAIL_REGEXP } from '../../constants/regexp';

const RecipientDialog = ({
  showModal = true,
  handleModalCancel = () => {},
  handleModalSubmit = () => {},
  editData,
  setPageError,
}) => {
  const { vesselList, ga4EventTrigger } = useContext(FinancialReportContext);
  const edit = !_.isEmpty(editData);
  const defaultValue = {
    vessel: [],
    subject: '',
    email_array: [''],
    cc_email_array: [''],
  };
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState(defaultValue);
  const [errors, setErrors] = useState({});
  const [validationError, setValidationError] = useState();
  const [initialEditData, setInitialEditData] = useState();
  const [vesselAccountantData, setVesselAccountantData] = useState({});

  const validateEmail = (value, field) => {
    let errorEmailList = {};
    value.map((item, index) => {
      if (field === 'email_array' && _.isEmpty(item)) {
        errorEmailList = { ...errorEmailList, [`${field + index}`]: 'Please enter email address' };
      } else if (!_.isEmpty(item) && !EMAIL_REGEXP.test(item)) {
        errorEmailList = { ...errorEmailList, [`${field + index}`]: 'Invalid email address' };
      } else if (value.length > 1 && _.isEmpty(item)) {
        errorEmailList = { ...errorEmailList, [`${field + index}`]: 'Please enter email address' };
      } else {
        errorEmailList = { ...errorEmailList, [`${field + index}`]: undefined };
      }
    });
    return { [field]: errorEmailList };
  };

  const validateVessel = (value) => ({
    vessel: _.isEmpty(value) ? 'Vessel is a required field' : undefined,
  });

  const validateSubject = (value, type) =>
    type === 'text'
      ? {
          subject: _.isEmpty(form.subject) && value ? 'Please select valid Subject' : undefined,
        }
      : {
          subject:
            _.isEmpty(value) && form.subject_text ? 'Please select valid Subject' : undefined,
        };

  const intialValidateError = () => ({
    ..._.omitBy(errors, (v) => v == null),
    ...validateVessel(form.vessel),
    ...validateSubject(form.subject_text, 'text'),
    ...validateEmail(form.email_array, 'email_array'),
    ...validateEmail(form.cc_email_array, 'cc_email_array'),
  });

  const validateForm = (field, value) => {
    let errObj = {
      ...errors,
    };

    switch (field) {
      case 'vessel':
        errObj = {
          ...errObj,
          ...validateVessel(value),
        };
        break;
      case 'subject':
        errObj = {
          ...errObj,
          ...validateSubject(value, 'select'),
        };
        break;
      case 'subject_text':
        errObj = {
          ...errObj,
          ...validateSubject(value, 'text'),
        };
        break;
      case 'email_array':
      case 'cc_email_array':
        errObj = {
          ...errObj,
          ...validateEmail(value, field),
        };
        break;
    }
    errObj = _.pickBy(errObj, (i) => i !== undefined);
    setErrors(errObj);
  };

  const fetchVesselAccountants = async (selectedVesselId) => {
    try {
      setLoading(true);
      const response = await vesselService.getOwnershipVessel(selectedVesselId);
      setVesselAccountantData(response.data.fleet_staff);
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const setFieldValue = (field, value) => {
    validateForm(field, value);
    if (field === 'vessel') {
      if (!_.isEmpty(value)) {
        fetchVesselAccountants(value[0].id);
      } else {
        setVesselAccountantData({});
      }
    }
    setForm((prevState) => ({
      ...prevState,
      [field]: value,
    }));
  };

  useEffect(() => {
    if (edit && editData) {
      const formData = {
        vessel: [{ id: editData.vessel_ownership_id, value: editData.vessel_name }],
        subject: editData?.subject ? [SUBJECTS.find((i) => i.id === editData.subject)] : [],
        email_array: !_.isEmpty(editData.email_array) ? [...editData.email_array] : [''],
        cc_email_array: !_.isEmpty(editData.cc_email_array) ? [...editData.cc_email_array] : [''],
      };

      const initialData = {
        vessel_ownership_id: editData.vessel_ownership_id,
        subject: editData.subject,
        email_array: !_.isEmpty(editData.email_array) ? [...editData.email_array] : [''],
        cc_email_array: !_.isEmpty(editData.cc_email_array) ? [...editData.cc_email_array] : [''],
      };
      setForm(formData);
      setInitialEditData(initialData);
    }
  }, [editData]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    let errorList = { ...intialValidateError() };
    errorList = _.pickBy(errorList, (i) => i !== undefined);
    errorList.cc_email_array = _.pickBy(errorList.cc_email_array, (i) => i !== undefined);
    errorList.email_array = _.pickBy(errorList.email_array, (i) => i !== undefined);
    errorList = _.pickBy(errorList, (i) => !_.isEmpty(i));

    if (_.isEmpty(errorList)) {
      setLoading(true);
      let submitFormData = {
        vessel_ownership_id: form.vessel[0]?.id,
        subject: form.subject[0]?.id ?? null,
        email_array: form.email_array,
        cc_email_array: form.cc_email_array,
      };

      try {
        if (edit) {
          ga4EventTrigger(
            'Confirm Edit Recipient List',
            'Cash Call Report Recipients - Menu',
            form.vessel[0].value,
          );
          submitFormData = _.fromPairs(
            _.differenceWith(_.toPairs(submitFormData), _.toPairs(initialEditData), _.isEqual),
          );
          submitFormData.id = editData.id;
          await vesselService.editRecipientList(submitFormData);
        } else {
          ga4EventTrigger(
            'Confirm Add Recipient List',
            'Cash Call Report Recipients - Menu',
            form.vessel[0].value,
          );
          await vesselService.addRecipientList(submitFormData);
        }
        handleModalSubmit();
        handleCancel();
      } catch (error) {
        if (error?.response?.status === 400) {
          setValidationError(`${error?.response.data}. Please try again.`);
          setLoading(false);
        } else {
          handleCancel();
          setPageError('Something went wrong with updating Recipients. Please try later');
        }
      }
    } else {
      setErrors(errorList);
    }
  };
  const handleCancel = () => {
    setForm(defaultValue);
    setErrors({});
    handleModalCancel();
    setLoading(false);
    setValidationError();
    setVesselAccountantData({});
  };

  const showEditVesselAccountant = (data) => {
    const { primary, secondary } = data;
    return (
      <>
        <Form.Control
          className="mb-3"
          disabled={true}
          value={primary?.email ?? 'Not assigned'}
          data-testid={`fml-recipient-dialog-primary-vessel-accountant`}
        />
        <Form.Control
          disabled={true}
          value={secondary?.email ?? 'Not assigned'}
          data-testid={`fml-recipient-dialog-secondary-vessel-accountant`}
        />
      </>
    );
  };

  return (
    <Modal
      id="recipient-modal"
      show={showModal}
      aria-labelledby="recipient-modal"
      data-testid="fml-recipient-list-dialog"
      centered
      size="lg"
      backdrop="static"
    >
      <Modal.Header>
        <Modal.Title>
          {!edit ? `Add Cash Call Report Recipient List` : `Edit Cash Call Report Recipient List`}
          <p className="pl-1 text-dark alert-danger required-field-text">
            <span className="text-danger"> Note:</span> if the subject is blank, this set of email
            recipients will be also used as default for other subjects which the email recipient
            is not defined. The email address of the Vessel Accountant is included automatically
            when the cash call report is sent.
          </p>
          <p className="required-field-text">* Required fields</p>
        </Modal.Title>
      </Modal.Header>

      {validationError && <ErrorAlert message={validationError} />}

      <CustomOverlayLoader active={loading}>
        <Form className="form-main-control">
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Vessel*</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="vessel_list"
                    inputProps={{ 'data-testid': 'fml-recipient-dialog-vessel' }}
                    multiple={false}
                    placeholder="Please Select"
                    disabled={edit}
                    isLoading={loading}
                    options={vesselList}
                    selected={form.vessel ? form.vessel : []}
                    showDropDownIcon={true}
                    clearOnFocus={true}
                    onChange={(e) => {
                      setFieldValue('vessel', e);
                    }}
                    handleClear={() => {
                      setFieldValue('vessel', []);
                    }}
                  />
                  <div className="validate-error">{errors.vessel}</div>
                </Form.Group>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Subject</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="subject"
                    inputProps={{ 'data-testid': 'fml-recipient-dialog-subject' }}
                    multiple={false}
                    disabled={edit}
                    placeholder="Please Select"
                    isLoading={loading}
                    options={SUBJECTS}
                    selected={form.subject ? form.subject : []}
                    showDropDownIcon={true}
                    clearOnFocus={true}
                    onInputChange={(e) => setFieldValue('subject_text', e)}
                    onChange={(e) => {
                      setFieldValue('subject', e);
                    }}
                    handleClear={() => {
                      setFieldValue('subject', []);
                      setFieldValue('subject_text', '');
                    }}
                  />
                  <div className="validate-error">{errors.subject}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Vessel Accountant</Form.Label>
                  {showEditVesselAccountant(
                    edit
                      ? editData.vessel_accountants
                      : {
                          primary: vesselAccountantData.primary_accountant,
                          secondary: vesselAccountantData.secondary_accountant,
                        },
                  )}
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">To Email Address*</Form.Label>
                  {form.email_array?.map((item, index) => (
                    <div key={`to-address-${index}`}>
                      <div className="d-flex align-items-center mb-3">
                        <Form.Control
                          value={item}
                          data-testid={`fml-recipient-dialog-to-address-${index}`}
                          onChange={(e) => {
                            const { email_array } = form;
                            email_array[index] = e.target.value.trim();
                            setFieldValue('email_array', email_array);
                          }}
                        />
                        {index === 0 ? (
                          <div className="width-30 ml-4 d-inline-block" />
                        ) : (
                          <Icon
                            icon="remove"
                            size={30}
                            className="ml-4"
                            onClick={() => {
                              const { email_array } = form;
                              email_array.splice(index, 1);
                              setFieldValue('email_array', email_array);
                            }}
                          />
                        )}
                      </div>
                      <div className="validate-error">
                        {!_.isEmpty(errors?.email_array) &&
                          errors.email_array?.[`${'email_array' + index}`]}
                      </div>
                    </div>
                  ))}

                  <Button
                    size="sm"
                    variant="outline-primary"
                    className="mr-2"
                    data-testid="fml-recipient-dialog-add-to-address"
                    onClick={() => {
                      setForm({ ...form, email_array: [...form.email_array, ''] });
                    }}
                  >
                    Add Another
                  </Button>
                </Form.Group>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">CC Email Address</Form.Label>
                  {form.cc_email_array?.map((item, index) => (
                    <div key={`cc-address-${index}`}>
                      <div className="d-flex align-items-center mb-3">
                        <Form.Control
                          value={item}
                          data-testid={`fml-recipient-dialog-cc-address-${index}`}
                          onChange={(e) => {
                            const { cc_email_array } = form;
                            cc_email_array[index] = e.target.value.trim();
                            setFieldValue('cc_email_array', cc_email_array);
                          }}
                        />
                        {index === 0 ? (
                          <div className="width-30 ml-4 d-inline-block" />
                        ) : (
                          <Icon
                            icon="remove"
                            size={30}
                            className="ml-4"
                            onClick={() => {
                              const { cc_email_array } = form;
                              cc_email_array.splice(index, 1);
                              setFieldValue('cc_email_array', cc_email_array);
                            }}
                          />
                        )}
                      </div>
                      <div className="validate-error">
                        {!_.isEmpty(errors?.cc_email_array) &&
                          errors?.cc_email_array?.[`${'cc_email_array' + index}`]}
                      </div>
                    </div>
                  ))}
                  <Button
                    size="sm"
                    variant="outline-primary"
                    className="mr-2"
                    data-testid="fml-recipient-dialog-add-cc-address"
                    onClick={() => {
                      setForm({ ...form, cc_email_array: [...form.cc_email_array, ''] });
                    }}
                  >
                    Add Another
                  </Button>
                </Form.Group>
              </Col>
            </Row>
          </Modal.Body>
          <Modal.Footer>
            <Button
              variant="primary"
              data-testid="fml-recipient-dialog-close"
              onClick={handleCancel}
            >
              Close
            </Button>
            <Button
              variant="secondary"
              data-testid="fml-recipient-dialog-save"
              type="submit"
              onClick={handleSubmit}
            >
              Save
            </Button>
          </Modal.Footer>
        </Form>
      </CustomOverlayLoader>
    </Modal>
  );
};
export default RecipientDialog;
