import _ from 'lodash';
import moment from 'moment';
import React, { useContext, useEffect, useState } from 'react';
import { Button, Col, Form, Modal, Row } from 'react-bootstrap';
import { FinancialReportContext } from '../../context/FinancialReportContext';
import { MONTHS, FUNDTYPES, CASHCALLSUBJECT } from '../../model/constants';
import CustomDatePicker from '../../component/customComponent/CustomDatePicker';
import DatePicker from 'react-datepicker';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';
import vesselService from '../../service/vessel-service';
import { formatDate, getTimeDiffWithUTC } from '../../util/view-utils';
import ErrorAlert from '../ErrorAlert';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import { usePrevious } from '../../util/customHooks';
import CustomFileUpload from '../customComponent/CustomFileUpload';

const AddCashCallReportModal = ({
  showModal = true,
  handleModalCancel = () => {},
  handleModalSubmit = () => {},
  setPageError,
  editData,
}) => {
  const { vesselList, filterData, dropdownData, ga4EventTrigger } =
    useContext(FinancialReportContext);
  const edit = !_.isEmpty(editData);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState({});
  const [errors, setErrors] = useState({});
  const [warnings, setWarnings] = useState({});
  const [validationError, setValidationError] = useState();
  const [, setIntialEditData] = useState();
  const [disableSameOnwerVessel, setDisableSameOwnerVessel] = useState(true);
  const [sameOwnerVesselList, setSameOwnerVesselList] = useState([]);
  const [disableSave, setDisableSave] = useState(true);
  const previousData = usePrevious(form);

  const validateFile = (value) => {
    if (value?.size && value?.size / 1000000 > 100) {
      return { file: 'Cannot upload a file larger than 100MB' };
    }
    if (_.isEmpty(value?.name)) {
      return { file: 'File is a required field' };
    }
    if (value.type === 'application/x-msdownload') {
      return { file: 'Cannot upload exe file' };
    }
    return { file: undefined };
  };

  const validateField = (field, value, label) => ({
    [field]: _.isEmpty(value) ? `${label} is a required field` : undefined,
  });

  const validateFundRequestNumber = (value) => {
    if (!_.isEmpty(value) && (value < 1 || value > 100)) {
      return {
        request_no: 'Fund Request Number should be between 1 and 100',
      };
    }
    return validateField('request_no', value, 'Fund Request Number');
  };

  const validateFundAmountNumber = (value) => {
    if (!_.isEmpty(value) && (value < 0 || value > BigInt('9999999999999999'))) {
      return {
        amount: 'Fund Request Amount should be between 0 and 9999999999999999',
      };
    }
    return validateField('amount', value, 'Fund Amount Requested');
  };

  const intialValidateError = () => ({
    ..._.omitBy(errors, (v) => v == null),
    ...validateField('vessel_list', form.vessel_list, 'Vessel'),
    ...validateField('book_month', form.book_month, 'Booking Month'),
    ...validateField('report_date', form.report_date, 'Report Submit Date'),
    ...validateField('fund_type', form.fund_type, 'Type of Fund'),
    ...validateField('book_year', form.book_year?.toString(), 'Booking year'),
    ...validateFundRequestNumber(_.toString(form.request_no)),
    ...validateFundAmountNumber(_.toString(form.amount)),
    ...validateField('subject', form.subject, 'Subject'),
    ...validateField('currency', form.currency, 'Fund Amount Currency'),
    ...validateFile(form?.file),
  });

  const validateForm = (field, value) => {
    let newErrors = {
      ...errors,
    };
    switch (field) {
      case 'vessel_list':
        newErrors = { ...newErrors, ...validateField(field, value, 'Vessel') };
        break;

      case 'book_month':
        newErrors = { ...newErrors, ...validateField(field, value, 'Booking Month') };
        break;

      case 'report_date':
        newErrors = { ...newErrors, ...validateField(field, value, 'Report Submit Date') };
        break;

      case 'file':
        newErrors = { ...newErrors, ...validateFile(value) };
        break;

      case 'fund_type':
        newErrors = { ...newErrors, ...validateField(field, value, 'Type of Fund') };
        break;

      case 'book_year':
        newErrors = { ...newErrors, ...validateField(field, value?.toString(), 'Booking year') };
        break;

      case 'request_no':
        newErrors = { ...newErrors, ...validateFundRequestNumber(value) };
        break;

      case 'amount':
        newErrors = { ...newErrors, ...validateFundAmountNumber(value) };
        break;

      case 'subject':
        newErrors = { ...newErrors, ...validateField(field, value, 'Subject') };
        break;

      case 'currency':
        newErrors = { ...newErrors, ...validateField(field, value, 'Fund Amount Currency') };
        break;
    }
    newErrors = _.pickBy(newErrors, (i) => i !== undefined);
    setDisableSave(!_.isEmpty(newErrors));
    setErrors(newErrors);
  };

  const setFieldValue = (field, value) => {
    console.log(`Setting field: ${field}, value: ${JSON.stringify(value)}`);
    validateForm(field, value);

    const updatedForm = {
      ...form,
      [field]: value,
      ...(field === 'vessel_list' ? { other_vessels: [] } : {}),
    };

    if (field === 'fund_type') {
      handleFundTypeChange(updatedForm, value);
    }

    if (field === 'subject') {
      validateSubject(updatedForm, value);
    }

    if (field === 'currency') {
      validateCurrency(updatedForm, value);
    }

    setForm(updatedForm);
  };

  const handleFundTypeChange = (updatedForm, value) => {
    const isEUETS = value[0]?.value === _.find(FUNDTYPES, { id: 'EU-ETS' })?.value;

    if (isEUETS) {
      updatedForm.subject = [CASHCALLSUBJECT.find((i) => i.value === 'EU-ETS Fund Request')];
      updatedForm.currency = [dropdownData?.data?.miscCurrencys?.find((i) => i.value === 'EUA')];
      removeErrors(['fund_type', 'subject', 'currency']);
      removeWarnings(['currency']);
    } else if (previousData?.fund_type?.[0]?.value === _.find(FUNDTYPES, { id: 'EU-ETS' })?.value) {
      updatedForm.subject = [];
      updatedForm.currency = [];
      removeErrors(['fund_type', 'subject', 'currency']);
      removeWarnings(['currency']);
    }
  };

  const validateSubject = (updatedForm, value) => {
    const EUETSSelected =  updatedForm.fund_type && updatedForm.fund_type.length > 0 && updatedForm.fund_type[0].value === _.find(FUNDTYPES, { id: 'EU-ETS' })?.value;
    const isSubjectEUETS = value[0]?.value === _.find(CASHCALLSUBJECT, { id: 'eu-ets' })?.value;

    if (!EUETSSelected && isSubjectEUETS) {
      setErrors({
        ...errors,
        subject: 'This is applicable for EU-ETS only',
      });
      setDisableSave(true);
    }
  };

  const validateCurrency = (updatedForm, value) => {
    const EUETSSelected = updatedForm.fund_type && updatedForm.fund_type.length > 0 && updatedForm.fund_type[0].value === _.find(FUNDTYPES, { id: 'EU-ETS' })?.value;
    const isCurrencyEUA = value[0]?.value === 'EUA';

    if (!EUETSSelected && isCurrencyEUA) {
      setErrors({
        ...errors,
        currency: 'EUA is applicable for EU-ETS only',
      });
      setDisableSave(true);
    }
    if (EUETSSelected && !isCurrencyEUA) {
      setWarnings({
        ...warnings,
        currency: 'Be aware that EUA is not selected',
      });
    } else {
      removeWarnings(['currency']);
    }
  };
  const removeWarnings = (keys) => {
    const updatedWarnings = {...warnings};
    keys.forEach((key) => {
      delete updatedWarnings[key];
    } );
    setWarnings(updatedWarnings);
  }
  const removeErrors = (keys) => {
    const updatedErrors = {...errors};
    keys.forEach((key) => {
      delete updatedErrors[key];
    } );
    setErrors(updatedErrors);
    setDisableSave(!_.isEmpty(updatedErrors));
  };
  const uploadFile = async (data) => {
    let url = '';
    let preSignedUrl = '';
    try {
      const response = await vesselService.getPreSignedUploadLink({
        record_type: 'cash-call',
        payload: data,
        action: edit ? 'replace' : 'upload',
        file_name: form.file.name,
      });
      url = response.data.url;
      preSignedUrl = response.data.pre_signed_link;
    } catch (e) {
      setValidationError(`${e?.response.data}. Please try again.`);
      setLoading(false);
    }

    if (url) {
      try {
        await vesselService.uploadPresignedDocument(preSignedUrl, form.file, form.file.type);
      } catch (e) {
        url = '';
        setValidationError(`${e?.response.data}. Please try again.`);
        setLoading(false);
      }
    }
    return url;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    let errorList = { ...intialValidateError() };
    errorList = _.pickBy(errorList, (i) => i !== undefined);
    if (_.isEmpty(errorList)) {
      setLoading(true);
      let vesselids = form.vessel_list.map((i) => i.id);
      if (!_.isEmpty(form.other_vessels)) {
        vesselids = [...vesselids, ...(form?.other_vessels?.map((i) => i.id) || null)];
      }
      let submitFormData = {
        vessel_ownership_ids: vesselids,
        report_date: moment(form.report_date).format('YYYY-MM-DD'),
        book_year: _.toNumber(moment(form.book_year).format('YYYY')),
        book_month: _.toNumber(form.book_month[0].id),
        subject: form.subject[0].id,
        fund_type: form.fund_type[0].id,
        request_no: _.toNumber(form.request_no),
        reference_no: form.reference_no || null,
        amount: form.amount,
        currency: form.currency[0].value,
        receipt_date: form.receipt_date ? moment(form.receipt_date).format('YYYY-MM-DD') : null,
        url: form.file?.name,
        inactive: form.inactive,
        is_sent: false,
      };

      try {
        if (edit) {
          ga4EventTrigger(
            'Confirm Edit Cash Call Report',
            'Cash Call Report - Menu',
            form.vessel_list[0].value,
          );
          submitFormData.id = editData.id;
          await vesselService.editCashCallReport(submitFormData);
          handleAfterSubmit();
        } else {
          ga4EventTrigger(
            'Confirm Add Cash Call Report',
            'Cash Call Report - Menu',
            form.vessel_list[0].value,
          );
          submitFormData.inactive = false;
          submitFormData.url = await uploadFile(submitFormData);
          if (!_.isEmpty(submitFormData?.url)) {
            await vesselService.addCashCallReport(submitFormData);
            handleAfterSubmit();
          }
        }
      } catch (error) {
        if (error?.response?.status === 400) {
          setValidationError(`${error?.response.data}. Please try again.`);
          setLoading(false);
        } else {
          handleCancel();
          setPageError(
            'Something went wrong with updating cash call report list. Please try later',
          );
        }
      }
    } else {
      setErrors(errorList);
      setDisableSave(true);
    }
  };

  const handleAfterSubmit = () => {
    handleModalSubmit();
    handleCancel();
  };

  const handleCancel = () => {
    setForm({});
    setErrors({});
    setWarnings({});
    setValidationError();
    handleModalCancel();
    setLoading(false);
  };

  useEffect(() => {
    if (!_.isEmpty(form.vessel_list)) {
      const sameOwnerList = vesselList.filter(
        (vessel) =>
          !_.isEqual(form.vessel_list[0], vessel) &&
          vessel.ownerId === form.vessel_list[0]?.ownerId,
      );
      setSameOwnerVesselList(_.isEmpty(sameOwnerList) ? form.vessel_list : sameOwnerList);
      setDisableSameOwnerVessel(_.isEmpty(sameOwnerList));
    }
  }, [vesselList, form.vessel_list]);

  useEffect(() => {
    if (edit && editData) {
      const otherVesselIds = editData?.vessel_ownership_ids?.filter(
        (i) => i !== editData.vessel_ownership_id,
      );
      const currentVessel = vesselList.find((vessel) => vessel.id === editData.vessel_ownership_id);
      let oldVessels = [];
      const sameOwnerList = vesselList.filter(
        (vessel) =>
          !_.isEqual(form.vessel_list[0], vessel) && vessel.ownerId === currentVessel?.ownerId,
      );
      if (
        !_.isEmpty(editData?.vessel_ownership_ids) &&
        !_.isEmpty(otherVesselIds) &&
        !_.isEmpty(sameOwnerList)
      ) {
        oldVessels = otherVesselIds.map((item) => {
          return sameOwnerList.find((vessel) => vessel.id === item);
        });
      }
      const formData = {
        vessel_list: [currentVessel],
        book_month: editData.book_month ? [MONTHS?.find((i) => i.id === editData.book_month)] : [],
        report_date: moment(editData.report_date).format('YYYY-MM-DD'),
        file: { name: editData.url },
        request_no: editData?.request_no,
        amount: editData?.amount,
        currency: editData.currency
          ? [dropdownData?.data?.miscCurrencys?.find((i) => i.value === editData.currency)]
          : [],
        book_year: moment(editData.book_month_year).toDate(),
        other_vessels: oldVessels,
        fund_type:
          editData.fund_type && FUNDTYPES.map((i) => i.id).includes(editData.fund_type)
            ? [FUNDTYPES.find((i) => i.id === editData.fund_type)]
            : [],
        reference_no: editData.reference_no || undefined,
        subject:
          editData.subject && CASHCALLSUBJECT.map((i) => i.id).includes(editData.subject)
            ? [CASHCALLSUBJECT.find((i) => i.id === editData.subject)]
            : [],
        receipt_date: editData.receipt_date || null,
        inactive: editData.inactive,
      };

      const intialData = {
        vessel_list: [{ id: editData.vessel_ownership_id, value: editData.vessel_name }],
        book_month: editData.book_month ? [MONTHS?.find((i) => i.id === editData.book_month)] : [],
        report_date: moment(editData.report_date).format('YYYY-MM-DD'),
        file: { name: editData.url },
        request_no: editData?.request_no,
        amount: editData?.amount,
        currency: editData.currency
          ? [dropdownData?.data?.miscCurrencys?.find((i) => i.value === editData.currency)]
          : [],
        book_year: moment(editData.book_year).toDate(),
        other_vessels: !_.isEmpty(editData?.vessel_ownership_ids)
          ? [
              editData?.vessel_ownership_ids
                ?.filter((i) => i !== editData.vessel_ownership_id)
                .map((item, index) => {
                  return { id: item, value: editData.vessel_names[index] };
                }),
            ]
          : [],
        fund_type: editData.fund_type ? [FUNDTYPES.find((i) => i.id === editData.fund_type)] : [],
        reference_no: editData.reference_no,
        subject: editData.subject ? [CASHCALLSUBJECT.find((i) => i.id === editData.subject)] : [],
        receipt_date: editData.receipt_date,
        inactive: editData.inactive,
        is_sent: editData.is_sent,
      };
      setForm(formData);
      setIntialEditData(intialData);
    } else {
      setForm({
        report_date: formatDate(new Date(), 'YYYY-MM-DD', ''),
        vessel_list:
          !_.isEmpty(filterData) && !_.isEmpty(filterData?.vessel) ? filterData?.vessel : [],
      });
    }
  }, [editData, filterData, vesselList]);

  return (
    <Modal
      id="survey-certificate-modal"
      show={showModal}
      aria-labelledby="survey-certificate-modal"
      centered
      size="lg"
      backdrop="static"
      data-testid="fml-cash-call-report-dialog"
    >
      <Modal.Header>
        <Modal.Title>
          {!edit ? `Add Cash Call Report` : `Edit Cash Call Report`}
          <p className="required-field-text">* Required fields</p>
        </Modal.Title>
      </Modal.Header>
      {validationError && <ErrorAlert message={validationError} />}

      <CustomOverlayLoader active={loading}>
        <Form className="form-main-control">
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Vessel*</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="vessel_list"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-cash-call-report-list-vessel' }}
                    multiple={false}
                    options={vesselList || []}
                    showDropDownIcon={true}
                    disabled={edit}
                    clearOnFocus={true}
                    selected={form.vessel_list ? form.vessel_list : []}
                    onChange={(e) => setFieldValue('vessel_list', e)}
                    handleClear={() => setFieldValue('vessel_list', [])}
                  />
                  <div className="validate-error">{errors.vessel_list}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Booking Month*</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="book_month"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-cash-call-report-list-booking-month' }}
                    multiple={false}
                    options={MONTHS || []}
                    showDropDownIcon={true}
                    clearOnFocus={true}
                    selected={form.book_month ? form.book_month : []}
                    onBlur={() =>
                      !_.isEmpty(previousData.book_month) &&
                      _.isEmpty(form.book_month) &&
                      setFieldValue('book_month', previousData.book_month)
                    }
                    onChange={(e) => setFieldValue('book_month', e)}
                    handleClear={() => {
                      setFieldValue('book_month', []);
                    }}
                  />
                  <div className="validate-error">{errors.book_month}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Report Submit Date</Form.Label>
                  <CustomDatePicker
                    value={form?.report_date}
                    dataTestId={'fml-cashcall-report-list-submit-date'}
                    onChange={(e) => setFieldValue('report_date', e)}
                    disabled
                  />
                  <div className="validate-error">{errors.report_date}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">
                    Fund Request Number (between 0 - 100)*
                  </Form.Label>
                  <Form.Control
                    type="number"
                    data-testid="fml-cash-call-report-fund-request-number"
                    required
                    min={1}
                    max={100}
                    name="request_no"
                    className="form-main-control"
                    value={form.request_no}
                    onChange={(e) => setFieldValue('request_no', e.target.value)}
                  />
                  <div className="validate-error">{errors.request_no}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Fund Amount Requested*</Form.Label>
                  <Form.Control
                    type="number"
                    data-testid="fml-cash-call-report-fund-amount-requested"
                    required
                    name="amount"
                    min={0}
                    max={BigInt('9999999999999999')}
                    className="form-main-control"
                    value={form.amount}
                    onChange={(e) => setFieldValue('amount', e.target.value)}
                  />
                  <div className="validate-error">{errors.amount}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Fund Amount Currency*</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="currency"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-cash-call-report-fund-amount-currency' }}
                    multiple={false}
                    options={dropdownData?.data?.miscCurrencys || []}
                    showDropDownIcon={true}
                    clearOnFocus={true}
                    selected={form.currency ? form.currency : []}
                    onBlur={() =>
                      !_.isEmpty(previousData.currency) &&
                      _.isEmpty(form.currency) &&
                      setFieldValue('currency', previousData.currency)
                    }
                    onChange={(e) => setFieldValue('currency', e)}
                    handleClear={() => {
                      setFieldValue('currency', []);
                    }}
                  />
                  <div className="validate-error">{errors.currency}</div>
                    <small className="form-text">{warnings.currency}</small>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Document*</Form.Label>
                  <CustomFileUpload
                    form={form}
                    dataTestId={'drawings-and-manuals'}
                    setFieldValue={setFieldValue}
                    edit={edit}
                  />
                  <div className="validate-error">{errors.file}</div>
                </Form.Group>
                {edit && (
                  <Form.Label className="edit-label pt-2">
                    Last Edited by {editData?.updated_by ?? editData?.created_by} on
                    {formatDate(
                      editData?.updated_at ?? editData?.created_at,
                      'DD MMM YYYY HH:mm',
                    )}
                    {getTimeDiffWithUTC()}
                  </Form.Label>
                )}
              </Col>

              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">
                    Vessels Of The Same Owner Included In This Report
                  </Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="other_vessels"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-cash-call-report-other-vessels-list' }}
                    multiple={true}
                    options={sameOwnerVesselList || []}
                    showDropDownIcon={true}
                    clearOnFocus={false}
                    selected={form.other_vessels ? form.other_vessels : []}
                    disabled={disableSameOnwerVessel}
                    onChange={(e) => setFieldValue('other_vessels', e)}
                    handleClear={() => setFieldValue('other_vessels', [])}
                    selectAll={true}
                  />
                  <div className="validate-error">{errors.other_vessels}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Booking Year*</Form.Label>
                  <DatePicker
                    showYearPicker
                    placeholderText="Select Year"
                    dateFormat="yyyy"
                    dataTestId={'fml-cashcall-report-booking-year'}
                    allowClear={true}
                    minDate={moment().subtract(4, 'year').toDate()}
                    maxDate={moment().add(1, 'year').toDate()}
                    selected={form.book_year}
                    onChange={(e) => setFieldValue('book_year', e)}
                  />
                  <div className="validate-error">{errors.book_year}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Type of Fund*</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="fund_type"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-cash-call-report-fund-type' }}
                    multiple={false}
                    options={FUNDTYPES || []}
                    showDropDownIcon={true}
                    clearOnFocus={true}
                    selected={form.fund_type ? form.fund_type : []}
                    onBlur={() =>
                      !_.isEmpty(previousData.fund_type) &&
                      _.isEmpty(form.fund_type) &&
                      setFieldValue('fund_type', previousData.fund_type)
                    }
                    onChange={(e) => setFieldValue('fund_type', e)}
                    handleClear={() => {
                      setFieldValue('fund_type', []);
                    }}
                  />
                  <div className="validate-error">{errors.fund_type}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Reference Number</Form.Label>
                  <Form.Control
                    type="text"
                    data-testid="fml-cash-call-report-reference-number"
                    required
                    name="reference_no"
                    className="form-main-control"
                    maxLength={50}
                    value={form.reference_no}
                    onChange={(e) => setFieldValue('reference_no', e.target.value)}
                  />
                  <div className="validate-error">{errors.reference_no}</div>
                </Form.Group>

                  <Form.Group className="form-group">
                    <Form.Label className="from-label">Subject*</Form.Label>
                    <CustomTypeAhead
                      id="basic-typeahead-single"
                      labelKey="value"
                      name="subject"
                      placeholder="Please select"
                      inputProps={{ 'data-testid': 'fml-cash-call-report-subject' }}
                      multiple={false}
                      options={CASHCALLSUBJECT || []}
                      showDropDownIcon={true}
                      clearOnFocus={true}
                      selected={form.subject ? form.subject : []}
                      onBlur={() =>
                        !_.isEmpty(previousData.subject) &&
                        _.isEmpty(form.subject) &&
                        setFieldValue('subject', previousData.subject)
                      }
                      onChange={(e) => setFieldValue('subject', e)}
                      handleClear={() => {
                        setFieldValue('subject', []);
                      }}
                      disabled={form.fund_type?.[0]?.value === 'EU-ETS'}
                    />
                    <div className="validate-error">{errors.subject}</div>

                  </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Fund Receipt Date</Form.Label>
                  <CustomDatePicker
                    value={form?.receipt_date}
                    dataTestId={'fml-cashcall-report-fund-receipt-date'}
                    onChange={(e) => setFieldValue('receipt_date', e)}
                  />
                  <div className="validate-error">{errors.receipt_date}</div>
                </Form.Group>
                {edit && (
                  <Form.Group className="form-group">
                    <Form.Label className="from-label">Mark as inactive</Form.Label>
                    <Form.Check
                      type="checkbox"
                      className="basic-checkbox"
                      label="Inactive"
                      data-testid="fml-cash-call-report-inactive"
                      checked={form.inactive}
                      onChange={(e) => setFieldValue('inactive', e.currentTarget.checked)}
                    />
                  </Form.Group>
                )}
              </Col>
            </Row>
          </Modal.Body>

          <Modal.Footer>
            <Button
              variant="primary"
              data-testid="fml-cash-call-report-close"
              onClick={handleCancel}
            >
              Close
            </Button>
            <Button
              variant="secondary"
              data-testid="fml-cash-call-report-save"
              type="submit"
              disabled={disableSave}
              onClick={handleSubmit}
            >
              Save
            </Button>
          </Modal.Footer>
        </Form>
      </CustomOverlayLoader>
    </Modal>
  );
};

export default AddCashCallReportModal;
