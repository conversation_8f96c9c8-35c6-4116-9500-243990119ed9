import _ from 'lodash';
import moment from 'moment';
import React, { useContext, useEffect, useState } from 'react';
import { Button, Col, Form, Modal, Row } from 'react-bootstrap';
import { FinancialReportContext } from '../../context/FinancialReportContext';
import { MONTHS } from '../../model/constants';
import CustomDatePicker from '../../component/customComponent/CustomDatePicker';
import DatePicker from 'react-datepicker';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';
import vesselService from '../../service/vessel-service';
import { formatDate, getTimeDiffWithUTC } from '../../util/view-utils';
import ErrorAlert from '../ErrorAlert';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import { SlashCircle } from 'react-bootstrap-icons';
import { usePrevious } from '../../util/customHooks';
import CustomFileUpload from '../customComponent/CustomFileUpload';

const FinancialReportDialog = ({
  showModal = true,
  handleModalCancel = () => {},
  handleModalSubmit = () => {},
  setPageError,
  editData,
  reportType,
}) => {
  const { vesselList, financialReportTypes, ga4EventTrigger } = useContext(FinancialReportContext);
  const edit = !_.isEmpty(editData);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState({
    report_date: formatDate(new Date(), 'YYYY-MM-DD', ''),
  });
  const [errors, setErrors] = useState({});
  const [validationError, setValidationError] = useState();
  const [intialEditData, setIntialEditData] = useState();
  const previousData = usePrevious(form);

  const validateVessel = (value) => ({
    vessel_list: _.isEmpty(value) ? 'Vessel is a required field' : undefined,
  });

  const validateBookingMonth = (value) => ({
    book_month: _.isEmpty(value) ? 'Booking Month is a required field' : undefined,
  });

  const validateReportSubmitDate = (value) => ({
    report_date: _.isEmpty(value) ? 'Report submit date is a required field' : undefined,
  });

  const validateFile = (value) => {
    const requiredSize = 100;
    if (value?.size && value?.size / 1000000 > requiredSize) {
      return { file: `Cannot upload a file larger than ${requiredSize}MB` };
    }
    if (_.isEmpty(value?.name)) {
      return { file: 'File is a required field' };
    }
    if (value.type === 'application/x-msdownload') {
      return { file: 'Cannot upload exe file' };
    }
    return { file: undefined };
  };

  const validateReportName = (value) => ({
    report_type_id: _.isEmpty(value) ? 'Name of report is a required field' : undefined,
  });

  const validateBookingYear = (value) => {
    return {
      book_year: _.isEmpty(value?.toString()) ? 'Booking year is a required field' : undefined,
    };
  };

  const validateRemark = (value) => {
    return {
      remark:
        value?.length > 80
          ? 'Remark should be within predefined limit of 80 characters.'
          : undefined,
    };
  };

  const intialValidateError = () => ({
    ..._.omitBy(errors, (v) => v == null),
    ...validateVessel(form?.vessel_list),
    ...validateBookingMonth(form?.book_month),
    ...validateReportSubmitDate(form?.report_date),
    ...validateFile(form?.file),
    ...validateReportName(form?.report_type_id),
    ...validateBookingYear(form?.book_year?.toString()),
  });

  const validateForm = (field, value) => {
    let newErrors = {
      ...errors,
    };
    switch (field) {
      case 'vessel_list':
        newErrors = { ...newErrors, ...validateVessel(value) };
        break;

      case 'book_month':
        newErrors = { ...newErrors, ...validateBookingMonth(value) };
        break;

      case 'report_date':
        newErrors = { ...newErrors, ...validateReportSubmitDate(value) };
        break;

      case 'file':
        newErrors = { ...newErrors, ...validateFile(value) };
        break;

      case 'report_type_id':
        newErrors = { ...newErrors, ...validateReportName(value) };
        break;

      case 'book_year':
        newErrors = { ...newErrors, ...validateBookingYear(value) };
        break;

      case 'remark':
        newErrors = { ...newErrors, ...validateRemark(value) };
        break;

      case 'inactive':
        newErrors = value ? {} : { ...newErrors };
        break;
    }
    newErrors = _.pickBy(newErrors, (i) => i !== undefined);
    setErrors(newErrors);
  };

  const setFieldValue = (field, value) => {
    validateForm(field, value);
    setForm({
      ...form,
      [field]: value,
    });
  };

  const uploadFile = async (data) => {
    let url = '';
    let preSignedUrl = '';
    try {
      const response = await vesselService.getPreSignedUploadLink({
        record_type: 'financial',
        payload: data,
        action: edit ? 'replace' : 'upload',
        file_name: form.file.name,
      });
      url = response.data.url;
      preSignedUrl = response.data.pre_signed_link;
    } catch (e) {
      setValidationError(`${e?.response.data}. Please try again.`);
      setLoading(false);
    }

    if (url) {
      try {
        await vesselService.uploadPresignedDocument(preSignedUrl, form.file, form.file.type);
      } catch (e) {
        url = '';
        setValidationError(`${e?.response.data}. Please try again.`);
        setLoading(false);
      }
    }
    return url;
  };

  const handleAfterSubmit = () => {
    handleModalSubmit();
    handleCancel();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    let errorList = form.inactive ? {} : { ...intialValidateError() };
    errorList = _.pickBy(errorList, (i) => i !== undefined);

    if (_.isEmpty(errorList)) {
      setLoading(true);
      let submitFormData = {
        vessel_ownership_id: form.vessel_list[0].id,
        book_month: form.book_month[0].id,
        report_date: moment(form.report_date).format('YYYY-MM-DD'),
        report_type_id: form.report_type_id[0].id,
        book_year: Number(moment(form.book_year).format('YYYY')),
        remark: form.remark,
        inactive: form.inactive,
      };

      try {
        if (edit) {
          ga4EventTrigger(
            'Confirm Edit Financial Report',
            `${reportType === 'accounts' ? 'Financial' : 'Owner'} Report - Menu`,
            editData.vessel_name,
          );
          submitFormData.file = form.file;
          submitFormData = form.inactive
            ? { inactive: true }
            : _.fromPairs(
                _.differenceWith(_.toPairs(submitFormData), _.toPairs(intialEditData), _.isEqual),
              );
          submitFormData.id = editData.id;
          if (submitFormData?.file) {
            submitFormData = _.omit(submitFormData, ['file']);
            submitFormData.url = await uploadFile(submitFormData);
            if (submitFormData?.url) {
              await vesselService.editFinancialReport(reportType, submitFormData);
              handleAfterSubmit();
            }
          } else {
            await vesselService.editFinancialReport(reportType, submitFormData);
            handleAfterSubmit();
          }
        } else {
          ga4EventTrigger(
            'Confirm Add Financial Report',
            `${reportType === 'accounts' ? 'Financial' : 'Owner'} Report - Menu`,
            form.vessel_list[0].value,
          );
          submitFormData.url = await uploadFile(submitFormData);
          if (submitFormData?.url) {
            await vesselService.addFinancialReport(reportType, submitFormData);
            handleAfterSubmit();
          }
        }
      } catch (error) {
        if (error?.response?.status === 400) {
          setValidationError(`${error?.response.data}. Please try again.`);
          setLoading(false);
        } else {
          handleCancel();
          setPageError(
            'Something went wrong with updating finanacial report list. Please try later',
          );
        }
      }
    } else {
      setErrors(errorList);
    }
  };

  const handleCancel = () => {
    setForm({ report_date: formatDate(new Date(), 'YYYY-MM-DD', '') });
    setErrors({});
    handleModalCancel();
    setLoading(false);
  };

  const getTitleReportType = () => {
    const map = {
      accounts: 'Financial Report',
      technical: 'Owner Report',
    };
    return map[reportType] || 'Financial Report';
  };

  useEffect(() => {
    if (edit && editData) {
      const formData = {
        vessel_list: [{ id: editData.vessel_ownership_id, value: editData.vessel_name }],
        book_month: [MONTHS?.find((i) => i.id === editData.book_month)],
        report_date: moment(editData.report_date).format('YYYY-MM-DD'),
        file: { name: editData.url },
        report_type_id: [financialReportTypes?.find((i) => i.value === editData.report_type)],
        book_year: moment(editData.book_month_year).toDate(),
        remark: editData?.remark,
        inactive: editData?.inactive ?? false,
      };

      const intialData = {
        vessel_ownership_id: editData.vessel_ownership_id,
        book_month: editData.book_month,
        report_date: moment(editData.report_date).format('YYYY-MM-DD'),
        file: { name: editData.url },
        report_type_id: financialReportTypes?.find((i) => i.value === editData.report_type)?.id,
        book_year: editData.book_year,
        remark: editData?.remark,
        inactive: editData?.inactive ?? false,
      };
      setForm(formData);
      setIntialEditData(intialData);
    }
  }, [editData]);

  return (
    <Modal
      id="financial-report-modal"
      show={showModal}
      aria-labelledby="financial-report-modal"
      centered
      size="lg"
      backdrop="static"
      data-testid="fml-financial-report-dialog"
    >
      <Modal.Header>
        <Modal.Title>
          {!edit ? `Add ${getTitleReportType()}` : `Edit ${getTitleReportType()}`}
          <p className="required-field-text">* Required fields</p>
        </Modal.Title>
      </Modal.Header>
      {validationError && <ErrorAlert message={validationError} />}

      <CustomOverlayLoader active={loading}>
        <Form className="form-main-control">
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Vessel*</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="vessel_list"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-financial-report-list-vessel' }}
                    multiple={false}
                    options={vesselList}
                    showDropDownIcon={true}
                    disabled={edit}
                    clearOnFocus={true}
                    selected={form.vessel_list ? form.vessel_list : []}
                    onChange={(e) => setFieldValue('vessel_list', e)}
                    handleClear={() => setFieldValue('vessel_list', [])}
                  />
                  <div className="validate-error">{errors.vessel_list}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Booking Month*</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="book_month"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-financial-report-list-booking-month' }}
                    multiple={false}
                    options={MONTHS}
                    showDropDownIcon={true}
                    clearOnFocus={true}
                    selected={form.book_month ? form.book_month : []}
                    disabled={form.inactive}
                    onBlur={() =>
                      !_.isEmpty(previousData.book_month) &&
                      _.isEmpty(form.book_month) &&
                      setFieldValue('book_month', previousData.book_month)
                    }
                    onChange={(e) => setFieldValue('book_month', e)}
                    handleClear={() => {
                      setFieldValue('book_month', []);
                    }}
                  />
                  <div className="validate-error">{errors.book_month}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Report Submit Date*</Form.Label>
                  <CustomDatePicker
                    value={form?.report_date}
                    dataTestId={'fml-financial-report-list-submit-date'}
                    onChange={(e) => setFieldValue('report_date', e)}
                    disabled
                  />
                  <div className="validate-error">{errors.report_date}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Document*</Form.Label>
                  <CustomFileUpload
                    form={form}
                    dataTestId={'financial-report-list'}
                    setFieldValue={setFieldValue}
                  />
                  <div className="validate-error">{errors.file}</div>
                </Form.Group>
                {edit && (
                  <Form.Label className="edit-label pt-2">
                    Last Edited by {editData?.updated_by ?? editData?.created_by} on
                    {formatDate(
                      editData?.updated_at ?? editData?.created_at,
                      'DD MMM YYYY HH:mm',
                    )}
                    {getTimeDiffWithUTC()}
                  </Form.Label>
                )}
              </Col>

              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Name of Report*</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="report_type_id"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-financial-report-list-reports' }}
                    multiple={false}
                    options={financialReportTypes.filter((rec) => rec.type === reportType)}
                    showDropDownIcon={true}
                    clearOnFocus={true}
                    selected={form.report_type_id ? form.report_type_id : []}
                    disabled={form.inactive}
                    onBlur={() =>
                      !_.isEmpty(previousData.report_type_id) &&
                      _.isEmpty(form.report_type_id) &&
                      setFieldValue('report_type_id', previousData.report_type_id)
                    }
                    onChange={(e) => setFieldValue('report_type_id', e)}
                    handleClear={() => {
                      setFieldValue('report_type_id', []);
                    }}
                  />
                  <div className="validate-error">{errors.report_type_id}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Booking Year*</Form.Label>
                  <DatePicker
                    showYearPicker
                    placeholderText="Select Year"
                    dateFormat="yyyy"
                    allowClear={true}
                    selected={form.book_year}
                    disabled={form.inactive}
                    minDate={moment().subtract(4, 'year').toDate()}
                    maxDate={moment().add(1, 'year').toDate()}
                    onChange={(e) => setFieldValue('book_year', e)}
                  />
                  <div className="validate-error">{errors.book_year}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Remarks</Form.Label>
                  <Form.Control
                    as="textarea"
                    maxLength="255"
                    name="remark"
                    value={form?.remark}
                    data-testid="fml-financial-report-list-remark"
                    disabled={form.inactive}
                    onChange={(e) => {
                      setFieldValue(
                        'remark',
                        !_.isEmpty(e.target.value.trim()) ? e.target.value : '',
                      );
                    }}
                  />
                  <div className="validate-error">{errors.remark}</div>
                </Form.Group>

                {edit && (
                  <Form.Group className="form-group">
                    <Col>
                      <Row>
                        <Form.Label className="from-label">Mark as inactive</Form.Label>
                      </Row>
                      <Row className="align-items-center">
                        <SlashCircle
                          color={form.inactive ? 'red' : 'grey'}
                          data-testid="fml-financial-report-list-inactive"
                          size={20}
                          onClick={(event) => {
                            setFieldValue('inactive', !form.inactive);
                          }}
                          name="inactive"
                        />
                        <p className="pl-1 m-0">Inactive</p>
                      </Row>
                    </Col>
                  </Form.Group>
                )}
              </Col>
            </Row>
          </Modal.Body>

          <Modal.Footer>
            <Button
              variant="primary"
              data-testid="fml-financial-report-list-close"
              onClick={handleCancel}
            >
              Close
            </Button>
            <Button
              variant="secondary"
              data-testid="fml-financial-report-list-save"
              type="submit"
              onClick={handleSubmit}
            >
              Save
            </Button>
          </Modal.Footer>
        </Form>
      </CustomOverlayLoader>
    </Modal>
  );
};

export default FinancialReportDialog;
