import React, { useContext } from 'react';
import { Form, Row } from 'react-bootstrap';
import { FinancialReportContext } from '../../context/FinancialReportContext';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';

const FinancialReportFilter = ({ loading, activeTab = '', filterData, setFilterData }) => {
  const { vesselList, financialReportTypes, ga4EventTrigger } = useContext(FinancialReportContext);

  return (
    <div className="no-print">
      <Row className="form-row">
        <Form.Label className="filter-reports">
          <b>Filter Reports</b>
        </Form.Label>
      </Row>
      <Row className="form-row">
        <Form.Group className="reports-filter-textField form-group">
          <Form.Control type="text" placeholder="Vessel Name" disabled />
        </Form.Group>
        <Form.Group className={`vessel-dropdown ${loading && 'disabled'} form-group`}>
          <CustomTypeAhead
            id="basic-typeahead-single"
            labelKey="value"
            name="report_list"
            placeholder="All Vessels"
            inputProps={{ 'data-testid': 'fml-financial-report-filter-vessel' }}
            multiple={false}
            options={vesselList}
            selected={filterData.vessel}
            showDropDownIcon={false}
            clearOnFocus={true}
            onChange={(e) => {
              setFilterData({ ...filterData, vessel: e });
            }}
            handleClear={() => {
              setFilterData({ ...filterData, vessel: [] });
            }}
          />
        </Form.Group>

        {['accounts', 'technical'].includes(activeTab) && (
          <>
            <Form.Group className="reports-filter-textField form-group">
              <Form.Control type="text" placeholder="Name of Report" disabled />
            </Form.Group>
            <Form.Group className={`vessel-dropdown ${loading && 'disabled'} form-group`}>
              <CustomTypeAhead
                id="basic-typeahead-single"
                labelKey="value"
                name="report_list"
                placeholder="All Reports"
                inputProps={{ 'data-testid': 'fml-financial-report-filter-report-type' }}
                multiple={false}
                options={financialReportTypes.filter((item) => item.type === activeTab)}
                selected={filterData.reportType}
                showDropDownIcon={false}
                clearOnFocus={true}
                onChange={(e) => {
                  ga4EventTrigger(
                    'Name of Report Filter',
                    `${activeTab === 'accounts' ? 'Financial' : 'Owner'} Report - Filter`,
                    e[0].value,
                  );
                  setFilterData({ ...filterData, reportType: e });
                }}
                handleClear={() => {
                  setFilterData({ ...filterData, reportType: [] });
                }}
              />
            </Form.Group>
          </>
        )}
      </Row>
    </div>
  );
};

export default FinancialReportFilter;
