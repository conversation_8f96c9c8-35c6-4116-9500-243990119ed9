import React from 'react';
import { Button, Modal } from 'react-bootstrap';

const ListViewModal = ({ showModal, handleModalClose = () => {}, data }) => {
  return (
    <Modal
      id="list-view-modal"
      show={showModal}
      aria-labelledby="list-view-modal"
      centered
      size="sm"
      backdrop="static"
    >
      <Modal.Header>
        <Modal.Title>
          <p>List of Emails</p>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {data?.map((item) => {
          return <p>{item}</p>;
        })}
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          data-testid="fml-recipient-list-view-modal-close"
          onClick={handleModalClose}
        >
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ListViewModal;
