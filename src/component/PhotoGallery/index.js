import React, { useEffect, useState } from 'react';
import { each } from 'lodash';
import { Carousel } from 'react-bootstrap';
import NoPhoto from '../../../public/icons/no-photo.svg';
import vesselService from '../../service/vessel-service';
import { Icon } from '../../styleGuide';
import PropTypes from 'prop-types';

const PhotoGallery = ({ data, vesselStatus, onError }) => {
  const [photoUrlMap, setPhotoUrlMap] = useState({});

  useEffect(() => {
    (async () => {
      try {
        if (data?.length > 0) {
          const photoIds = data.map((p) => `${p.path}`);
          const photoUrlRes = await vesselService.requestVesselDownloadUrls(photoIds);
          const promises = [];
          each(photoUrlRes.data, (value, key) =>
            promises.push(vesselService.getPresignedDocument(value)),
          );
          if (promises.length > 0) {
            const response = await Promise.allSettled(promises);
            let photoMap = {};
            photoIds.map((id, index) => {
              if (response[index].status === 'fulfilled') {
                photoMap = {
                  ...photoMap,
                  [id]: {
                    url: window.URL.createObjectURL(
                      new Blob([response[index].value.data], {
                        type: response[index].value.headers['content-type'],
                      }),
                    ),
                    is_available: true,
                  },
                };
              } else {
                photoMap = { ...photoMap, [id]: { url: NoPhoto, is_available: false } };
              }
            });
            setPhotoUrlMap(photoMap);
          }
        }
      } catch (error) {
        onError?.(error.response);
        console.error(`Get vessel photos by ID: ${photoUrlMap} failed. Error: ${error}`);
      }
    })();
  }, []);

  return (
    <Carousel interval={null}>
      {data?.length > 0 ? (
        data
          .slice()
          .sort((a, b) => a.order - b.order)
          .map((p, pIdx) => (
            <Carousel.Item key={pIdx}>
              {vesselStatus === 'draft' && (
                <div className="carousel-wrapper">
                  <Icon icon="vessel-draft" className="vessel_image_draft" />
                </div>
              )}
              <img
                className={`d-block ${
                  photoUrlMap[`${p.path}`]?.is_available ? 'w-100' : 'mx-auto'
                } rounded`}
                height="280"
                style={{ objectFit: 'cover' }}
                src={photoUrlMap[`${p.path}`]?.url}
                alt="vessel-photo"
              />
            </Carousel.Item>
          ))
      ) : (
        <Carousel.Item>
          <div
            className="carousel-wrapper rounded"
            style={{ background: '#F8F9FA', width: '100%', height: 280 }}
          >
            {vesselStatus === 'draft' ? (
              <Icon icon="vessel-draft" size={100} className="vessel_image" />
            ) : (
              <Icon icon="photo" size={100} className="vessel_image" />
            )}
          </div>
        </Carousel.Item>
      )}
    </Carousel>
  );
};

PhotoGallery.propTypes = {
  data: PropTypes.array,
  vesselStatus: PropTypes.string,
  onError: PropTypes.func,
};

export default PhotoGallery;
