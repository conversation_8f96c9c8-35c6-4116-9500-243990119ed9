import React, { useState, useEffect } from 'react';
import { Container, Form, Row, Col, Button } from 'react-bootstrap';
import { useHistory, useLocation } from 'react-router-dom';
import SearchController from '../../controller/search-controller';
import DropDownSearchMenu from './DropDownSearchMenu.js';
import ClearAllModalView from './ClearAllModalView.js';
import SubtypeField from './SubtypeField.js';
import { mapVesselQueryToSearchCriteria } from '../../util/search-query';
import InputMissingModal from './InputMissingModal';
import styleGuide from '../../styleGuide';
import { resetAllTabs } from '../../util/local-storage-helper';
import Spinner from '../Spinner';
import { findSubtype } from '../../util/view-utils';
import PropTypes from 'prop-types';

const { Icon } = styleGuide;

const Result = (props) => {
  const defaultDropDownValue = {
    emailTypes: [],
    flags: [],
    hmUnderwriters: [],
    owners: [],
    phoneTypes: [],
    piClubs: [],
    ihmProviders: [],
    portOfRegistrys: [],
    vesselClasss: [],
    vesselServiceStatuss: [],
    vesselTypes: [],
  };

  const controller = new SearchController();
  const history = useHistory();

  const [filters, setFilters] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dropDownData, setDropDownData] = useState(defaultDropDownValue);
  const [modalShow, setModalShow] = React.useState(false);
  const [inputMissingModalShow, setInputMissingModalShow] = useState(false);

  const query = useLocation().search.substring(1);

  const addFilter = () => {
    if (filters.length === 0 || filters[filters.length - 1].type === '') return;
    let newFilter = { type: '' };
    setFilters((oldArray) => [...oldArray, newFilter]);
  };

  const handleClearFilters = () => {
    setFilters([]);
    clearFields();
    setModalShow(false);
  };

  const removeEmptyFilters = (filter) => {
    return filter.filter(
      (item) => item.subtype !== null && item.subtype !== undefined && item.subtype !== '',
    );
  };

  const handleSearchResult = () => {
    if (filters.length < 1) {
      setInputMissingModalShow(true);
    } else {
      let filteredList = removeEmptyFilters(filters);
      if (filteredList.length === 0) {
        window.history.replaceState({}, null, `${history.location.pathname}`);
        return;
      }
      const query = controller.getVesselQuery(filters);
      //reset Local Storage page nos for all tabs and store query
      resetAllTabs();
      history.push(`/vessel/active-vessels?${query}`);
    }
  };

  const handleClose = () => {
    resetAllTabs();
    history.push(`/vessel/active-vessels`);
  };

  const onRemoveItem = (index) => {
    let rowId = index;
    const array = [...filters];
    if (rowId > -1) {
      array.splice(rowId, 1);
    }
    setFilters(array);
  };

  const onFilterDateFieldChange = (value, index) => {
    let filtersState = [...filters];
    filtersState[index] = { type: filtersState[index].type, subtype: value };
    setFilters([...filtersState]);
  };

  const onFilterTypeChange = (event, index) => {
    let element = event[0];
    let filtersState = [...filters];
    if (element === undefined) {
      filtersState[index] = { type: '', subtype: '' };
      setFilters([...filtersState]);
      return;
    }
    let type = controller.getType(element.type);
    if (index === null || index === undefined) {
      let newFilter = { type, subtype: '' };
      setFilters([newFilter]);
      return;
    }
    if (type) {
      filtersState[index] = { type, subtype: '' };
      setFilters([...filtersState]);
    }
  };

  const onFilterSubtypeChange = (value, index, customValue) => {
    let filtersState = [...filters];
    let subtype = value;
    if (!value) {
      filtersState[index] = { type: filtersState[index].type, subtype: '' };
      setFilters([...filtersState]);
      return;
    }

    if (filtersState[index].type.inputType === 'dropdown') {
      subtype = findSubtype(dropDownData[filtersState[index].type.type], value);
    }

    if (filtersState[index].type.inputType === 'number_range') {
      subtype = customValue;
    }

    filtersState[index] = { type: filtersState[index].type, subtype };
    setFilters([...filtersState]);
  };
  const FilterRow = (props) => {
    return props.filters.map((filter, index) => (
      <Row key={index} className="advanced_search__filter-row">
        <Form.Group className="form-group" as={Col} xs="12" md="5">
          <DropDownSearchMenu
            defaultSelected={[filter.type]}
            placeholder="Select Category"
            onChange={(e) => onFilterTypeChange(e, index)}
          />
        </Form.Group>

        {filter.type && (
          <>
            <Form.Group className="form-group" as={Col} xs={10} md={5}>
              {SubtypeField({
                type: filter.type,
                subtype: filter.subtype,
                disabled: false,
                onSubtypeChange: (e, customValue) =>
                  onFilterSubtypeChange(e.target.value, index, customValue),
                onDateFieldChange: (value) => onFilterDateFieldChange(value, index),
                dropDownData: dropDownData,
                title: false,
                onYearFieldChange: (value) => onFilterDateFieldChange(value, index),
                min: filter.subtype.min ?? '',
                max: filter.subtype.max ?? '',
              })}
            </Form.Group>

            <Form.Group className="form-group" as={Col} xs={2} md={1}>
              <Icon
                icon="remove"
                size={30}
                className="remove"
                onClick={onRemoveItem.bind(this, index)}
              />
            </Form.Group>
          </>
        )}
      </Row>
    ));
  };

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const { dropDownData } = await controller.onLoadPage();
        setDropDownData(dropDownData);
        setFilters(mapVesselQueryToSearchCriteria(query, dropDownData));
      } catch (error) {
        console.error(error);
      }
      setLoading(false);
    })();
  }, []);

  return (
    <>
      {loading ? (
        <Spinner alignClass={`load-spinner`} />
      ) : (
        <div className="advanced_search">
          <Container>
            <Row>
              <Col>
                <h5 className="advanced_search__title">
                  Advanced Search{' '}
                  {removeEmptyFilters(filters).length
                    ? `(${removeEmptyFilters(filters).length})`
                    : ''}
                  <Icon icon="close" size={30} onClick={handleClose} />
                </h5>
                <hr className="line1px" />
              </Col>
            </Row>

            <Form>
              <Row>
                <Form.Label className="category">
                  <b>Category</b>
                </Form.Label>
              </Row>

              {FilterRow({ filters: filters })}

              {filters.length === 0 && (
                <Row>
                  <Form.Group className="form-group" as={Col} md="5">
                    <DropDownSearchMenu
                      onChange={(e) => onFilterTypeChange(e)}
                      placeholder="Select Category"
                    />
                  </Form.Group>
                </Row>
              )}

              <Button
                variant="outline-primary"
                className="w-100 advanced_search__add-button"
                size="sm"
                onClick={addFilter}
              >
                Add
              </Button>

              <Button
                variant="primary"
                className="w-100 advanced_search__show-results-button"
                size="sm"
                onClick={handleSearchResult}
              >
                Show Results
              </Button>
            </Form>
            <InputMissingModal show={inputMissingModalShow} setShow={setInputMissingModalShow} />
          </Container>

          <ClearAllModalView
            show={modalShow}
            onClose={() => setModalShow(false)}
            onConfirm={handleClearFilters}
          />
        </div>
      )}
    </>
  );
};

Result.propTypes = {
  filters: PropTypes.object,
};

export default Result;
