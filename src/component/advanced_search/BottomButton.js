import React from 'react';
import { Button } from 'react-bootstrap';
import PropTypes from 'prop-types';

const BottomButton = (props) => {
  return (
    <div className="fixed-bottom bottom-component">
      <Button
        data-testid="fml-bottomButton-save"
        variant="secondary"
        className="bottom-component__button"
        onClick={props.onClick}
        disabled={props.disabled}
      >
        {props.title}
      </Button>
    </div>
  );
};

BottomButton.propTypes = {
  onClick: PropTypes.func,
  title: PropTypes.string,
  disabled: PropTypes.bool,
};

export default BottomButton;
