import { groupBy } from 'lodash';
import React, { Fragment } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>u, MenuItem, Typeahead } from 'react-bootstrap-typeahead';
import SearchController from '../../controller/search-controller';
import PropTypes from 'prop-types';

class DropDownSearchMenu extends React.Component {
  render() {
    const props = {};
    props.renderMenu = this._renderMenu;
    props.onChange = this.props.onChange;
    const placeholder = this.props.placeholder ? this.props.placeholder : 'Please Select';
    if (this.props.defaultSelected) props.defaultSelected = this.props.defaultSelected;
    const controller = new SearchController();
    let allSearchFilters = controller.getAllFilters();
    if (!this.props.roleConfig?.eorb?.view) {
      allSearchFilters = allSearchFilters.filter((filter) => filter.type !== 'eorbStatuses');
    }
    const searchTypes = allSearchFilters;
    const ref = React.createRef();

    const handleChange = (event) => {
      props.onChange(event, ref.current);
    };

    return (
      <Typeahead
        ref={ref}
        {...props}
        labelKey="name"
        id="search-type-menu"
        onChange={handleChange}
        options={searchTypes}
        placeholder={placeholder}
      />
    );
  }

  _renderMenu = (results, menuProps, state) => {
    let index = 0;
    const sections = groupBy(results, 'section');
    const items = Object.keys(sections).map((section) => (
      <Fragment key={section}>
        {index !== 0 && <Menu.Divider />}
        <Menu.Header>{section}</Menu.Header>
        {sections[section].map((i) => {
          const item = (
            <MenuItem key={index} option={i} position={index}>
              <Highlighter search={state.text}>{i.name}</Highlighter>
            </MenuItem>
          );

          index += 1;
          return item;
        })}
      </Fragment>
    ));

    return <Menu {...menuProps}>{items}</Menu>;
  };
}

DropDownSearchMenu.propTypes = {
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  defaultSelected: PropTypes.string,
  roleConfig: PropTypes.object,
};

export default DropDownSearchMenu;
