import React from 'react';
import { Form, Row, Col } from 'react-bootstrap';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import TakeoverDropDownSearchControl from '../takeover/TakeoverDropDownSearchControl.js';
import PropTypes from 'prop-types';

const SubtypeField = (props) => {
  let type = props.type.inputType;
  let defaultField = (
    <SubtypeFieldText
      isFilterRow={true}
      subtype={''}
      disabled={props.disabled !== null ? props.disabled : true}
      title={props.title !== null ? props.title : true}
    />
  );

  if (type === 'dropdown') {
    return <SubtypeFieldDropDown {...props} />;
  }

  if (type === 'text') {
    return <SubtypeFieldText {...props} />;
  }

  if (type === 'date') {
    return <SubtypeFieldDate {...props} />;
  }

  if (type === 'year') {
    return <SubtypeFieldYear {...props} />;
  }

  if (type === 'number_range') {
    return <SubtypeFieldNumberRange {...props} />;
  }

  if (type === 'number') {
    return <SubtypeFieldNumber {...props} />;
  }

  return defaultField;
};

SubtypeField.propTypes = {
  type: PropTypes.object,
  disabled: PropTypes.bool,
  title: PropTypes.string,
};

const SubtypeFieldText = (props) => {
  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <Form.Control
        className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
        disabled={props.disabled}
        onChange={props.onSubtypeChange}
        value={props.subtype}
      />
    </Form.Group>
  );
};

SubtypeFieldText.propTypes = {
  isFilterRow: PropTypes.bool,
  disabled: PropTypes.bool,
  title: PropTypes.string,
  onSubtypeChange: PropTypes.func,
  subtype: PropTypes.string,
};

const SubtypeFieldNumber = (props) => {
  let limit = 12;
  if (props.type.type === 'imo_number') {
    limit = 7;
  }
  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <Form.Control
        className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
        type="number"
        onInput={(e) => {
          e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, limit);
        }}
        disabled={props.disabled}
        onChange={props.onSubtypeChange}
        value={props.subtype}
      />
    </Form.Group>
  );
};

SubtypeFieldNumber.propTypes = {
  type: PropTypes.object,
  isFilterRow: PropTypes.bool,
  disabled: PropTypes.bool,
  title: PropTypes.string,
  onSubtypeChange: PropTypes.func,
  subtype: PropTypes.string,
};

const SubtypeFieldNumberRange = (props) => {
  let labelClass = props.disabled ? 'label-to-in-row' : 'label-to-on-top';

  let minValue = props.min;
  let maxValue = props.max;

  if (props.isFilterRow) {
    minValue = props.subtype.min;
    maxValue = props.subtype.max;
  }

  const onMinNumberChange = (event) => {
    let value = event.target.value;
    props.onSubtypeChange(event, { min: value, max: maxValue });
  };

  onMinNumberChange.propTypes = {
    onSubtypeChange: PropTypes.func,
  };

  const onMaxNumberChange = (event) => {
    let value = event.target.value;
    props.onSubtypeChange(event, { min: minValue, max: value });
  };

  onMaxNumberChange.propTypes = {
    onSubtypeChange: PropTypes.func,
  };

  const limit = 12;

  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      <Row>
        <Form.Group as={Col} className="mb-0 form-group">
          {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
          <Form.Control
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            type="number"
            onInput={(e) => {
              e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, limit);
            }}
            disabled={props.disabled}
            onChange={onMinNumberChange}
            value={minValue}
          />
        </Form.Group>{' '}
        <div className={labelClass}>to</div>
        <Form.Group as={Col} className="mb-0 form-group">
          {props.title ? <Form.Label className="hidden-label">.</Form.Label> : null}
          <Form.Control
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            type="number"
            onInput={(e) => {
              e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, limit);
            }}
            disabled={props.disabled}
            onChange={onMaxNumberChange}
            value={maxValue}
          />
        </Form.Group>
      </Row>
    </Form.Group>
  );
};

SubtypeFieldNumberRange.propTypes = {
  isFilterRow: PropTypes.bool,
  disabled: PropTypes.bool,
  title: PropTypes.string,
  onSubtypeChange: PropTypes.func,
  subtype: PropTypes.string,
  min: PropTypes.number,
  max: PropTypes.number,
};

const SubtypeFieldYear = (props) => {
  const date = getDate(props.subtype);

  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <DatePicker
        className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
        disabled={props.disabled}
        selected={date}
        onChange={props.onYearFieldChange}
        showYearPicker
        placeholderText="Please select"
        dateFormat="yyyy"
      />
    </Form.Group>
  );
};

SubtypeFieldYear.propTypes = {
  subtype: PropTypes.string,
  isFilterRow: PropTypes.bool,
  disabled: PropTypes.bool,
  title: PropTypes.string,
  onYearFieldChange: PropTypes.func,
};

function getDate(value) {
  if ((typeof value === 'string' || value instanceof String) && value != '') {
    return moment(value).toDate();
  } else {
    return value;
  }
}

const SubtypeFieldDate = (props) => {
  const { subtype = {} } = props;
  const { startDate = null, endDate = null } = subtype;

  const onDatesChange = (type, date) => {
    let value = { startDate: startDate, endDate: endDate };

    if (type === 'startDate') {
      value = { startDate: date, endDate: endDate };
    }

    if (type === 'endDate') {
      value = { startDate: startDate, endDate: date };
    }

    props.onDateFieldChange(value);
  };

  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <Row className="m-auto">
        <span className="align-self-center pr-1">From</span>
        <Form.Group as={Col} className="pl-0 mb-0 form-group">
          <DatePicker
            selectsStart
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            disabled={props.disabled}
            selected={startDate}
            startDate={startDate}
            endDate={endDate}
            onChange={onDatesChange.bind(this, 'startDate')}
            dateFormat="d MMM yyyy"
          />
        </Form.Group>{' '}
        <span className="align-self-center pr-1">To</span>
        <Form.Group as={Col} className="px-0 mb-0 form-group">
          <DatePicker
            selectsEnd
            disabled={props.disabled}
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            selected={endDate}
            onChange={onDatesChange.bind(this, 'endDate')}
            startDate={startDate}
            endDate={endDate}
            minDate={startDate}
            dateFormat="d MMM yyyy"
          />
        </Form.Group>
      </Row>
    </Form.Group>
  );
};

SubtypeFieldDate.propTypes = {
  isFilterRow: PropTypes.bool,
  disabled: PropTypes.bool,
  title: PropTypes.string,
  onDateFieldChange: PropTypes.func,
  subtype: PropTypes.object,
};

const SubtypeFieldDropDown = (props) => {
  const type = props.type.type;
  const data = props.dropDownData[type] ?? [];
  let selectedValue = props.subtype ? props.subtype.id : '';
  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <TakeoverDropDownSearchControl
        name={type}
        selectedValue={selectedValue}
        dropDownValues={data}
        onInputChange={props.onSubtypeChange}
        disabled={props.disabled}
      />
    </Form.Group>
  );
};

SubtypeFieldDropDown.propTypes = {
  dropDownData: PropTypes.object,
  subtype: PropTypes.object,
  title: PropTypes.string,
  onSubtypeChange: PropTypes.func,
  type: PropTypes.object,
  disabled: PropTypes.bool,
};

export default SubtypeField;
