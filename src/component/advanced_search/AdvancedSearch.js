import React, { useState, useEffect } from 'react';
import { Container, Form, Col, Button, Row } from 'react-bootstrap';
import { toString } from 'lodash';
import moment from 'moment';
import SearchController from '../../controller/search-controller';
import DropDownSearchMenu from './DropDownSearchMenu.js';
import SubtypeField from './SubtypeField.js';
import styleGuide from '../../styleGuide';
import { resetAllTabs } from '../../util/local-storage-helper';
import { useHistory } from 'react-router-dom';
import { findSubtype } from '../../util/view-utils';
import Spinner from '../../component/Spinner';
import { fleetStaffTypes } from '../../util/search-query';
import PropTypes from 'prop-types';

const { Icon } = styleGuide;

const Result = ({ setFilters, filters, eventTracker, roleConfig }) => {
  const defaultDropDownValue = {
    nationalities: [],
  };

  const history = useHistory();
  const controller = new SearchController();
  const [dropDownList, setDropDownList] = useState(defaultDropDownValue);
  const [isLoading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const addFilter = () => {
    if (filters.length === 0 || filters[filters.length - 1].type === '') return;
    let newFilter = { type: '' };
    setFilters((oldArray) => [...oldArray, newFilter]);
  };

  const onRemoveItem = (index) => {
    let rowId = index;
    const array = [...filters];
    if (rowId > -1) {
      array.splice(rowId, 1);
    }
    setFilters(array);
    if (array.length === 0) {
      resetAllTabs();
      window.history.pushState({}, null, `${history.location.pathname}`);
    }
  };

  const onFilterDateFieldChange = (value, index) => {
    let filtersState = [...filters];
    eventTracker(
      'filterSubTypeChange',
      `${toString(filtersState[index]?.type?.name)} - startDate: ${toString(
        value?.startDate ? moment(value?.startDate).format('DD MMM yyyy') : '',
      )} endDate: ${toString(value?.endDate ? moment(value?.endDate).format('DD MMM yyyy') : '')}`,
    );
    filtersState[index] = { type: filtersState[index].type, subtype: value };
    setFilters([...filtersState]);
  };

  const onFilterTypeChange = async (event, index) => {
    let element = event[0];
    let filtersState = [...filters];
    if (element === undefined) {
      filtersState[index] = { type: '', subtype: '' };
      setFilters([...filtersState]);
      return;
    }
    eventTracker('filterTypeChange', element?.name);
    let type = controller.getType(element.type);

    if (type && [...Object.keys(fleetStaffTypes), 'tech_group'].includes(type.type)) {
      // Load staff data for selected staffType
      setError('');
      setLoading(true);
      const staffList = await controller.loadStaffTypesData(type.type, setError);
      setDropDownList({ ...dropDownList, [element.type]: staffList });
      setLoading(false);
    }

    if (index === null || index === undefined) {
      let newFilter = { type, subtype: '' };
      setFilters([newFilter]);
      return;
    }
    if (type) {
      filtersState[index] = { type, subtype: '' };
      setFilters([...filtersState]);
    }
  };

  const onFilterSubtypeChange = (value, index, customValue) => {
    let filtersState = [...filters];
    let subtype = value;
    if (!value) {
      filtersState[index] = { type: filtersState[index].type, subtype: '' };
      setFilters([...filtersState]);
      return;
    }

    if (filtersState[index].type.inputType === 'dropdown') {
      subtype = findSubtype(
        customValue?.length ? customValue : dropDownList[filtersState[index].type.type],
        value,
      );
    }

    if (filtersState[index].type.inputType === 'number_range') {
      subtype = customValue;
    }
    eventTracker(
      'filterSubTypeChange',
      `${toString(filtersState[index]?.type?.name)} - ${subtype}`,
    );
    filtersState[index] = { type: filtersState[index].type, subtype };
    setFilters([...filtersState]);
  };

  const FilterRow = (props) => {
    return props.filters.map((filter, index) => (
      <Row key={index} id={index} className="advanced_search__filter-row-borderless">
        <Form.Group className="form-group" as={Col} md="5">
          <DropDownSearchMenu
            defaultSelected={[filter.type]}
            placeholder="Select Category"
            onChange={(e) => onFilterTypeChange(e, index)}
          />
          {error && filter.type && <p className="validate-error">{error}</p>}
        </Form.Group>

        {filter.type &&
          (isLoading ? (
            <Spinner />
          ) : (
            <>
              {SubtypeField({
                type: filter.type,
                subtype: filter.subtype,
                disabled: false,
                onSubtypeChange: (e, customValue) =>
                  onFilterSubtypeChange(e.target.value, index, customValue),
                onDateFieldChange: (value) => onFilterDateFieldChange(value, index),
                dropDownData: dropDownList,
                title: false,
                onYearFieldChange: (value) => onFilterDateFieldChange(value, index),
                min: filter.subtype.min ?? '',
                max: filter.subtype.max ?? '',
              })}

              <Form.Group className="form-group" as={Col} md={1}>
                <Icon
                  icon="remove"
                  size={30}
                  className="remove"
                  onClick={onRemoveItem.bind(this, index)}
                />
              </Form.Group>
            </>
          ))}
      </Row>
    ));
  };

  useEffect(() => {
    (async () => {
      try {
        const { dropDownData } = await controller.onLoadPage(filters);
        setDropDownList(dropDownData);
      } catch (error) {
        console.error(error);
      }
    })();
  }, [filters]);

  return (
    <div className="advanced_search">
      <Container>
        <Form>
          <Row>
            <Form.Group className="form-group m-0" as={Col} md="6">
              <Form.Label>
                <b>Category</b>
              </Form.Label>
            </Form.Group>
          </Row>

          {FilterRow({ filters: filters })}

          {filters.length === 0 && (
            <Row>
              <Form.Group className="form-group" as={Col} md="5">
                <DropDownSearchMenu
                  onChange={(e) => onFilterTypeChange(e)}
                  placeholder="Select Category"
                  roleConfig={roleConfig}
                />
              </Form.Group>
              {isLoading && <Spinner />}
            </Row>
          )}

          <Button variant="outline-primary" className="mb-3" size="sm" onClick={addFilter}>
            Add
          </Button>
        </Form>
      </Container>
    </div>
  );
};

Result.propTypes = {
  setFilters: PropTypes.func,
  filters: PropTypes.object,
  eventTracker: PropTypes.func,
  roleConfig: PropTypes.object,
};

export default Result;
