import { groupBy } from 'lodash';
import React, { Fragment } from 'react';
import { <PERSON>lighter, Menu, MenuItem, Typeahead } from 'react-bootstrap-typeahead';

const CustomDropDownSearchMenu = (props) => {
  const ref = React.createRef();
  const handleChange = (event) => {
    props.onChange(event, ref.current);
  };
  const _renderMenu = (results, menuProps, state) => {
    let index = 0;
    const sections = groupBy(results, 'section');
    const items = Object.entries(sections).map(([key, value]) => (
      <Fragment key={key}>
        {index !== 0 && <Menu.Divider />}
        {value.map((i) => {
          const item = (
            <MenuItem key={index} option={i} position={index}>
              <Highlighter search={state.text}>{i.name}</Highlighter>
            </MenuItem>
          );

          index += 1;
          return item;
        })}
      </Fragment>
    ));

    return <Menu {...menuProps}>{items}</Menu>;
  };

  return (
    <Typeahead
      ref={ref}
      {...props}
      selected={props.defaultSelected}
      inputProps={{ 'data-testid': props.dataTestId }}
      labelKey="name"
      id="search-type-menu"
      onChange={handleChange}
      renderMenu={_renderMenu}
      options={props.searchMenuOptions}
      placeholder={props.placeholder || 'Please Select'}
    />
  );
};

export default CustomDropDownSearchMenu;
