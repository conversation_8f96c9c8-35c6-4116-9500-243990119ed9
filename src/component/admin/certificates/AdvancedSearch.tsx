import React from 'react';
import { Container, <PERSON>, Col, Button, Row } from 'react-bootstrap';
import { toString, parseInt } from 'lodash';
import CustomDropDownSearchMenu from './CustomDropdownSearchMenu';
import SubtypeField from '../../advanced_search/SubtypeField';
import styleGuide from '../../../styleGuide';
const { Icon } = styleGuide;
import * as searchTypes from '../../../constants/certificates';

const AdvancedSearch = ({ setFilters, filters, dropDownData, ga4EventTrigger }) => {
  const searchMenu = searchTypes.CERTIFICATE_SEARCH_TYPES.filter((types) => {
    const selectedFilter = filters.find((filteredValue) => filteredValue.type.name === types.name);
    return typeof selectedFilter === 'undefined';
  });

  let searchMenuOptions = searchMenu;

  const addFilter = () => {
    if (filters.length === 0 || filters[filters.length - 1].type === '') return;
    setFilters((oldArray) => [...oldArray, { type: '' }]);
  };

  const onRemoveItem = (index) => {
    const array = [...filters];
    if (index > -1) {
      array.splice(index, 1);
    }
    setFilters(array);
  };

  const onFilterTypeChange = (event, index) => {
    let element = event[0];
    let filtersState = [...filters];
    if (element === undefined) {
      filtersState[index] = { type: '', subtype: '' };
      setFilters([...filtersState]);
      return;
    }
    const type = searchTypes.CERTIFICATE_SEARCH_TYPES.find((item) => {
      return item.type === element.type;
    });
    ga4EventTrigger('Category', 'Certificate Advance Search', type.name);
    if (!index) {
      setFilters([{ type, subtype: '' }]);
      return;
    }
    if (type) {
      filtersState[index] = { type, subtype: '' };
      setFilters([...filtersState]);
    }
  };

  const onFilterSubtypeChange = async (value, index, customValue) => {
    let filtersState = [...filters];
    let subtype = value;
    let newSelectedItem = value;
    if (!value) {
      filtersState[index] = { type: filtersState[index].type, subtype: '' };
      setFilters([...filtersState]);
      return;
    }
    switch (filtersState[index].type.inputType) {
      case 'multiselect':
        subtype = findSubtype(dropDownData[filtersState[index].type.type], value);
        if (value.length > 1) {
          const existingIds = filtersState[index].subtype.map((item) => item.id);
          newSelectedItem = subtype.filter((item) => !existingIds.includes(item.id));
        } else {
          newSelectedItem = subtype;
        }
        break;
      case 'dropdown':
        subtype = findDropdownSubtype(dropDownData[filtersState[index].type.type], value);
        break;
      case 'number_range':
        subtype = customValue;
        break;
      default:
        break;
    }
    ga4EventTrigger(
      'Value',
      'Certificate Advance Search',
      `${toString(filtersState[index]?.type?.name)} - ${newSelectedItem[0].value}`,
    );
    filtersState[index] = { type: filtersState[index].type, subtype };
    setFilters([...filtersState]);
  };

  const findSubtype = (array, value) =>
    array.filter((element) => {
      if (typeof value === 'object') {
        return value.includes(element.id);
      }
      if (typeof value === 'string') {
        return element.id === value;
      }
      return element.value === parseInt(value);
    });

  const findDropdownSubtype = (array, value) =>
    array.find((element) => {
      if (typeof value === 'object') {
        return value.includes(element.id);
      }
      if (typeof value === 'string') {
        return element.id === value;
      }
      return element.value === parseInt(value);
    });

  const FilterRow = (props) => {
    searchMenuOptions = searchMenu;
    return props.filters?.map((filter, index) => (
      <Row key={index} id={index} className="advanced_search__filter-row-borderless form-row">
        <Form.Group as={Col} md="5" className="form-group">
          <CustomDropDownSearchMenu
            defaultSelected={[filter.type]}
            placeholder="Select Category"
            onChange={(e) => onFilterTypeChange(e, index)}
            searchMenuOptions={searchMenuOptions}
            dataTestId="fml-admin-certificate-list-filter-menu"
          />
        </Form.Group>

        {filter.type && (
          <>
            {SubtypeField({
              type: filter.type,
              subtype: filter.subtype,
              disabled: false,
              disablePreviousDates: false,
              dropDownData: dropDownData,
              title: false,
              dataTestId: `fml-admin-certificate-list-subtype-${filter.type.name}`,
              onSubtypeChange: (e, customValue) =>
                onFilterSubtypeChange(e.target.value, index, customValue),
            })}

            <Form.Group as={Col} md={1} className="form-group">
              <Icon
                icon="remove"
                size={30}
                className="remove"
                onClick={onRemoveItem.bind(this, index)}
                dataTestId={`fml-admin-certificate-list-remove-${filter.type.name}-filter`}
              />
            </Form.Group>
          </>
        )}
      </Row>
    ));
  };

  return (
    <div className="advanced_search">
      <Container>
        <Form>
          <Row className="form-row">
            <Form.Group as={Col} md="6" className="m-0 form-group">
              <Form.Label className="filterHeading form-label">
                <b>Filter Certificates</b>
              </Form.Label>
            </Form.Group>
          </Row>
          {filters.length === 0 && (
            <Row className="form-row">
              <Form.Group as={Col} md="5" className="form-group">
                <CustomDropDownSearchMenu
                  onChange={(e) => onFilterTypeChange(e)}
                  placeholder="Select Category"
                  searchMenuOptions={searchMenuOptions}
                  dataTestId="fml-admin-certificate-list-filter-menu"
                />
              </Form.Group>
            </Row>
          )}
          {FilterRow({ filters: filters })}
          <Button
            data-testid="fml-admin-certificate-list-add-another-filter"
            variant="outline-primary"
            className="mb-3"
            size="sm"
            onClick={addFilter}
            hidden={searchMenuOptions.length === 0}
          >
            Add
          </Button>
        </Form>
      </Container>
    </div>
  );
};

export default AdvancedSearch;
