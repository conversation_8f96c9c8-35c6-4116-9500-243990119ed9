import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import {
  Col,
  Button,
  Modal,
  Form,
  Row,
  DropdownButton,
  Dropdown,
  InputGroup,
  OverlayTrigger,
  Tooltip,
} from 'react-bootstrap';
import { formatDate } from '../../../util/view-utils';
import ConfirmModal from '../../customComponent/CustomConfirmationModal';
import vesselService from '../../../service/vessel-service';
import { CERTIFICATE_GROUP, SURVEY_DEPARTMENT } from '../../../constants/certificates';
import { toast } from 'react-toastify';
import CustomDropDown from '../../customComponent/CustomDropDown';
import { FaSearch } from 'react-icons/fa';

const TechGroupDropdown = ({
  techGroupFilter,
  setTechGroupFilter,
  setFieldValue,
  techGroupsList,
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter tech groups based on the search query
  const filteredTechGroups = techGroupsList.filter((techGroup) =>
    techGroup.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  // Handle the checkbox click and update techGroupFilter
  const handleCheckboxChange = (techGroup, e) => {
    e.stopPropagation(); // Prevent dropdown from closing immediately
    setTechGroupFilter((prev) => {
      const updatedFilter = {
        ...prev,
        [techGroup]: !prev[techGroup], // Toggle checkbox state
      };
      const selectedTechGroups = Object.entries(updatedFilter)
        .filter(([_, value]) => value)
        .map(([key]) => key);
      setFieldValue('tech_group', selectedTechGroups); // Update the selected tech groups
      return updatedFilter;
    });
  };

  // Handle clearing all selections
  const handleClearAll = (e) => {
    e.preventDefault(); // Prevent form submission or page refresh
    e.stopPropagation(); // Prevent dropdown from closing immediately
    setTechGroupFilter({}); // Clear selected filters
    setFieldValue('tech_group', []); // Clear the form field
  };

  // Construct the dropdown title based on selected tech groups
  const dropdownTitle =
    Object.entries(techGroupFilter)
      .filter(([_, value]) => value)
      .map(([key]) => key)
      .join(', ') || 'Please Select';


  useEffect(() => {

    return () => {
      setTechGroupFilter({})
    }
  }, [])

  return (
    <DropdownButton
      title={dropdownTitle}
      className="cert-group-dropdown flex-item"
      variant="light"
      drop="down"
    >
      {/* Search Input */}
      <Dropdown.Item>
        <InputGroup
          className="mb-3"
          style={{
            position: 'relative',
            display: 'flex',
            alignItems: 'center',
            width: '100%',
          }}
        >
          <Form.Control
            style={{ paddingLeft: '40px' }}
            placeholder="Search..."
            value={searchQuery}
            onClick={(e) => e.stopPropagation()} 
            onChange={(e) => setSearchQuery(e.target.value)} 
            onFocus={(e) => {
              e.target.style.paddingLeft = '0px';
            }}
            onBlur={(e) => {
              e.target.style.paddingLeft = '40px';
            }}
          />
          <FaSearch
            style={{
              position: 'absolute',
              left: '15px',
              zIndex: 1,
              color: '#aaa',
            }}
          />
        </InputGroup>
      </Dropdown.Item>

      {/* Loop over filtered techGroupsList and create a checkbox for each one */}
      <div style={{ maxHeight: '20vh', overflowY: 'auto' }}>
        {filteredTechGroups.map((techGroup) => (
          <Dropdown.Item
            eventKey={techGroup}
            key={techGroup}
            onClick={(e) => handleCheckboxChange(techGroup, e)} // Prevent dropdown closing
          >
            <Form.Check>
              <span className="basic-checkbox">
                <Form.Check.Input
                  type="checkbox"
                  className="custom-colour-checkbox"
                  onClick={(e) => handleCheckboxChange(techGroup, e)} // Prevent dropdown closing
                  checked={!!techGroupFilter[techGroup]} // Control the checkbox based on techGroupFilter state
                />
              </span>
              <Form.Check.Label>{techGroup}</Form.Check.Label>
            </Form.Check>
          </Dropdown.Item>
        ))}

        {/* Clear All Button */}
        <Dropdown.Item
          eventKey="clear-all-tec-group"
          className="text-primary text-underline"
          onClick={handleClearAll} // Clear all selected filters
        >
          Clear All
        </Dropdown.Item>
      </div>
    </DropdownButton>
  );
};

const AdminCertificateDialog = ({
  showModal,
  edit = false,
  handleModalCancel,
  handleModalSubmit,
  data,
  handleModalClear,
  ga4EventTrigger = () => {},
  techGroupsList,
  roleConfig,
  canUserEditCertificate = () => {},
}) => {
  const [form, setForm] = useState({
    name: '',
    group: '',
    department: '',
    tech_group: [],
    is_visible: false,
    is_compulsory: false,
    is_important: false,
    inactive: false,
    applicable_vessel_type: '',
  });
  const [errors, setErrors] = useState({});
  const [techGroupFilter, setTechGroupFilter] = useState({});
  const [vesselType, setVesselType] = useState({
    tanker: false,
    dry: false,
  });
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [disable, setDisable] = useState(true);
  const [isDisabledConfirm, setIsDisabledConfirm] = useState(false);

  const setFieldValue = (field, value) => {
    setDisable(false);
    setForm({
      ...form,
      [field]: value,
    });

    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: null,
      });
    }
  };

  useEffect(() => {
    if (edit && data) {
      const formData = {
        name: data.name,
        group: data.group,
        department: data.department,
        is_visible: data.is_visible,
        is_compulsory: data.is_compulsory,
        is_important: data.is_important,
        inactive: data.inactive,
        applicable_vessel_type: data.applicable_vessel_type,
      };
      setForm(formData);
      setVesselType({
        tanker: data.applicable_vessel_type === 'tanker' || data.applicable_vessel_type === 'all',
        dry: data.applicable_vessel_type === 'dry' || data.applicable_vessel_type === 'all',
      });
    }
  }, [edit]);

  const validateForm = () => {
    const { name, group, department, applicable_vessel_type } = form;
    const newErrors = {};
    if (!name || name === '') newErrors.name = 'Survey / Certificate is a required field';
    if (!group || group === '') newErrors.group = 'Certificate Group is a required field';
    if (!department || department === '')
      newErrors.department = 'Survey Department is a required field';
    if (!applicable_vessel_type || applicable_vessel_type === '')
      newErrors.applicable_vessel_type = 'Please select at least one vessel type (Dry or Tanker)';
    return newErrors;
  };

  function getApplicableVesselType(vesselType) {
    if (vesselType.tanker && vesselType.dry) {
      return 'all';
    } else if (vesselType.tanker) {
      return 'tanker';
    } else if (vesselType.dry) {
      return 'dry';
    } else {
      return '';
    }
  }

  const handleVesselTypeChange = (type) => {
    const updatedVesselType = {
      ...vesselType,
      [type]: !vesselType[type],
    };
    setVesselType(updatedVesselType);
    const applicableVesselType = getApplicableVesselType(updatedVesselType);
    setFieldValue('applicable_vessel_type', applicableVesselType);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const formErrors = validateForm();
    if (!_.isEmpty(formErrors)) {
      setErrors(formErrors);
    } else {
      setShowConfirmModal(true);
      handleModalCancel();
    }
  };

  const handleCancel = () => {
    handleModalCancel();
    handleModalClear();
    setForm({
      name: '',
      group: '',
      department: '',
      is_visible: false,
      is_compulsory: false,
      is_important: false,
      inactive: false,
      applicable_vessel_type: '',
    });
    setErrors({});
    setShowConfirmModal(false);
    setDisable(true);
    setVesselType({
      tanker: false,
      dry: false,
    });
  };

  const handleConfirm = async () => {
    setIsDisabledConfirm(true);
    ga4EventTrigger(
      edit ? 'Confirm Edit Vessel Survey' : 'Confirm Define Vessel Survey',
      'All Surveys Certificates - Menu',
      form.name,
    );
    try {
      const payload = {
        ...form,
        applicable_vessel_type: form.applicable_vessel_type || null,
      };
      edit
        ? await vesselService.updateAdminCertificate(payload, data.id)
        : await vesselService.createAdminCertificate(payload);
      handleModalSubmit();
      handleCancel();
      toast.success(edit ? 'Changes saved successfully' : 'Certificate changed successfully');
    } catch (error) {
      toast.error('Error saving the changes');
    }
    setIsDisabledConfirm(false);
    setVesselType({
      tanker: false,
      dry: false,
    });
  };

  return (
    <>
      <Modal
        id="survey-certificate-modal"
        className="crt-admin-survey-certificate-modal"
        show={showModal}
        aria-labelledby="survey-certificate-modal"
        centered
        size="lg"
        backdrop="static"
      >
        <Modal.Header className="sticky-header">
          <Modal.Title>
            {!edit ? 'Define New Survey/Certificate' : 'Edit Survey/Certificate'}
          </Modal.Title>
        </Modal.Header>

        <Form className="form-main-control">
          <Modal.Body>
            <div>
              {edit && (
                <Form.Label className="edit-label pt-2">
                  Last Edited by {data.updated_by ?? data.created_by} on{' '}
                  {formatDate(data.updated_at ?? data.created_at, 'DD MMM YYYY HH:mm')}
                </Form.Label>
              )}
            </div>
            <div className="crt-admin-top-body">
              <Row>
                <Col>
                  <Form.Group className="form-group" controlId="survey-certificate">
                    <Form.Label className="form-label">Survey / Certificate*</Form.Label>
                    <Form.Control
                      value={form.name}
                      data-testid="fml-admin-certificate-name"
                      placeholder="Please Type"
                      isInvalid={errors.name}
                      onChange={(e) => {
                        setFieldValue('name', e.target.value);
                      }}
                      style={{ width: '100%' }}
                    />
                    <Form.Control.Feedback type="invalid">{errors.name}</Form.Control.Feedback>
                  </Form.Group>
                </Col>
              </Row>

              <Row className="">
                <Col md={6}>
                  <Form.Group className="form-group">
                    <Form.Label className="form-label">Certificate Group*</Form.Label>
                    <CustomDropDown
                      key={'crt-admin-group-dropdown'}
                      selectedItems={form?.group}
                      itemsList={CERTIFICATE_GROUP}
                      fieldName="group"
                      clearText="Clear All"
                      onchange={(e) => {
                        setFieldValue('group', e);
                      }}
                    />
                    <Form.Control.Feedback type="invalid">{errors.group}</Form.Control.Feedback>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="form-group">
                    <Form.Label className="form-label">Survey Department*</Form.Label>
                    <CustomDropDown
                      key={'crt-admin-department-dropdown'}
                      selectedItems={form?.department}
                      itemsList={SURVEY_DEPARTMENT}
                      fieldName="group"
                      clearText="Clear All"
                      onchange={(value) => {
                        setFieldValue('department', value);
                      }}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.department}
                    </Form.Control.Feedback>
                  </Form.Group>
                </Col>
              </Row>
              <Row>
                <Col>
                  <Form.Group className="form-group admin-tech-group">
                    <Form.Label
                      className="from-label"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      title="Applies to ALL vessels in Tech Group."
                    >
                      Tech Group
                    </Form.Label>
                    <TechGroupDropdown
                      key={'crt-admin-techgroup-dropdown'}
                      techGroupFilter={techGroupFilter}
                      techGroupsList={techGroupsList}
                      setFieldValue={setFieldValue}
                      setTechGroupFilter={setTechGroupFilter}
                    />
                  </Form.Group>
                  <span className="admin-helper-text">
                    Applies to all vessels in the tech group
                  </span>
                </Col>
              </Row>
            </div>
            <div>
              <div className="crt-admin-bottom-body">
                <Row className="mt-3">
                  <Col md={5} className="ml-4">
                    <Form.Group className="form-group">
                      <Form.Check
                        type="checkbox"
                        className="basic-checkbox"
                        data-testid="fml-admin-certificate-visible"
                        checked={form.is_visible}
                        onChange={(e) => setFieldValue('is_visible', e.currentTarget.checked)}
                      />
                      <label className="ml-2">Visible to Owners</label>
                      <br />
                      <p className="ml-2">Selecting this will show it to owners</p>
                    </Form.Group>
                  </Col>
                  <Col md={5} className="ml6">
                    <Form.Group className="form-group">
                      <Form.Check
                        type="checkbox"
                        className="basic-checkbox"
                        checked={form.is_compulsory}
                        data-testid="fml-admin-certificate-compulsory"
                        onChange={(e) => setFieldValue('is_compulsory', e.currentTarget.checked)}
                      />
                      <label className="ml-2">Mark as Statutory</label>
                      <br />
                      <p className="ml-2">Selecting this will apply to all vessels</p>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={5} className="ml-4">
                    <Form.Group className="form-group">
                      <Form.Check
                        type="checkbox"
                        className="basic-checkbox"
                        checked={form.is_important}
                        data-testid="fml-admin-certificate-important"
                        onChange={(e) => {
                          setFieldValue('is_important', e.currentTarget.checked);
                        }}
                      />
                      <label className="ml-2">Mark as Important</label>
                      <br />
                      <p className="ml-2">Selecting this will mark it as Important</p>
                    </Form.Group>
                  </Col>
                  <Col md={5} className="ml6">
                    <Form.Group className="form-group">
                      <label style={{ marginLeft: '-1.5rem' }}>Select Type</label>
                      <Row>
                        <Col>
                          <Form.Check
                            type="checkbox"
                            className="basic-checkbox"
                            checked={vesselType.dry}
                            onChange={() => handleVesselTypeChange('dry')}
                            data-testid="fml-admin-certificate-dry "
                          />
                          <label className="ml-2">Dry</label>
                          <br />
                        </Col>
                        <Col>
                          <Form.Check
                            type="checkbox"
                            className="basic-checkbox"
                            checked={vesselType.tanker}
                            onChange={() => handleVesselTypeChange('tanker')}
                            data-testid="fml-admin-certificate-tanker"
                          />
                          <label className="ml-2">Tanker</label>
                          <br />
                        </Col>
                      </Row>
                      {errors.applicable_vessel_type && (
                        <div className="applicable-vessel-error">
                          {errors.applicable_vessel_type}
                        </div>
                      )}
                    </Form.Group>
                  </Col>
                </Row>
              </div>
            </div>

            <div className="vessel-type-info-wrapper infotext">
              Select <b>'Dry' or 'Tanker'</b> to define the applicability of the certificates.
              Select both if the certificate applies to <b>both</b> vessel types.
            </div>
          </Modal.Body>

          <Modal.Footer className="sticky-footer">
            <Button
              variant="primary"
              data-testid="fml-admin-certificate-cancel"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <OverlayTrigger
              overlay={
                disable ? (
                  <Tooltip id="remark-tooltip" className="custom-tooltip-vessel-list">
                    Add all mandatory fields
                  </Tooltip>
                ) : (
                  <></>
                )
              }
              placement="bottom"
            >
              <Button
                variant="secondary"
                data-testid="fml-admin-certificate-save"
                type="submit"
                disabled={disable}
                onClick={handleSubmit}
              >
                Save
              </Button>
            </OverlayTrigger>
          </Modal.Footer>
        </Form>
      </Modal>

      <ConfirmModal
        showConfirmModal={showConfirmModal}
        setShowConfirmModal={setShowConfirmModal}
        title={
          edit
            ? 'Confirm making changes to Survey/Certificate?'
            : 'Confirm Defining New Survey/Certificate?'
        }
        content={
          edit &&
          'This action will affect existing vessels having this survey/certificate. Marking as inactive will result their uploaded document and input data to be removed.'
        }
        confirmText={'Confirm'}
        handleCancel={handleCancel}
        handleConfirm={handleConfirm}
        isDisabledConfirm={isDisabledConfirm}
      />
    </>
  );
};

export default AdminCertificateDialog;
