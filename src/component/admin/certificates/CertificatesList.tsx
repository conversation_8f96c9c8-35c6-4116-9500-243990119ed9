import { AdminContext } from '../../../context/AdminContext';
import _ from 'lodash';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Button, ButtonGroup, Col, Dropdown, Form, Row, Tab } from 'react-bootstrap';
import { useDebounce, useDebouncedCallback } from 'use-debounce';
import vesselService from '../../../service/vessel-service';
import { formatValue } from '../../../util/view-utils';
import { canUserEditCertificate } from '../../../util/certificatesAndSurveys';
import CustomTable from '../../customComponent/CustomTable';
import ErrorAlert from '../../ErrorAlert';
import AdminCertificateDialog from './AdminCertificateDialog';
import { checkOperation } from '../../../util/certificates-export/utils';
import {
  CERTIFICATE_GROUP,
  COMPULSORY_TO_ALL_VESSELS,
  MARK_AS_IMPORTANT,
  SURVEY_DEPARTMENT,
  VISIBLE_TO_OWNERS,
  TECH_GROUPS,
} from '../../../constants/certificates';
import AdvancedSearch from './AdvancedSearch';
import httpService from '../../../service/http-service';
import keycloakService from '../../../service/keycloak-service';
import { CheckedIcon, InactiveStatus, EditAction } from './HelperComponents';

const CertificatesList = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([{ id: 'name', desc: false }]);
  const [pageSize, setPageSize] = useState(200);
  const [pageIndex, setPageIndex] = useState(0);
  const [certificatesList, setCertificatesList] = useState([]);
  const [searchedKeyword, setSearchedKeyword] = useState('');
  const [showCertificateModal, setShowCertificateModal] = useState(false);
  const [editData, setEditData] = useState({});
  const { ga4EventTrigger = () => {}, roleConfig } = useContext(AdminContext);
  const [filters, setFilters] = useState([]);
  const [query, setQuery] = useState('');
  const [debouncedFilters] = useDebounce(filters, 700);
  const [techGroupsList, setTechGroupsList] = useState([]);
  const [techGroupsLoadError, setTechGroupsLoadError] = useState('');
  const getTechGroupsList = async () => {
    try {
      setTechGroupsList([]);
      const staffList = await keycloakService.getStaffList(TECH_GROUPS);
      setTechGroupsList(staffList.data.map((staff) => staff.full_name));
      setTechGroupsLoadError('');
    } catch (error) {
      if (httpService.axios.isCancel(error)) {
        return;
      }
      setTechGroupsLoadError('Failed to fetch Tech Groups data.');
    }
  };

  const columns = useMemo(
    () => [
      {
        Header: 'Survey/Certificate',
        accessor: (row) => formatValue(row.name),
        id: 'name',
        name: 'name',
        type: 'text',
        width: 230,
        maxWidth: 230,
      },
      {
        Header: 'Certificate Group',
        accessor: (row) =>
          formatValue(CERTIFICATE_GROUP.find((group) => group.id === row.group)?.value),
        id: 'group',
        name: 'group',
        type: 'text',
      },
      {
        Header: 'Survey Department',
        accessor: (row) => checkOperation(formatValue(row.department)),
        id: 'department',
        type: 'department',
        name: 'text',
      },
      {
        Header: 'Type',
        accessor: (row) => {
          const type = row.applicable_vessel_type || '---';
          return type.charAt(0).toUpperCase() + type.slice(1);
        },
        id: 'applicable_vessel_type',
        name: 'applicable_vessel_type',
        type: 'text',
      },
      {
        Header: 'Visible to Owners',
        accessor: (row) => <CheckedIcon condition={row.is_visible} />, // NOSONAR
        id: 'is_visible',
        name: 'is_visible',
        type: 'text',
      },
      {
        Header: 'Compulsory for All Vessels',
        accessor: (row) => <CheckedIcon condition={row.is_compulsory} />, // NOSONAR
        id: 'is_compulsory',
        name: 'is_compulsory',
        type: 'text',
      },
      {
        Header: 'Mark as Important',
        accessor: (row) => <CheckedIcon condition={row.is_important} />, // NOSONAR
        id: 'is_important',
        name: 'is_important',
        type: 'text',
      },
      {
        Header: 'Status',
        accessor: (row) => <InactiveStatus inactive={row.inactive} />, // NOSONAR
        id: 'inactive',
        name: 'inactive',
        type: 'text',
        maxWidth: 60,
      },
      {
        Header: 'Action',
        accessor: (row, index) => (
          <EditAction
            row={row}
            index={index}
            roleConfig={roleConfig}
            onEdit={(row) => {
              setEditData(row);
              setShowCertificateModal(true);
              getTechGroupsList();
            }}
            ga4EventTrigger={ga4EventTrigger}
          /> // NOSONAR
        ),
        id: 'action',
        name: 'action',
        type: 'text',
        disableSortBy: true,
        maxWidth: 60,
      },
    ],
    [],
  );

  const getCertificateQuery = (searchCriteria) =>
    searchCriteria
      .reduce((arr, item) => {
        arr.push({
          key: item.type.type,
          value: item.subtype.id,
        });
        return arr;
      }, [])
      .map(({ key, value }) => {
        return `${key}=${encodeURIComponent(value)}`;
      })
      .join('&');

  useEffect(() => {
    if (debouncedFilters.length) {
      let filteredList = removeEmptyFilters(debouncedFilters);
      const filterQuery = getCertificateQuery(filteredList);
      setQuery(filterQuery);
      query !== filterQuery && fetchData(sortData, 0, pageSize, searchedKeyword, filterQuery);
    } else if (query !== '') {
      setQuery('');
      fetchData(sortData, 0, pageSize, searchedKeyword, '');
    }
  }, [debouncedFilters]);

  const fetchData = async (
    sortData = [{ id: 'name', desc: false }],
    pageIndex = 0,
    pageSize = 200,
    searchKeyword = '',
    query = '',
  ) => {
    try {
      setLoading(true);
      setCertificatesList([]);
      const response = await vesselService.getAdminCertificatesList(
        {
          sortBy: sortData,
          pageSize: pageSize,
          pageIndex: pageIndex,
        },
        query,
        searchKeyword,
      );
      setCertificatesList(response.data.results);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
      setError(null);
    } catch (error) {
      if (httpService.axios.isCancel(error)) {
        return;
      }
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchData(sortData, value, pageSize, searchedKeyword, query);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', 'All Surveys Certificates - List', value);
    setPageSize(value);
    fetchData(sortData, pageIndex, value, searchedKeyword, query);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'All Surveys Certificates - List', value[0]?.id);
    setSortData(value);
    fetchData(value, pageIndex, pageSize, searchedKeyword, query);
  };

  const handleKeywordChange = useDebouncedCallback((value) => {
    ga4EventTrigger('Keyword', 'All Surveys Certificates - List', value.trim().toLowerCase());
    setPageIndex(0);
    setSearchedKeyword(value.trim().toLowerCase());
    fetchData(sortData, 0, pageSize, value, query);
  }, 700);

  useEffect(() => {
    fetchData();
  }, []);

  const handleSubmit = () => {
    fetchData(sortData, pageIndex, pageSize, searchedKeyword, query);
    setEditData({});
  };

  const handleCancel = () => {
    setShowCertificateModal(false);
  };

  const handleClear = () => {
    setEditData({});
  };

  const removeEmptyFilters = (filter) => {
    return filter.filter((item) => Boolean(item.subtype));
  };

  return (
    <Tab.Container>
      <Row className="no-print">
        <Col xs={6} md={3}>
          <div className="quick-filters-button mb-2">
            <Form.Control
              id="search-bar"
              type="text"
              name="keyword"
              placeholder="Type to filter survey/certificate"
              data-testid="fml-certificates-list-keyword-search"
              onClick={() => ga4EventTrigger('Click', 'All Surveys Certificates - List', 'Keyword')}
              onChange={(e) => handleKeywordChange(e.target.value)}
            />
          </div>
        </Col>
        <Col>
          <ButtonGroup className="advanced-search">
            <Dropdown
              alignRight={false}
              onToggle={(isOpen) =>
                isOpen &&
                ga4EventTrigger('Click', 'Vessel Itinerary Advance Search', 'Vessel Itinerary')
              }
            >
              <Dropdown.Toggle
                variant="outline-primary"
                data-testid="fml-itinerary-list-advance-search"
                id="dropdown-advanced-search"
              >
                Advanced Search{' '}
                {removeEmptyFilters(debouncedFilters).length
                  ? `(${removeEmptyFilters(debouncedFilters).length})`
                  : ''}
              </Dropdown.Toggle>
              <Dropdown.Menu>
                <div className="advanced-search-menu">
                  {AdvancedSearch({
                    filters: filters,
                    setFilters: setFilters,
                    ga4EventTrigger: ga4EventTrigger,
                    dropDownData: {
                      group: CERTIFICATE_GROUP,
                      department: SURVEY_DEPARTMENT,
                      applicable_vessel_type: [
                        { id: 'tanker', value: 'Tanker' },
                        { id: 'dry', value: 'Dry' },
                        { id: 'all', value: 'All' },
                      ],
                      is_visible: VISIBLE_TO_OWNERS,
                      is_compulsory: COMPULSORY_TO_ALL_VESSELS,
                      is_important: MARK_AS_IMPORTANT,
                    },
                  })}
                </div>
              </Dropdown.Menu>
            </Dropdown>
          </ButtonGroup>
        </Col>
        <Col className="row justify-content-end mr-auto">
          {roleConfig?.admin?.certificates?.create && (
            <Button
              variant="outline-primary"
              data-testid="fml-certificates-list-defineNewCertificate-button"
              className="ml-3"
              hidden={!roleConfig.admin.certificates.manage}
              onClick={() => {
                ga4EventTrigger(
                  'Define New Vessel Survey',
                  'All Surveys Certificates - Menu',
                  'Define New Vessel Survey',
                );
                setShowCertificateModal(true);
                getTechGroupsList();
              }}
            >
              Define New Survey/Certificate
            </Button>
          )}
        </Col>
      </Row>

      {error && <ErrorAlert message={error} />}
      <CustomTable
        column={columns}
        tableRef={null}
        reportData={certificatesList}
        isLoading={loading}
        pagination={true}
        pageCount={pageCount}
        totalCount={totalCount}
        setPageNo={onPageIndexChange}
        setPageListSize={onPageSizeChange}
        setSortData={onSortChange}
        pageNo={pageIndex}
        pageSizeList={[10, 20, 50, 100, 200, 500]}
        pageSize={pageSize}
      />

      <AdminCertificateDialog
        showModal={showCertificateModal}
        edit={!_.isEmpty(editData)}
        data={editData}
        handleModalCancel={handleCancel}
        handleModalSubmit={handleSubmit}
        handleModalClear={handleClear}
        ga4EventTrigger={ga4EventTrigger}
        roleConfig={roleConfig}
        techGroupsList={techGroupsList}
        techGroupsLoadError={techGroupsLoadError}
        canUserEditCertificate={canUserEditCertificate}
      />
    </Tab.Container>
  );
};
export default CertificatesList;
