import React from "react";
import { Icon } from "../../../styleGuide";
import { canUserEditCertificate } from "../../../util/certificatesAndSurveys";
import { formatValue } from "../../../util/view-utils";
import { SlashCircle } from "react-bootstrap-icons";


export const CheckedIcon = ({ condition }) => (
    <div className="text-center">
      {condition && <Icon icon="checked" size={20} color="green" />}
    </div>
  );

export const InactiveStatus = ({ inactive }) => (
    <div className="text-center">{inactive && <SlashCircle color="red" size={20} />}</div>
  );

export const EditAction = ({ row, index, roleConfig, onEdit, ga4EventTrigger }) => (
    <div className="text-center" hidden={!canUserEditCertificate(roleConfig, row.department)}>
      <Icon
        icon="Edit"
        size={20}
        className="edit-icon"
        data-testid={`fml-certificates-list-edit-button-${index}`}
        onClick={() => {
          ga4EventTrigger(
            'Edit Vessel Survey',
            'All Surveys Certificates - Menu',
            formatValue(row.name),
          );
          onEdit(row);
        }}
      />
    </div>
  );