import React, { useContext, useEffect, useState } from 'react';
import { <PERSON><PERSON>, Tooltip, OverlayTrigger, Form } from 'react-bootstrap';
import vesselService from '../../service/vessel-service';
import styleGuide from '../../styleGuide';
import { formatValue } from '../../util/view-utils';
import VESSEL_TYPE from '../../constants/vessel-type';
import INTERVAL_UNIT from '../../constants/interval-unit';
import { SlashCircle } from 'react-bootstrap-icons';
import _ from 'lodash';
import EmergencyDrillDialog from '../emergencyDrills/EmergencyDrillDialog';
import { useIsMount } from '../../util/useIsMount';
import CustomTable from '../customComponent/CustomTable';
import { AdminContext } from '../../context/AdminContext';
const { Icon } = styleGuide;

const EmergencyDrillList = () => {
  const [loading, setLoading] = useState(false);
  const [drillList, setDrillList] = useState([]);
  const [rowId, setRowId] = useState();
  const [sortData, setSortData] = useState([{ id: 'name', desc: true }]);
  const [showEmergencyDrillModal, setShowEmergencyDrillModal] = useState(false);
  const isMount = useIsMount();
  const { ga4EventTrigger = () => {}, roleConfig } = useContext(AdminContext);

  const createEmergencyDrillDialog = () => {
    setShowEmergencyDrillModal(true);
  };

  const cancelButtonHandler = () => {
    setShowEmergencyDrillModal(false);
    setRowId();
  };

  const columns = [
    {
      Header: 'Drills',
      accessor: (row) => <div className="line-text-truncate">{formatValue(row.name)}</div>,
      sticky: 'left',
      id: 'name',
      name: 'drills',
      type: 'text',
      maxWidth: 60,
    },
    {
      Header: 'Applicable To',
      accessor: (row) =>
        _.get(row, 'parsed_type')?.map((val) => {
          return <li className="list-unstyled">{VESSEL_TYPE[val] || val}</li>;
        }),
      id: 'vessel_type',
      name: 'applicable',
      type: 'text',
      disableSortBy: true,
      maxWidth: 50,
    },
    {
      Header: 'Description',
      accessor: (row, index) => (
        <OverlayTrigger
          overlay={
            <Tooltip id="desc_tooltip" className="tooltip">
              {row.description}
            </Tooltip>
          }
          placement={`${index + 1 === drillList.length ? 'top' : 'bottom'}`}
        >
          <div className="line-text-truncate">{row.description}</div>
        </OverlayTrigger>
      ),
      id: 'description',
      name: 'description',
      type: 'text',
      disableSortBy: true,
      maxWidth: 80,
    },
    {
      Header: 'Drill Period',
      accessor: (row) => (
        <div className='"text-capitalize"'>
          {row.interval + ' ' + INTERVAL_UNIT[row.interval_unit]}
        </div>
      ),
      id: 'period',
      name: 'period',
      type: 'item',
      disableSortBy: true,
      maxWidth: 30,
    },
    {
      Header: 'Status',
      headerClassName: 'justify-content-center',
      accessor: (row) => (
        <div className="text-center">
          {row.inactive === true && <SlashCircle color="red" size={20}></SlashCircle>}
        </div>
      ),
      id: 'inactive',
      type: 'item',
      maxWidth: 20,
    },
    {
      Header: 'Action',
      headerClassName: 'justify-content-center ',
      accessor: (row) => (
        <div className="text-center">
          {roleConfig.admin.drills.edit ? (
            <Icon
              icon="Edit"
              size={20}
              className="edit-icon"
              onClick={() => {
                ga4EventTrigger('Edit Drill', 'Drill Master List - Menu', formatValue(row.name));
                setShowEmergencyDrillModal(true);
                setRowId(row);
              }}
            />
          ) : null}
        </div>
      ),
      type: 'item',
      disableSortBy: true,
      maxWidth: 20,
    },
  ];

  const fetchDrillList = async () => {
    setLoading(true);
    try {
      const response = await vesselService.getEmergencyDrillList(sortData);
      setDrillList(response.data);
    } catch (error) {
      console.log('Unable to fetch drill List', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    !isMount && ga4EventTrigger('Sorting', 'Drill Master List - Menu', sortData[0]?.id);
    fetchDrillList();
  }, [sortData]);

  return roleConfig.admin.drills.view ? (
    <div>
      <EmergencyDrillDialog
        showEmergencyDrillModal={showEmergencyDrillModal}
        setShowEmergencyDrillModal={setShowEmergencyDrillModal}
        cancelButtonHandler={cancelButtonHandler}
        rowId={rowId}
        isLoading={loading}
        setLoading={setLoading}
        fetchDrillList={fetchDrillList}
        ga4EventTrigger={ga4EventTrigger}
      />

      <div className="row justify-content-end pr-3">
        <Button
          variant="outline-primary"
          data-testid="fml-emergencyDrillList-defineNewDrill"
          className="ml-3"
          onClick={() => {
            ga4EventTrigger('Define New Drill', 'Drill Master List - Menu', 'Define New Drill');
            createEmergencyDrillDialog();
          }}
          hidden={!roleConfig.admin.drills.create}
        >
          Define New Drill
        </Button>
      </div>

      <CustomTable
        column={columns}
        reportData={drillList}
        tableRef={null}
        isLoading={loading}
        setSortData={setSortData}
      />
    </div>
  ) : (
    <Form.Label className="technical-reports pt-2 pb-2">
      <b>Admin</b>
    </Form.Label>
  );
};

export default EmergencyDrillList;
