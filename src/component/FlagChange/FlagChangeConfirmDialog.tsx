import _ from 'lodash';
import React, { useState, useMemo, useContext } from 'react';
import { Button, Col, Form, Modal, Row } from 'react-bootstrap';
import vesselService from '../../service/vessel-service';
import ErrorAlert from '../ErrorAlert';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import CustomTable from '../customComponent/CustomTable';
import CustomDatePicker from '../../component/customComponent/CustomDatePicker';
import { formatValue } from '../../util/view-utils';
import { useParams } from 'react-router-dom';
import { DetailContext } from '../../context/DetailContext';

const FlagChangeConfirmDialog = ({
  showModal = false,
  handleModalCancel = () => {},
  handleModalSubmit = () => {},
  setPageError = () => {},
  data = {},
}) => {
  let { ownershipId } = useParams();
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState({});
  const [errors, setErrors] = useState({});
  const [validationError, setValidationError] = useState();
  const { ga4EventTrigger = () => {}, vesselName } = useContext(DetailContext);

  const columns = useMemo(
    () => [
      {
        Header: 'Country',
        accessor: (row, index) => formatValue(row.flag_country[0]?.value),
        id: 'country',
        name: 'country',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Port of Registry',
        accessor: (row) => formatValue(row.port_of_registry[0]?.value),
        id: 'port_of_registry',
        name: 'port_of_registry',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Flag Office',
        accessor: (row) => formatValue(row?.flag_office[0]?.value),
        id: 'flag_office',
        name: 'flag_office',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Call sign',
        id: 'call_sign',
        type: 'text',
        accessor: (row) => formatValue(row.call_sign),
        disableSortBy: true,
      },
    ],
    [data],
  );

  const intialValidateError = () => ({
    ..._.omitBy(errors, (v) => v == null),
    ...validateEffectiveDate(form?.effective_date),
  });

  const validateEffectiveDate = (value) => ({
    effective_date: _.isEmpty(value) ? 'Effective Date is a required field' : undefined,
  });

  const validateForm = (field, value) => {
    let newErrors = {
      ...errors,
    };
    if (field === 'effective_date') {
      newErrors = { ...newErrors, ...validateEffectiveDate(value) };
    }
    newErrors = _.pickBy(newErrors, (i) => i !== undefined);
    setErrors(newErrors);
  };

  const setFieldValue = (field, value) => {
    validateForm(field, value);
    setForm({
      ...form,
      [field]: value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    let errorList = { ...intialValidateError() };
    errorList = _.pickBy(errorList, (i) => i !== undefined);

    if (_.isEmpty(errorList)) {
      setLoading(true);
      const submitFormData = {
        ownership_id: Number(ownershipId),
        country_code: data.flag_country[0]?.id,
        port_code: data.port_of_registry[0]?.id,
        office_id: data.flag_office[0]?.id,
        call_sign: data.call_sign,
        start_date: form.effective_date,
      };

      try {
        await vesselService.flagOfficeChange(submitFormData);
        handleModalSubmit();
        handleCancel();
      } catch (error) {
        if (error?.response?.status === 400) {
          setValidationError(`${error?.response.data}. Please try again.`);
          setLoading(false);
        } else {
          handleCancel();
          setPageError('Something went wrong with updating flag change. Please try later');
        }
      }
    } else {
      setErrors(errorList);
    }
  };

  const handleCancel = () => {
    setForm({});
    setErrors({});
    handleModalCancel();
    setLoading(false);
  };

  const handleEnableDisableSave = useMemo(() => {
    return !(!_.isEmpty(form?.effective_date) && form?.is_impact && form?.is_ensure);
  }, [form]);

  const isDateNotBeforeMinDate = (date, effDate) => {
    const minDate = new Date(effDate ? effDate : null); // NOSONAR
    return date >= minDate;
  };

  return (
    <Modal
      id="flag-change-confirm-modal"
      show={showModal}
      aria-labelledby="flag-change-confirm-modal"
      centered
      size="lg"
      backdrop="static"
    >
      <Modal.Header>
        <Modal.Title>
          Confirm Changes
          <p className="required-field-text">* Required fields</p>
          <p className="pl-1 text-dark alert-danger required-field-text">
            <p className="text-danger"> This will impact Portage Bill</p>
            <p>
              You are about to change the flag and by changing this will have impact on crew wages
              and portage bill. if you do not understand the consequences, please contact payroll
              team.
            </p>
          </p>
        </Modal.Title>
      </Modal.Header>
      {validationError && <ErrorAlert message={validationError} />}

      <CustomOverlayLoader active={loading}>
        <Form className="form-main-control">
          <Modal.Body>
            <div>
              <p>Review your changes</p>
              <CustomTable
                column={columns}
                className={`flag-change-table`}
                reportData={!_.isEmpty(data) && [data]}
                tableRef={null}
              />
            </div>
            <Row>
              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Effective Date for the changes*</Form.Label>
                  <CustomDatePicker
                    value={form?.effective_date}
                    dataTestId={'fml-flag-change-confirm-effective-date'}
                    onChange={(e) => setFieldValue('effective_date', e)}
                    filterDate={(date) =>
                      isDateNotBeforeMinDate(date, data?.effective_date ?? null)
                    }
                  />
                  <div className="validate-error">{errors.effective_date}</div>
                </Form.Group>
              </Col>
              <Col md={12}>
                <Form.Group className="form-group">
                  <Form.Check
                    type="checkbox"
                    className="basic-checkbox"
                    label={<p>I understand the impact and would like to proceed</p>}
                    data-testid="fml-flag-change-confirm-uderstand-impact"
                    checked={form.is_impact}
                    onChange={(e) => setFieldValue('is_impact', e.currentTarget.checked)}
                  />
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Check
                    type="checkbox"
                    className="basic-checkbox"
                    label={
                      <p>
                        I ensure the following documents and procedures has already been completed
                      </p>
                    }
                    data-testid="fml-flag-change-confirm-ensure"
                    checked={form.is_ensure}
                    onChange={(e) => setFieldValue('is_ensure', e.currentTarget.checked)}
                  />
                </Form.Group>
                <ul>
                  <li>
                    Provisional Certificate of Registry, Safe manning certificate, Bunker CLC has
                    been received.
                  </li>
                  <li>
                    Radio station license, Radio accounting authority and INMARSAT numbers,
                    Tonnage certificate, and SOLAS certificate have been received.
                  </li>
                  <li>
                    Crew documents (Crew CRA, Crew Medical, CLC'92, Crew article, new CBA) have
                    been received.
                  </li>
                  <li>
                    Flag change survey and Endorsement of all manuals have been done by class.
                  </li>
                  <li>
                    ISPS - SSP approval, ISPS - Cert. change, CSR FORM-2, VRP, U.S. COFR, U.S.
                    COFR, California, P and I cover sheet + H&M have been received.
                  </li>
                  <li>Radio survey technician is arranged for SART, EPIRB, VDR, Survey</li>
                  <li>
                    New port of registry is stenciled on the vessel's aft., life jackets,
                    lifebuoys, lifeboats and life rafts.
                  </li>
                  <li>
                    Official log book, Oil record book and cargo record book for the flag to be
                    sent to the vessel.
                  </li>
                </ul>
              </Col>
            </Row>
          </Modal.Body>

          <Modal.Footer>
            <Button
              variant="primary"
              data-testid="fml-change-flag-confirm-close"
              onClick={() => {
                ga4EventTrigger(
                  'Close Flag Change Confirmation',
                  'Flag Change - list',
                  vesselName,
                );
                handleCancel();
              }}
            >
              Close
            </Button>
            <Button
              variant="secondary"
              data-testid="ml-change-flag-confirm-save"
              disabled={handleEnableDisableSave}
              type="submit"
              onClick={(e) => {
                ga4EventTrigger(
                  'Save Flag Change Confirmation',
                  'Flag Change - list',
                  vesselName,
                );
                handleSubmit(e);
              }}
            >
              Save
            </Button>
          </Modal.Footer>
        </Form>
      </CustomOverlayLoader>
    </Modal>
  );
};

export default FlagChangeConfirmDialog;
