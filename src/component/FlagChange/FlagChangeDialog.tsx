import _ from 'lodash';
import React, { useEffect, useState, useContext } from 'react';
import { Button, Col, Form, Modal, Row } from 'react-bootstrap';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';
import vesselService from '../../service/vessel-service';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import CustomCountryPortDropDown from '../customComponent/CustomCountryPortDropDown';
import { DetailContext } from '../../context/DetailContext';

const FlagChangeDialog = ({
  showModal = false,
  handleModalCancel = () => {},
  handleModalSubmit = () => {},
  data = {},
}) => {
  const [form, setForm] = useState({});
  const [errors, setErrors] = useState({});
  const [disableFields, setDisableFields] = useState(true);
  const [flagOfficeOptions, setFlagOfficeOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const { ga4EventTrigger = () => {}, vesselName } = useContext(DetailContext);

  const formFields = {
    flag_country: 'Flag Country',
    flag_office: 'Flag Office',
    call_sign: 'Call Sign',
    port_of_registry: 'Port of Registry',
  };

  const validateFields = (field, value) => ({
    [field]: _.isEmpty(value) ? `${formFields[field]} is a required field` : undefined,
  });

  const intialValidateError = () => ({
    ..._.omitBy(errors, (v) => v == null),
    ...validateFields('flag_country', form?.flag_country),
    ...validateFields('flag_office', form?.flag_office),
    ...validateFields('call_sign', form?.call_sign),
    ...validateFields('port_of_registry', form?.port_of_registry),
  });

  const validateForm = (field, value) => {
    let newErrors = {
      ...errors,
    };
    switch (field) {
      case 'flag_country':
      case 'flag_office':
      case 'port_of_registry':
      case 'call_sign':
        newErrors = { ...newErrors, ...validateFields(field, value) };
        break;
    }
    newErrors = _.pickBy(newErrors, (i) => i !== undefined);
    setErrors(newErrors);
  };

  const setFieldValue = async (field, value) => {
    validateForm(field, value);

    if (field === 'flag_country') {
      setDisableFields(false);
      setForm({
        [field]: value,
      });
      if (!_.isEmpty(value)) {
        setLoading(true);
      }
    } else {
      setForm((prevState) => ({
        ...prevState,
        [field]: value,
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    let errorList = { ...intialValidateError() };
    errorList = _.pickBy(errorList, (i) => i !== undefined);

    if (_.isEmpty(errorList)) {
      const flagData = _.last(data?.flags);
      let submitFormData = {
        flag_office: form.flag_office,
        flag_country: form.flag_country,
        port_of_registry: form.port_of_registry,
        call_sign: form.call_sign,
        effective_date: flagData?.start_date ?? '',
      };
      handleModalSubmit(submitFormData);
    } else {
      setErrors(errorList);
    }
  };

  const handleCancel = () => {
    setForm({});
    setErrors({});
    handleModalCancel();
    setDisableFields(true);
  };

  const fetchOptions = async () => {
    const responseFlagOffice = await vesselService.getFlagOffice();
    setFlagOfficeOptions(responseFlagOffice.data.flags);
  };

  const fetchIntialFlagData = async () => {
    setLoading(true);
    if (!_.isEmpty(data?.flags)) {
      const flagData = _.last(data?.flags);
      const formData = {
        call_sign: flagData?.call_sign,
        flag_country: flagData?.country_code
          ? [{ id: flagData?.country_code, value: flagData?.country_desc }]
          : [],
        flag_office: !_.isEmpty(flagData?.office) ? [{ ...flagData?.office }] : [],
        port_of_registry: flagData?.port_code
          ? [{ id: flagData?.port_code, value: flagData?.port_desc }]
          : [],
      };
      setForm(formData);
    }
    fetchOptions();
    setLoading(false);
  };

  useEffect(() => {
    if (showModal) {
      fetchIntialFlagData();
    }
  }, [data, showModal]);

  return (
    <Modal
      id="flag-change-modal"
      show={showModal}
      aria-labelledby="flag-change-modal"
      centered
      size="lg"
      backdrop="static"
    >
      <Modal.Header>
        <Modal.Title>
          Change Flag
          <p className="required-field-text">* Required fields</p>
          <div className="pl-1 text-dark alert-danger required-field-text">
            <p className="text-danger"> This will impact Portage Bill</p>
            <p>
              You are about to change the flag and by changing this will have impact on crew wages
              and portage bill. if you do not understand the consequences, please contact payroll
              team.
            </p>
          </div>
        </Modal.Title>
      </Modal.Header>
      <CustomOverlayLoader active={loading}>
        <Form className="form-main-control">
          <Modal.Body>
            <Row>
              <Col md={12}>
                <CustomCountryPortDropDown
                  required={true}
                  labelPort={'Port of Registry'}
                  countryData={form.flag_country ? form.flag_country : []}
                  portData={form.port_of_registry ? form.port_of_registry : []}
                  onChangeCountry={(e) => setFieldValue('flag_country', e)}
                  handleClearCountry={() => setFieldValue('flag_country', [])}
                  onChangePort={(e) => setFieldValue('port_of_registry', e)}
                  handleClearPort={() => setFieldValue('port_of_registry', [])}
                  errorTextCountry={errors.flag_country}
                  errorTextPort={errors.port_of_registry}
                  disablePort={disableFields}
                  afterPortFetch={() => setLoading(false)}
                  filterPortBy="is_port_of_registration"
                />
              </Col>
              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Office*</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="office"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-flag-change-office' }}
                    multiple={false}
                    options={flagOfficeOptions}
                    showDropDownIcon={true}
                    clearOnFocus={true}
                    selected={form.flag_office ? form.flag_office : []}
                    disabled={disableFields}
                    onChange={(e) => setFieldValue('flag_office', e)}
                    handleClear={() => setFieldValue('flag_office', [])}
                  />
                  <div className="validate-error">{errors.flag_office}</div>
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Call Sign*</Form.Label>
                  <Form.Control
                    value={form?.call_sign ? form?.call_sign : ''}
                    data-testid="fml-recipient-dialog-to-address"
                    placeholder="Please input"
                    disabled={disableFields}
                    onChange={(e) => {
                      setFieldValue('call_sign', e.target.value);
                    }}
                  />
                  <div className="validate-error">{errors.call_sign}</div>
                </Form.Group>
              </Col>
            </Row>
          </Modal.Body>

          <Modal.Footer>
            <Button
              variant="primary"
              data-testid="fml-change-flag-close"
              onClick={() => {
                ga4EventTrigger('Close Flag Change', 'Flag Change - list', vesselName);
                handleCancel();
              }}
            >
              Close
            </Button>
            <Button
              variant="secondary"
              data-testid="fml-change-flag-save"
              disabled={disableFields}
              type="submit"
              onClick={(e) => {
                ga4EventTrigger('Save Flag Change', 'Flag Change - list', vesselName);
                handleSubmit(e);
              }}
            >
              Save
            </Button>
          </Modal.Footer>
        </Form>
      </CustomOverlayLoader>
    </Modal>
  );
};

export default FlagChangeDialog;
