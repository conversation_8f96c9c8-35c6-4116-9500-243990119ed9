import React, { useMemo } from 'react';
import { formatDate, formatValue } from '../../util/view-utils';
import CustomTable from '../customComponent/CustomTable';
import _ from 'lodash';

const FlagChangeList = ({ flagData }) => {
  const columns = useMemo(
    () => [
      {
        Header: 'Country',
        accessor: (row) => formatValue(row?.country_desc),
        id: 'country',
        name: 'country',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Port of Registry',
        accessor: (row) => formatValue(row?.port_desc),
        id: 'port_of_registry',
        name: 'port_of_registry',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Flag Office',
        accessor: (row) => formatValue(row?.office?.value),
        id: 'flag_office',
        name: 'flag_office',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Call sign',
        id: 'call_sign',
        type: 'text',
        accessor: (row) => formatValue(row?.call_sign),
        disableSortBy: true,
      },
      {
        Header: 'Effective Period',
        id: 'period',
        type: 'text',
        accessor: (row) =>
          `${formatDate(row?.start_date)} to ${row?.end_date ? formatDate(row?.end_date) : 'now'}`,
        disableSortBy: true,
      },
    ],
    [flagData],
  );

  return (
    <>
      <div className="details_page__table_head p-2 font-weight-bold">Flag</div>
      <CustomTable
        column={columns}
        className={`flag-change-list`}
        reportData={!_.isEmpty(flagData) ? flagData : []}
        tableRef={null}
      />
    </>
  );
};

export default FlagChangeList;
