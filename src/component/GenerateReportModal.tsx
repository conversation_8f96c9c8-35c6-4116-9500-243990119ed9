import React from 'react';
import { Button, Modal } from 'react-bootstrap';
import '../pages/styles/monthly-financial.scss';
const GenerateReportModal = (props) => {
  const { onClose, onConfirm, open, headingText, headingDescription, loading } = props;
  return (
    <Modal show={open} onHide={onClose} className="mt-5">
      <Modal.Body className="p-4">
        <p className="h5 fw-light">{headingText}</p>
        <p className="fw-900 text-xs my-4">{headingDescription}</p>
        <hr />
        <div className="d-flex justify-content-end align-items-center gap-2">
          <Button
            variant="secondary"
            className="mr-2 px-4 py-2"
            onClick={onClose}
            disabled={loading}
            dataTestId="genrate-rpt-modal-cancel"
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            className="px-4 py-2"
            onClick={onConfirm}
            disabled={loading}
            dataTestId="genrate-rpt-modal-confirm"
          >
            Confirm
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};
export default GenerateReportModal;
