import React from 'react';
import PropTypes from 'prop-types';

const Spinner = (props) => {
  return (
    <div className={`d-flex ${props.alignClass}`} data-testid="fml-spinner">
      <div className="spinner-border text-secondary" role="status">
        <span className="sr-only">Loading...</span>
      </div>
    </div>
  );
};

Spinner.defaultProps = { alignClass: 'justify-content-center' };

Spinner.propTypes = {
  alignClass: PropTypes.string,
};

export default Spinner;
