import _ from 'lodash';
import React, { useRef, useState } from 'react';
import { Col, Row, ButtonToolbar } from 'react-bootstrap';
import { ChevronExpand, ChevronDown } from 'react-bootstrap-icons';
import { <PERSON>u, MenuItem, Typeahead, Highlighter } from 'react-bootstrap-typeahead';
import { Icon } from '../../styleGuide';

const CustomTypeAhead = (props) => {
  const ref = useRef(null);
  const selectAllOption = { id: 'all', value: 'Select all' };
  const [selectedAll, setSelectedAll] = useState(false);

  const onChangeTypeAhead = (event) => {
    let selectedItemList = _.isEmpty(props?.selected) ? [] : [...(props?.selected || {})];
    if (!_.isEmpty(selectedItemList) && _.includes(selectedItemList, event[event.length - 1])) {
      const updatedList = _.remove(selectedItemList, (item) => {
        return event[event.length - 1].id !== item.id;
      });
      selectedItemList = updatedList;
    } else {
      selectedItemList = [...event];
      if (props?.selectionCount && selectedItemList.length > props?.selectionCount) {
        selectedItemList.shift();
      }
    }

    if (props?.selectAll && !_.isEmpty(event) && _.includes(event, selectAllOption)) {
      if (selectedAll) {
        props?.onChange([]);
        setSelectedAll(false);
      } else {
        props?.onChange(props?.options);
        setSelectedAll(true);
      }
    } else {
      setSelectedAll(false);
      props?.onChange(props?.multiple ? selectedItemList : event);
    }
    ref.current.blur();
  };

  const onFocusTypeAhead = (event) => {
    setSelectedAll(false);
    if (props?.clearOnFocus) {
      ref.current.clear();
      ref.current.toggleMenu();
      if (!_.isEmpty(props?.selected)) {
        props?.handleClear();
      }
      if (_.isEmpty(props?.selected) && event.target.value) {
        event.target.value = '';
        props?.handleClear();
      }
    }
  };

  const customDropdownDesign = {};

  if (props?.multiple) {
    customDropdownDesign.renderMenu = (
      results,
      { newSelectionPrefix, paginationText, renderMenuItemChildren, ...menuProps },
      state,
    ) => {
      const dropDownOptions = props.selectAll ? [selectAllOption, ...props.options] : props.options;
      const items = dropDownOptions?.map((i, index) => (
        <MenuItem key={index} option={i} position={index}>
          {selectedAll || _.includes(props?.selected, i) ? (
            <Icon
              icon="checked"
              size={20}
              className="default"
              style={{ verticalAlign: 'top', color: '#17A2B8', marginRight: '5px' }}
            />
          ) : (
            <div
              style={{
                display: 'inline-block',
                width: '20px',
                marginRight: '5px',
              }}
            />
          )}
          <Highlighter search={state.text}>{i.value}</Highlighter>
        </MenuItem>
      ));

      return <Menu {...menuProps}>{items}</Menu>;
    };
  }

  const onclickIcon = () => {
    ref.current.clear();
    ref.current.toggleMenu();
    setSelectedAll(false);
    if (!_.isEmpty(props?.selected) && props?.clearOnFocus) {
      props?.handleClear();
    }
    if (_.isEmpty(props?.selected) && ref.current.state.text) {
      ref.current.state.text = '';
      props?.handleClear();
    }
  };

  return (
    <Row className="m-0">
      <Col className="position-relative p-0">
        <Typeahead
          ref={ref}
          id={props?.id}
          labelKey={props?.labelKey}
          inputProps={props?.inputProps}
          onChange={onChangeTypeAhead}
          options={props?.options}
          placeholder={props?.placeholder}
          selected={props?.selected}
          disabled={props?.disabled}
          onFocus={onFocusTypeAhead}
          onBlur={props?.onBlur}
          multiple={props?.multiple}
          onInputChange={props?.onInputChange}
          {...customDropdownDesign}
        />
        {props?.showDropDownIcon && !props?.disabled && (
          <ButtonToolbar className="interval-angle-icon">
            {(props.icontype === 'chevrondown') ? (
              <ChevronDown
                onClick={() => {
                  onclickIcon();
                }}
              />
            ) : (
              <ChevronExpand
                onClick={() => {
                  onclickIcon();
                }}
              />
            )}
          </ButtonToolbar>
        )}
      </Col>
    </Row>
  );
};
export default CustomTypeAhead;
