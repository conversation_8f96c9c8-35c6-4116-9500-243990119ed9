import React from 'react'

export default function FileSvg({fill = '#0091B8'}) {
  return (
    <div id='file-svg'>
        <svg viewBox="0 0 256 256" class="document-thumbnail"><path d="M71.923 43.923L69 46.846v80.942c0 80.028.023 80.972 2.045 83.543 1.126 1.43 3.713 3.063 5.75 3.629 5.021 1.394 102.389 1.394 107.41 0 2.037-.566 4.624-2.199 5.75-3.629 2.014-2.561 2.045-3.621 2.045-69.184V75.563l-17.282-17.281L157.437 41H74.846l-2.923 2.923M123 119.68c-.825.341-1.831 1.115-2.235 1.72-1.281 1.918-.898 26.067.435 27.4 1.71 1.71 11.348 1.485 14.894-.349 4.459-2.306 6.898-7.005 6.902-13.3.005-6.46-2.31-11.745-5.974-13.64-3.118-1.612-11.808-2.747-14.022-1.831m28.75.4c-1.535.894-1.75 2.74-1.75 15.001 0 12.022.229 14.07 1.636 14.61 3.047 1.169 4.847-1.112 5.166-6.544.302-5.131.313-5.147 3.715-5.147 2.246 0 3.738-.607 4.363-1.776 1.534-2.865-.466-5.224-4.43-5.224-2.992 0-3.45-.324-3.45-2.443 0-2.157.494-2.478 4.241-2.75 3.261-.236 4.327-.754 4.609-2.241.202-1.064-.442-2.526-1.432-3.25-2.118-1.549-10.157-1.698-12.668-.236m-56.312 1.22c-.274.716-.373 7.348-.218 14.738.258 12.371.433 13.467 2.214 13.808 2.513.483 4.566-2.399 4.566-6.409 0-2.926.314-3.232 3.909-3.806 8.424-1.348 11.958-10.918 6.168-16.708-2.641-2.641-3.561-2.923-9.531-2.923-4.501 0-6.768.415-7.108 1.3m6.562 8.2c0 3.551.999 4.205 4.418 2.893 3.23-1.239 1.12-6.393-2.618-6.393-1.39 0-1.8.796-1.8 3.5m25 5v8.5h2.845c5.001 0 7.751-5.257 6.024-11.512-1-3.623-3.312-5.488-6.803-5.488-1.926 0-2.066.576-2.066 8.5" fill="#fcfbfa" fill-rule="evenodd"></path><path d="M17.627 1.459C12.239 3.393 7.035 7.835 3.5 13.519l-3 4.824v219.314l3.021 4.887c3.525 5.701 9.967 10.942 15.254 12.41 2.457.683 39.656 1.037 109.297 1.04 119.529.007 111.488.541 120.004-7.974 2.586-2.587 5.204-6.392 5.906-8.583.693-2.165 1.655-4.364 2.139-4.887 1.16-1.253 1.161-214.842.001-214.125-.483.298-1.134-.733-1.445-2.291-.851-4.252-6.626-11.208-12.133-14.613L237.658.5 129.579.285C39.45.105 20.857.3 17.627 1.459M.482 128c0 59.125.121 83.313.268 53.75.147-29.563.147-77.938 0-107.5C.603 44.687.482 68.875.482 128m71.441-84.077L69 46.846v80.942c0 80.028.023 80.972 2.045 83.543 1.126 1.43 3.713 3.063 5.75 3.629 5.021 1.394 102.389 1.394 107.41 0 2.037-.566 4.624-2.199 5.75-3.629 2.014-2.561 2.045-3.621 2.045-69.184V75.563l-17.282-17.281L157.437 41H74.846l-2.923 2.923M123 119.68c-.825.341-1.831 1.115-2.235 1.72-1.281 1.918-.898 26.067.435 27.4 1.71 1.71 11.348 1.485 14.894-.349 4.459-2.306 6.898-7.005 6.902-13.3.005-6.46-2.31-11.745-5.974-13.64-3.118-1.612-11.808-2.747-14.022-1.831m28.75.4c-1.535.894-1.75 2.74-1.75 15.001 0 12.022.229 14.07 1.636 14.61 3.047 1.169 4.847-1.112 5.166-6.544.302-5.131.313-5.147 3.715-5.147 2.246 0 3.738-.607 4.363-1.776 1.534-2.865-.466-5.224-4.43-5.224-2.992 0-3.45-.324-3.45-2.443 0-2.157.494-2.478 4.241-2.75 3.261-.236 4.327-.754 4.609-2.241.202-1.064-.442-2.526-1.432-3.25-2.118-1.549-10.157-1.698-12.668-.236m-56.312 1.22c-.274.716-.373 7.348-.218 14.738.258 12.371.433 13.467 2.214 13.808 2.513.483 4.566-2.399 4.566-6.409 0-2.926.314-3.232 3.909-3.806 8.424-1.348 11.958-10.918 6.168-16.708-2.641-2.641-3.561-2.923-9.531-2.923-4.501 0-6.768.415-7.108 1.3m6.562 8.2c0 3.551.999 4.205 4.418 2.893 3.23-1.239 1.12-6.393-2.618-6.393-1.39 0-1.8.796-1.8 3.5m25 5v8.5h2.845c5.001 0 7.751-5.257 6.024-11.512-1-3.623-3.312-5.488-6.803-5.488-1.926 0-2.066.576-2.066 8.5" 
        fill={fill} fill-rule="evenodd"></path></svg>
    </div>
  )
}
