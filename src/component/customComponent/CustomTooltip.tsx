import React, { useRef } from 'react';
import { Tooltip, OverlayTrigger } from 'react-bootstrap';
const CustomTooltip = ({
  content = '',
  tooltipText = '',
  placement = 'bottom',
  id = 'desc_tooltip',
  className = '',
  onToggle = (isOpen) => {},
}) => {
  const target = useRef(null);
  return (
    <OverlayTrigger
      overlay={
        <Tooltip id={id} className={`tooltip ${className}`}>
          {tooltipText}
        </Tooltip>
      }
      target={target.current ?? undefined}
      placement={placement}
      onToggle={(isOpen) => {
        onToggle(isOpen);
      }}
    >
      <div ref={target}>{content}</div>
    </OverlayTrigger>
  );
};

export default CustomTooltip;
