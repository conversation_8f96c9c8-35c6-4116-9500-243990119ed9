import React from 'react';
import { Button, Modal } from 'react-bootstrap';

const CustomInfoModal = ({
  showModal,
  title = '',
  content = '',
  confirmText = 'OK',
  handleConfirm = () => {},
}) => {
  return (
    <Modal aria-labelledby="contained-modal-title-vcenter" centered show={showModal}>
      <Modal.Header>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body>{content}</Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          data-testid="fml-custom-info-modal-confirm-button"
          onClick={() => handleConfirm()}
        >
          {confirmText}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CustomInfoModal;
