import React from 'react';
import { Anchor } from 'react-bootstrap';
import { Icon } from '../../styleGuide';
const CustomDropDownItemCheck = React.forwardRef((props, ref) => {
  return (
    <Anchor className="dropdown-item" role="button" onClick={props.onClick}>
      {props.checked ? (
        <Icon
          icon="checked"
          size={20}
          className="default"
          style={{ verticalAlign: 'top', color: '#17A2B8', marginRight: '5px' }}
        />
      ) : (
        <div
          style={{
            display: 'inline-block',
            width: '20px',
            marginRight: '5px',
          }}
        />
      )}
      {props.children}
    </Anchor>
  );
});

export default CustomDropDownItemCheck;
