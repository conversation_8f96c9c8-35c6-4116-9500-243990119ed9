import React, { useEffect, useRef } from 'react';

const FileUploader = ({ onUpload, children, acceptFileTypes }) => {
  const drop = useRef(null);
  const inputFile = useRef(null);

  useEffect(() => {
    drop.current.addEventListener('dragover', handleDragOver);
    drop.current.addEventListener('drop', handleDrop);
    return () => {
      drop.current?.removeEventListener('dragover', handleDragOver);
      drop.current?.removeEventListener('drop', handleDrop);
    };
  }, []);

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const onSelectFileFromFileManager = (e) => {
    const files = [...e.target.files];
    if (files.length) {
      handleFileUpload(files);
    }
  };

  const onButtonClick = () => {
    inputFile.current.click();
  };

  const handleFileUpload = (files) => {
    onUpload(files);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const files = [...e.dataTransfer.files];
    handleFileUpload(files);
  };

  return (
    <div className="file-drag-and-drop-area" ref={drop}>
      {children}
      <input
        type="file"
        accept={acceptFileTypes}
        multiple={true}
        onChange={onSelectFileFromFileManager}
        onClick={(event) => {
          event.target.value = null;
        }}
        ref={inputFile}
      />
      <p className="browse-file" data-testid="browse-file-btn" onClick={onButtonClick} aria-hidden="true">
        Browse Files
      </p>
    </div>
  );
};

export default FileUploader;
