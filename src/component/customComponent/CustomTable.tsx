/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import _ from 'lodash';
import React, { useEffect, useMemo } from 'react';
import { Form } from 'react-bootstrap';
import { useSticky } from 'react-table-sticky';
import { useFlexLayout, useSortBy, useTable, usePagination } from 'react-table';
import styleGuide from '../../styleGuide';
import { fetchIcon, PageNum } from '../../util/view-utils';
import NoResult from '../NoResult';
import Spinner from '../Spinner';

const { Icon } = styleGuide;

const CustomTable = ({
  column = [],
  reportData = [],
  tableRef = null,
  isLoading = false,
  pageCount = 0,
  totalCount = 0,
  setPageNo = () => {},
  setSortData = () => {},
  pagination = false,
  pageSizeList = [10, 20, 50, 100],
  setPageListSize = () => {},
  pageNo,
  className = '',
  showData = true,
  showHeader = true,
  name = '',
  highlightedRows = [],
  pageSize = 10,
  paginationComponent = null
}) => {
  const defaultColumns = useMemo(() => column, [column]);
  return (
    <div className={`vessel-table ${className}`}>
      <Table
        columns={defaultColumns}
        data={reportData}
        tableRef={tableRef}
        isLoading={isLoading}
        totalPageCount={pageCount}
        totalCount={totalCount}
        setSortData={setSortData}
        setPageNo={setPageNo}
        pagination={pagination}
        pageSizeList={pageSizeList}
        setPageListSize={setPageListSize}
        pageNo={pageNo}
        showData={showData}
        showHeader={showHeader}
        highlightedRows={highlightedRows}
        name={name}
        defaultPageSize={pageSize}
        paginationComponent={paginationComponent}
      />
    </div>
  );
};

const Table = ({
  columns,
  data,
  tableRef,
  isLoading,
  totalPageCount,
  totalCount,
  setSortData,
  setPageNo,
  pagination,
  pageSizeList,
  setPageListSize,
  pageNo,
  showData,
  showHeader,
  highlightedRows,
  name,
  defaultPageSize,
  paginationComponent,
}) => {
  const defaultColumn = useMemo(
    () => ({
      minWidth: 30,
      maxWidth: 120,
    }),
    [],
  );

  let {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    rows,
    gotoPage,
    setPageSize,
    canPreviousPage,
    canNextPage,
    pageCount,
    state: { pageIndex, sortBy },
  } = useTable(
    {
      columns,
      defaultColumn,
      data,
      manualPagination: true,
      manualSortBy: true,
      autoResetPage: false,
      autoResetSortBy: false,
      isLoading,
      pageCount: totalPageCount,
    },
    useSortBy,
    useFlexLayout,
    useSticky,
    usePagination,
  );

  if (pageNo !== undefined) {
    pageIndex = pageNo;
    canPreviousPage = pageNo;
    canNextPage = pageNo + 1 < totalCount / defaultPageSize;
  }

  useEffect(() => {
    if (!_.isEmpty(sortBy)) {
      setSortData(sortBy);
    }
  }, [sortBy]);

  const pageSwitch = async (pageNo) => {
    setPageNo(pageNo);
    gotoPage(pageNo);
  };

  const pageSizeSwitch = async (pageSize) => {
    setPageSize(pageSize);
    setPageListSize(pageSize);
  };

  const filterPages = (visiblePages, totalPages) =>
    visiblePages.filter((page) => page <= totalPages);
  const getVisiblePages = (page, total) => {
    if (total < 7) {
      return filterPages([1, 2, 3, 4, 5, 6], total);
    }
    if (page % 5 >= 0 && page > 4 && page + 2 < total) {
      return [1, page - 1, page, page + 1, total];
    }
    if (page % 5 >= 0 && page > 4 && page + 2 >= total) {
      return [1, total - 3, total - 2, total - 1, total];
    }
    return [1, 2, 3, 4, 5, total];
  };
  const visiblePages = getVisiblePages(pageIndex, pageCount);

  const checkContainHighlight = (index) => {
    const data = highlightedRows.filter((item) => {
      if (_.includes(item?.list, index)) {
        return item?.class;
      }
    });
    return data[0]?.class;
  };

  return (
    <>
      {pagination && (
        <div className="d-flex pl-2 no-print pagination-count-wrapper">
          <div className="page-number-border">
            <PageNum
              onClick={() => (canPreviousPage ? pageSwitch(pageIndex - 1) : '')}
              disabled={!canPreviousPage}
              dataTestId="fml-pagination-previous"
            >
              {'<'}
            </PageNum>
            {visiblePages.map((page, index, array) => (
              <PageNum
                key={index}
                active={page - 1 === pageIndex}
                disabled={page - 1 === pageIndex}
                onClick={() => pageSwitch(page - 1)}
                dataTestId={`fml-pagination-${page}`}
              >
                {array[index - 1] + 2 < page ? `...${page}` : page}
              </PageNum>
            ))}
            <PageNum
              onClick={() => (canNextPage ? pageSwitch(pageIndex + 1) : '')}
              disabled={!canNextPage}
              dataTestId="fml-pagination-next"
            >
              {'>'}
            </PageNum>
          </div>
          <Form inline="true">
            <Form.Control
              as="select"
              value={defaultPageSize}
              className="ml-3"
              onChange={(e) => {
                pageSizeSwitch(Number(e.target.value));
              }}
              data-testid="fml-select-pageSize"
            >
              {pageSizeList.map((pageSize) => (
                <option key={pageSize} value={pageSize} data-testid={`fml-show-${pageSize}`}>
                  Show {pageSize}
                </option>
              ))}
            </Form.Control>
          </Form>
          <div className="list-count">
            <b>{totalCount}</b> Results
          </div>
        </div>
      )}
      <div
        {...getTableProps()}
        className="table sticky"
        data-test-id={`fml-${name}-custom-table`}
        ref={tableRef}
      >
        {showHeader && (
          <div className="header">
            {headerGroups.map((headerGroup, index) => (
              <div key={index} {...headerGroup.getHeaderGroupProps()} className="tr">
                {headerGroup.headers.map((column, index2) => {
                  const thProps = {
                    ...column.getHeaderProps(column.getSortByToggleProps({ title: undefined })),
                  };

                  return (
                    <div
                      key={index2}
                      {...thProps}
                      className={`th d-flex justify-left-center align-items-center border-line ${column.headerClassName} ${column.customClass}`}
                    >
                      <div className="text-left">{column.render('Header')}</div>
                      {column.canSort && (
                        <Icon
                          icon={fetchIcon(column)}
                          data-testid="fml-custom-table-sort"
                          size={20}
                          className="default float-none"
                        />
                      )}
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        )}
        {isLoading && <Spinner alignClass={'spinner-table'} />}
        {showData ? (
          <div {...getTableBodyProps()} className="body">
            {rows.length > 0 && !isLoading
              ? rows.map((row, index) => {
                  prepareRow(row);

                  return (
                    <div
                      key={index}
                      {...row.getRowProps()}
                      className={`tr ${
                        !_.isEmpty(highlightedRows) && checkContainHighlight(index)
                      } `}
                    >
                      {row.cells.map((cell, index2) => {
                        const tdProps = cell.getCellProps();

                        return (
                          <div
                            key={index2}
                            {...tdProps}
                            data-testid={`${cell.column.Header}-${index}-exp`}
                            className={`td ${
                              row?.original?.vessel_ref_id ? 'vessel-company-type' : ''
                            } ${cell.column.customClass}`}
                          >
                            {cell.render('Cell')}
                          </div>
                        );
                      })}
                    </div>
                  );
                })
              : !isLoading && <NoResult />}
          </div>
        ) : null}
      </div>
      {paginationComponent && paginationComponent()}
      {pagination && (
        <div className="d-flex p-2 no-print">
          <div className="page-number-border">
            <PageNum
              onClick={() => (canPreviousPage ? pageSwitch(pageIndex - 1) : '')}
              disabled={!canPreviousPage}
            >
              {'<'}
            </PageNum>
            {visiblePages.map((page, index, array) => (
              <PageNum
                key={index}
                active={page - 1 === pageIndex}
                disabled={page - 1 === pageIndex}
                onClick={() => pageSwitch(page - 1)}
              >
                {array[index - 1] + 2 < page ? `...${page}` : page}
              </PageNum>
            ))}
            <PageNum
              onClick={() => (canNextPage ? pageSwitch(pageIndex + 1) : '')}
              disabled={!canNextPage}
            >
              {'>'}
            </PageNum>
          </div>
          <Form inline="true">
            <Form.Control
              as="select"
              value={defaultPageSize}
              className="ml-3"
              onChange={(e) => {
                pageSizeSwitch(Number(e.target.value));
              }}
            >
              {pageSizeList.map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  Show {pageSize}
                </option>
              ))}
            </Form.Control>
          </Form>
        </div>
      )}
    </>
  );
};

export default CustomTable;
