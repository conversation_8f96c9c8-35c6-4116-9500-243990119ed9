import React from 'react';
import { Col, Form, Row } from 'react-bootstrap';

const CustomGmtInput = ({ label, setFieldValue, form, smt, type }) => {
  return (
    <>
      <Form.Group className="form-group">
        <Form.Label className="text-capitalize">{label}</Form.Label>
        <Row className="align-items-center">
          {type === 'gmt' && (
            <Col md={3} className="pr-0">
              <Form.Control
                as="select"
                className="form-select"
                data-testid="fml-custom-gmt-input-symbol"
                value={form?.gmt_sign}
                onChange={(e) =>
                  setFieldValue({ label: label }, e.target.value, 'gmt', 'gmt_symbol')
                }
              >
                <option value="+">+</option>
                <option value="-">-</option>
              </Form.Control>
            </Col>
          )}

          <Col md={3} className="pr-0">
            <Form.Control
              as="select"
              className="form-select"
              data-testid="fml-custom-gmt-input-hours"
              value={form?.gmt_hrs}
              onChange={(e) => setFieldValue({ label: label }, e.target.value, 'gmt', 'gmt_hrs')}
            >
              {[...Array(15)].map((item, index) => (
                <option key={index} value={index.toString().padStart(2, '0')}>
                  {index.toString().padStart(2, '0')}
                </option>
              ))}
            </Form.Control>
          </Col>

          <Col md={1} className="text-center p-0">
            hrs
          </Col>

          <Col md={3} className="pr-1">
            <Form.Control
              as="select"
              value={form?.gmt_mins}
              data-testid="fml-custom-gmt-input-minutes"
              onChange={(e) => setFieldValue({ label: label }, e.target.value, 'gmt', 'gmt_mins')}
            >
              {['00', '30', '45'].map((item, index) => (
                <option key={index} value={item}>
                  {item}
                </option>
              ))}
            </Form.Control>
          </Col>

          <Col md={1} className="text-center p-0">
            mins
          </Col>
        </Row>
      </Form.Group>
      <Form.Group className="form-group">
        <Form.Label className="text-capitalize">
          {type === 'gmt' ? 'Report Date Time (SMT)' : 'Report Time'}
        </Form.Label>
        <Form.Control
          as="input"
          data-testid="fml-custom-gmt-input-disable-text"
          value={type === 'time' ? [form.gmt_hrs, form.gmt_mins].join(':') : smt}
          disabled={true}
        />
      </Form.Group>
    </>
  );
};

export default CustomGmtInput;
