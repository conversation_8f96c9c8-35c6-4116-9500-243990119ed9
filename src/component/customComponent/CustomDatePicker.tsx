import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { formatDate, stringAsDate } from '../../util/view-utils';

const CustomDatePicker = ({
  name,
  value,
  onChange,
  placeholder = 'Please select',
  hasDuplicates,
  disabled,
  showTimeSelect = false,
  dataTestId,
  customProps = {},
  filterDate,
  activeTab,
  wrapclassname = "",
  icon = '',
}) => {
  const onDateChange = (date) => {
    if (activeTab === 'mrv') onChange(formatDate(date, `YYYY`.trim(), ''));
    else onChange(formatDate(date, `DD MMM YYYY ${showTimeSelect ? 'HH:mm' : ''}`.trim(), ''));
  };

  let className = '';
  let wrapperClassName = wrapclassname;

  if (hasDuplicates) {
    className = 'form-control is-invalid warning';
  }

  if (disabled) {
    className = 'date-picker-disabled';
    wrapperClassName = `date-picker-wrapper-disabled ${wrapclassname}`;
  }

  if (activeTab === 'mrv') {
    return (
      <div className={wrapperClassName}>
        <DatePicker
          name={name}
          customInput={<input data-testid={dataTestId} type="text" />}
          isClearable={!disabled}
          peekNextMonth
          showMonthDropdown={false} // Do not show month dropdown for MRV
          dateFormatCalendar=" "
          showYearDropdown
          showYearPicker
          dropdownMode="select"
          selected={stringAsDate(value, !showTimeSelect)}
          onChange={onDateChange}
          showTimeSelect={showTimeSelect}
          timeIntervals={15}
          timeFormat="HH:mm"
          placeholderText={placeholder}
          dateFormat={`yyyy`}
          className={className}
          autoComplete={'none'}
          disabled={disabled}
          filterDate={filterDate}
          {...customProps}
        />
      </div>
    );
  } else {
    return (
      <div className={wrapperClassName}>
        <DatePicker
          name={name}
          customInput={<input data-testid={dataTestId} type="text" />}
          isClearable={!disabled}
          peekNextMonth
          showMonthDropdown
          dateFormatCalendar=" "
          showYearDropdown
          dropdownMode="select"
          selected={stringAsDate(value, !showTimeSelect)}
          onChange={onDateChange}
          showTimeSelect={showTimeSelect}
          timeIntervals={15}
          timeFormat="HH:mm"
          placeholderText={placeholder}
          dateFormat={`d MMM yyyy ${showTimeSelect ? 'HH:mm' : ''}`.trim()}
          className={className}
          autoComplete={'none'}
          disabled={disabled}
          filterDate={filterDate}
          {...customProps}
        />
        {icon}
      </div>
    );
  }
};

export default CustomDatePicker;