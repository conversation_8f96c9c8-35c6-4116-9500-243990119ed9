import React from 'react';

export const CustomCalendarIcon = ({
  fill = '#6C757D',
  width = 20,
  height = 20,
  classname = 'custom-calander-icon',
  style = {},
  ref = null,
}) => {
  return (
    <div id="custom-calander-icon" ref={ref} className={classname} style={style}>
      <svg
        width={width}
        height={height}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.19999 3.69999C7.19999 3.47909 7.0209 3.29999 6.79999 3.29999C6.57909 3.29999 6.39999 3.47909 6.39999 3.69999V4.79999H4.99999C4.06111 4.79999 3.29999 5.56111 3.29999 6.49999V15.1C3.29999 16.0389 4.06111 16.8 4.99999 16.8H15.2C16.1389 16.8 16.9 16.0389 16.9 15.1V6.49999C16.9 5.56111 16.1389 4.79999 15.2 4.79999H14.2V3.69999C14.2 3.47909 14.0209 3.29999 13.8 3.29999C13.5791 3.29999 13.4 3.47909 13.4 3.69999V4.79999H7.19999V3.69999ZM4.99999 5.59999H15.2C15.697 5.59999 16.1 6.00294 16.1 6.49999V7.49999H4.09999V6.49999C4.09999 6.00294 4.50294 5.59999 4.99999 5.59999ZM4.09999 15.1V8.29999H16.1V15.1C16.1 15.597 15.697 16 15.2 16H4.99999C4.50294 16 4.09999 15.597 4.09999 15.1Z"
          fill={fill}
          stroke="#6C757D"
          stroke-width="0.2"
        ></path>
      </svg>
    </div>
  );
};

export const CustomSearchIcon = ({
  width = 20,
  height = 20,
  fill = 'none',
  classname = '',
  style = {},
}) => {
  return (
    <div id="custom-search-icon" className={classname} style={style}>
      <svg
        width={width}
        height={height}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2658_50232)">
          <g clipPath="url(#clip1_2658_50232)">
            <path
              d="M15.853 15.146L12.441 11.734C13.2075 10.7864 13.5813 9.58079 13.4852 8.36575C13.3891 7.15071 12.8304 6.01886 11.9245 5.20354C11.0185 4.38822 9.83421 3.95158 8.6158 3.98364C7.39739 4.01571 6.23772 4.51403 5.37588 5.37588C4.51403 6.23772 4.01571 7.39739 3.98365 8.6158C3.95158 9.83421 4.38822 11.0185 5.20354 11.9245C6.01886 12.8304 7.15071 13.3891 8.36575 13.4852C9.58079 13.5813 10.7864 13.2075 11.734 12.441L15.146 15.853C15.1921 15.9008 15.2473 15.9388 15.3083 15.965C15.3693 15.9913 15.4349 16.005 15.5013 16.0056C15.5677 16.0062 15.6335 15.9936 15.695 15.9684C15.7564 15.9433 15.8123 15.9061 15.8592 15.8592C15.9061 15.8123 15.9433 15.7564 15.9684 15.695C15.9936 15.6335 16.0062 15.5677 16.0056 15.5013C16.005 15.4349 15.9913 15.3693 15.9651 15.3083C15.9388 15.2473 15.9008 15.1921 15.853 15.146ZM8.75 12.497C8.00819 12.497 7.28305 12.277 6.6663 11.8648C6.04955 11.4526 5.56891 10.8668 5.28517 10.1814C5.00143 9.49597 4.92734 8.74182 5.07228 8.01431C5.21721 7.2868 5.57467 6.61861 6.09941 6.09429C6.62416 5.56996 7.29263 5.21304 8.02025 5.06869C8.74788 4.92433 9.50197 4.99902 10.1871 5.28331C10.8723 5.5676 11.4578 6.04871 11.8695 6.66579C12.2812 7.28287 12.5006 8.00819 12.5 8.75C12.4981 9.74372 12.1024 10.6962 11.3994 11.3985C10.6965 12.1009 9.74372 12.4959 8.75 12.497Z"
              fill="#333333"
            />
          </g>
        </g>
        <defs>
          <clipPath id="clip0_2658_50232">
            <rect width={width} height={height} fill={fill}></rect>
          </clipPath>
          <clipPath id="clip1_2658_50232">
            <rect width="12" height="12" fill={fill} transform="translate(4 4)"></rect>
          </clipPath>
        </defs>
      </svg>
    </div>
  );
};
