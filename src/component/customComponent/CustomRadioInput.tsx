import React from 'react';
import { Form, Row, Col } from 'react-bootstrap';

const CustomRadioInput = ({ label, value, onChange, disabled }) => {  
  return (
    <Form.Group className="form-group">
      <Form.Label className="text-capitalize">{label}</Form.Label>

      <Row>
        {['Yes', 'No'].map((item, index) => (
          <Col md={3} key={index}>
            <Form.Check
              type="radio"
              label={item}
              id="default-radio"
              data-testid={`fml-custom-radio-input-${item}`}
              checked={item === value}
              onChange={(e) => onChange(e.currentTarget.checked && item)}
              disabled={disabled}
            />
          </Col>
        ))}
      </Row>
    </Form.Group>
  );
};

export default CustomRadioInput;
