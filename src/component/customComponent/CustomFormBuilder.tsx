import _ from 'lodash';
import React, { useCallback, useEffect, useState, useRef } from 'react';
import { Card, Col, Form, Row } from 'react-bootstrap';
import CustomTypeAhead from './CustomTypeAhead';
import CustomCoordinateInput from './CustomCoordinateInput';
import CustomGmtInput from './CustomGmtInput';
import CustomRadioInput from './CustomRadioInput';
import CustomDatePicker from './CustomDatePicker';
import moment from 'moment';
import CustomCountryPortDropDown from './CustomCountryPortDropDown';
import { validateForm } from '../../util/validateFormFields';
import { Icon } from '../../styleGuide';
import { bdnFuels } from '../../constants/bdn-fuels';

const CustomFormBuilder = ({
  form,
  setForm,
  formData,
  setLoading = () => {},
  errors,
  setErrors,
  setValidationError,
  setIsEditable,
  reportVersion = 1,
  showCard,
  setShowCard,
}) => {
  const [disableInputs, setDisableInputs] = useState(false);
  const currentFieldValue = useRef(null);
  const handleKeyPress = (precision, event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
    if (precision === 0 && event.key === '.') {
      event.preventDefault();
    }
  };

  const setCountryInfo = (country, port) => {
    const data = [];
    if (!_.isEmpty(country)) data.push(country[0]?.value);
    if (!_.isEmpty(port)) data.push(port[0]?.value);
    return data.join(', ');
  };

  // set value to fields
  const setFieldValue = (data, value, type = '', customPath = '') => {
    setValidationError();
    setIsEditable(true);
    currentFieldValue.current = value[0]?.value ? value[0]?.value : currentFieldValue.current;
    if (!_.isEmpty(type)) {
      if (type === 'radio' && value === 'No') {
        setErrors({});
      } else {
        delete errors[customPath];
        setErrors({ ...validateForm(type, data, errors, value, customPath) });
      }
    }

    const effectivePath =
      data.path_v3 && data.path_v3.trim() !== ''
        ? data.path_v3
        : data.path_v2 && data.path_v2.trim() !== ''
        ? data.path_v2
        : data.path;

    const currentFormData = {
      ...form,
      [['countryport', 'coordinate', 'gmt', 'degree', 'minute'].includes(type)
        ? customPath
        : effectivePath]: value,
    };

    if (customPath === 'country') {
      currentFormData.port = [];
      currentFormData.countryPortInfo = setCountryInfo(value, []);
    }

    if (customPath === 'port') {
      currentFormData.countryPortInfo = setCountryInfo(currentFormData.country, value);
    }

    if (
      (type === 'radio' && data.validation_json.related && value === 'No') ||
      (type === 'typeahead' && data.validation_json.related)
    ) {
      // clear all errors
      setErrors({});
    }

    if (
      (type === 'radio' && data.validation_json.related && value === 'No') ||
      (type === 'typeahead' && data.validation_json.related && value[0]?.value === 'N/A')
    ) {
      data.validation_json.radioRel?.map((relItem) => {
        if (relItem.type === 'number') {
          formData[relItem.path] = 0;
        } else formData[relItem.path] = '';
      });
    }

    if (
      type === 'typeahead' &&
      data.validation_json.related &&
      !bdnFuels.includes(value[0]?.value)
    ) {
      setShowCard(false);
      setForm({ ...currentFormData });
      // return;
    } else {
      setShowCard(true);
      setForm({ ...formData });
    }

    if (
      type === 'radio' &&
      data.name.includes('APPLICABLE') &&
      data.group_label === 'consumption' &&
      value === 'No'
    ) {
      setDisableInputs(true);
      const updatedFormData = { ...currentFormData };

      Object.keys(updatedFormData).forEach((key) => {
        if (
          key.toLowerCase().includes('hfo') ||
          key.toLowerCase().includes('lfo') ||
          key.toLowerCase().includes('mgo') ||
          key.toLowerCase().includes('emission') ||
          key.toLowerCase().includes('report_json.cons.ce.cons') ||
          key.toLowerCase().includes('report_json.cons.igg.cons') ||
          key.toLowerCase().includes('consumed')
        ) {
          updatedFormData[key] = '0.00';
        }
        if (
          key.toLowerCase().includes('report_json.cons.ce.fuel3_desc') ||
          key.toLowerCase().includes('report_json.cons.igg.fuel3_desc') ||
          key.toLowerCase().includes('report_json.consumption.cargo_engine.fuel3') ||
          key.toLowerCase().includes('report_json.consumption.inert_gas_generator.fuel3')
        ) {
          updatedFormData[key] = [];
        }
        if (key.toLowerCase().includes('fuel3_type')) {
          updatedFormData[key] = '';
        }
      });

      setForm({ ...updatedFormData });
      return;
    } else {
      setDisableInputs(false);
    }

    if (
      type === 'radio' &&
      data.name.includes('APPLICABLE') &&
      data.group_label === 'consumption' &&
      value === 'No'
    ) {
      setDisableInputs(true);
    } else {
      setDisableInputs(false);
    }

    setForm({ ...currentFormData });
  };

  const disableFormField = (path, type) => {
    if (type === 'radio') {
      return form[path] !== 'Yes';
    }
    if (type === 'typeahead') {
      return form[path]?.[0]?.value === 'N/A';
    }
  };

  // form item render

  const formItem = useCallback(
    (data) => {
      const { label, path, validation_json, path_v2, path_v3 } = data;
      const {
        type,
        options,
        min,
        max,
        limit,
        smt,
        precision,
        subType,
        pathRel,
        pathRelType,
        maxLength,
        portLabel,
        countryLabel,
        step,
      } = validation_json;
      let { disabled } = validation_json;
      data.reportVersion = reportVersion;
      let isOtherFuelType = false;
      let isCargoUnChanged = true;
      if (
        (data.name.includes('FUEL3TYPE') || data.name.includes('FUEL3EMISSION')) &&
        reportVersion === 1
      ) {
        isOtherFuelType = true;
      } else if (
        (data.name.includes('FUEL3TYPE') || data.name.includes('FUEL3EMISSION')) &&
        form[data.validation_json.pathRel]?.[0]?.value !== 'Fuel not listed'
      ) {
        isOtherFuelType = true;
      }
      if (reportVersion === 1 && data.name === 'BUNKERFUEL3') {
        disabled = true;
      }
      if (data.name === 'CARGONAME' && form['report_json.cargo.load_dis_flag'] !== 'Yes') {
        isCargoUnChanged = false;
      }

      const applicableRel = validation_json?.related?.find((element) =>
        element.includes('APPLICABLE'),
      );
      if (applicableRel) {
        disabled = false;
      }

      if (
        Object.keys(form).some((key) => key.endsWith('applicable') && form[key] === 'No') &&
        (data.edit_label.endsWith('Fuel3') ||
          data.name.includes('CONSUMED') ||
          data.name.includes('HFO') ||
          data.name.includes('MGO') ||
          data.name.includes('LFO') ||
          data.edit_label.endsWith('Fuel3 Type') ||
          data.edit_label.includes('Other Fuel Type') ||
          data.path_v2.endsWith('fuel3_emission'))
      ) {
        disabled = true;
      }

      const effectivePath =
        path_v3 && path_v3.trim() !== ''
          ? path_v3
          : path_v2 && path_v2.trim() !== ''
          ? path_v2
          : path;

      switch (type) {
        case 'typeahead':
          if (subType === 'countryport') {
            return (
              <>
                <CustomCountryPortDropDown
                  required={true}
                  labelPort={portLabel}
                  labelCountry={countryLabel}
                  countryData={form.country ?? []}
                  portData={form.port ?? []}
                  errorTextCountry={errors.country}
                  errorTextPort={errors.port}
                  offset={12}
                  onChangeCountry={(e) => {
                    setFieldValue(data, e, subType, 'country');
                    setLoading(true);
                  }}
                  handleClearCountry={() => {
                    setFieldValue(data, [], subType, 'country');
                  }}
                  onChangePort={(e) => {
                    setFieldValue(data, e, subType, 'port');
                  }}
                  handleClearPort={() => {
                    setFieldValue(data, [], subType, 'port');
                  }}
                  disablePort={false}
                  afterPortFetch={() => {
                    setLoading(false);
                  }}
                />
                <div className="d-flex justify-content-between">
                  <Form.Control
                    value={form?.countryPortInfo}
                    data-testid="fml-compare-reports-country-port-info"
                    disabled={true}
                  />
                  <Icon
                    icon="remove"
                    size={20}
                    className="remove my-auto mx-2"
                    onClick={() => {
                      setFieldValue(data, [], subType, 'country');
                    }}
                    data-testid={`fml-compare-reports-country-port-remove`}
                  />
                </div>
              </>
            );
          } else {
            return (
              <Form.Group className="form-group">
                <Form.Label>{label}</Form.Label>
                <CustomTypeAhead
                  id="basic-typeahead-single"
                  labelKey="value"
                  name={effectivePath}
                  placeholder="Please select"
                  inputProps={{
                    'data-testid': `fml-custom-form-component-typeahead-${effectivePath}`,
                  }}
                  multiple={false}
                  options={options ?? []}
                  showDropDownIcon={true}
                  clearOnFocus={true}
                  disabled={disabled || disableInputs}
                  selected={form[effectivePath] ?? []}
                  onChange={(e) => setFieldValue(data, e, type)}
                  handleClear={() => setFieldValue(data, [], type)}
                />
                <div className="validate-error">{errors[effectivePath]}</div>
              </Form.Group>
            );
          }
        case 'number':
          return (
            <Form.Group className="form-group">
              <Form.Label className="text">{label}</Form.Label>
              <Form.Control
                type="number"
                data-testid={`fml-custom-form-component-number-${effectivePath}`}
                min={min}
                max={max}
                maxLength={maxLength}
                name={effectivePath}
                onKeyPress={(e) => handleKeyPress(precision, e)}
                className="form-main-control"
                disabled={
                  disabled ||
                  disableFormField(pathRel, pathRelType) ||
                  isOtherFuelType ||
                  disableInputs
                }
                value={form[effectivePath] ?? ''}
                onChange={(e) => setFieldValue(data, e.target.value, type, effectivePath)}
                isInvalid={Boolean(errors[effectivePath]?.length)}
                step={step}
              />
              <div className="validate-error">{errors[effectivePath]}</div>
            </Form.Group>
          );
        case 'textarea':
          return (
            <Form.Group className="form-group">
              <Form.Label className="text-capitalize">{label}</Form.Label>
              <Form.Control
                as="textarea"
                maxLength={limit}
                data-testid={`fml-custom-form-component-textarea-${effectivePath}`}
                required
                disabled={disabled || disableFormField(pathRel, pathRelType) || !isCargoUnChanged}
                name={effectivePath}
                value={form[effectivePath] ?? ''}
                onChange={(e) => setFieldValue(data, e.target.value, type)}
              />
              <div className="validate-error">{errors[effectivePath]}</div>
            </Form.Group>
          );

        case 'coordinate':
          if (subType !== 'position') {
            return (
              <CustomCoordinateInput
                label={subType}
                setFieldValue={setFieldValue}
                form={form}
                errors={errors}
              />
            );
          } else {
            return (
              <>
                <CustomCoordinateInput
                  label={'latitude'}
                  setFieldValue={setFieldValue}
                  form={form}
                  errors={errors}
                />
                <CustomCoordinateInput
                  label={'longitude'}
                  setFieldValue={setFieldValue}
                  form={form}
                  errors={errors}
                />
              </>
            );
          }

        case 'radio':
          return (
            <CustomRadioInput
              label={label}
              value={form[effectivePath]}
              onChange={(e) => setFieldValue(data, e, type)}
              disabled={disabled || disableFormField(pathRel, pathRelType)}
            />
          );

        case 'input':
          return (
            <Form.Group className="form-group">
              <Form.Label className="text-capitalize">{label}</Form.Label>
              <Form.Control
                data-testid={`fml-custom-form-component-input-${effectivePath}`}
                name={effectivePath}
                disabled={isOtherFuelType || disableInputs || disabled}
                value={form[effectivePath] ?? ''}
                onChange={(e) => setFieldValue(data, e.target.value, type)}
              />
              <div className="validate-error">{errors[effectivePath]}</div>
            </Form.Group>
          );

        case 'time':
          return (
            <CustomGmtInput
              type={type}
              label={label}
              smt={smt}
              setFieldValue={setFieldValue}
              form={form}
            />
          );

        case 'datetime':
          if (subType === 'gmt') {
            return (
              <CustomGmtInput
                type={subType}
                label={label}
                smt={smt}
                setFieldValue={setFieldValue}
                form={form}
              />
            );
          } else {
            return (
              <Form.Group className="form-group">
                <Form.Label className="text-capitalize">{label}</Form.Label>
                <CustomDatePicker
                  value={form[effectivePath]}
                  data-testid={`fml-custom-form-component-date-picker-${effectivePath}`}
                  onChange={(e) => setFieldValue(data, e, type)}
                  disabled={disabled || disableFormField(pathRel, pathRelType)}
                  showTimeSelect={true}
                />
                <div className="validate-error">{errors[effectivePath]}</div>
              </Form.Group>
            );
          }
      }
    },
    [formData, form, errors],
  );

  // 1. updating value according to type
  useEffect(() => {
    if (!_.isEmpty(formData)) {
      const dataItems = {};
      formData?.map((item) => {
        const { validation_json, path, value, editValue, path_v2, path_v3 } = item;
        const newPath = path_v3 ?? path_v2 ?? path;
        const fieldValue = editValue ?? value;
        if (validation_json?.type === 'typeahead') {
          if (validation_json?.subType === 'countryport') {
            dataItems.country = validation_json.countryValue;
            dataItems.port = validation_json.portValue;
            dataItems.countryPortInfo = validation_json.countryPortInfo;
          } else {
            const valueTypeahead = fieldValue
              ? validation_json?.options.find((optionItem) => optionItem.value === fieldValue)
              : undefined;
            dataItems[newPath] = valueTypeahead ? [valueTypeahead] : [];
          }
        } else if (validation_json.type === 'datetime') {
          if (validation_json.subType === 'gmt') {
            dataItems.gmt_symbol = '+';
            dataItems.gmt_hrs = '00';
            dataItems.gmt_mins = '00';
          } else {
            dataItems[newPath] = moment(fieldValue).isValid()
              ? moment(fieldValue).format('DD MMM YYYY HH:mm:ss')
              : '';
          }
        } else if (validation_json.type === 'coordinate') {
          if (validation_json.subType === 'position') {
            let [co1, co2, dir] = validation_json?.latitude?.match(/.{1,2}/g) || null;
            dataItems['latitude-1'] = co1;
            dataItems['latitude-2'] = co2;
            dataItems['latitude-direction'] = dir;
            co1 = validation_json?.longitude?.substring(0, 3);
            [co2, dir] = validation_json?.longitude?.substring(3)?.match(/.{1,2}/g) || null;
            dataItems['longitude-1'] = co1;
            dataItems['longitude-2'] = co2;
            dataItems['longitude-direction'] = dir;
          } else {
            const [co1, co2, dir] = fieldValue?.split(':') || '';
            dataItems[`${validation_json.subType}-1`] = co1;
            dataItems[`${validation_json.subType}-2`] = co2;
            dataItems[`${validation_json.subType}-direction`] = dir;
          }
        } else if (validation_json.type === 'time') {
          const [hrs, mins] = fieldValue.split(':') || '';
          dataItems.gmt_hrs = hrs ?? '00';
          dataItems.gmt_mins = mins ?? '00';
        } else dataItems[newPath] = fieldValue;
      });

      setForm(dataItems);
    }
  }, [formData]);

  const getFormFields = () => {
    currentFieldValue.current =
      currentFieldValue.current === null || currentFieldValue.current === undefined
        ? formData.filter((x) => x?.label?.includes('Fuel3 Type'))[0]?.value
        : currentFieldValue.current;
    let isFuelNotListed = false;

    if (
      currentFieldValue.current !== null &&
      currentFieldValue.current !== undefined &&
      bdnFuels.includes(currentFieldValue.current)
    ) {
      isFuelNotListed = true;
    }

    const bdnItems = formData.filter((item) => item.validation_json?.isBDN);
    const nonBdnItems = formData.filter((item) => !item.validation_json?.isBDN);

    const cardStyle = {
      padding: '5px',
    };

    if (isFuelNotListed) {
      setShowCard(true);
      const fuelNotListedItems = nonBdnItems.filter(
        (item) => item.label.includes('Fuel3 Type') || item.name.includes('APPLICABLE'),
      );
      const remainingNonBdnItems = nonBdnItems.filter(
        (item) => !item.label.includes('Fuel3 Type') && !item.name.includes('APPLICABLE'),
      );
      return (
        <>
          {fuelNotListedItems.length > 0 &&
            fuelNotListedItems.map((item, index) => (
              <div key={`custom-element-nonbdn-${index}`}>{formItem(item)}</div>
            ))}
          {showCard && (
            <>
              Bunker Delivery Note (BDN) Overview
              <Card style={cardStyle}>
                <div className="row">
                  {bdnItems.map((item, index) => (
                    <div
                      key={`custom-element-${index}`}
                      className={item.name.includes('BDNUID') ? 'col-12' : 'col-6'}
                    >
                      {formItem(item)}
                    </div>
                  ))}
                </div>
              </Card>
            </>
          )}
          {remainingNonBdnItems.length > 0 &&
            remainingNonBdnItems.map((item, index) => (
              <div key={`custom-element-nonbdn-${index}`}>{formItem(item)}</div>
            ))}
        </>
      );
    } else {
      setShowCard(false);
      return (
        <>
          {nonBdnItems.map((item, index) => (
            <div key={`custom-element-${index}`}>{formItem(item)}</div>
          ))}
        </>
      );
    }
  };

  return (
    <Form className="form-main-control">
      <Row>
        <Col md={12}>{getFormFields()}</Col>
      </Row>
    </Form>
  );
};

export default CustomFormBuilder;
