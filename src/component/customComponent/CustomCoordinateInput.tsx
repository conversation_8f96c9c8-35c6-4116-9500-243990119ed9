import React from 'react';
import { Col, Form, Row } from 'react-bootstrap';

const CustomCoordinateInput = ({ label, setFieldValue, form, errors }) => {
  const direction =
    label === 'longitude'
      ? [
          { value: 'East', id: 'E' },
          { value: 'West', id: 'W' },
        ]
      : [
          { value: 'North', id: 'N' },
          { value: 'South', id: 'S' },
        ];
  return (
    <>
      <Form.Group className="form-group">
        <Form.Label className="text-capitalize">{label}</Form.Label>
        <Row>
          <Col md={5}>
            <Form.Control
              type="number"
              data-testid={`fml-custom-co-ordinate-input-${label}-1`}
              required
              min={0}
              max={label === 'latitude' ? 89 : 179}
              name={`${label}-1`}
              className="form-main-control"
              value={form[`${label}-1`] ?? ''}
              onChange={(e) =>
                setFieldValue(
                  {
                    label: `${label}-degree`,
                    validation_json: {
                      precision: 2,
                      min: 0,
                      max: label === 'latitude' ? 89 : 179,
                    },
                  },
                  e.target.value,
                  'degree',
                  `${label}-1`,
                )
              }
            />
            <div className="validate-error">{errors[`${label}-1-degree`]}</div>
          </Col>

          <Col md={1} className="text-center">
            :
          </Col>

          <Col md={5}>
            <Form.Control
              type="number"
              data-testid={`fml-custom-co-ordinate-input-${label}-2`}
              required
              min={0}
              max={59}
              name={`${label}-2`}
              className="form-main-control"
              value={form[`${label}-2`] ?? ''}
              onChange={(e) =>
                setFieldValue(
                  {
                    label: `${label}-minute`,
                    validation_json: { precision: 2, min: 0, max: 59 },
                  },
                  e.target.value,
                  'minute',
                  `${label}-2`,
                )
              }
            />
            <div className="validate-error">{errors[`${label}-2-minute`]}</div>
          </Col>
        </Row>
      </Form.Group>
      <Form.Group className="form-group">
        <Row>
          {direction?.map((item, index) => (
            <Col md={2} key={index}>
              <Form.Check
                type="radio"
                label={item.value}
                id="default-radio"
                data-testid={`fml-admin-certificate-visible-${label}`}
                checked={item.id === form[`${label}-direction`]}
                onChange={(e) =>
                  setFieldValue(
                    { label: label },
                    e.currentTarget.checked && item.id,
                    'coordinate',
                    `${label}-direction`,
                  )
                }
              />
            </Col>
          ))}
        </Row>
      </Form.Group>
    </>
  );
};

export default CustomCoordinateInput;
