import React from 'react';
import { Button, Form, Modal } from 'react-bootstrap';
import { Typeahead } from 'react-bootstrap-typeahead';

const CustomCopyModal = ({
  showCopyModal,
  setShowCopyModal,
  selectedCopyVessel,
  setSelectedCopyVessel,
  vesselLists,
  modalHeading,
  handleCopy,
}) => {
  return (
    <Modal
      aria-labelledby="contained-modal-title-vcenter"
      centered
      show={showCopyModal}
      onHide={() => setShowCopyModal(false)}
    >
      <Modal.Header>
        <Modal.Title>Copy {modalHeading} from Vessel</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form.Label className="font-weight-bold">Vessel</Form.Label>
        <Typeahead
          id="basic-typeahead-single"
          labelKey="value"
          onChange={setSelectedCopyVessel}
          options={vesselLists}
          placeholder="Please select"
          selected={selectedCopyVessel}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          onClick={() => {
            setShowCopyModal(false);
            setSelectedCopyVessel([]);
          }}
        >
          Cancel
        </Button>
        <Button variant="secondary" onClick={() => handleCopy()}>
          Copy
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CustomCopyModal;
