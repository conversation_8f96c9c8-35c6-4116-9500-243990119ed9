import React from 'react';
import { But<PERSON>, Modal, OverlayTrigger, Toolt<PERSON> } from 'react-bootstrap';

const ConfirmModal = ({
  showConfirmModal,
  setShowConfirmModal = () => { },
  title = '',
  content,
  cancelText = 'Cancel',
  confirmText = 'Confirm',
  handleCancel = () => { },
  handleConfirm = () => { },
  isDisabledConfirm = false,
  hideCancel = false,
  isDisabledCancel = false,
  children,
  customClassName = '',
  customBtnClass = '',
  error = '',
  confirmButtonTooltipText = '',
  id = '',
  headerClassName="",
  footerClassName=""
}) => {
  return (
    <Modal
      id = {id} 
      aria-labelledby="contained-modal-title-vcenter"
      centered
      show={showConfirmModal}
      onHide={() => setShowConfirmModal(false)}
      data-testid="fml-confirm-dialog"
      className={customClassName}
    >
      <Modal.Header className={headerClassName}>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      {error && <div className='alert-danger ml-3' style={{ padding: '10px 20px', color: '#dc3545', marginRight: '20px' }}>{error}</div>}
      <Modal.Body>{content}</Modal.Body>
      {children && <Modal.Body>{children}</Modal.Body>}
      <Modal.Footer className={footerClassName}>
        <Button
          variant="primary"
          data-testid="fml-custom-confirm-modal-cancel-button"
          onClick={() => handleCancel()}
          hidden={hideCancel}
          disabled={isDisabledCancel}
          className={customBtnClass}
        >
          {cancelText}
        </Button>
        {confirmButtonTooltipText ? <OverlayTrigger
          overlay={
            <Tooltip id="desc_tooltip" className="tooltip-header">
              {confirmButtonTooltipText}
            </Tooltip>
          }
          placement="bottom"
        >
          <Button
            variant="secondary"
            data-testid="fml-custom-confirm-modal-confirm-button"
            onClick={() => handleConfirm()}
            disabled={isDisabledConfirm}
            className={customBtnClass}
          >
            {confirmText}
          </Button>
        </OverlayTrigger> : <Button
          variant="secondary"
          data-testid="fml-custom-confirm-modal-confirm-button"
          onClick={() => handleConfirm()}
          disabled={isDisabledConfirm}
          className={customBtnClass}
        >
          {confirmText}
        </Button>}
      </Modal.Footer>
    </Modal>
  );
};

export default ConfirmModal;
