import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import { Col, Row, Form } from 'react-bootstrap';
import { VesselContext } from '../../context/VesselContext';
import { getPortsByCountry } from '../../service/reference-service';
import CustomTypeAhead from './CustomTypeAhead';

const CustomCountryPortDropDown = ({
  countryData = [],
  portData = [],
  onChangeCountry = () => {},
  onChangePort = () => {},
  handleClearCountry = () => {},
  handleClearPort = () => {},
  errorTextCountry = '',
  errorTextPort = '',
  disableCountry = false,
  disablePort = false,
  required,
  labelCountry = 'Country',
  labelPort = 'Port',
  filterPortBy = '',
  rowData = [],
  formUpdate = (e) => {},
  afterPortFetch = () => {},
  offset,
  icontype = "",
}) => {
  const { countries } = useContext(VesselContext);

  const [portOptions, setPortOptions] = useState([]);

  const fetchFlagPorts = async (value) => {
    setPortOptions([]);
    if (!_.isEmpty(value)) {
      const getPortsResponse = await getPortsByCountry(`countryCode=${value[0]?.id}`);

      let portList = !_.isEmpty(filterPortBy.toString())
        ? getPortsResponse.data.ports?.filter((port) => port[`${filterPortBy}`])
        : getPortsResponse.data.ports;
      portList = portList.map((item) => ({ id: item.city_code, value: item.name }));

      setPortOptions(portList);
      afterPortFetch();
    } else setPortOptions([]);
  };

  const updateRowInfo = () => {
    const { port } = rowData;
    const dropDownInformation = port ? port.split(',') : '';
    const isCountryValueAccurate = countries?.some(function (el) {
      return el.value == dropDownInformation[0];
    });
    let updatedData = {
      ...rowData,
      country: [dropDownInformation[0] && isCountryValueAccurate ? dropDownInformation[0] : ''],
      port_info: [
        dropDownInformation[1] && isCountryValueAccurate ? dropDownInformation[1].trim() : '',
      ],
    };
    formUpdate(updatedData);
  };

  useEffect(() => {
    updateRowInfo();
  }, []);

  useEffect(() => {
    if (!_.isEmpty(countryData) && !_.isEmpty(portOptions) && portOptions.length === 1)
      onChangePort([...portOptions]);
  }, [countryData, portOptions]);

  useEffect(() => {
    if (!_.isEmpty(countryData)) {
      fetchFlagPorts(countryData);
    }
  }, [countryData]);

  return (
    <Row className="m-0">
      <Col className="p-0" md={offset}>
        <Form.Group className="form-group">
          <Form.Label>
            {labelCountry}
            {required && '*'}
          </Form.Label>
          <CustomTypeAhead
            id="basic-typeahead-single"
            labelKey="value"
            name="country"
            placeholder="Please select"
            inputProps={{ 'data-testid': 'fml-custom-component-country' }}
            multiple={false}
            options={countries}
            showDropDownIcon={true}
            disabled={disableCountry}
            clearOnFocus={true}
            selected={countryData}
            icontype={icontype}
            onChange={(e) => {
              onChangeCountry(e);
              fetchFlagPorts(e);
            }}
            handleClear={() => {
              handleClearCountry();
              setPortOptions([]);
            }}
          />
        </Form.Group>
        <div className="validate-error">{errorTextCountry}</div>
      </Col>
      <Col className={`${!offset ? 'ml-2' : ''} p-0`} md={offset}>
        <Form.Group className="form-group">
          <Form.Label>
            {labelPort}
            {required && '*'}
          </Form.Label>
          <CustomTypeAhead
            id="basic-typeahead-single"
            labelKey="value"
            name="port"
            placeholder="Please select"
            inputProps={{ 'data-testid': 'fml-custom-component-port' }}
            multiple={false}
            options={portOptions}
            showDropDownIcon={true}
            disabled={disablePort}
            clearOnFocus={true}
            selected={portData}
            onChange={(e) => onChangePort(e)}
            handleClear={() => handleClearPort()}
            icontype={icontype}
          />
          <div className="validate-error">{errorTextPort}</div>
        </Form.Group>
      </Col>
    </Row>
  );
};
export default CustomCountryPortDropDown;
