import React, { useState } from 'react';
import Spinner from '../Spinner';
import { Icon } from '../../styleGuide';
import { Form } from 'react-bootstrap';

const CustomFileUpload = ({
  form,
  dataTestId = '',
  setFieldValue = (field, value) => {},
  edit = false,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const uploadFormField = (
    <label
      htmlFor="upload-document"
      data-testid={`fml-${dataTestId}-document`}
      className="border border-primary rounded p-2 text-primary"
    >
      Upload
    </label>
  );
  const renderFormField = () => {
    return dialogOpen ? <Spinner alignClass="upload-document" /> : uploadFormField;
  };
  return (
    <div className={`${form.inactive ? 'disable-area' : ''}`}>
      <Form.Control
        type="file"
        id="upload-document"
        className="d-none"
        onClick={() => {
          setDialogOpen(true);
          window.addEventListener('focus', () => {
            setTimeout(() => setDialogOpen(false), 70000);
            window.removeEventListener('focus', () => {});
          });
        }}
        onChange={(e) => {
          setDialogOpen(false);
          setFieldValue('file', e.target.files[0]);
        }}
      />
      {form.file?.name ? (
        <div className="d-flex justify-content-between">
          <p className="text-primary text-decoration-underline w-75 text-truncate">
            {form.file.name}
          </p>
          {!edit && (
            <Icon
              icon="remove"
              size={20}
              className="remove"
              onClick={() => setFieldValue('file', null)}
              dataTestId={`fml-${dataTestId}-remove-document`}
            />
          )}
        </div>
      ) : (
        renderFormField()
      )}
    </div>
  );
};

export default CustomFileUpload;
