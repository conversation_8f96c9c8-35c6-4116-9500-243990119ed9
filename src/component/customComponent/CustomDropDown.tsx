import React, { useState, useEffect } from 'react';
import { DropdownButton, Dropdown, Form } from 'react-bootstrap';

const CustomDropDown = ({
  selectedItems = '',
  itemsList = [],
  onchange = (a, b) => {},
  styleitemdiv = { maxHeight: '20vh', overflowY: 'auto' }
}) => {
  const [selected, setSelected] = useState();
  // Handle clearing all selections
  const handleClearAll = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setSelected(null);
    onchange('', e);
  };

  const handleOnChange = (id, value) => {
    setSelected(value);
    onchange(id, value);
  };

  // Construct the dropdown title based on selected iteams groups
  const dropdownTitle = selected ?? 'Please Select';
  
  useEffect(() => {
    const list = itemsList.filter(e => e.id === selectedItems);
    if (list.length > 0) {
      setSelected(list[0]?.value)
    } else {
      setSelected(selectedItems)
    }
    
    return () => {
      setSelected('')
    }
  }, [selectedItems])
  

  return (
    <DropdownButton
      title={dropdownTitle}
      className="cert-group-dropdown flex-item"
      variant="light"
      drop="down"
    >
      <div style={styleitemdiv}>
        {itemsList.map(({id, value}) => (
          <Dropdown.Item
            eventKey={value}
            key={value}
            onClick={() => handleOnChange(id, value)} // Prevent dropdown closing
          >
            <Form.Label>{value}</Form.Label>
          </Dropdown.Item>
        ))}

        <Dropdown.Item
          eventKey="clear-all"
          className="text-primary text-underline"
          onClick={handleClearAll} // Clear all selected filters
          key={'clear'}
        >
          Clear
        </Dropdown.Item>
      </div>
    </DropdownButton>
  );
};

export default CustomDropDown;
