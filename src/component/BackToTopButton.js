import React, { useState } from 'react';
import styleGuide from '../styleGuide';
import PropTypes from 'prop-types';

const { Icon } = styleGuide;

const ScrollArrow = ({ onClick = () => {} }) => {
  const [showScroll, setShowScroll] = useState(false);

  const checkScrollTop = () => {
    if (!showScroll && window.scrollY > 400) {
      setShowScroll(true);
    } else if (showScroll && window.scrollY <= 400) {
      setShowScroll(false);
    }
  };
  const scrollTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    onClick();
  };

  window.addEventListener('scroll', checkScrollTop);

  return (
    <div className="back-to-top">
      <Icon
        icon="up-filled"
        size={40}
        onClick={scrollTop}
        style={{ display: showScroll ? 'flex' : 'none' }}
      />
      <span
        onClick={scrollTop}
        aria-hidden="true"
        style={{ display: showScroll ? 'flex' : 'none' }}
        data-testid="fml-back-to-top"
      >
        Back to top
      </span>
    </div>
  );
};

ScrollArrow.propTypes = {
  onClick: PropTypes.func,
};

export default ScrollArrow;
