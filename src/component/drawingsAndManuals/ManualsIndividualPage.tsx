import React, { useEffect, useState } from 'react';
import { Link, useParams, useHistory } from 'react-router-dom';
import vesselService from '../../service/vessel-service';
import { Icon } from '../../styleGuide';
import getURLParams, { base64_encode } from '../../util/getURLParams';
import { formatDate, formatValue } from '../../util/view-utils';
import CustomTable from '../customComponent/CustomTable';
import ErrorAlert from '../ErrorAlert';
import ManualDialog from './ManualDialog';

const { PARIS2_URL } = process.env;

const ManualsIndividualPage = () => {
  const { ownershipId } = useParams();
  const history = useHistory();
  const reportId = getURLParams('reportId', history.location.search);
  const reportType = getURLParams('reportType', history.location.search);
  const [loading, setLoading] = useState(false);
  const [manualVesselList, setManualVesselList] = useState([]);
  const [rowData, setRowData] = useState({});
  const [showManualModal, setShowManualModal] = useState(false);
  const [error, setError] = useState(null);
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([{ id: 'report_date', desc: true }]);

  const columns = [
    {
      Header: 'No.',
      accessor: (row, index) => index + 1,
      sticky: 'left',
      id: 'id',
      name: 'id',
      type: 'text',
      maxWidth: 30,
      disableSortBy: true,
    },
    {
      Header: 'Vessel',
      accessor: (row) => (
        <Link className="button-link" to={`/vessel/ownership/details/${row.vessel_ownership_id}`}>
          {formatValue(row.vessel_name)}
        </Link>
      ),
      id: 'vessel_name',
      name: 'vessel_name',
      type: 'text',
      maxWidth: 120,
    },
    {
      Header: 'Date Uploaded',
      accessor: (row) => formatDate(row.report_date, 'DD MMM YYYY'),
      id: 'report_date',
      name: 'report_date',
      type: 'date',
      maxWidth: 80,
    },
    {
      Header: 'Remarks',
      accessor: (row) => formatValue(row.remarks),
      id: 'remarks',
      name: 'remarks',
      type: 'text',
      disableSortBy: true,
      maxWidth: 120,
    },
    {
      Header: 'Document',
      id: 'document',
      type: 'item',
      accessor: (row) =>
        row.url ? (
          <Link
            className="button-link"
            to={{
              pathname: `${PARIS2_URL}/vessel/document?source=drawing&path=${base64_encode(
                row.url,
              )}&id=${row.id}`,
            }}
            target="_blank"
          >
            View
          </Link>
        ) : (
          '---'
        ),
      disableSortBy: true,
      maxWidth: 60,
    },
    {
      Header: 'Action',
      accessor: (row, index) => (
        <div className="text-center">
          <Icon
            icon="Edit"
            data-testid={`fml-drawing-and-manual-list-edit-${index}`}
            size={20}
            style={{ color: 'black' }}
            onClick={() => {
              setRowData(row);
              setShowManualModal(true);
            }}
          />
        </div>
      ),
      id: 'action',
      name: 'action',
      type: 'text',
      disableSortBy: true,
      maxWidth: 40,
    },
  ];

  const fetchAssignedVesselsToManuals = async (
    sortData = [{ id: 'report_date', desc: true }],
    pageIndex = 0,
    pageSize = 10,
  ) => {
    setLoading(true);
    try {
      const response = await vesselService.getAssignedVesselsListToManuals(reportId, {
        sortBy: sortData,
        pageSize: pageSize,
        pageIndex: pageIndex,
      });
      setManualVesselList(response.data.results);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
      setError(null);
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchAssignedVesselsToManuals();
  }, []);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchAssignedVesselsToManuals(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    setPageSize(value);
    setPageIndex(0);
    fetchAssignedVesselsToManuals(sortData, 0, value);
  };

  const onSortChange = (value) => {
    setSortData(value);
    fetchAssignedVesselsToManuals(value, pageIndex, pageSize);
  };

  const handleModalCancel = () => {
    setShowManualModal(false);
    setRowData({});
  };

  const handleModalSubmit = () => {
    fetchAssignedVesselsToManuals();
    setRowData({});
  };

  return (
    <div className="container">
      <div className="d-flex justify-content-between no-print">
        <div className="font-weight-bold technical-reports mb-4">{reportType}</div>
        <Icon
          icon="close"
          size={30}
          onClick={() => history.push(`/vessel/ownership/details/${ownershipId}/manuals`)}
        />
      </div>

      {error && <ErrorAlert message={error} />}
      <CustomTable
        column={columns}
        reportData={manualVesselList}
        tableRef={null}
        isLoading={loading}
        pagination={true}
        pageCount={pageCount}
        totalCount={totalCount}
        pageNo={pageIndex}
        setPageNo={onPageIndexChange}
        setPageListSize={onPageSizeChange}
        setSortData={onSortChange}
        name="fml-manuals-individual-page"
        pageSize={pageSize}
      />

      <ManualDialog
        showModal={showManualModal}
        handleModalSubmit={handleModalSubmit}
        handleModalCancel={handleModalCancel}
        setPageError={setError}
        editData={rowData}
        inactiveShow={true}
      />
    </div>
  );
};

export default ManualsIndividualPage;
