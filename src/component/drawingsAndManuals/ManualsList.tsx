import _ from 'lodash';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { DetailContext } from '../../context/DetailContext';
import vesselService from '../../service/vessel-service';
import { Icon } from '../../styleGuide';
import { formatDate, formatValue } from '../../util/view-utils';
import CustomTable from '../customComponent/CustomTable';
import ErrorAlert from '../ErrorAlert';
import ManualDialog from './ManualDialog';
import { base64_encode } from '../../util/getURLParams';

const { PARIS2_URL } = process.env;

const ManualsList = () => {
  const {
    ownershipId,
    ga4EventTrigger = () => {},
    vesselName,
    handleError = () => {},
    error,
    setError = () => {},
    roleConfig,
  } = useContext(DetailContext);
  const [loading, setLoading] = useState(false);
  const [rowData, setRowData] = useState({});
  const [manualsListData, setManualsListData] = useState([]);
  const [showManualModal, setShowManualModal] = useState(false);
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([{ id: 'report_date', desc: true }]);
  const [showInactive, setshowInactive] = useState(true);

  const columns = useMemo(() => [
    {
      Header: 'No.',
      accessor: (row, index) => index + 1,
      sticky: 'left',
      id: 'id',
      name: 'id',
      type: 'text',
      maxWidth: 30,
      disableSortBy: true,
    },
    {
      Header: 'Manual Type',
      accessor: (row) => (
        <Link
          to={`/vessel/${ownershipId}/manuals/assigned-vessels?reportId=${row.report_type_id}&reportType=${row.report_type}`}
          className="button-link"
          onClick={() => {
            ga4EventTrigger(
              'View Manual Type on all vessels',
              'Drawing and Manuals - link',
              _.get(row, 'report_type'),
            );
          }}
        >
          {formatValue(row.report_type)}
        </Link>
      ),
      id: 'report_type',
      name: 'report_type',
      type: 'text',
      maxWidth: 120,
    },
    {
      Header: 'Date Uploaded',
      accessor: (row) => formatDate(row.report_date, 'DD MMM YYYY'),
      id: 'report_date',
      name: 'report_date',
      type: 'date',
      maxWidth: 80,
    },
    {
      Header: 'Remarks',
      accessor: (row) => <div className="line-text-truncate">{formatValue(row.remarks)}</div>,
      id: 'remarks',
      name: 'remarks',
      type: 'text',
      disableSortBy: true,
      maxWidth: 120,
    },
    {
      Header: 'Document',
      id: 'document',
      type: 'item',
      accessor: (row) =>
        row.url ? (
          <Link
            className="button-link"
            onClick={() => {
              ga4EventTrigger(
                'View Manual',
                'Drawings and Manuals - link',
                _.get(row, 'report_type'),
              );
            }}
            to={{
              pathname: `${PARIS2_URL}/vessel/document?source=drawing&path=${base64_encode(
                row.url,
              )}&id=${row.id}`,
            }}
            target="_blank"
          >
            View
          </Link>
        ) : (
          '---'
        ),
      disableSortBy: true,
      maxWidth: 60,
    },
    ...(roleConfig.vessel.edit ?[{
      Header: 'Action',
      accessor: (row, index) => (
        <div className="text-center">
          <Icon
            icon="Edit"
            data-testid={`fml-drawing-and-manual-list-edit-${index}`}
            size={20}
            style={{ color: 'black' }}
            onClick={() => {
              ga4EventTrigger(
                'Edit Manual',
                'Drawings and Manuals - menu',
                _.get(row, 'report_type'),
              );
              setRowData(row);
              setShowManualModal(true);
            }}
          />
        </div>
      ),
      id: 'action',
      name: 'action',
      type: 'text',
      disableSortBy: true,
      maxWidth: 40,
    }] : []),
  ], [roleConfig]);

  const fetchManualsList = async (
    sortData = [{ id: 'report_date', desc: true }],
    pageIndex = 0,
    pageSize = 10,
  ) => {
    setLoading(true);
    try {
      const response = await vesselService.getManualsList(ownershipId, {
        sortBy: sortData,
        pageSize: pageSize,
        pageIndex: pageIndex,
      });
      setManualsListData(response.data.results);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
      setError(null);
    } catch (error) {
      handleError(error.response);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchManualsList();
  }, []);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchManualsList(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', 'Drawings and Manuals - list', value);
    setPageSize(value);
    setPageIndex(0);
    fetchManualsList(sortData, 0, value);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Drawings and Manuals - list', value[0]?.id);
    setSortData(value);
    fetchManualsList(value, pageIndex, pageSize);
  };

  const handleModalCancel = () => {
    setShowManualModal(false);
    setRowData({});
    setshowInactive(true);
  };

  const handleModalSubmit = () => {
    fetchManualsList();
    setRowData({});
    setshowInactive(true);
  };

  return (
    <div>
      {roleConfig.vessel.edit && <div className="d-flex justify-content-end no-print">
        <Button
          size="md"
          variant="outline-primary"
          className="mr-2"
          data-testid="fml-manual-list-add-Button"
          onClick={() => {
            ga4EventTrigger('Add Manual', 'Drawings and Manuals - menu', vesselName);
            setShowManualModal(true);
            setshowInactive(false);
          }}
        >
          Add
        </Button>
      </div>}

      {error && <ErrorAlert message={error} />}
      <CustomTable
        column={columns}
        reportData={manualsListData}
        tableRef={null}
        isLoading={loading}
        pagination={true}
        pageCount={pageCount}
        totalCount={totalCount}
        pageNo={pageIndex}
        setPageNo={onPageIndexChange}
        setPageListSize={onPageSizeChange}
        setSortData={onSortChange}
        name="fml-manual-list"
        pageSize={pageSize}
      />

      <ManualDialog
        showModal={showManualModal}
        handleModalCancel={handleModalCancel}
        handleModalSubmit={handleModalSubmit}
        setPageError={setError}
        editData={rowData}
        inactiveShow={showInactive}
      />
    </div>
  );
};

export default ManualsList;
