import _ from 'lodash';
import moment from 'moment';
import React, { useContext, useEffect, useState } from 'react';
import { Button, Form, Modal, Col, Row } from 'react-bootstrap';
import { DetailContext } from '../../context/DetailContext';
import vesselService from '../../service/vessel-service';
import CustomDatePicker from '../../component/customComponent/CustomDatePicker';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';
import ErrorAlert from '../ErrorAlert';
import CustomFileUpload from '../customComponent/CustomFileUpload';
import { SlashCircle } from 'react-bootstrap-icons';

const ManualDialog = ({
  showModal,
  editData,
  handleModalCancel,
  setPageError,
  handleModalSubmit,
  inactiveShow
}) => {
  const {
    vesselName,
    manualTypes,
    vesselList,
    ownershipId,
    ga4EventTrigger = () => { },
  } = useContext(DetailContext);
  const vesselData = vesselList?.find((item) => item.id === Number(ownershipId));
  const edit = !_.isEmpty(editData);
  const [errors, setErrors] = useState({});
  const [form, setForm] = useState({
    vessel_list: vesselData,
    report_date: moment().format('YYYY-MM-DD'),
  });
  const [initialEditData, setInitialEditData] = useState();
  const [loading, setLoading] = useState(false);
  const [validationError, setValidationError] = useState();

  const validateManualType = (value) => ({
    manual_type: _.isEmpty(value) ? 'Manual Type is a required field' : undefined,
  });

  const validateReportDate = (value) => ({
    report_date: _.isEmpty(value) ? 'Date is a required field' : undefined,
  });

  const validateFile = (value) => {
    const requiredSize = 100;
    if (value?.size && value?.size / 1000000 > requiredSize) {
      return { file: `Cannot upload a file larger than ${requiredSize}MB` };
    }
    if (_.isEmpty(value?.name)) {
      return { file: 'File is a required field' };
    }
    return { file: undefined };
  };

  const validateForm = (field, value) => {
    let newErrors = {
      ...errors,
    };
    switch (field) {
      case 'manual_type':
        newErrors = { ...newErrors, ...validateManualType(value) };
        break;
      case 'report_date':
        newErrors = { ...newErrors, ...validateReportDate(value) };
        break;
      case 'file':
        newErrors = { ...newErrors, ...validateFile(value) };
        break;
    }
    newErrors = _.pickBy(newErrors, (i) => i !== undefined);
    setErrors(newErrors);
  };

  const setFieldValue = (field, value) => {
    validateForm(field, value);
    setForm({
      ...form,
      [field]: value,
    });
  };

  const initialValidateError = () => ({
    ..._.omitBy(errors, (v) => v == null),
    ...validateManualType(form?.manual_type),
    ...validateReportDate(form?.report_date),
    ...validateFile(form?.file),
  });

  const handleAfterSubmit = () => {
    handleModalSubmit();
    handleCancel();
  };

  const uploadFile = async (data) => {
    let url = '';
    let preSignedUrl = '';
    try {
      const response = await vesselService.getPreSignedUploadLink({
        record_type: 'drawing',
        payload: data,
        action: edit ? 'replace' : 'upload',
        file_name: form.file.name,
      });
      url = response.data.url;
      preSignedUrl = response.data.pre_signed_link;
    } catch (e) {
      setValidationError(`${e?.response.data}. Please try again.`);
      setLoading(false);
    }
    if (url) {
      try {
        await vesselService.uploadPresignedDocument(preSignedUrl, form.file, form.file.type);
      } catch (e) {
        url = '';
        setValidationError(`${e?.response.data}. Please try again.`);
        setLoading(false);
      }
    }
    return url;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    let errorList = { ...initialValidateError() };
    errorList = _.pickBy(errorList, (i) => i !== undefined);
    if (_.isEmpty(errorList)) {
      setLoading(true);
      let submitFormData = {
        report_date: moment(form.report_date).format('YYYY-MM-DD'),
        report_type_id: form.manual_type[0].id,
        remarks: !_.isEmpty(form.remarks) ? form.remarks : null,
        url: form.file?.name,
        vessel_ownership_id: form.vessel_list?.id,
        inactive: form?.inactive,
      };

      try {
        if (edit) {
          submitFormData = _.omit(submitFormData, ['url']);
          submitFormData.file = form.file;
          submitFormData = _.fromPairs(
            _.differenceWith(_.toPairs(submitFormData), _.toPairs(initialEditData), _.isEqual),
          );
          submitFormData.id = editData.id;
          if (submitFormData?.file) {
            submitFormData = _.omit(submitFormData, ['file']);
            submitFormData.url = await uploadFile(submitFormData);
            if (submitFormData?.url) {
              await vesselService.editManualsReport(submitFormData);
              handleAfterSubmit();
            }
          } else {
            await vesselService.editManualsReport(submitFormData);
            handleAfterSubmit();
          }
        } else {
          ga4EventTrigger(
            'Submit Add Manual',
            'Drawings and Manuals - menu',
            form.vessel_list?.value,
          );
          submitFormData.url = await uploadFile(submitFormData);
          if (!_.isEmpty(submitFormData?.url)) {
            await vesselService.addManualsReport(submitFormData);
            handleAfterSubmit();
          }
        }
      } catch (error) {
        if (error?.response?.status === 400) {
          setValidationError(`${error?.response.data}. Please try again.`);
          setLoading(false);
        } else {
          handleCancel();
          setPageError('Something went wrong with updating manual report list. Please try later');
        }
      }
    } else {
      setErrors(errorList);
    }
  };

  useEffect(() => {
    if (edit && editData) {
      const formData = {
        vessel_list: { id: editData.vessel_ownership_id, value: editData.vessel_name },
        report_date: moment().format('YYYY-MM-DD'),
        manual_type: [{ id: editData.report_type_id, value: editData.report_type }],
        remarks: editData?.remarks,
        file: { name: editData.url },
      };

      const initialData = {
        report_date: moment(editData.report_date).format('YYYY-MM-DD'),
        file: { name: editData.url },
        report_type_id: editData.report_type_id,
        remarks: editData.remarks,
        vessel_ownership_id: editData.vessel_ownership_id,
      };
      setForm(formData);
      setInitialEditData(initialData);
    }
  }, [editData]);

  const handleCancel = () => {
    setForm({ vessel_list: vesselData, report_date: moment().format('YYYY-MM-DD') });
    setErrors({});
    setValidationError();
    handleModalCancel();
    setLoading(false);
  };

  return (
    <Modal
      id="agent-popup"
      show={showModal}
      aria-labelledby="agent-modal"
      centered
      size="lg"
      backdrop="static"
      data-testid="fml-manual-dialog"
    >
      <Modal.Header>
        <Modal.Title style={{ borderBottom: '0' }}>
          {!edit ? `New Manual for ${vesselName}` : `Edit Manual for ${vesselName}`}
          <div className="required-field-text">* Required fields</div>
        </Modal.Title>
      </Modal.Header>
      {validationError && <ErrorAlert message={validationError} />}

      <CustomOverlayLoader active={loading}>
        <Form className="form-main-control">
          <Modal.Body>
            <Form.Group className="form-group">
              <Form.Label className="from-label">Ship*</Form.Label>
              <Form.Control
                type="text"
                data-testid="fml-manual-dialog-manual-vessel"
                value={form?.vessel_list?.value}
                name="vessel_list"
                disabled={true}
              />
            </Form.Group>

            <Form.Group className="form-group">
              <Form.Label className="from-label">Manual Type*</Form.Label>
              <CustomTypeAhead
                id="basic-typeahead-single"
                labelKey="value"
                name="manual_type"
                placeholder="Please select"
                inputProps={{ 'data-testid': 'fml-manual-dialog-manual-type' }}
                multiple={false}
                options={manualTypes}
                selected={form.manual_type ? form.manual_type : []}
                onChange={(e) => setFieldValue('manual_type', e)}
                handleClear={() => setFieldValue('manual_type', [])}
                showDropDownIcon={true}
                clearOnFocus={true}
              />
              <div className="validate-error">{errors.manual_type}</div>
            </Form.Group>

            <Form.Group className="form-group">
              <Form.Label className="from-label">Date*</Form.Label>
              <CustomDatePicker
                value={form?.report_date}
                data-testid={'fml-manual-dialog-reportDate'}
                disabled={true}
              />
              <div className="validate-error">{errors.report_date}</div>
            </Form.Group>

            <Form.Group className="form-group">
              <Form.Label className="from-label">Remark</Form.Label>
              <Form.Control
                as="textarea"
                maxLength="100"
                data-testid="fml-manual-dialog-remark"
                value={form.remarks ?? ''}
                onChange={(e) => {
                  setFieldValue(
                    'remarks',
                    !_.isEmpty(e.target.value.trim()) ? e.target.value : '',
                  );
                }}
              />
              <div className="validate-error">{errors.remarks}</div>
            </Form.Group>

            <Form.Group className="form-group">
              <Form.Label className="from-label">Document*</Form.Label>
              <CustomFileUpload
                form={form}
                dataTestId={'drawings-and-manuals'}
                setFieldValue={setFieldValue}
              />
              <div className="validate-error">{errors.file}</div>
            </Form.Group>

            {inactiveShow && (
              <Form.Group>
                <Col>
                  <Row>
                    <Form.Label className="from-label">
                      Mark as Inactive
                    </Form.Label>
                  </Row>
                  <Row className="align-items-center">
                    <SlashCircle
                      color={form?.inactive ? 'red' : 'grey'}
                      data-testid="fml-drawings-and-manuals-inactive"
                      size={20}
                      onClick={(event) => {
                        setFieldValue('inactive', !form?.inactive);
                      }}
                      name="inactive"
                    />
                    <p className="pl-1 m-0">Inactive</p>
                  </Row>
                  {form?.inactive === true && <Row>
                    <Form.Label className="disable-area">This will remove the item from vessel
                    </Form.Label>
                  </Row>}
                </Col>
              </Form.Group>)}
          </Modal.Body>

          <Modal.Footer style={{ borderTop: '0', width: '100%' }}>
            <div className="ml-auto">
              <Button
                variant="primary"
                data-testid="fml-manual-dialog-cancel"
                className="m-2"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                variant="secondary"
                data-testid="fml-manual-dialog-save"
                type="submit"
                onClick={handleSubmit}
              >
                Save
              </Button>
            </div>
          </Modal.Footer>
        </Form>
      </CustomOverlayLoader>
    </Modal>
  );
};
export default ManualDialog;
