import React from 'react';
import moment from 'moment';
import _ from 'lodash';
import { formatDate, assignReplaceUser, highlightText } from '../../util/view-utils';
import { EORB_PRODUCT_NAME, EORB_STATUSES } from '../../model/constants';

const formatMoment = (date) => {
  if (date) {
    return moment(date);
  }
  return '';
};

const generateAssignReplaceUserDiv = (subroles, accessorParams) => (
  <div>
    {subroles.map((subrole, index) => {
      const user = {
        subrole,
        ownershipid: accessorParams.row.id,
        tech_group: accessorParams.row.fleet_staff?.tech_group,
        ...(index < accessorParams.values.length ? accessorParams.values[index] : '---'),
      };
      const { type, label } =
        Array.isArray(accessorParams.type) &&
        Array.isArray(accessorParams.label) &&
        accessorParams.type.length === accessorParams.label.length
          ? { type: accessorParams.type[index], label: accessorParams.label[index] }
          : { type: accessorParams.type, label: '' };
      return (
        <React.Fragment key={subrole}>
          {assignReplaceUser(
            user,
            accessorParams.setAssignUserActionStatus,
            type,
            label,
            accessorParams.row.keyword,
          )}
          {index < subroles.length - 1 && ', '}
        </React.Fragment>
      );
    })}
  </div>
);

const items = (setAssignUserActionStatus = () => {}, roleConfig = {}) => {
  return [
    {
      type: 'header',
      Header: 'BASIC',
    },
    {
      type: 'item',
      Header: 'Vessel Name',
      id: 'vessel.name',
      name: 'name',
      accessor: (row) => <b>{highlightText(row.name, row.keyword)}</b>,
      sortType: (a, b) => {
        const aName = a.original.name ? a.original.name.toLowerCase() : '';
        const bName = b.original.name ? b.original.name.toLowerCase() : '';
        if (aName < bName) return -1;
        if (aName > bName) return 1;
        return 0;
      },
      order: 0,
      customClass: 'no-flex',
      minWidth: 330,
    },
    {
      type: 'item',
      Header: 'IMO Number',
      id: 'vessel.imo_number',
      name: 'imo_number',
      accessor: (row) => highlightText(row.imo_number, row.keyword),
      width: 150,
      order: 1,
      disableSortBy: true,
    },
    {
      type: 'item_value',
      Header: 'Owners',
      id: 'owner.value',
      name: 'owner.value',
      accessor: (row) => highlightText(row?.owner?.value, row.keyword),
      width: 250,
      order: 2,
      disableSortBy: true,
    },
    {
      type: 'item_value',
      Header: 'Vessel Type',
      id: 'vessel_type.value',
      name: 'vessel_type.value',
      accessor: (row) => highlightText(row?.vessel_type?.value, row.keyword),
      width: 200,
      order: 3,
      disableSortBy: true,
    },
    {
      type: 'item_value',
      Header: 'Emission Type',
      id: 'emission_type.emission_type',
      name: 'emission_type.emission_type',
      accessor: (row) => highlightText(row?.emission_type?.emission_type, row.keyword),
      width: 200,
      order: 4,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Shipyard',
      id: 'vessel.shipyard_text',
      name: 'shipyard_text',
      accessor: (row) => highlightText(row.shipyard_text, row.keyword),
      width: 200,
      order: 5,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Vessel Hull Number',
      id: 'vessel.vessel_hull_number',
      name: 'vessel_hull_number',
      accessor: (row) => highlightText(row.vessel_hull_number, row.keyword),
      order: 6,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Year Built/Date of Delivery',
      id: 'vessel.date_of_delivery',
      name: 'date_of_delivery',
      accessor: (row) => {
        return row.date_of_delivery
          ? moment(row.date_of_delivery).format('DD MMM YYYY')
          : formatDate(row.year_of_delivery, 'YYYY');
      },
      sortType: (a, b) => {
        const aDate = a.original.date_of_delivery
          ? moment(a.original.date_of_delivery).valueOf()
          : formatMoment(a.original.year_of_delivery);

        const bDate = b.original.date_of_delivery
          ? moment(b.original.date_of_delivery).valueOf()
          : formatMoment(b.original.year_of_delivery);

        if (aDate < bDate) return -1;
        if (aDate > bDate) return 1;
        return 0;
      },
      order: 7,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Expected Date of Takeover',
      id: 'vessel.expected_date_of_takeover',
      name: 'expected_date_of_takeover',
      accessor: (row) =>
        row.expected_date_of_takeover
          ? moment(row.expected_date_of_takeover).format('DD MMM YYYY')
          : '---',
      sortType: (a, b) => {
        const aDate = a.original.expected_date_of_takeover
          ? moment(a.original.expected_date_of_takeover).valueOf()
          : '';
        const bDate = b.original.expected_date_of_takeover
          ? moment(b.original.expected_date_of_takeover).valueOf()
          : '';
        if (aDate < bDate) return -1;
        if (aDate > bDate) return 1;
        return 0;
      },
      order: 8,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Date of Takeover',
      id: 'vessel.date_of_takeover',
      name: 'date_of_takeover',
      accessor: (row) =>
        row.date_of_takeover ? moment(row.date_of_takeover).format('DD MMM YYYY') : '---',
      sortType: (a, b) => {
        const aDate = a.original.date_of_takeover
          ? moment(a.original.date_of_takeover).valueOf()
          : '';
        const bDate = b.original.date_of_takeover
          ? moment(b.original.date_of_takeover).valueOf()
          : '';
        if (aDate < bDate) return -1;
        if (aDate > bDate) return 1;
        return 0;
      },
      order: 9,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Date of Handover',
      id: 'vessel.owner_end_date',
      name: 'owner_end_date',
      accessor: (row) =>
        row.owner_end_date ? moment(row.owner_end_date).format('DD MMM YYYY') : '---',
      sortType: (a, b) => {
        const aDate = a.original.owner_end_date ? moment(a.original.owner_end_date).valueOf() : '';
        const bDate = b.original.owner_end_date ? moment(b.original.owner_end_date).valueOf() : '';
        if (aDate < bDate) return -1;
        if (aDate > bDate) return 1;
        return 0;
      },
      width: 150,
      order: 10,
      disableSortBy: false,
    },
    {
      type: 'divider',
    },
    {
      type: 'last_arr_item',
      Header: 'Flag - Office',
      id: 'flag.value',
      name: 'flags',
      subName: 'office.value',
      accessor: (row) =>
        !_.isEmpty(row.flags)
          ? highlightText(_.last(row?.flags)?.office?.value, row.keyword)
          : '---',
      order: 11,
      disableSortBy: true,
    },
    {
      type: 'last_arr_item',
      Header: 'Port of Registry',
      id: 'vessel.port_of_registry_text',
      name: 'flags',
      subName: 'port_desc',
      accessor: (row) =>
        !_.isEmpty(row.flags) ? highlightText(_.last(row?.flags)?.port_desc, row.keyword) : '---',
      order: 12,
      disableSortBy: true,
    },
    {
      type: 'last_arr_item',
      Header: 'Call Sign',
      id: 'vessel.call_sign',
      name: 'flags',
      subName: 'call_sign',
      accessor: (row) =>
        !_.isEmpty(row.flags) ? highlightText(_.last(row?.flags)?.call_sign, row.keyword) : '---',
      order: 13,
      disableSortBy: true,
    },
    {
      type: 'join_arr_item',
      Header: 'Class',
      id: 'vessel_class_notations.vessel_class_id',
      name: 'class_notations',
      subName: 'id',
      accessor: (row) =>
        row.class_notations
          ? highlightText(row.class_notations.map((i) => i.id).join(', '), row.keyword)
          : '---',
      order: 14,
      disableSortBy: true,
    },
    {
      type: 'item_value',
      Header: 'H & M Underwriter',
      id: 'h_m_underwriter.value',
      name: 'h_m_underwriter.value',
      accessor: (row) => highlightText(row?.h_m_underwriter?.value, row.keyword),
      order: 15,
      disableSortBy: true,
    },
    {
      type: 'item_value',
      Header: 'P & I Club',
      id: 'p_i_club.value',
      name: 'p_i_club.value',
      accessor: (row) => highlightText(row?.p_i_club?.value, row.keyword),
      order: 16,
      disableSortBy: true,
    },
    {
      type: 'item_value',
      Header: 'EU Verifier',
      id: 'vessel_class_regulation.vessel_class',
      name: 'vessel_class_regulation.vessel_class',
      accessor: (row) =>
        highlightText(row.vessel_class_regulation[0]?.vessel_class?.value, row.keyword),
      order: 16,
      disableSortBy: true,
    },
    {
      type: 'divider',
    },
    {
      type: 'header',
      Header: 'EXTERNAL PLATFORM',
    },
    {
      type: 'item',
      Header: 'e-ORB',
      id: 'vessel_product_status',
      name: 'vessel_product_eorb',
      accessor: (row) => {
        if (row.vessel_product?.length) {
          const product = row.vessel_product.find(
            (product) => product.product_name === EORB_PRODUCT_NAME,
          );
          if (!_.isEmpty(product)) {
            if (product.status === EORB_STATUSES.ACTIVE) {
              return (
                <a
                  onClick={(e) => e.stopPropagation()}
                  className="eorb-href-class"
                  href={product.link}
                  target="_blank"
                  title={`Last Synchronization: ${moment(product.synchronized_at).format(
                    'DD/MM/YYYY',
                  )}`}
                  rel="noreferrer"
                >
                  <b>{highlightText('Open')}</b>
                </a>
              );
            }
            return <div className="eorb-disabled-class">(Pending Installation)</div>;
          }
        }
        return <div className="eorb-disabled-class">(Not Installed)</div>;
      },
      order: 17,
      disableSortBy: true,
    },
    {
      type: 'divider',
    },
    {
      type: 'header',
      Header: 'CONTACT',
    },
    {
      type: 'join_contact_items',
      Header: 'Email',
      id: 'vessel_email.email',
      name: 'emails',
      subName1: 'email_type.value',
      subName2: 'email',
      accessor: (row) =>
        row.emails.length > 0
          ? highlightText(
              row.emails.map((i) => `${i.email_type.value}: ${i.email}`).join(', '),
              row.keyword,
            )
          : '---',
      width: 330,
      order: 18,
      disableSortBy: true,
    },
    {
      type: 'join_contact_items',
      Header: 'Phone',
      id: 'vessel_phone.phone_number',
      name: 'phones',
      subName1: 'phone_type.value',
      subName2: 'phone_number',
      accessor: (row) =>
        row.phones.length > 0
          ? highlightText(
              row.phones.map((i) => `${i.phone_type.value}: ${i.phone_number}`).join(', '),
              row.keyword,
            )
          : '---',
      width: 250,
      order: 19,
      disableSortBy: true,
    },
    {
      type: 'divider',
    },
    {
      type: 'header',
      Header: 'PARTICULARS',
    },
    {
      type: 'item',
      Header: 'Life Boat Capacity',
      id: 'vessel.life_boat_capacity',
      name: 'life_boat_capacity',
      accessor: (row) => highlightText(row.life_boat_capacity, row.keyword),
      order: 20,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Length O.A.',
      id: 'vessel.length_oa',
      name: 'length_oa',
      accessor: (row) => highlightText(row.length_oa, row.keyword),
      order: 21,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Length B.P.',
      id: 'vessel.length_bp',
      name: 'length_bp',
      accessor: (row) => highlightText(row.length_bp, row.keyword),
      order: 22,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Depth',
      id: 'vessel.depth',
      name: 'depth',
      accessor: (row) => highlightText(row.depth, row.keyword),
      order: 23,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Breadth (Extreme)',
      id: 'vessel.breadth_extreme',
      name: 'breadth_extreme',
      accessor: (row) => highlightText(row.breadth_extreme, row.keyword),
      order: 24,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Summer Draft',
      id: 'vessel.summer_draft',
      name: 'summer_draft',
      accessor: (row) => highlightText(row.summer_draft, row.keyword),
      order: 25,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Summer DWT',
      id: 'vessel.summer_dwt',
      name: 'summer_dwt',
      accessor: (row) => highlightText(row.summer_dwt, row.keyword),
      order: 26,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'International GRT',
      id: 'vessel.international_grt',
      name: 'international_grt',
      accessor: (row) => highlightText(row.international_grt, row.keyword),
      order: 27,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'International NRT',
      id: 'vessel.international_nrt',
      name: 'international_nrt',
      accessor: (row) => highlightText(row.international_nrt, row.keyword),
      order: 28,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Service Speed',
      id: 'vessel.service_speed',
      name: 'service_speed',
      accessor: (row) => highlightText(row.service_speed, row.keyword),
      order: 29,
      disableSortBy: true,
    },
    {
      type: 'divider',
    },
    {
      type: 'header',
      Header: 'OFFICE DATA',
    },
    {
      type: 'item_value',
      Header: 'Tech Group',
      id: 'vessel.techgroup',
      name: 'fleet_staff.tech_group',
      accessor: (row) =>
        roleConfig.techGroups.manage && row.temp_ref_id
          ? assignReplaceUser(
              {
                subrole: 'tech_group',
                ownershipid: row.id,
                full_name: row.fleet_staff?.tech_group,
                osc_pilot: row.osc_pilot,
                tech_group_modal: true,
              },
              setAssignUserActionStatus,
              'tech_group',
              'tech group',
              row.keyword,
            )
          : highlightText(row.fleet_staff?.tech_group),
      order: 29,
      disableSortBy: true,
    },
    {
      type: 'item_value',
      Header: 'Superintendent',
      id: 'superintendent',
      name: 'fleet_staff.superintendent.full_name',
      width: 250,
      accessor: (row) =>
        roleConfig.vessel.staff.supdt && row.temp_ref_id
          ? generateAssignReplaceUserDiv(['superintendent'], {
              row,
              values: [row.fleet_staff?.superintendent],
              setAssignUserActionStatus,
              type: ['superintendent'],
              label: ['superintendent'],
            })
          : highlightText(row.fleet_staff?.superintendent?.full_name),
      order: 31,
      disableSortBy: true,
    },
    {
      type: 'join_staff_item',
      Header: 'QHSE',
      id: 'qhse',
      name: 'fleet_staff.qhse_deputy_general_manager.full_name',
      subName: 'fleet_staff.qhse_manager.full_name',
      width: 250,
      accessor: (row) => (
        <div>
          {row.fleet_staff?.qhse_deputy_general_manager?.full_name ?? '---'}
          {', '}
          {roleConfig.vessel.staff.qhse
            ? assignReplaceUser(
                { subrole: 'qhse', ownershipid: row.id, ...row.fleet_staff?.qhse_manager },
                setAssignUserActionStatus,
                'qhse',
                'QHSE Manager',
                row.keyword,
              )
            : highlightText(row.fleet_staff?.qhse_manager?.full_name)}
        </div>
      ),
      order: 32,
      disableSortBy: true,
    },
    {
      type: 'join_staff_item',
      Header: 'Operations',
      id: 'operations',
      name: 'fleet_staff.qhse_deputy_general_manager.full_name',
      subName: 'fleet_staff.qhse_manager.full_name',
      width: 250,
      accessor: (row) => (
        <div>
          {row.fleet_staff?.operation_director?.full_name ?? '---'}
          {', '}
          {roleConfig.vessel.staff.operation && row.temp_ref_id
            ? assignReplaceUser(
                {
                  subrole: 'operation',
                  ownershipid: row.id,
                  ...row.fleet_staff?.operation_manager,
                },
                setAssignUserActionStatus,
                'operation',
                'operation manager',
                row.keyword,
              )
            : highlightText(row.fleet_staff?.operation_manager?.full_name)}
        </div>
      ),
      order: 33,
      disableSortBy: true,
    },
    {
      type: 'join_staff_item',
      Header: 'Vessel Accountants',
      id: 'vessel_accountants',
      name: 'fleet_staff.primary_accountant.full_name',
      subName: 'fleet_staff.secondary_accountant.full_name',
      width: 250,
      accessor: (row) =>
        roleConfig.vessel.staff.accountant && row.temp_ref_id ? (
          generateAssignReplaceUserDiv(['accountant1', 'accountant2'], {
            row,
            values: [row.fleet_staff?.primary_accountant, row.fleet_staff?.secondary_accountant],
            setAssignUserActionStatus,
            type: ['accountant', 'accountant'],
            label: ['primary accountant', 'Secondary accountant'],
          })
        ) : (
          <div>
            {' '}
            {highlightText(row.fleet_staff?.primary_accountant?.full_name)} {', '}{' '}
            {highlightText(row.fleet_staff?.secondary_accountant?.full_name)}{' '}
          </div>
        ),
      order: 34,
      disableSortBy: true,
    },
    {
      type: 'join_staff_item',
      Header: 'Payroll Accountants',
      id: 'payroll_accountants',
      name: 'fleet_staff.primary_payroll.full_name',
      subName: 'fleet_staff.secondary_payroll.full_name',
      width: 250,
      accessor: (row) =>
        roleConfig.vessel.staff.payroll ? (
          generateAssignReplaceUserDiv(['payroll1', 'payroll2'], {
            row,
            values: [row.fleet_staff?.primary_payroll, row.fleet_staff?.secondary_payroll],
            setAssignUserActionStatus,
            type: ['payroll', 'payroll'],
            label: ['primary accountant', 'Secondary accountant'],
          })
        ) : (
          <div>
            {' '}
            {highlightText(row.fleet_staff?.primary_payroll?.full_name)} {', '}{' '}
            {highlightText(row.fleet_staff?.secondary_payroll?.full_name)}{' '}
          </div>
        ),
      order: 35,
      disableSortBy: true,
    },
    {
      type: 'join_buyers_item',
      Header: 'Procurement',
      id: 'buyers',
      name: 'fleet_staff.buyer_senior_lead.full_name',
      subName: 'fleet_staff.buyer_lead.full_name',
      subName1: 'fleet_staff.buyer.full_name',
      width: 250,
      accessor: (row) =>
        roleConfig.vessel.staff.buyer ? (
          generateAssignReplaceUserDiv(['buyer_senior_lead', 'buyer_lead', 'buyer'], {
            row,
            values: [
              row.fleet_staff?.buyer_senior_lead,
              row.fleet_staff?.buyer_lead,
              row.fleet_staff?.buyer,
            ],
            setAssignUserActionStatus,
            type: ['buyer_senior_lead', 'buyer_lead', 'buyer'],
            label: ['senior lead buyer', 'lead buyer', 'buyer'],
          })
        ) : (
          <div>
            {' '}
            {highlightText(row.fleet_staff?.buyer_senior_lead?.full_name)} {', '}{' '}
            {highlightText(row.fleet_staff?.buyer_lead?.full_name)} {', '}{' '}
            {highlightText(row.fleet_staff?.buyer?.full_name)}{' '}
          </div>
        ),
      order: 36,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Vessel Short Code',
      id: 'vessel.vessel_short_code',
      name: 'vessel_short_code',
      accessor: (row) => highlightText(row.vessel_short_code, row.keyword),
      order: 37,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Vessel Account Code (OLD)',
      id: 'vessel.vessel_account_code',
      name: 'vessel_account_code',
      accessor: (row) => highlightText(row.vessel_account_code, row.keyword),
      order: 38,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Vessel Account Code',
      id: 'vessel.vessel_account_code_new',
      name: 'vessel_account_code_new',
      accessor: (row) => highlightText(row.vessel_account_code_new, row.keyword),
      order: 39,
      disableSortBy: true,
    },
    {
      type: 'item',
      Header: 'Vessel Tel FAC Code',
      id: 'vessel.vessel_tec_fac_code',
      name: 'vessel_tel_fac_code',
      accessor: (row) => highlightText(row.vessel_tel_fac_code, row.keyword),
      order: 40,
      disableSortBy: true,
    },
  ];
};

export default items;
