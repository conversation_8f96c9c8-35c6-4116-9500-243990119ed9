/* eslint-disable react/jsx-key */
/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo } from 'react';
import { OverlayTrigger, Form, Popover } from 'react-bootstrap';
import { useTable, useSortBy, usePagination, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import styleGuide from '../../styleGuide';
import {
  storePageSize,
  storePageNumber,
  getPageTableState,
  storePageSort,
} from '../../util/local-storage-helper';
import { LOCAL_STORAGE_FIELDS, OWNERSHIP_CHANGE_REQUEST_STATUS } from '../../model/constants';
import Spinner from '../Spinner';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';

const { Icon } = styleGuide;

const isOwnershipChangePending = (requestStatus) =>
  requestStatus === OWNERSHIP_CHANGE_REQUEST_STATUS.PENDING;

const VesselTable = ({
  tabName,
  vessels,
  visitVessel,
  visitUpdateVessel,
  selectedColumns,
  isLoading,
  fetchData,
  pageCount,
  query,
  keyword,
  initSort,
  roleConfig,
  eventTracker,
}) => {
  let columns = [
    {
      Header: 'No.',
      accessor: 'id',
      maxWidth: 90,
      sticky: 'left',
      id: 'vessel.id',
    },
    ...selectedColumns,
  ];

  if (roleConfig?.vessel?.edit) {
    columns.push({
      Header: 'Actions',
      id: 'actions',
      accessor: (row) => (
        <div
          style={{
            textAlign: 'center',
            cursor: roleConfig.vessel.edit ? 'auto' : 'none',
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
          aria-hidden="true"
        >
          <OverlayTrigger
            rootClose
            trigger={
              roleConfig.vessel.edit &&
              !isOwnershipChangePending(row.ownership_change_request?.request_status)
                ? 'click'
                : []
            }
            onToggle={(isOpen) => {
              if (isOpen) eventTracker('actions', 'Actions');
            }}
            key="bottom"
            placement="bottom"
            overlay={
              <Popover>
                <Popover.Body>
                  <ul className="List__PopoverMenu">
                    <li
                      data-testid="fml-vesselList-editLink"
                      onClick={visitUpdateVessel.bind(this, row.vessel_id, row.id, row.name)}
                      aria-hidden="true"
                    >
                      Edit
                    </li>
                  </ul>
                </Popover.Body>
              </Popover>
            }
          >
            <div>
              {['active-vessels', 'new-takeovers'].includes(tabName) && (
                <Icon icon="more" size={20} className="default" style={{ cursor: 'pointer' }} />
              )}
            </div>
          </OverlayTrigger>
        </div>
      ),
      disableSortBy: true,
      maxWidth: 80,
      sticky: 'right',
    });
  }

  //Remove No, Actions columns while loading
  if (isLoading) {
    const removeOnLoad = ['No.', 'Actions'];
    columns = columns.filter(
      (col_obj) => !(col_obj.Header && removeOnLoad.includes(col_obj.Header)),
    );
  }

  return (
    <div className="vessel-table">
      <Table
        columns={columns}
        data={vessels}
        visitVessel={visitVessel}
        isLoading={isLoading}
        tabName={tabName}
        fetchData={fetchData}
        totalPageCount={pageCount}
        keyword={keyword}
        query={query}
        initSort={initSort}
        eventTracker={eventTracker}
      />
    </div>
  );
};

VesselTable.propTypes = {
  tabName: PropTypes.string,
  vessels: PropTypes.object,
  visitVessel: PropTypes.func,
  visitUpdateVessel: PropTypes.func,
  selectedColumns: PropTypes.array,
  isLoading: PropTypes.bool,
  fetchData: PropTypes.func,
  pageCount: PropTypes.number,
  query: PropTypes.string,
  keyword: PropTypes.string,
  initSort: PropTypes.string,
  roleConfig: PropTypes.object,
  eventTracker: PropTypes.func,
};

const PageNum = ({ active, disabled, children, onClick }) => {
  const className = `page-num page-num-${active ? 'active' : 'inactive'} page-num-${
    disabled ? 'disabled' : 'enabled'
  }`;
  return (
    <div className={className} onClick={onClick} aria-hidden="true">
      {children}
    </div>
  );
};

PageNum.propTypes = {
  active: PropTypes.string,
  disabled: PropTypes.bool,
  children: PropTypes.string,
  onClick: PropTypes.func,
};

const Table = ({
  columns,
  data,
  visitVessel,
  isLoading,
  tabName,
  fetchData,
  totalPageCount,
  keyword,
  query,
  initSort,
  eventTracker,
}) => {
  const defaultColumn = useMemo(
    () => ({
      minWidth: 120,
      width: 120,
    }),
    [],
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    page,
    canPreviousPage,
    canNextPage,
    pageCount,
    gotoPage,
    setPageSize,
    state: { pageIndex, pageSize, sortBy },
  } = useTable(
    {
      columns,
      data,
      defaultColumn,
      initialState: { pageIndex: 0, sortBy: initSort },
      manualPagination: true,
      manualSortBy: true,
      autoResetPage: false,
      autoResetSortBy: false,
      pageCount: totalPageCount,
    },
    useSortBy,
    usePagination,
    useSticky,
    useFlexLayout,
  );

  const resetPage = async (page_no = 0, page_size = 10) => {
    await fetchData({ pageSize: page_size, sortBy, pageIndex: page_no });
    setPageSize(page_size);
    gotoPage(page_no);
  };

  useEffect(() => {
    storePageSort(sortBy, LOCAL_STORAGE_FIELDS.masterKey);
  }, [sortBy]);

  useEffect(() => {
    (async function reset_page() {
      const { pageIndex, pageSize } = getPageTableState(tabName);
      await resetPage(pageIndex, pageSize);
    })();
  }, [tabName, query, keyword, sortBy]);

  const pageSwitch = async (page_no) => {
    await fetchData({ pageSize, sortBy, pageIndex: page_no });
    gotoPage(page_no);
    storePageNumber(tabName, page_no);
    eventTracker('pageSwitch', page_no);
  };
  const pageSizeSwitch = async (page_size) => {
    //Internally pageIndex gets recalibrated as follows
    const new_index = Math.floor((pageIndex * pageSize) / page_size);
    await fetchData({ pageIndex: new_index, sortBy, pageSize: page_size });
    setPageSize(page_size);
    storePageSize(tabName, page_size);
    storePageNumber(tabName, new_index);
    eventTracker('pageSizeSwitch', page_size);
  };
  const filterPages = (visiblePages, totalPages) =>
    visiblePages.filter((page) => page <= totalPages);
  const getVisiblePages = (page, total) => {
    if (total < 7) {
      return filterPages([1, 2, 3, 4, 5, 6], total);
    }
    if (page % 5 >= 0 && page > 4 && page + 2 < total) {
      return [1, page - 1, page, page + 1, total];
    }
    if (page % 5 >= 0 && page > 4 && page + 2 >= total) {
      return [1, total - 3, total - 2, total - 1, total];
    }
    return [1, 2, 3, 4, 5, total];
  };
  const visiblePages = getVisiblePages(pageIndex, pageCount);

  const ascDescIcon = (column) => {
    if (column.isSortedDesc) {
      return (
        <Icon
          icon="sort-ascending"
          size={20}
          className="default"
          onClick={() => eventTracker('sortBy', `None - ${column.render('Header')}`)}
        />
      );
    } else {
      return (
        <Icon
          icon="sort-descending"
          size={20}
          className="default"
          onClick={() => eventTracker('sortBy', `Desc - ${column.render('Header')}`)}
        />
      );
    }
  };

  return (
    <>
      <div {...getTableProps()} className="table sticky">
        <div className="header">
          {headerGroups.map((headerGroup) => (
            <div {...headerGroup.getHeaderGroupProps()} className="tr">
              {headerGroup.headers.map((column) => {
                const thProps = column.getHeaderProps(column.getSortByToggleProps());
                return (
                  <div {...thProps} className={`th ${column.customClass}`}>
                    {column.render('Header')}
                    <span>
                      {column.canSort &&
                        (column.isSorted ? (
                          ascDescIcon(column)
                        ) : (
                          <Icon
                            icon="sort-off"
                            size={20}
                            className="default"
                            onClick={() =>
                              eventTracker('sortBy', `ASC - ${column.render('Header')}`)
                            }
                          />
                        ))}
                    </span>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        {isLoading && <Spinner alignClass={'spinner-table'} />}
        <div data-testid={'fml-vesselList-vesselTable'} {...getTableBodyProps()} className="body">
          {page.map((row, rIdx) => {
            prepareRow(row);
            return (
              <div
                {...row.getRowProps()}
                className="tr"
                onClick={visitVessel.bind(this, row.original.id, row.original.name)}
                aria-hidden="true"
              >
                {row.cells.map((cell, cIdx) => {
                  const tdProps = cell.getCellProps();
                  if (cell.column.Header == 'Vessel Name') {
                    const itemLink = `/vessel/ownership/details/${row?.cells[0]?.value}`;
                    const anchorStyle = {
                      minWidth: '330px',
                    };
                    return (
                      <Link key={cIdx} to={itemLink} className="td" style={anchorStyle}>
                        <div {...tdProps}>{cell.render('Cell')}</div>
                      </Link>
                    );
                  }
                  return (
                    <div {...tdProps} className="td">
                      {cell.render('Cell')}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      </div>

      <br />
      <div className="d-flex justify-content-center p-4">
        <div className="page-number-border">
          <PageNum onClick={() => pageSwitch(pageIndex - 1)} disabled={!canPreviousPage}>
            {'<'}
          </PageNum>
          {visiblePages.map((page, index, array) => (
            <PageNum
              key={index}
              active={page - 1 === pageIndex}
              disabled={page - 1 === pageIndex}
              onClick={() => pageSwitch(page - 1)}
            >
              {array[index - 1] + 2 < page ? `...${page}` : page}
            </PageNum>
          ))}
          <PageNum onClick={() => pageSwitch(pageIndex + 1)} disabled={!canNextPage}>
            {'>'}
          </PageNum>
        </div>
        <Form>
          <Form.Control
            as="select"
            value={pageSize}
            className="ml-3"
            onChange={(e) => {
              pageSizeSwitch(Number(e.target.value));
              // Store in Local Storage
              storePageSize(tabName, e.target.value);
            }}
          >
            {[10, 20, 50, 100].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                Show {pageSize}
              </option>
            ))}
          </Form.Control>
        </Form>
      </div>
    </>
  );
};

Table.propTypes = {
  columns: PropTypes.array,
  data: PropTypes.object,
  visitVessel: PropTypes.func,
  isLoading: PropTypes.bool,
  tabName: PropTypes.string,
  fetchData: PropTypes.func,
  totalPageCount: PropTypes.number,
  keyword: PropTypes.string,
  query: PropTypes.string,
  initSort: PropTypes.string,
  eventTracker: PropTypes.func,
};

export default VesselTable;
