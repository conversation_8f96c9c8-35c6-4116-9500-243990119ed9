import React, { useContext } from 'react';
import { Dropdown, Anchor } from 'react-bootstrap';
import styleGuide from '../../styleGuide';
import { VesselContext } from '../../context/VesselContext';
const { Icon } = styleGuide;
import PropTypes from 'prop-types';

const CustomItem = React.forwardRef((props, ref) => {
  return (
    <Anchor className="dropdown-item" role="button" onClick={props.onClick}>
      {props.checked ? (
        <Icon
          icon="checked"
          size={20}
          className="default"
          style={{ verticalAlign: 'top', color: '#17A2B8', marginRight: '5px' }}
        />
      ) : (
        <div
          style={{
            display: 'inline-block',
            width: '20px',
            marginRight: '5px',
          }}
        >
          {' '}
        </div>
      )}
      {props.children}
    </Anchor>
  );
});

CustomItem.propTypes = {
  onClick: PropTypes.func,
  checked: PropTypes.bool,
  children: PropTypes.node,
};

const TableColumnsButton = ({ selectedColumns, onSelectColumn, menuItem }) => {
  const { roleConfig } = useContext(VesselContext);

  const isSelected = (item) => selectedColumns.some(({ id }) => id === item.id);

  return (
    <Dropdown
      className="mr-2"
      alignRight
      onSelect={(eventKey, event) => {
        onSelectColumn(menuItem[eventKey]);
      }}
    >
      <Dropdown.Toggle
        variant="outline-primary"
        id="dropdown-table-columns"
        data-testid="fml-table-columns"
      >
        Table Columns
      </Dropdown.Toggle>
      <Dropdown.Menu>
        {menuItem?.map((item, idx) => {
          if (!roleConfig?.eorb?.view && ['e-ORB', 'EXTERNAL PLATFORM'].includes(item.Header)) {
            return;
          }
          const checked = isSelected(item);
          if (item.type === 'header') {
            return <Dropdown.Header key={idx}>{item.Header}</Dropdown.Header>;
          } else if (item.type === 'divider') {
            return <Dropdown.Divider key={idx} />;
          } else {
            return (
              <Dropdown.Item key={idx} as={CustomItem} checked={checked} eventKey={idx}>
                {item.Header}
              </Dropdown.Item>
            );
          }
        })}
      </Dropdown.Menu>
    </Dropdown>
  );
};

TableColumnsButton.propTypes = {
  selectedColumns: PropTypes.array,
  onSelectColumn: PropTypes.func,
  menuItem: PropTypes.array,
};

export default TableColumnsButton;
