import React from 'react';
import { Button, Modal } from 'react-bootstrap';
import '../pages/styles/monthly-financial.scss';
const ReopenReportModal = (props) => {
  const { onClose, onConfirm, reopenId, isDisable } = props;
  return (
    <Modal show={!!reopenId} onHide={onClose} className="mt-5">
      <Modal.Body className="p-4">
        <p className="h5 fw-light">Reopen Report</p>
        <p className="fw-900 text-xs my-4">Are you sure you want to reopen this report?</p>
        <hr />
        <div className="d-flex justify-content-end align-items-center gap-2">
          <Button
            variant="secondary"
            className="mr-2 px-4 py-2"
            dataTestId="reopen-rpt-modal-cancel"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            className="px-4 py-2"
            dataTestId="reopen-rpt-modal-confirm"
            onClick={onConfirm}
            disabled={isDisable}
          >
            Confirm
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};
export default ReopenReportModal;