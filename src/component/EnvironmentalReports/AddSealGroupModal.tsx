import _ from 'lodash';
import React, { useState } from 'react';
import { Button, Col, Form, Modal, Row } from 'react-bootstrap';
import { SEAL_TYPE } from '../../constants/seal-type';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import ErrorAlert from '../ErrorAlert';
import vesselService from '../../service/vessel-service';

const AddSealGroupModal = ({
  showModal = true,
  handleModalCancel = () => {},
  handleModalSubmit = () => {},
  ownershipId,
  setServerError = () => {},
}) => {
  const [form, setForm] = useState({});
  const [errors, setErrors] = useState({});
  const [validationError, setValidationError] = useState();
  const [loading, setLoading] = useState(false);
  const [isDisabledSave, setIsDisabledSave] = useState(true);

  const setFieldValue = (field, value) => {
    let errObj = {
      ...errors,
      ...validateForm(),
    };
    setForm({
      ...form,
      [field]: value,
    });
    if (field === 'first_num') {
      const validateFirstNum = sealNoValidation(value);
      errObj = {
        ...errObj,
        [field]: validateFirstNum ?? undefined,
      };
      if (!validateFirstNum && form.last_num) {
        const validateLastNum = lastNumValidation(value, form.last_num);
        errObj = {
          ...errObj,
          [field]: validateLastNum ?? undefined,
        };
      }
    } else if (field === 'last_num') {
      const validateLastNum = lastNumValidation(form.first_num, value);
      errObj = {
        ...errObj,
        first_num: validateLastNum ? errObj.first_num : undefined,
        [field]: validateLastNum ?? undefined,
      };
    } else if (errors[field]) {
      errObj = {
        ...errObj,
        [field]: undefined,
      };
    }
    errObj = _.pickBy(errObj, (i) => i !== undefined);
    setErrors(errObj);
    setIsDisabledSave(!_.isEmpty(errObj));
  };

  const sealNoValidation = (value) => {
    const numb = value.match(/\d/g);
    let error;
    if (!numb || numb.length < 3) {
      error = 'Seal# should contain at least 3 digits.';
    } else if (!value.match(/\d{3}$/g)) {
      error = 'Seal#: The last 3 digits must be numeric value.';
    }
    return error;
  };

  const lastNumValidation = (firstNum, lastNum) => {
    if (firstNum.length !== lastNum.length) {
      return 'First Seal# and Last Seal# should have same length';
    }
    let error = sealNoValidation(lastNum);
    if (!error) {
      const firstNumPrefix = firstNum?.substring(0, firstNum.length - 3);
      const lastNumPrefix = lastNum?.substring(0, firstNum.length - 3);
      const firstNumValue = firstNum?.substring(firstNumPrefix.length, firstNum.length);
      const lastNumValue = lastNum?.substring(firstNumPrefix.length, lastNum.length);
      if (firstNumPrefix !== lastNumPrefix) {
        error = `First Seal# prefix(${firstNumPrefix}) and Last Seal# prefix(${lastNumPrefix}) should be same.`;
      } else if (firstNumValue.length > lastNumValue.length || firstNumValue > lastNumValue) {
        error = 'Last Seal# should be greater than First Seal#.';
      }
    }
    return error;
  };

  const validateForm = () => {
    const { first_num, last_num, seal_type } = form;
    const newErrors = { ..._.omitBy(errors, (v) => v == null) };
    if (!first_num || _.isEmpty(first_num))
      newErrors.first_num = 'First Seal # is a required field';
    if (!last_num || _.isEmpty(last_num)) newErrors.last_num = 'Last Seal # is a required field';
    if (!seal_type || _.isEmpty(seal_type)) newErrors.seal_type = 'Seal Type is a required field';
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setLoading(true);
    try {
      setValidationError(null);
      setServerError(null);
      await vesselService.addSealGroup(ownershipId, form);
      handleModalSubmit();
      setForm({});
      setIsDisabledSave(true);
    } catch (e) {
      if (e.response.status === 400) {
        setValidationError(`${e.response.data}. Please try again.`);
      } else {
        handleCancel();
        setServerError('Something went wrong with add seal group. Please try later');
      }
      setIsDisabledSave(false);
    }
    setLoading(false);
  };

  const handleCancel = () => {
    setForm({});
    setErrors({});
    setValidationError(null);
    handleModalCancel();
    setIsDisabledSave(true);
  };

  return (
    <Modal
      id="seal-group-modal"
      show={showModal}
      aria-labelledby="seal-group-modal"
      centered
      size="lg"
      backdrop="static"
    >
      <Modal.Header>
        <Modal.Title>
          {'Add Seal Group'}
          <p className="required-field-text">* Required fields</p>
        </Modal.Title>
      </Modal.Header>

      {validationError && <ErrorAlert message={validationError} />}
      <CustomOverlayLoader active={loading}>
        <Form className="form-main-control">
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="form-group" controlId="seal-group">
                  <Form.Label className="from-label">First Seal# *</Form.Label>
                  <Form.Control
                    value={form?.first_num}
                    maxLength={10}
                    data-testid="fml-vessel-seal-first-no"
                    autoComplete="off"
                    onChange={(e) => {
                      setFieldValue('first_num', e.target.value);
                    }}
                  />
                  <div className="validate-error">{errors.first_num}</div>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Last Seal# *</Form.Label>
                  <Form.Control
                    value={form?.last_num}
                    data-testid="fml-vessel-seal-last-no"
                    autoComplete="off"
                    maxLength={10}
                    onChange={(e) => {
                      setFieldValue('last_num', e.target.value);
                    }}
                  />
                  <div className="validate-error">{errors.last_num}</div>
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Type*</Form.Label>
                  <Form.Control
                    as="select"
                    name="seal_type"
                    value={form?.seal_type}
                    onChange={(e) => setFieldValue('seal_type', e.target?.value)}
                    data-testid="fml-vessel-seal-type"
                    className="form-select"
                  >
                    <option value="" hidden>
                      Please Select
                    </option>
                    {SEAL_TYPE.map((item) => (
                      <option className="font-size-large" value={item} key={item}>
                        {item}
                      </option>
                    ))}
                  </Form.Control>
                  <div className="validate-error">{errors.seal_type}</div>
                </Form.Group>
              </Col>
            </Row>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="primary" data-testid="fml-seal-group-close" onClick={handleCancel}>
              Close
            </Button>
            <Button
              variant="secondary"
              data-testid="fml-seal-group-save"
              type="submit"
              onClick={handleSubmit}
              disabled={isDisabledSave}
            >
              Save
            </Button>
          </Modal.Footer>
        </Form>
      </CustomOverlayLoader>
    </Modal>
  );
};

export default AddSealGroupModal;
