import React, { useContext, useState } from 'react';
import { Tab, Form, Row } from 'react-bootstrap';
import { useParams } from 'react-router-dom';
import CustomDatePicker from '../../component/customComponent/CustomDatePicker';
import moment from 'moment';
import VesselDropDown from '../TechnicalReports/VesselDropDown';
import { EnvironmentalReportContext } from '../../context/EnvironmentalReportContext';

const EnvironmentalReportsFilter = ({ loading, activeTab = '' }) => {
  const {
    vesselList,
    filterData,
    setFilterData,
    ga4EventTrigger = () => {},
  } = useContext(EnvironmentalReportContext);
  const { ownershipId } = useParams();
  const [errors, setErrors] = useState({
    startDate: !filterData.startDate ? 'Please provide a valid date' : undefined,
    endDate: !filterData.endDate ? 'Please provide a valid date' : undefined,
  });
  const onInputChange = async (date, name) => {
    setFilterData({
      ...filterData,
      [name]: date ? moment(date).format('YYYY-MM-DD') : '',
    });
    if (
      activeTab === 'waste-stream' ||
      activeTab === 'mrv' ||
      activeTab === 'imo-collection' ||
      activeTab === 'ets'
    ) {
      let errMessage = date ? undefined : 'Please provide a valid date';
      setErrors({ ...errors, [name]: errMessage });
    }
  };

  const handleDropdownChange = (event) => {
    setFilterData({ ...filterData, vessel: event });
  };

  const handleVesselClear = () => {
    setFilterData({ ...filterData, vessel: [] });
  };
  let startDate = filterData.startDate;
  let year = startDate?moment(startDate).format('YYYY'):null;

  return (
    <Tab.Container>
      <Row className="form-row">
        <Form.Label className="filter-reports">
          <b>Filter Reports</b>
        </Form.Label>
      </Row>

      <Row className="form-row">
        <Form.Group className="reports-filter-textField form-group">
          <Form.Control type="text" placeholder="Report Submit Date" disabled />
        </Form.Group>

        {activeTab === 'mrv' && (
          <Form.Group className="startDatePicker form-group">
            <CustomDatePicker
              value={year}
              onChange={(event) => onInputChange(event, 'startDate')}
              dataTestId="fml-environmental-report-filter-start-date"
              disabled={loading}
              activeTab={activeTab}
            />
            {(activeTab === 'mrv') && <div className="validate-error">{errors.startDate}</div>}
          </Form.Group>
        )}

        {activeTab !== 'mrv' && (
          <>
            <Form.Group className="startDatePicker form-group">
              <CustomDatePicker
                value={filterData.startDate}
                onChange={(event) => onInputChange(event, 'startDate')}
                dataTestId="fml-environmental-report-filter-start-date"
                disabled={loading}
                activeTab={activeTab}
              />
              {(activeTab === 'waste-stream' ||
                activeTab === 'imo-collection' ||
                activeTab === 'ets') && <div className="validate-error">{errors.startDate}</div>}
            </Form.Group>
          
            <Form.Group className=" datePickerRange form-group">To</Form.Group>

            <Form.Group className="endDatePicker form-group">
              <CustomDatePicker
                value={filterData.endDate}
                onChange={(event) => onInputChange(event, 'endDate')}
                dataTestId="fml-environmental-report-filter-end-date"
                disabled={loading}
                activeTab={activeTab}
              />
              {(activeTab === 'waste-stream' ||
                activeTab === 'imo-collection' ||
                activeTab === 'ets') && <div className="validate-error">{errors.endDate}</div>}
            </Form.Group>
          </>
        )}
        
        <Form.Group className="reports-filter-textField form-group">
          <Form.Control type="text" placeholder="Vessel Name" disabled />
        </Form.Group>
        <Form.Group className={`vessel-dropdown ${loading && 'disabled'} form-group`}>
          <VesselDropDown
            onChange={handleDropdownChange}
            dropdownData={vesselList}
            labelKey="value"
            selectedItem={filterData.vessel}
            handleClear={handleVesselClear}
            placeholder="All Vessels"
            dataTestId="fml-environmental-report-filter-vessel"
          />
        </Form.Group>
      </Row>
      {(activeTab === 'waste-stream' || activeTab === 'mrv') && (
        <Row className="form-row">
          <Form.Group className="reports-filter-textField form-group">
            <Form.Check
              type="checkbox"
              className="basic-checkbox"
              checked={filterData.eu}
              data-testid="fml-environmental-report-filter-eu-port"
              label="EU Ports Only"
              onChange={(e) => {
                if (e.currentTarget.checked) {
                  ga4EventTrigger(
                    'Display EU port only',
                    `${activeTab === 'mrv' ? 'MRV' : 'Waste Stream Analysis'} Report - Filter`,
                    vesselList.filter((vessel) => `${vessel.id}` === ownershipId)[0]?.value,
                  );
                }
                setFilterData({ ...filterData, eu: e.currentTarget.checked });
              }}
            />
          </Form.Group>
        </Row>
      )}
    </Tab.Container>
  );
};

export default EnvironmentalReportsFilter;
