import _ from 'lodash';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { EnvironmentalReportContext } from '../../../context/EnvironmentalReportContext';
import vesselService from '../../../service/vessel-service';
import { formatValue } from '../../../util/view-utils';
import CustomTable from '../../customComponent/CustomTable';
import ErrorAlert from '../../ErrorAlert';

const SealManagementList = ({ loading, setLoading, error, setError }) => {
  const { filterData, vesselList, ga4EventTrigger = () => {} } = useContext(
    EnvironmentalReportContext,
  );
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([{ id: 'vessel_name', desc: false }]);
  const [sealList, setSealList] = useState([]);
  const [isSealFilterActive, setIsSealFilterActive] = useState(false);
  const sealNumberColumns = useMemo(
    () => [
      {
        Header: 'Seal #',
        accessor: (row) => row.serial,
        id: 'SealNo',
        name: 'serial',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Type',
        accessor: (row) => row.seal_type,
        id: 'seal_type',
        name: 'seal_type',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Location',
        accessor: (row) => row.location_type,
        id: 'location_type',
        name: 'location_type',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Status',
        accessor: (row) => row.status,
        id: 'status',
        name: 'status',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
    ],
    [],
  );

  const columns = useMemo(() => {
    const cols = [
      {
        Header: 'No.',
        accessor: (row, index) => pageSize * pageIndex + index + 1,
        sticky: 'left',
        id: 'id',
        name: 'id',
        type: 'text',
        maxWidth: 50,
      },
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link
            onClick={() =>
              ga4EventTrigger(
                'Link to Vessel Details',
                'Seal Report - Link',
                row.vessel_name ? row.vessel_name : row.name,
              )
            }
            to={`/vessel/ownership/details/${
              row.vessel_ownership_id ? row.vessel_ownership_id : row.id
            }`}
            className="button-link"
          >
            {formatValue(row.vessel_name ? row.vessel_name : row.name)}
          </Link>
        ),
        id: 'vessel_name',
        name: 'vessel_name',
        type: 'text',
      },
      {
        Header: 'Unused Seals',
        id: 'viewUnused',
        type: 'item',
        accessor: (row) => (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
            aria-hidden="true"
          >
            <Link
              className="button-link"
              onClick={() =>
                ga4EventTrigger(
                  'Link to Unused Seals',
                  'Seal Report - Link',
                  row.vessel_name ? row.vessel_name : row.name,
                )
              }
              to={`/vessel/report/environmental/seals/${
                row.vessel_ownership_id ? row.vessel_ownership_id : row.id
              }/unused`}
            >
              View
            </Link>
          </div>
        ),
        disableSortBy: true,
      },
      {
        Header: 'Affixed Seals',
        id: 'viewAffixed',
        type: 'item',
        accessor: (row) => (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
            aria-hidden="true"
          >
            <Link
              onClick={() =>
                ga4EventTrigger(
                  'Link to Affixed Seals',
                  'Seal Report - Link',
                  row.vessel_name ? row.vessel_name : row.name,
                )
              }
              className="button-link"
              to={`/vessel/report/environmental/seals/${
                row.vessel_ownership_id ? row.vessel_ownership_id : row.id
              }/affixed`}
            >
              View
            </Link>
          </div>
        ),
        disableSortBy: true,
      },
      {
        Header: 'Seal Log',
        id: 'viewSeal',
        type: 'item',
        accessor: (row) => (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
            aria-hidden="true"
          >
            <Link
              onClick={() =>
                ga4EventTrigger(
                  'Link to Seal Logs',
                  'Seal Report - Link',
                  row.vessel_name ? row.vessel_name : row.name,
                )
              }
              className="button-link"
              to={`/vessel/report/environmental/seals/${
                row.vessel_ownership_id ? row.vessel_ownership_id : row.id
              }/logs`}
            >
              View
            </Link>
          </div>
        ),
        disableSortBy: true,
      },
    ];
    if (isSealFilterActive) cols.push(...sealNumberColumns);
    return cols;
  }, [isSealFilterActive, pageSize, pageIndex]);

  const fetchSealList = async (sortData = [], pageIndex = 0, pageSize = 10) => {
    setLoading(true);
    setError(null);
    try {
      let pageFilterData = '';
      let response = {};
      let total = vesselList.length;
      if (filterData.serial && filterData.serial !== '') {
        pageFilterData = `serial=${filterData.serial}`;
        response = await vesselService.getSealList(
          !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
          {
            sortBy: sortData,
            pageSize: pageSize,
            pageIndex: pageIndex,
          },
          pageFilterData,
        );
        setSealList(response.data.results);
        total = response.data.total;
        setIsSealFilterActive(true);
      } else {
        let filteredVessel = _.clone(vesselList);
        if (!_.isEmpty(filterData) && filterData?.vessel[0]?.id) {
          filteredVessel = filteredVessel.filter(
            (vessel) => vessel.id === filterData?.vessel[0]?.id,
          );
          total = filteredVessel.length;
        }
        if (!_.isEmpty(sortData) && sortData[0]?.id === 'vessel_name') {
          filteredVessel = filteredVessel.sort((a, b) =>
            sortData[0]?.desc ? b.value.localeCompare(a.value) : a.value.localeCompare(b.value),
          );
        }
        const vesselListChunk = _.chunk(filteredVessel, pageSize);
        setSealList(vesselListChunk[pageIndex]);
        setIsSealFilterActive(false);
      }
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchSealList(sortData, pageIndex, pageSize);
  }, [filterData, vesselList]);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchSealList(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', 'Seal Report - List', value);
    setPageSize(value);
    setPageIndex(0);
    fetchSealList(sortData, 0, value);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Seal Report - List', value[0]?.id);
    setSortData(value);
    fetchSealList(value, pageIndex, pageSize);
  };

  return (
    <div className="pt-4">
      {error && <ErrorAlert message={error} />}
      <CustomTable
        column={columns}
        reportData={sealList}
        tableRef={null}
        isLoading={loading}
        pagination={true}
        pageCount={pageCount}
        totalCount={totalCount}
        setPageNo={onPageIndexChange}
        setPageListSize={onPageSizeChange}
        setSortData={onSortChange}
        pageNo={pageIndex}
        pageSize={pageSize}
        className="drill-table-top-border"
      />
    </div>
  );
};

export default SealManagementList;
