import React, { useContext, useEffect, useMemo, useState } from 'react';
import { ButtonToolbar, Dropdown } from 'react-bootstrap';
import { EnvironmentalReportContext } from '../../../context/EnvironmentalReportContext';
import vesselService from '../../../service/vessel-service';
import { Icon } from '../../../styleGuide';
import { formatValue } from '../../../util/view-utils';
import CustomDropDownItemCheck from '../../customComponent/CustomDropDownItemCheck';
import CustomTable from '../../customComponent/CustomTable';
import ErrorAlert from '../../ErrorAlert';
import { getMrvExcelData } from '../../../util/mrv-excel-data';
import _ from 'lodash';
import moment from 'moment';
import { CH4_N2O_RELEASE_YEAR, getCustomColumnHeader, mrvColumnsToBeRemovedForPastYears } from '../../../util/mrv';

const MrvReport = ({
  loading,
  setLoading,
  error,
  setError,
  selectedVoyageColumnIds,
  selectedPortColumnIds,
  onChangeMrvReportTableColumns,
}) => {
  const { filterData, setExcelData } = useContext(EnvironmentalReportContext);
  const [mrvVoyageReport, setMrvVoyageReport] = useState([]);
  const [mrvPortReport, setMrvPortReport] = useState([]);
  const [selectedVoyageColumns, setSelectedVoyageColumns] = useState([]);
  const [selectedPortColumns, setSelectedPortColumns] = useState([]);

  const mrvVoyageColumns = useMemo(
    () => {
      const filterYear = moment.utc(filterData?.startDate).format('YYYY');
      let voyageCols = [
        {
          Header: 'Voyage No.',
          accessor: (row, index) => index + 1,
          id: 'voyage_no',
          name: 'voyage_no',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Port of Departure',
          id: 'port_departure',
          name: 'port_departure',
          type: 'text',
          accessor: (row) => formatValue(row?.port_departure),
          disableSortBy: true,
        },
        {
          Header: 'EU Port of Departure',
          accessor: (row) => formatValue(row.eu_port_departure),
          id: 'eu_port_departure',
          name: 'eu_port_departure',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Port of Arrival',
          accessor: (row) => formatValue(row.port_arrival),
          id: 'port_arrival',
          name: 'port_arrival',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'EU Port of arrival',
          accessor: (row) => formatValue(row.eu_port_arrival),
          id: 'eu_port_arrival',
          name: 'eu_port_arrival',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Leg Start / End Date',
          accessor: (row) => formatValue(row.leg_date),
          id: 'leg_date',
          name: 'leg_date',
          type: 'text',
          maxWidth: 230,
          width: 230,
          disableSortBy: true,
        },
        {
          Header: 'Hour of departure (UTC)',
          accessor: (row) => formatValue(row.departure_hour),
          id: 'departure_hour',
          name: 'departure_hour',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Hour of arrival (UTC)',
          accessor: (row) => formatValue(row.arrival_hour),
          id: 'arrival_hour',
          name: 'arrival_hour',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Time spent at sea (hr)',
          accessor: (row) => formatValue(row.time_spent_sea),
          id: 'time_spent_sea',
          name: 'time_spent_sea',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Distance (nm)',
          accessor: (row) => formatValue(row.distance),
          id: 'distance',
          name: 'distance',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Cargo Carried (tons)',
          accessor: (row) => formatValue(row.cargo_carried),
          id: 'cargo_carried',
          name: 'cargo_carried',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Voyage Type',
          accessor: (row) => formatValue(row.voyage_type),
          id: 'voyage_type',
          name: 'voyage_type',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO Consumption (tons)',
          accessor: (row) => formatValue(row.hfo_cons),
          id: 'hfo_cons',
          name: 'hfo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.hfo_emission_factor),
          id: 'hfo_emission_factor',
          name: 'hfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.hfo_co2_emitted),
          id: 'hfo_co2_emitted',
          name: 'hfo_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_hfo_emission_factor),
          id: 'ch4_hfo_emission_factor',
          name: 'ch4_hfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_hfo_emitted),
          id: 'ch4_hfo_emitted',
          name: 'ch4_hfo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_hfo_emission_factor),
          id: 'n2o_hfo_emission_factor',
          name: 'n2o_hfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_hfo_emitted),
          id: 'n2o_hfo_emitted',
          name: 'n2o_hfo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO Consumption (tons)',
          accessor: (row) => formatValue(row.lfo_cons),
          id: 'lfo_cons',
          name: 'lfo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.lfo_emission_factor),
          id: 'lfo_emission_factor',
          name: 'lfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.lfo_co2_emitted),
          id: 'lfo_co2_emitted',
          name: 'lfo_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_lfo_emission_factor),
          id: 'ch4_lfo_emission_factor',
          name: 'ch4_lfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_lfo_emitted),
          id: 'ch4_lfo_emitted',
          name: 'ch4_lfo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_lfo_emission_factor),
          id: 'n2o_lfo_emission_factor',
          name: 'n2o_lfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_lfo_emitted),
          id: 'n2o_lfo_emitted',
          name: 'n2o_lfo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO Consumption (tons)',
          accessor: (row) => formatValue(row.mgo_cons),
          id: 'mgo_cons',
          name: 'mgo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.mgo_emission_factor),
          id: 'mgo_emission_factor',
          name: 'mgo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.mgo_co2_emitted),
          id: 'mgo_co2_emitted',
          name: 'mgo_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_mgo_emission_factor),
          id: 'ch4_mgo_emission_factor',
          name: 'ch4_mgo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_mgo_emitted),
          id: 'ch4_mgo_emitted',
          name: 'ch4_mgo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_mgo_emission_factor),
          id: 'n2o_mgo_emission_factor',
          name: 'n2o_mgo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_mgo_emitted),
          id: 'n2o_mgo_emitted',
          name: 'n2o_mgo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG Consumption (tons)',
          accessor: (row) => formatValue(row.lng_cons),
          id: 'lng_cons',
          name: 'lng_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.lng_emission_factor),
          id: 'lng_emission_factor',
          name: 'lng_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.lng_co2_emitted),
          id: 'lng_co2_emitted',
          name: 'lng_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG Consumption - Main Engine (tons)',
          accessor: (row) => formatValue(row.me_lng),
          id: 'me_lng',
          name: 'me_lng',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Slippage Coefficient - Main Engine',
          accessor: (row) => formatValue(row.lng_engine_category_main_engine_emission_factor),
          id: 'lng_engine_category_main_engine_emission_factor',
          name: 'lng_engine_category_main_engine_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG Consumption - Diesel Generator (tons)',
          accessor: (row) => formatValue(row.ge_lng),
          id: 'ge_lng',
          name: 'ge_lng',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Slippage Coefficient - Diesel Generator',
          accessor: (row) => formatValue(row.lng_engine_category_diesel_generator_emission_factor),
          id: 'lng_engine_category_diesel_generator_emission_factor',
          name: 'lng_engine_category_diesel_generator_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_lng_emission_factor),
          id: 'ch4_lng_emission_factor',
          name: 'ch4_lng_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_lng_emitted),
          id: 'ch4_lng_emitted',
          name: 'ch4_lng_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_lng_emission_factor),
          id: 'n2o_lng_emission_factor',
          name: 'n2o_lng_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_lng_emitted),
          id: 'n2o_lng_emitted',
          name: 'n2o_lng_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) Consumption (tons)',
          accessor: (row) => formatValue(row.lpg_propane_cons),
          id: 'lpg_propane_cons',
          name: 'lpg_propane_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.lpg_propane_emission_factor),
          id: 'lpg_propane_emission_factor',
          name: 'lpg_propane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.lpg_propane_co2_emitted),
          id: 'lpg_propane_co2_emitted',
          name: 'lpg_propane_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_lpg_propane_emission_factor),
          id: 'ch4_lpg_propane_emission_factor',
          name: 'ch4_lpg_propane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_lpg_propane_emitted),
          id: 'ch4_lpg_propane_emitted',
          name: 'ch4_lpg_propane_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_lpg_propane_emission_factor),
          id: 'n2o_lpg_propane_emission_factor',
          name: 'n2o_lpg_propane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_lpg_propane_emitted),
          id: 'n2o_lpg_propane_emitted',
          name: 'n2o_lpg_propane_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) Consumption (tons) ',
          accessor: (row) => formatValue(row.lpg_butane_cons),
          id: 'lpg_butane_cons',
          name: 'lpg_butane_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.lpg_butane_emission_factor),
          id: 'lpg_butane_emission_factor',
          name: 'lpg_butane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) CO\u2082 emitted (tons) ',
          accessor: (row) => formatValue(row.lpg_butane_co2_emitted),
          id: 'lpg_butane_co2_emitted',
          name: 'lpg_butane_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_lpg_butane_emission_factor),
          id: 'ch4_lpg_butane_emission_factor',
          name: 'ch4_lpg_butane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_lpg_butane_emitted),
          id: 'ch4_lpg_butane_emitted',
          name: 'ch4_lpg_butane_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_lpg_butane_emission_factor),
          id: 'n2o_lpg_butane_emission_factor',
          name: 'n2o_lpg_butane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_lpg_butane_emitted),
          id: 'n2o_lpg_butane_emitted',
          name: 'n2o_lpg_butane_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol Consumption (tons)',
          accessor: (row) => formatValue(row.methanol_cons),
          id: 'methanol_cons',
          name: 'methanol_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.methanol_emission_factor),
          id: 'methanol_emission_factor',
          name: 'methanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.methanol_co2_emitted),
          id: 'methanol_co2_emitted',
          name: 'methanol_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_methanol_emission_factor),
          id: 'ch4_methanol_emission_factor',
          name: 'ch4_methanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_methanol_emitted),
          id: 'ch4_methanol_emitted',
          name: 'ch4_methanol_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_methanol_emission_factor),
          id: 'n2o_methanol_emission_factor',
          name: 'n2o_methanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_methanol_emitted),
          id: 'n2o_methanol_emitted',
          name: 'n2o_methanol_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol Consumption (tons)',
          accessor: (row) => formatValue(row.ethanol_cons),
          id: 'ethanol_cons',
          name: 'ethanol_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.ethanol_emission_factor),
          id: 'ethanol_emission_factor',
          name: 'ethanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.ethanol_co2_emitted),
          id: 'ethanol_co2_emitted',
          name: 'ethanol_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_ethanol_emission_factor),
          id: 'ch4_ethanol_emission_factor',
          name: 'ch4_ethanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_ethanol_emitted),
          id: 'ch4_ethanol_emitted',
          name: 'ch4_ethanol_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_ethanol_emission_factor),
          id: 'n2o_ethanol_emission_factor',
          name: 'n2o_ethanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_ethanol_emitted),
          id: 'n2o_ethanol_emitted',
          name: 'n2o_ethanol_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Other fuel Name',
          accessor: (row) => formatValue(row.other_fuel_name),
          id: 'other_fuel_name',
          name: 'other_fuel_name',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Other fuel Consumptions',
          accessor: (row) => formatValue(row.other_fuel_cons),
          id: 'other_fuel_cons',
          name: 'other_fuel_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Other fuel CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.other_fuel_co2_emission_factor),
          id: 'other_fuel_co2_emission_factor',
          name: 'other_fuel_co2_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Other fuel CO\u2082 Emitted (tons)',
          accessor: (row) => formatValue(row.other_fuel_co2_emitted),
          id: 'other_fuel_co2_emitted',
          name: 'other_fuel_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.total_co2_emitted),
          id: 'total_co2_emitted',
          name: 'total_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_total_emitted),
          id: 'ch4_total_emitted',
          name: 'ch4_total_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_total_emitted),
          id: 'n2o_total_emitted',
          name: 'n2o_total_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total CH\u2084 (CO\u2082 equivalents) emitted (tons)',
          accessor: (row) => formatValue(row.ch4_total_co2_emitted),
          id: 'ch4_total_co2_emitted',
          name: 'ch4_total_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total N\u2082O (CO\u2082 equivalents) emitted (tons)',
          accessor: (row) => formatValue(row.n2o_total_co2_emitted),
          id: 'n2o_total_co2_emitted',
          name: 'n2o_total_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total GHG (CO\u2082 equivalents) emitted (tons)',
          accessor: (row) => formatValue(row.total_ghg),
          id: 'total_ghg',
          name: 'total_ghg',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: getCustomColumnHeader('co2_emitted_voyage_between_eu_port', filterYear),
          accessor: (row) => formatValue((row.eu_port_arrival === 'Yes' && row.eu_port_departure === 'Yes') ? row.co2_emitted_voyage_between_eu_port : 0),
          id: 'co2_emitted_voyage_between_eu_port',
          name: 'co2_emitted_voyage_between_eu_port',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: getCustomColumnHeader('co2_emitted_voyage_from_eu_port', filterYear),
          accessor: (row) => formatValue((row.eu_port_arrival === 'No' && row.eu_port_departure === 'Yes') ? row.co2_emitted_voyage_from_eu_port : 0),
          id: 'co2_emitted_voyage_from_eu_port',
          name: 'co2_emitted_voyage_from_eu_port',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: getCustomColumnHeader('co2_emitted_voyage_to_eu_port', filterYear),
          accessor: (row) => formatValue((row.eu_port_arrival === 'Yes' && row.eu_port_departure === 'No') ? row.co2_emitted_voyage_to_eu_port : 0),
          id: 'co2_emitted_voyage_to_eu_port',
          name: 'co2_emitted_voyage_to_eu_port',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Transport Work (tn*nm)',
          accessor: (row) => formatValue(row.transport_work),
          id: 'transport_work',
          name: 'transport_work',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Fuel consumption per distance (tn/nm)',
          accessor: (row) => formatValue(row.fuel_cons_per_distance),
          id: 'fuel_cons_per_distance',
          name: 'fuel_cons_per_distance',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Fuel consumption per transport work (gr/tn*nm)',
          accessor: (row) => formatValue(row.fuel_cons_per_transport_work),
          id: 'fuel_cons_per_transport_work',
          name: 'fuel_cons_per_transport_work',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: getCustomColumnHeader('co2_emitted_per_distance', filterYear),
          accessor: (row) => formatValue(row.co2_emitted_per_distance),
          id: 'co2_emitted_per_distance',
          name: 'co2_emitted_per_distance',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: getCustomColumnHeader('co2_emitted_per_transport_work', filterYear),
          accessor: (row) => formatValue(row.co2_emitted_per_transport_work),
          id: 'co2_emitted_per_transport_work',
          name: 'co2_emitted_per_transport_work',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Boiler consumption for cargo heating HFO',
          accessor: (row) => formatValue(row.boiler_hfo_cons),
          id: 'boiler_hfo_cons',
          name: 'boiler_hfo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Boiler consumption for cargo heating MGO/MDO',
          accessor: (row) => formatValue(row.boiler_mgo_cons),
          id: 'boiler_mgo_cons',
          name: 'boiler_mgo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Boiler consumption for cargo heating Fuel 3',
          accessor: (row) => formatValue(row.boiler_fuel3_cons),
          id: 'boiler_fuel3_cons',
          name: 'boiler_fuel3_cons',
          type: 'text',
          disableSortBy: true,
        },
      ];
      // if filterYear is less than 2024, remove certain columns
      if (Number(filterYear) < CH4_N2O_RELEASE_YEAR) {
        voyageCols = voyageCols.filter((col) => !mrvColumnsToBeRemovedForPastYears.includes(col.id));

      }
      return voyageCols;
    },
    [filterData?.startDate],
  );

  const mrvPortColumns = useMemo(
    () => {
      const filterYear = moment.utc(filterData?.startDate).format('YYYY');
      let portsCol = [
        {
          Header: 'Voyage No.',
          accessor: (row, index) => index + 1,
          id: 'voyage_no',
          name: 'voyage_no',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Port of Departure',
          accessor: (row) => formatValue(row.port),
          id: 'port',
          name: 'port',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Arrival in the (EU) port',
          accessor: (row) => formatValue(row.eu_port_arrival_date),
          id: 'eu_port_arrival_date',
          name: 'eu_port_arrival_date',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Departure in the (EU) port',
          accessor: (row) => formatValue(row.eu_port_departure_date),
          id: 'eu_port_departure_date',
          name: 'eu_port_departure_date',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Hour of arrival (UTC)',
          accessor: (row) => formatValue(row.arrival_hour),
          id: 'arrival_hour',
          name: 'arrival_hour',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Hour of departure (UTC)',
          accessor: (row) => formatValue(row.departure_hour),
          id: 'departure_hour',
          name: 'departure_hour',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Time spent on land (hours)',
          accessor: (row) => formatValue(row.time_spent_land),
          id: 'time_spent_land',
          name: 'time_spent_land',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Distance (nm)',
          accessor: (row) => formatValue(row.distance),
          id: 'distance',
          name: 'distance',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total HFO Consumption (tons)',
          accessor: (row) => formatValue(row.hfo_cons),
          id: 'hfo_cons',
          name: 'hfo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.hfo_emission_factor),
          id: 'hfo_emission_factor',
          name: 'hfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.hfo_co2_emitted),
          id: 'hfo_co2_emitted',
          name: 'hfo_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_hfo_emission_factor),
          id: 'ch4_hfo_emission_factor',
          name: 'ch4_hfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_hfo_emitted),
          id: 'ch4_hfo_emitted',
          name: 'ch4_hfo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_hfo_emission_factor),
          id: 'n2o_hfo_emission_factor',
          name: 'n2o_hfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'HFO N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_hfo_emitted),
          id: 'n2o_hfo_emitted',
          name: 'n2o_hfo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total LFO Consumption (tons)',
          accessor: (row) => formatValue(row.lfo_cons),
          id: 'lfo_cons',
          name: 'lfo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.lfo_emission_factor),
          id: 'lfo_emission_factor',
          name: 'lfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.lfo_co2_emitted),
          id: 'lfo_co2_emitted',
          name: 'lfo_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_lfo_emission_factor),
          id: 'ch4_lfo_emission_factor',
          name: 'ch4_lfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_lfo_emitted),
          id: 'ch4_lfo_emitted',
          name: 'ch4_lfo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_lfo_emission_factor),
          id: 'n2o_lfo_emission_factor',
          name: 'n2o_lfo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LFO N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_lfo_emitted),
          id: 'n2o_lfo_emitted',
          name: 'n2o_lfo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total MGO Consumption (tons)',
          accessor: (row) => formatValue(row.mgo_cons),
          id: 'mgo_cons',
          name: 'mgo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.mgo_emission_factor),
          id: 'mgo_emission_factor',
          name: 'mgo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.mgo_co2_emitted),
          id: 'mgo_co2_emitted',
          name: 'mgo_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_mgo_emission_factor),
          id: 'ch4_mgo_emission_factor',
          name: 'ch4_mgo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_mgo_emitted),
          id: 'ch4_mgo_emitted',
          name: 'ch4_mgo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_mgo_emission_factor),
          id: 'n2o_mgo_emission_factor',
          name: 'n2o_mgo_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'MGO N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_mgo_emitted),
          id: 'n2o_mgo_emitted',
          name: 'n2o_mgo_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total LNG Consumption (tons)',
          accessor: (row) => formatValue(row.lng_cons),
          id: 'lng_cons',
          name: 'lng_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG Consumption - Main Engine (tons)',
          accessor: (row) => formatValue(row.me_lng),
          id: 'me_lng',
          name: 'me_lng',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Slippage Coefficient - Main Engine',
          accessor: (row) => formatValue(row.lng_engine_category_main_engine_emission_factor),
          id: 'lng_engine_category_main_engine_emission_factor',
          name: 'lng_engine_category_main_engine_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG Consumption - Diesel Generator (tons)',
          accessor: (row) => formatValue(row.ge_lng),
          id: 'ge_lng',
          name: 'ge_lng',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Slippage Coefficient - Diesel Generator',
          accessor: (row) => formatValue(row.lng_engine_category_diesel_generator_emission_factor),
          id: 'lng_engine_category_diesel_generator_emission_factor',
          name: 'lng_engine_category_diesel_generator_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.lng_emission_factor),
          id: 'lng_emission_factor',
          name: 'lng_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.lng_co2_emitted),
          id: 'lng_co2_emitted',
          name: 'lng_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_lng_emission_factor),
          id: 'ch4_lng_emission_factor',
          name: 'ch4_lng_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_lng_emitted),
          id: 'ch4_lng_emitted',
          name: 'ch4_lng_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_lng_emission_factor),
          id: 'n2o_lng_emission_factor',
          name: 'n2o_lng_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LNG N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_lng_emitted),
          id: 'n2o_lng_emitted',
          name: 'n2o_lng_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) Consumption (tons)',
          accessor: (row) => formatValue(row.lpg_propane_cons),
          id: 'lpg_propane_cons',
          name: 'lpg_propane_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.lpg_propane_emission_factor),
          id: 'lpg_propane_emission_factor',
          name: 'lpg_propane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) CO\u2082 Emitted (tons)',
          accessor: (row) => formatValue(row.lpg_propane_co2_emitted),
          id: 'lpg_propane_co2_emitted',
          name: 'lpg_propane_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_lpg_propane_emission_factor),
          id: 'ch4_lpg_propane_emission_factor',
          name: 'ch4_lpg_propane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) CH\u2084 Emitted (tons)',
          accessor: (row) => formatValue(row.ch4_lpg_propane_emitted),
          id: 'ch4_lpg_propane_emitted',
          name: 'ch4_lpg_propane_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_lpg_propane_emission_factor),
          id: 'n2o_lpg_propane_emission_factor',
          name: 'n2o_lpg_propane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Propane) N\u2082O Emitted (tons)',
          accessor: (row) => formatValue(row.n2o_lpg_propane_emitted),
          id: 'n2o_lpg_propane_emitted',
          name: 'n2o_lpg_propane_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) Consumption (tons)',
          accessor: (row) => formatValue(row.lpg_butane_cons),
          id: 'lpg_butane_cons',
          name: 'lpg_butane_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.lpg_butane_emission_factor),
          id: 'lpg_butane_emission_factor',
          name: 'lpg_butane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) CO\u2082 Emitted (tons)',
          accessor: (row) => formatValue(row.lpg_butane_co2_emitted),
          id: 'lpg_butane_co2_emitted',
          name: 'lpg_butane_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_lpg_butane_emission_factor),
          id: 'ch4_lpg_butane_emission_factor',
          name: 'ch4_lpg_butane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) CH\u2084 Emitted (tons)',
          accessor: (row) => formatValue(row.ch4_lpg_butane_emitted),
          id: 'ch4_lpg_butane_emitted',
          name: 'ch4_lpg_butane_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_lpg_butane_emission_factor),
          id: 'n2o_lpg_butane_emission_factor',
          name: 'n2o_lpg_butane_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'LPG (Butane) N\u2082O Emitted (tons)',
          accessor: (row) => formatValue(row.n2o_lpg_butane_emitted),
          id: 'n2o_lpg_butane_emitted',
          name: 'n2o_lpg_butane_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol Consumption (tons)',
          accessor: (row) => formatValue(row.methanol_cons),
          id: 'methanol_cons',
          name: 'methanol_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.methanol_emission_factor),
          id: 'methanol_emission_factor',
          name: 'methanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol CO\u2082 Emitted (tons)',
          accessor: (row) => formatValue(row.methanol_co2_emitted),
          id: 'methanol_co2_emitted',
          name: 'methanol_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_methanol_emission_factor),
          id: 'ch4_methanol_emission_factor',
          name: 'ch4_methanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol CH\u2084 Emitted (tons)',
          accessor: (row) => formatValue(row.ch4_methanol_emitted),
          id: 'ch4_methanol_emitted',
          name: 'ch4_methanol_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_methanol_emission_factor),
          id: 'n2o_methanol_emission_factor',
          name: 'n2o_methanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Methanol N\u2082O Emitted (tons)',
          accessor: (row) => formatValue(row.n2o_methanol_emitted),
          id: 'n2o_methanol_emitted',
          name: 'n2o_methanol_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol Consumption (tons)',
          accessor: (row) => formatValue(row.ethanol_cons),
          id: 'ethanol_cons',
          name: 'ethanol_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.ethanol_emission_factor),
          id: 'ethanol_emission_factor',
          name: 'ethanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol CO\u2082 Emitted (tons)',
          accessor: (row) => formatValue(row.ethanol_co2_emitted),
          id: 'ethanol_co2_emitted',
          name: 'ethanol_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol CH\u2084 Emission Factor',
          accessor: (row) => formatValue(row.ch4_ethanol_emission_factor),
          id: 'ch4_ethanol_emission_factor',
          name: 'ch4_ethanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol CH\u2084 Emitted (tons)',
          accessor: (row) => formatValue(row.ch4_ethanol_emitted),
          id: 'ch4_ethanol_emitted',
          name: 'ch4_ethanol_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol N\u2082O Emission Factor',
          accessor: (row) => formatValue(row.n2o_ethanol_emission_factor),
          id: 'n2o_ethanol_emission_factor',
          name: 'n2o_ethanol_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Ethanol N\u2082O Emitted (tons)',
          accessor: (row) => formatValue(row.n2o_ethanol_emitted),
          id: 'n2o_ethanol_emitted',
          name: 'n2o_ethanol_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Other fuel Name',
          accessor: (row) => formatValue(row.other_fuel_name),
          id: 'other_fuel_name',
          name: 'other_fuel_name',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Other fuel Consumptions',
          accessor: (row) => formatValue(row.other_fuel_cons),
          id: 'other_fuel_cons',
          name: 'other_fuel_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Other fuel CO\u2082 Emission Factor',
          accessor: (row) => formatValue(row.other_fuel_co2_emission_factor),
          id: 'other_fuel_co2_emission_factor',
          name: 'other_fuel_co2_emission_factor',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Other fuel CO\u2082 Emitted (tons)',
          accessor: (row) => formatValue(row.other_fuel_co2_emitted),
          id: 'other_fuel_co2_emitted',
          name: 'other_fuel_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total CO\u2082 emitted (tons)',
          accessor: (row) => formatValue(row.total_co2_emitted),
          id: 'total_co2_emitted',
          name: 'total_co2_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total CH\u2084 emitted (tons)',
          accessor: (row) => formatValue(row.ch4_total_emitted),
          id: 'ch4_total_emitted',
          name: 'ch4_total_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total N\u2082O emitted (tons)',
          accessor: (row) => formatValue(row.n2o_total_emitted),
          id: 'n2o_total_emitted',
          name: 'n2o_total_emitted',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Total GHG (CO\u2082 equivalents) emitted (tons)',
          accessor: (row) => formatValue(row.total_ghg),
          id: 'total_ghg',
          name: 'total_ghg',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Boiler consumption for cargo heating HFO',
          accessor: (row) => formatValue(row.boiler_hfo_cons),
          id: 'boiler_hfo_cons',
          name: 'boiler_hfo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Boiler consumption for cargo heating MGO/MDO',
          accessor: (row) => formatValue(row.boiler_mgo_cons),
          id: 'boiler_mgo_cons',
          name: 'boiler_mgo_cons',
          type: 'text',
          disableSortBy: true,
        },
        {
          Header: 'Boiler consumption for cargo heating Fuel 3',
          accessor: (row) => formatValue(row.boiler_fuel3_cons),
          id: 'boiler_fuel3_cons',
          name: 'boiler_fuel3_cons',
          type: 'text',
          disableSortBy: true,
        },
      ];
      // if filterYear is less than 2024, remove certain columns
      if (Number(filterYear) < CH4_N2O_RELEASE_YEAR) {
        portsCol = portsCol.filter((col) => !mrvColumnsToBeRemovedForPastYears.includes(col.id));

      }
      return portsCol;
    },
    [filterData?.startDate],
  );

  const fetchMrvReport = async () => {
    setLoading(true);
    setMrvVoyageReport([]);
    setMrvPortReport([]);
    try {
      if (filterData?.startDate && filterData?.endDate) {
        const startYear = filterData.startDate;
        filterData.startDate = moment(startYear).startOf('year').format('YYYY-MM-DD');
        filterData.endDate = moment(startYear).endOf('year').format('YYYY-MM-DD');
        let pageFilterData = '';
        if (filterData?.startDate || filterData?.endDate || filterData?.eu) {
          pageFilterData = `startDate=${filterData.startDate ? filterData.startDate : ''}&endDate=${filterData.endDate ? filterData.endDate : ''
            }&eu=${filterData.eu}`;
        }
        const response = await vesselService.getMrvReport(
          !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
          pageFilterData,
        );
        const { voyage, port } = response.data;
        setMrvVoyageReport(voyage);
        setMrvPortReport(response.data.port);
        setExcelData({ ...getMrvExcelData(filterData.vessel[0]?.name, voyage, port, filterData.startDate) });
        setError(null);
      }
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  const onSelectDropDown = (id, checked, type) => {
    if (type === 'voyage') {
      if (selectedVoyageColumnIds?.length > 1) {
        if (checked) onChangeMrvReportTableColumns(type, [...selectedVoyageColumnIds, id]);
        else {
          const listIds = _.remove(selectedVoyageColumnIds, function (n) {
            return n != id;
          });
          onChangeMrvReportTableColumns(type, listIds);
        }
      }
    }
    if (type === 'port') {
      if (selectedPortColumnIds?.length > 1) {
        if (checked) onChangeMrvReportTableColumns(type, [...selectedPortColumnIds, id]);
        else {
          const listIds = _.remove(selectedPortColumnIds, function (n) {
            return n != id;
          });
          onChangeMrvReportTableColumns(type, listIds);
        }
      }
    }
  };

  useEffect(() => {
    const voyageColumnsData = _.filter(mrvVoyageColumns, function (o) {
      return _.includes(selectedVoyageColumnIds, o.id);
    });
    const portColumnsData = _.filter(mrvPortColumns, function (o) {
      return _.includes(selectedPortColumnIds, o.id);
    });
    setSelectedVoyageColumns(voyageColumnsData);
    setSelectedPortColumns(portColumnsData);
  }, [selectedVoyageColumnIds, selectedPortColumnIds, mrvVoyageColumns, mrvPortColumns]);

  useEffect(() => {
    if (filterData?.vessel[0]?.id) {
      fetchMrvReport();
    }
  }, [filterData]);

  return (
    <div>
      <div className="pt-2">
        {error && <ErrorAlert message={error} />}
        <hr className="border-line" />
        <div className="d-flex justify-content-between">
          <div className="emergency-drills font-weight-bold">Monitoring During Voyage</div>

          <ButtonToolbar>
            <Dropdown alignRight className="ml-auto">
              <Dropdown.Toggle
                variant="outline-primary"
                id="dropdown-more"
                data-testid="fml-mrv-report-voyage-table-columns"
              >
                Table Columns
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {mrvVoyageColumns.map((item) => {
                  const checked = _.includes(selectedVoyageColumnIds, item.id);
                  return (
                    <Dropdown.Item
                      key={item.id}
                      as={CustomDropDownItemCheck}
                      checked={checked}
                      onClick={() => onSelectDropDown(item.id, !checked, 'voyage')}
                    >
                      {item.showToolTip ? item.name : item.Header}
                    </Dropdown.Item>
                  );
                })}
              </Dropdown.Menu>
            </Dropdown>
          </ButtonToolbar>
        </div>

        {filterData?.vessel[0]?.id ? (
          <CustomTable
            column={selectedVoyageColumns}
            reportData={mrvVoyageReport}
            tableRef={null}
            isLoading={loading}
            pagination={false}
            className="drill-table-top-border"
          />
        ) : (
          <div className="no-result-found mt-5">
            <Icon icon="alert" className="alert-icon-no-search" />
            <div data-testid="fml-no-result">
              <b>No Vessel Selected. Please select a vessel</b>
            </div>
          </div>
        )}
      </div>

      <div className="pt-2">
        {error && <ErrorAlert message={error} />}
        <hr className="border-line" />
        <div className="d-flex justify-content-between">
          <div className="emergency-drills font-weight-bold">Monitoring During Port</div>

          <ButtonToolbar>
            <Dropdown alignRight className="ml-auto">
              <Dropdown.Toggle
                variant="outline-primary"
                id="dropdown-more"
                data-testid="fml-mrv-report-port-table-columns"
              >
                Table Columns
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {mrvPortColumns.map((item) => {
                  const checked = _.includes(selectedPortColumnIds, item.id);
                  return (
                    <Dropdown.Item
                      key={item.id}
                      as={CustomDropDownItemCheck}
                      checked={checked}
                      onClick={() => onSelectDropDown(item.id, !checked, 'port')}
                    >
                      {item.showToolTip ? item.name : item.Header}
                    </Dropdown.Item>
                  );
                })}
              </Dropdown.Menu>
            </Dropdown>
          </ButtonToolbar>
        </div>

        {filterData?.vessel[0]?.id ? (
          <CustomTable
            column={selectedPortColumns}
            reportData={mrvPortReport}
            tableRef={null}
            isLoading={loading}
            pagination={false}
            className="drill-table-top-border"
          />
        ) : (
          <div className="no-result-found mt-5">
            <Icon icon="alert" className="alert-icon-no-search" />
            <div data-testid="fml-no-result">
              <b>No Vessel Selected. Please select a vessel</b>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MrvReport;