import { EnvironmentalReportContext } from '../../../context/EnvironmentalReportContext';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import vesselService from '../../../service/vessel-service';
import { formatDate, formatValue } from '../../../util/view-utils';
import CustomTable from '../../customComponent/CustomTable';
import ErrorAlert from '../../ErrorAlert';

const SealLogList = ({ ownershipId }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState();
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([]);
  const [sealList, setSealList] = useState();
  const { ga4EventTrigger = () => {} } = useContext(EnvironmentalReportContext);

  const columns = useMemo(
    () => [
      {
        Header: 'First Seal#',
        accessor: (row) => formatValue(row.serial),
        sticky: 'left',
        id: 'serial',
        type: 'text',
      },
      {
        Header: 'Location',
        accessor: (row) =>
          row.location === 'Ship' ? `${row.location} - ${row.location_desc}` : row.location,
        id: 'location',
        type: 'text',
      },
      {
        Header: 'Status',
        id: 'status',
        type: 'text',
        accessor: (row) => formatValue(row.status),
      },
      {
        Header: 'Date',
        id: 'date',
        type: 'date',
        accessor: (row) => formatDate(row.date, 'DD MMM YYYY'),
      },
    ],
    [],
  );

  const fetchSealLog = async (
    sortData = [{ id: 'affixed_date', desc: true }],
    pageIndex = 0,
    pageSize = 10,
  ) => {
    setLoading(true);
    setError(null);
    try {
      const response = await vesselService.getSealLog(
        ownershipId,
        {
          sortBy: sortData,
          pageSize: pageSize,
          pageIndex: pageIndex,
        },
        'mode=log',
      );
      setSealList(response.data.results);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchSealLog(sortData, pageIndex, pageSize);
  }, []);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchSealLog(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', 'Seal Report - List', value);
    setPageSize(value);
    setPageIndex(0);
    fetchSealLog(sortData, 0, value);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Seal Report - List', value[0]?.id);
    setSortData(value);
    fetchSealLog(value, pageIndex, pageSize);
  };

  return (
    <div className="pt-4">
      {error && <ErrorAlert message={error} />}
      <CustomTable
        column={columns}
        reportData={sealList}
        tableRef={null}
        isLoading={loading}
        pagination={true}
        pageCount={pageCount}
        totalCount={totalCount}
        setPageNo={onPageIndexChange}
        setPageListSize={onPageSizeChange}
        setSortData={onSortChange}
        pageNo={pageIndex}
        className="drill-table-top-border"
        pageSize={pageSize}
      />
    </div>
  );
};

export default SealLogList;
