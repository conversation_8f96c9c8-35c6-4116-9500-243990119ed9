import { EnvironmentalReportContext } from '../../../context/EnvironmentalReportContext';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Button, Form } from 'react-bootstrap';
import vesselService from '../../../service/vessel-service';
import { Icon } from '../../../styleGuide';
import { formatDate, formatValue } from '../../../util/view-utils';
import ConfirmModal from '../../customComponent/CustomConfirmationModal';
import CustomTable from '../../customComponent/CustomTable';
import ErrorAlert from '../../ErrorAlert';
import Spinner from '../../Spinner';
import AddSealGroupModal from '../AddSealGroupModal';
import _ from 'lodash';

const UnusedSealList = ({ ownershipId }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState();
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([]);
  const [sealList, setSealList] = useState();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [rowData, setRowData] = useState({});
  const [deleteReason, setDeleteReason] = useState('');
  const [disableConfirm, setDisableConfirm] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const {
    vesselList,
    ga4EventTrigger = () => {},
    activeVesselList,
    roleConfig,
  } = useContext(EnvironmentalReportContext);
  const vesselName = vesselList.filter(({ id }) => Number(id) === Number(ownershipId))[0]?.value;

  const columns = useMemo(
    () => [
      {
        Header: '#',
        accessor: (row) => formatValue(row.batch),
        sticky: 'left',
        id: 'batch',
        name: 'batch',
        type: 'text',
        maxWidth: 50,
      },
      {
        Header: 'First Seal#',
        accessor: (row) => formatValue(row.first_num),
        id: 'first_num',
        name: 'first_num',
        type: 'text',
      },
      {
        Header: 'Last Seal#',
        id: 'last_num',
        type: 'text',
        accessor: (row) => formatValue(row.last_num),
        name: 'last_num',
      },
      {
        Header: 'Type',
        id: 'seal_type',
        type: 'text',
        accessor: (row) => formatValue(row.seal_type),
        name: 'seal_type',
      },
      {
        Header: 'Location',
        accessor: (row) => formatValue(row.location_type),
        id: 'location_type',
        type: 'text',
        maxWidth: 120,
        name: 'location_type',
      },
      {
        Header: 'Status',
        accessor: (row) => formatValue(row.status),
        id: 'status',
        type: 'text',
        maxWidth: 120,
        name: 'status',
      },
      {
        Header: 'Delete Reason',
        accessor: (row) => formatValue(row.delete_reason),
        id: 'deleteReason',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
        name: 'delete_reason',
      },
      {
        Header: 'Date',
        accessor: (row) => formatDate(row.date, 'DD MMM YYYY'),
        id: 'date',
        name: 'date',
        type: 'date',
        maxWidth: 120,
      },
      {
        Header: 'Action',
        accessor: (row, index) => (
          <div className="text-center">
            {row.status === 'In Transit' ? (
              <Icon
                icon="Delete"
                size={20}
                style={{ color: 'black' }}
                onClick={() => {
                  ga4EventTrigger('Delete Seal Group', 'Seal Report Detail - Menu', vesselName);
                  setShowConfirmModal(true);
                  setRowData(row);
                }}
              />
            ) : null}
          </div>
        ),
        id: 'action',
        name: 'action',
        type: 'text',
        disableSortBy: true,
        maxWidth: 60,
      },
    ],
    [],
  );

  const fetchSealGroup = async (sortData = [], pageIndex = 0, pageSize = 10) => {
    setError(null);
    setLoading(true);
    try {
      const response = await vesselService.getSealGroup(
        ownershipId,
        {
          sortBy: sortData,
          pageSize: pageSize,
          pageIndex: pageIndex,
        },
        '',
      );
      setSealList(response.data.results);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchSealGroup(sortData, pageIndex, pageSize);
  }, []);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchSealGroup(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', 'Seal Report - List', value);
    setPageSize(value);
    setPageIndex(0);
    fetchSealGroup(sortData, 0, value);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Seal Report - List', value[0]?.id);
    setSortData(value);
    fetchSealGroup(value, pageIndex, pageSize);
  };

  const handleDelete = async () => {
    ga4EventTrigger('Confirm Delete Seal Group', 'Seal Report Detail - Menu', vesselName);
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setShowConfirmModal(false);
    setIsUpdating(true);
    setError(null);
    try {
      await vesselService.deleteSealGroup(ownershipId, {
        batch: rowData.batch,
        first_num: rowData.first_num,
        last_num: rowData.last_num,
        delete_reason: deleteReason,
      });
      setRowData({});
      fetchSealGroup(sortData, pageIndex, pageSize);
    } catch (error) {
      setError('Oops, something went wrong with delete. Please try again.');
    }
    setIsUpdating(false);
  };

  const handleCancel = () => {
    setShowConfirmModal(false);
  };

  const handleSubmit = () => {
    ga4EventTrigger('Confirm Add Seal Group', 'Seal Report Detail - Menu', vesselName);
    setShowAddModal(false);
    fetchSealGroup([{ id: 'batch', desc: true }]);
  };

  return (
    <>
      <div className="d-flex flex-row-reverse">
        {roleConfig.vessel.edit &&
          _.find(activeVesselList, ({ id }) => Number(id) === Number(ownershipId)) && (
            <div className="no-print">
              <Button
                size="sm"
                variant="outline-primary"
                className="mr-2"
                data-testid="fml-vessel-seal-add-seal-group-Button"
                onClick={() => {
                  ga4EventTrigger('Add Seal Group', 'Seal Report Detail - Menu', vesselName);
                  setShowAddModal(true);
                }}
              >
                Add Seal Group
              </Button>
            </div>
          )}
      </div>
      <div className="pt-4">
        {isUpdating && <Spinner alignClass="update-spinner" />}
        {error && <ErrorAlert message={error} />}
        <CustomTable
          column={columns}
          reportData={sealList}
          tableRef={null}
          isLoading={loading}
          pagination={true}
          pageCount={pageCount}
          totalCount={totalCount}
          setPageNo={onPageIndexChange}
          setPageListSize={onPageSizeChange}
          setSortData={onSortChange}
          pageNo={pageIndex}
          className="drill-table-top-border"
          pageSize={pageSize}
        />
        <AddSealGroupModal
          showModal={showAddModal}
          handleModalCancel={() => setShowAddModal(false)}
          handleModalSubmit={handleSubmit}
          ownershipId={ownershipId}
          setServerError={setError}
        />
        <ConfirmModal
          showConfirmModal={showConfirmModal}
          setShowConfirmModal={setShowConfirmModal}
          title={`Confirm Deleting the Batch ${rowData?.batch} of Seal?`}
          content={
            <div>
              <Form.Label className="form-label">Reason</Form.Label>
              <Form.Control
                type="text"
                as="textarea"
                data-testid="fml-seal-delete-reason"
                className="message-input"
                onChange={(e) => {
                  const value = e.target.value;
                  setDeleteReason(value);
                  setDisableConfirm(!value || value === '');
                }}
              />
            </div>
          }
          isDisabledConfirm={disableConfirm}
          handleCancel={handleCancel}
          handleConfirm={handleDelete}
        />
      </div>
    </>
  );
};

export default UnusedSealList;
