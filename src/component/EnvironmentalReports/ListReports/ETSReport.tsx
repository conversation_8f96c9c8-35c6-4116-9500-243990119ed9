import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import _ from 'lodash';
import moment from 'moment';
import { ButtonToolbar, Dropdown } from 'react-bootstrap';
import { EnvironmentalReportContext } from '../../../context/EnvironmentalReportContext';
import vesselService from '../../../service/vessel-service';
import { Icon } from '../../../styleGuide';
import { getEUETSExcelData } from '../../../util/eu-ets-export/eu-ets-excel-data';
import ErrorAlert from '../../ErrorAlert';
import CustomDropDownItemCheck from '../../customComponent/CustomDropDownItemCheck';
import CustomTable from '../../customComponent/CustomTable';
import ETSColumns from './ETSComponents/ETSColumns';
import { EU_BANNER_CHECK } from '../../../model/constants';

const ETSReport = ({
  loading,
  setLoading,
  error,
  setError,
  selectedVoyageColumnIds,
  selectedPortColumnIds,
  selectedPendingVoyageColumnIds,
  onChangeEtsReportTableColumns,
  ga4EventTrigger,
}) => {
  const {
    filterData,
    setExcelData,
    roleConfig,
    setBannerInfo,
  } = useContext(EnvironmentalReportContext);
  const [ETSVoyageReport, setETSVoyageReport] = useState([]);
  const [ETSPortReport, setETSPortReport] = useState([]);
  const [ETSPendingVoyageReport, setETSPendingVoyageReport] = useState([]);
  const [selectedPendingVoyageColumns, setSelectedPendingVoyageColumns] = useState([]);
  const [selectedVoyageColumns, setSelectedVoyageColumns] = useState([]);
  const [selectedPortColumns, setSelectedPortColumns] = useState([]);
  const [emissionRate, setEmissionRate] = useState('');
  const [lastUpdatedDateOfEmissionRate, setLastUpdatedDateOfEmissionRate] = useState('');
  const [selectedVesselId, setSelectedVesselId] = useState('');
  const [ownershipData, setOwnershipData] = useState(null);
  const [voyagesUpdated, setVoyagesUpdated] = useState(false);
  const [estimateFuelEUPenalty, setEstimateFuelEUPenalty] = useState('0.00');

  const voyagesUpdatedCallback = useCallback(() => {
    setVoyagesUpdated(!voyagesUpdated);
  }, [ETSVoyageReport, ETSPendingVoyageReport]);
  const SyncDataHandler = useCallback(async () => {
    setLoading(true);
    try {
      const vesselId = filterData.vessel[0].id;
      console.log(`Syncing data for vesselId: ${vesselId}`);
      ga4EventTrigger('Click', 'EU-ETS – Manual Sync', '[Sync]');
      const response = await vesselService.syncVendorData(
        vesselId,
        moment(filterData.startDate).year(),
        filterData.dataSource[0]?.value,
      );
      if (response.status === 200) {
        setError(null);
        voyagesUpdatedCallback();
      }
    } catch (error) {
      setError(`Failed to sync data.`);
      console.log(`error in SyncDataHandler: `, error);
    }
    setLoading(false);
  }, [filterData, setLoading, setError, voyagesUpdatedCallback]);
  const ETSVoyageColumns = useMemo(() => {
    return ETSColumns.getETSVoyageColums(filterData?.startDate, voyagesUpdatedCallback);
  }, [filterData?.startDate, voyagesUpdatedCallback]);

  const ETSPendingVoyageColumns = useMemo(() => {
    return ETSColumns.getETSPendingVoyageColumns(
      filterData?.startDate,
      roleConfig,
      filterData.vessel.length ? filterData.vessel[0].id : '',
      voyagesUpdatedCallback,
    );
  }, [filterData, roleConfig, voyagesUpdatedCallback]);

  const ETSPortColumns = useMemo(() => {
    return ETSColumns.getETSPortColumns(filterData?.startDate);
  }, [filterData?.startDate]);

  const checkEUBannerReport = (reportData) => {
    setBannerInfo(false);
    const countriesToCheck = EU_BANNER_CHECK.BANNER_COUNTRY;

    const hasMatchingCountryVoyage = reportData.voyage?.some(
      (eachReport) =>
        (eachReport.arrival_country && countriesToCheck.includes(eachReport.arrival_country)) ||
        (eachReport.departure_country && countriesToCheck.includes(eachReport.departure_country)),
    );

    const hasMatchingCountryPort = reportData.port?.some(
      (eachReport) => eachReport.port_country && countriesToCheck.includes(eachReport.port_country),
    );

    const hasMatchingCountryPendingVoyage =
      reportData.pendingVoyage?.departure_country &&
      countriesToCheck.includes(reportData.pendingVoyage.departure_country);

    const hasAnyMatchingCountry =
      hasMatchingCountryVoyage || hasMatchingCountryPort || hasMatchingCountryPendingVoyage;

    if (hasAnyMatchingCountry) setBannerInfo(true);
  };

  const fetchETSReportYTD = async (vesselId, pageFilterData) => {
    const updateFilterData = (pageFilterData) => {
      const params = new URLSearchParams(pageFilterData);
      const year = new Date(params.get('startDate')).getFullYear();
      const currentDate = new Date().toISOString().split('T')[0];
      params.set('endDate', currentDate);

      const datasourceValue = params.get('datasource');
      return {
        year,
        filterString: `startDate=${year}-01-01&endDate=${params.get('endDate')}&regulation=EU${
          datasourceValue === 'NAVTOR' ? `&datasource=${datasourceValue}` : ''
        }`,
      };
    };
    const { year, filterString } = updateFilterData(pageFilterData);
    // If the year is less than 2025, return default empty arrays
    if (year < 2025) {
      return { voyage: [], port: [] };
    }

    try {
      const etsReportResponseYTD = await vesselService.getETSReport(vesselId, filterString);
      const { voyage, port } = etsReportResponseYTD.data || {};
      return { voyage: voyage || [], port: port || [] };
    } catch (error) {
      console.error('Error fetching ETS report:', error);
      return { voyage: [], port: [] }; // Return empty arrays in case of error
    }
  };

  const fetchETSReport = useCallback(async (ownershipData) => {
    setLoading(true);
    const  currentVendorDataSourceOptions = await vesselService.getReportEnabledDatasourceConfigs( filterData.vessel[0].id);
    // Don't fetch if dataSource is empty and currentVendorDataSourceOptions is not empty
  if (filterData.dataSource.length === 0 && currentVendorDataSourceOptions.data.length > 0) {
      setLoading(false);
    return;
  }
    const vesselId = filterData.vessel[0].id;
    try {
      if (filterData?.startDate && filterData?.endDate && filterData?.vessel.length && vesselId) {
        const params = new URLSearchParams();

        // Add date and regulation parameters
        params.append('startDate', filterData?.startDate || '');
        params.append('endDate', filterData?.endDate || '');
        params.append('regulation', 'EU');
        if (filterData?.dataSource?.length && filterData.dataSource[0]?.value !== 'PARIS') {
          params.append('datasource', filterData.dataSource[0].value);
          console.log(`pageFilterData with datasource`, params.toString());
        }

        const pageFilterData = params.toString();
        const etsReportResponse = await vesselService.getETSReport(vesselId, pageFilterData);
        const { voyage, port, pendingVoyage, emissionRate } = etsReportResponse.data;
        const YTDFuelEUPenaltyData = await fetchETSReportYTD(vesselId, pageFilterData);
        const totalEstimateFuelEUPenalty = [
          ...YTDFuelEUPenaltyData.voyage,
          ...YTDFuelEUPenaltyData.port,
        ].reduce((sum, item) => sum + (item.estimate_fuel_eu_penalty || 0), 0);

        const totalComplianceBalance = [...voyage, ...port].reduce(
          (sum, item) => sum + (item.compliance_balance || 0),
          0,
        );
        const voyageEnergyConsumed = [...voyage].reduce(
          (sum, item) => sum + (item.total_energy_consumed || 0),
          0,
        );
        const portEnergyConsumed = [...port].reduce(
          (sum, item) => sum + (item.total_energy_consumed || 0),
          0,
        );
        const averageGHGIntensitySumProductVoyage = [...voyage].reduce(
          (sum, item) =>
            sum +
            (parseFloat(item.total_energy_consumed) || 0) *
              (parseFloat(item.average_ghg_intensity) || 0),
          0,
        );
        const averageGHGIntensitySumProductPort = [...port].reduce(
          (sum, item) =>
            sum +
            (parseFloat(item.total_energy_consumed) || 0) *
              (parseFloat(item.average_ghg_intensity) || 0),
          0,
        );
        const averageGHGIntensity =
          (averageGHGIntensitySumProductVoyage + averageGHGIntensitySumProductPort) /
          (voyageEnergyConsumed + portEnergyConsumed);

        setEstimateFuelEUPenalty(
          Number(totalEstimateFuelEUPenalty) < 0
            ? '0.00'
            : Number(totalEstimateFuelEUPenalty).toFixed(2),
        );
        setETSVoyageReport(voyage);
        setETSPortReport(port);
        if (pendingVoyage) {
          setETSPendingVoyageReport([pendingVoyage]);
        }
        checkEUBannerReport(etsReportResponse.data);
        setEmissionRate(emissionRate.rate);
        const lastUpdatedDateOfEmissionRate = moment(emissionRate.updated_at).format('DD MMM YYYY');
        setLastUpdatedDateOfEmissionRate(lastUpdatedDateOfEmissionRate);
        const pendingVoyages = pendingVoyage ? [pendingVoyage] : [];
        // set data for excel top config here
        const sampleDataObjForExcelTopSection = voyage[0] || port[0];
        const topSectionData = {
          nameOfShip: ownershipData?.name || '---',
          lfoEmissionFactor: '---',
          lngEmissionFactor: '---',
          methanolEmissionFactor: '---',
          hfoEmissionFactor: '---',
          mgoEmissionFactor: '---',
          lpgPropaneEmissionFactor: '---',
          ethanolEmissionFactor: '---',
          lpgButaneEmissionFactor: '---',
          verifierName: '---',
          totalEUAAccrued: 0,
          averageGHGIntensity: averageGHGIntensity ?? 0,
          totalComplianceBalance: totalComplianceBalance ?? 0,
          totalEstimateFuelEUPenalty: totalEstimateFuelEUPenalty ?? 0,
        };
        topSectionData.totalEUAAccrued =
          voyage.reduce((acc, v) => {
            return acc + parseFloat(v.eua);
          }, 0) +
          port.reduce((acc, p) => {
            return acc + parseFloat(p.eua);
          }, 0);
        if (sampleDataObjForExcelTopSection) {
          topSectionData.lfoEmissionFactor = sampleDataObjForExcelTopSection.lfo_emission_factor;
          topSectionData.lngEmissionFactor = sampleDataObjForExcelTopSection.lng_emission_factor;
          topSectionData.methanolEmissionFactor =
            sampleDataObjForExcelTopSection.methanol_emission_factor;
          topSectionData.hfoEmissionFactor = sampleDataObjForExcelTopSection.hfo_emission_factor;
          topSectionData.mgoEmissionFactor = sampleDataObjForExcelTopSection.mgo_emission_factor;
          topSectionData.lpgPropaneEmissionFactor =
            sampleDataObjForExcelTopSection.lpg_propane_emission_factor;
          topSectionData.ethanolEmissionFactor =
            sampleDataObjForExcelTopSection.ethanol_emission_factor;
          topSectionData.lpgButaneEmissionFactor =
            sampleDataObjForExcelTopSection.lpg_butane_emission_factor;
          topSectionData.verifierName = sampleDataObjForExcelTopSection.class_name ?? '---';
        }
        setExcelData({
          ...getEUETSExcelData({
            vesselName: ownershipData?.name,
            voyage,
            pendingVoyages,
            port,
            vesselOwnershipData: ownershipData,
            reportingYear: getReportingYear(filterData.startDate, filterData.endDate),
            reportingDuration: getReportingDuration(filterData.startDate, filterData.endDate),
            etsRate: emissionRate.rate,
            etsRateDate: lastUpdatedDateOfEmissionRate,
            filterData,
            topSectionData,
          }),
        });
        setError(null);
      }
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
      console.log(`error in ETSReport: `, error);
    }
    setLoading(false);
  }, [filterData.startDate, filterData.endDate, filterData.vessel[0], filterData.dataSource]);

  const getReportingYear = (startDate, endDate) => {
    const uniqueYears = [
      ...new Set([
        moment(startDate).toDate().getFullYear(),
        moment(endDate).toDate().getFullYear(),
      ]),
    ];
    return uniqueYears.join(', ');
  };

  const getReportingDuration = (startDate, endDate) => {
    return `${moment(startDate).format('DD/MM/YYYY')} to ${moment(endDate).format('DD/MM/YYYY')}`;
  };

  const onSelectDropDown = (id, checked, type) => {
    if (type === 'voyage') {
      if (selectedVoyageColumnIds?.length > 1) {
        if (checked) onChangeEtsReportTableColumns(type, [...selectedVoyageColumnIds, id]);
        else {
          const listIds = _.remove(selectedVoyageColumnIds, function (n) {
            return n != id;
          });
          onChangeEtsReportTableColumns(type, listIds);
        }
      }
    }
    if (type === 'port') {
      if (selectedPortColumnIds?.length > 1) {
        if (checked) onChangeEtsReportTableColumns(type, [...selectedPortColumnIds, id]);
        else {
          const listIds = _.remove(selectedPortColumnIds, function (n) {
            return n != id;
          });
          onChangeEtsReportTableColumns(type, listIds);
        }
      }
    }
    if (type === 'pending-voyage') {
      if (selectedPendingVoyageColumnIds?.length > 1) {
        if (checked) onChangeEtsReportTableColumns(type, [...selectedPendingVoyageColumnIds, id]);
        else {
          const listIds = _.remove(selectedPendingVoyageColumnIds, function (n) {
            return n != id;
          });
          onChangeEtsReportTableColumns(type, listIds);
        }
      }
    }
    if (checked) {
      ga4EventTrigger('Click', `EU-ETS – Table Columns`, `${type}-${id}`);
    }
    ga4EventTrigger('Click', `EU-ETS – Link`, 'Click Table Columns');
  };

  useEffect(() => {
    const voyageColumnsData = ETSVoyageColumns.filter((o) =>
      selectedVoyageColumnIds.includes(o.id),
    );
    const portColumnsData = ETSPortColumns.filter((o) => selectedPortColumnIds.includes(o.id));
    const pendingVoyageColumns = ETSPendingVoyageColumns.filter((o) =>
      selectedPendingVoyageColumnIds.includes(o.id),
    );
    setSelectedVoyageColumns(voyageColumnsData);
    setSelectedPortColumns(portColumnsData);
    setSelectedPendingVoyageColumns(pendingVoyageColumns);
  }, [
    selectedVoyageColumnIds,
    selectedPortColumnIds,
    selectedPendingVoyageColumnIds,
    ETSPortColumns,
    ETSVoyageColumns,
    ETSPendingVoyageColumns,
  ]);

  useEffect(() => {
    (async () => {
      if (filterData?.vessel[0]?.id) {
        let vesselOwnershipData = ownershipData;
        if (filterData.vessel[0].id !== selectedVesselId) {
          setSelectedVesselId(filterData.vessel[0].id);
          setLoading(true);
          const ownershipResponse = await vesselService.getOwnershipVessel(filterData.vessel[0].id);
          vesselOwnershipData = ownershipResponse.data;
          setOwnershipData(vesselOwnershipData);
        }
        fetchETSReport(vesselOwnershipData);
      }
    })();
  }, [filterData, voyagesUpdated]);

  return (
    <div className="eu-ets">
      <div className="pt-2">
        {error && <ErrorAlert message={error} />}
        <hr className="border-line" />
        {filterData.dataSource?.length > 0 && filterData.dataSource[0]?.value !== 'PARIS' && (
          <div>
            {roleConfig.vessel.manualSyncReport && (
                <button className="btn btn-outline-primary mr-2" onClick={SyncDataHandler}>
                Sync Data
                </button>
            )}
            <span className="small">
              Last updated at {filterData.dataSource[0]?.lastSyncAt}
            </span>
          </div>
        )}
        <div className="d-flex justify-content-between heading">
          <div className="emergency-drills font-weight-bold">Monitoring During Voyage</div>
          <div className="rate-container-parent">
            {estimateFuelEUPenalty ? (
              <div className="rate-container">
                YTD FuelEU Penalty (€): &nbsp; <span>{estimateFuelEUPenalty}</span>
              </div>
            ) : (
              <></>
            )}
            {emissionRate ? (
              <div className="rate-container">
                Carbon Rate (€/ton): &nbsp; <span>{emissionRate}</span> &nbsp; (as of{' '}
                {lastUpdatedDateOfEmissionRate})
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>
        <div className="d-flex justify-content-between m-20">
          <div className="sub-heading">Completed Voyages</div>
          <ButtonToolbar>
            <Dropdown alignRight className="ml-auto">
              <Dropdown.Toggle
                variant="outline-primary"
                id="dropdown-more"
                data-testid="fml-ETS-report-voyage-table-columns">
                Table Columns
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {ETSVoyageColumns.map((item) => {
                  const checked = selectedVoyageColumnIds.includes(item.id);
                  return (
                    <Dropdown.Item
                      key={item.id}
                      as={CustomDropDownItemCheck}
                      checked={checked}
                      onClick={() => onSelectDropDown(item.id, !checked, 'voyage')}>
                      {item.showToolTip ? item.name : item.Header}
                    </Dropdown.Item>
                  );
                })}
              </Dropdown.Menu>
            </Dropdown>
          </ButtonToolbar>
        </div>

        {filterData?.vessel[0]?.id ? (
          <CustomTable
            column={selectedVoyageColumns}
            reportData={ETSVoyageReport}
            isLoading={loading}
            pagination={false}
            className="drill-table-top-border"
          />
        ) : (
          <div className="no-result-found mt-5">
            <Icon icon="alert" className="alert-icon-no-search" />
            <div data-testid="fml-no-result">
              <b>No Vessel Selected. Please select a vessel</b>
            </div>
          </div>
        )}
      </div>
      <div className="pt-2">
        {error && <ErrorAlert message={error} />}
        <hr className="border-line" />
        <div className="d-flex justify-content-between m-20 fx-v-center">
          <div className="sub-heading">Pending Voyages</div>
          <ButtonToolbar>
            <Dropdown alignRight className="ml-auto">
              <Dropdown.Toggle
                variant="outline-primary"
                id="dropdown-more"
                data-testid="fml-ETS-report-voyage-table-columns">
                Table Columns
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {ETSPendingVoyageColumns.map((item) => {
                  const checked = selectedPendingVoyageColumnIds.includes(item.id);
                  return (
                    <Dropdown.Item
                      key={item.id}
                      as={CustomDropDownItemCheck}
                      checked={checked}
                      onClick={() => onSelectDropDown(item.id, !checked, 'pending-voyage')}>
                      {item.showToolTip ? item.name : item.Header}
                    </Dropdown.Item>
                  );
                })}
              </Dropdown.Menu>
            </Dropdown>
          </ButtonToolbar>
        </div>

        {filterData?.vessel[0]?.id ? (
          <CustomTable
            column={selectedPendingVoyageColumns}
            reportData={ETSPendingVoyageReport}
            isLoading={loading}
            pagination={false}
            className="drill-table-top-border"
          />
        ) : (
          <div className="no-result-found mt-5">
            <Icon icon="alert" className="alert-icon-no-search" />
            <div data-testid="fml-no-result">
              <b>No Vessel Selected. Please select a vessel</b>
            </div>
          </div>
        )}
      </div>

      <div className="pt-2">
        {error && <ErrorAlert message={error} />}
        <hr className="border-line" />
        <div className="d-flex justify-content-between">
          <div className="emergency-drills font-weight-bold">Monitoring During Port</div>

          <ButtonToolbar>
            <Dropdown alignRight className="ml-auto">
              <Dropdown.Toggle
                variant="outline-primary"
                id="dropdown-more"
                data-testid="fml-ETS-report-port-table-columns">
                Table Columns
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {ETSPortColumns.map((item) => {
                  const checked = selectedPortColumnIds.includes(item.id);
                  return (
                    <Dropdown.Item
                      key={item.id}
                      as={CustomDropDownItemCheck}
                      checked={checked}
                      onClick={() => onSelectDropDown(item.id, !checked, 'port')}>
                      {item.showToolTip ? item.name : item.Header}
                    </Dropdown.Item>
                  );
                })}
              </Dropdown.Menu>
            </Dropdown>
          </ButtonToolbar>
        </div>

        {filterData?.vessel[0]?.id ? (
          <CustomTable
            column={selectedPortColumns}
            reportData={ETSPortReport}
            isLoading={loading}
            pagination={false}
            className="drill-table-top-border"
          />
        ) : (
          <div className="no-result-found mt-5">
            <Icon icon="alert" className="alert-icon-no-search" />
            <div data-testid="fml-no-result">
              <b>No Vessel Selected. Please select a vessel</b>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ETSReport;
