import download from 'downloadjs';
import React, { useEffect, useState } from 'react';
import ConfirmModal from '../../../../component/customComponent/CustomConfirmationModal';
import vesselService from '../../../../service/vessel-service';
import { Col, Form, OverlayTrigger, Popover } from 'react-bootstrap';
import { MdOutlineFileUpload } from 'react-icons/md';
import FileUploader from '../../../../component/customComponent/FileUploader';
import { Icon } from '../../../../styleGuide';
import { isValidFileFormat, isValidFileSize } from '../../../../util/eu-ets';
import { PENDING_VERIFICATION, VerificationStatusDropdown } from './VerificationStatusDropdown';
import { VERIFICATION_STATUS_TYPE } from '../../../../constants/ets';

export const CompletedVoyageActionCell = (rowDetails, voyagesUpdatedCallback) => {
  const ButtonLink = ({ row, voyagesUpdatedCallback }) => {
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    const [loading, setLoading] = useState(false);
    const [isShowFileUploader, setIsShowFileUploader] = useState(true);
    const [selectedFile, setSelectedFile] = useState();
    const [fileName, setFileName] = useState(undefined);
    const [error, setError] = useState('');
    const [selectedStatus, setSelectedStatus] = useState(row.verification_status);
    useEffect(() => {
      if (row.attachment_key) {
        setIsShowFileUploader(false);
        setFileName(row.attachment_key.split('/').pop());
      }
    }, [row.attachment_key]);

    const downloadFile = async (id) => {
      let preSignedUrl = '';
      const responseUrl = await vesselService.getPreSignedUploadLink({
        record_type: 'eu-ets',
        payload: {
          id,
        },
        action: 'download',
      });
      preSignedUrl = responseUrl.data.pre_signed_link;
      const responseData = await vesselService.getPresignedDocument(preSignedUrl);
      let fileData = responseData.data;
      let type = responseData.headers['content-type'];
      const blob = new Blob([fileData], { type: type });
      const fileName = row.attachment_key.split('/').pop();
      download(blob, fileName, type);
    };

    const uploadFile = async (data, isEdit) => {
      let url = '';
      let preSignedUrl = '';
      try {
        const response = await vesselService.getPreSignedUploadLink({
          record_type: 'eu-ets',
          payload: data,
          action: isEdit ? 'replace' : 'upload',
          file_name: data.file.name,
        });
        url = response.data.url;
        preSignedUrl = response.data.pre_signed_link;
      } catch (e) {
        setLoading(false);
      }

      if (url) {
        try {
          await vesselService.uploadPresignedDocument(preSignedUrl, data.file, data.file.type);
        } catch (e) {
          url = '';
          setLoading(false);
        }
      }
      return url;
    };

    const onClickEditIcon = () => {
      setShowConfirmationModal(true);
      setError('');
    };
    const onVerificationStatusChange = async (updatedStatus) => {
      setSelectedStatus(updatedStatus);
    };
    const handleEditVoyageSave = async () => {
      try {
        setLoading(true);
        const promises = [];
        let attachment_key = null;
        let statusToUpdate = selectedStatus;
        if (selectedFile) {
          attachment_key = await uploadFile({ file: selectedFile }, row.attachment_key);
        } else if (fileName) {
          attachment_key = row.attachment_key;
        }
        if (row.attachment_key !== attachment_key) {
          promises.push(
            vesselService.uploadVoyageVerificationDocument(attachment_key, row.ets_voyage_id),
          );
        }

        if (!attachment_key) {
          statusToUpdate = VERIFICATION_STATUS_TYPE.DATA_NOT_SENT;
          setSelectedStatus(PENDING_VERIFICATION);
        } else {
          statusToUpdate = VERIFICATION_STATUS_TYPE.VERIFIED;
          setSelectedStatus(statusToUpdate);
        }
        if (row.verification_status !== statusToUpdate) {
          promises.push(
            vesselService.updateVerificationStatus(
              {
                verification_status:
                  statusToUpdate === PENDING_VERIFICATION
                    ? VERIFICATION_STATUS_TYPE.DATA_NOT_SENT
                    : statusToUpdate,
              },
              row.ets_voyage_id,
            ),
          );
        }

        await Promise.all(promises);

        voyagesUpdatedCallback();
        setLoading(false);
        setShowConfirmationModal(false);
      } catch (err) {
        setLoading(false);
      }
    };

    const onFileSelect = (file) => {
      setError('');
      if (file) {
        if (!isValidFileFormat(file, ['pdf'])) {
          // allow only PDF
          setError('Please select a PDF file');
        } else if (!isValidFileSize(file, 10)) {
          // max 10MB
          setError('Please select a file less than 10MB');
        } else {
          file.uploadTime = new Date();
          setSelectedFile(file);
          setFileName(file.name);
          setIsShowFileUploader(false);
          setSelectedStatus(VERIFICATION_STATUS_TYPE.VERIFIED);
        }
      } else {
        setSelectedFile(undefined);
        setFileName(undefined);
        setIsShowFileUploader(true);
        setSelectedStatus(VERIFICATION_STATUS_TYPE.DATA_NOT_SENT);
      }
    };

    const onClickDelete = () => {
      document.body.click();
      setSelectedFile(undefined);
      setFileName(undefined);
      setIsShowFileUploader(true);
      setSelectedStatus(VERIFICATION_STATUS_TYPE.DATA_NOT_SENT);
    };

    const onClickDownload = () => {
      downloadFile(row.ets_voyage_id);
    };
    const onCancelClick = () => {
      if (row.attachment_key) {
        setFileName(row.attachment_key.split('/').pop());
        setSelectedStatus(row.verification_status);
        setIsShowFileUploader(false);
      } else {
        setFileName(undefined);
        setIsShowFileUploader(true);
      }
      setSelectedFile(undefined);
      setShowConfirmationModal(false);
      setSelectedStatus(row.verification_status);
    };
    return (
      <>
        <div
          className="delete-voyage-icon"
          role="button"
          tabIndex={0}
          onClick={() => onClickEditIcon()}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              onClickEditIcon();
            }
          }}
        >
          <Icon icon="pencil" />
        </div>
        <ConfirmModal
          title={'Edit Voyage'}
          showConfirmModal={showConfirmationModal}
          cancelText="Cancel"
          confirmText="Save"
          handleCancel={onCancelClick}
          handleConfirm={handleEditVoyageSave}
          isDisabledConfirm={loading}
          isDisabledCancel={loading}
          customClassName="edit-voyage-modal"
          customBtnClass="btn"
        >
          <Col sm={12}>
            <Form.Label className="edit-voyage-label">Verification Status</Form.Label>
            <VerificationStatusDropdown
              status={selectedStatus}
              disabled={true}
              onVerificationStatusChange={onVerificationStatusChange}
            />
            <div className="upload-container">
              <Form.Label className="edit-voyage-label">Attachment</Form.Label>
              {isShowFileUploader ? (
                <>
                  <FileUploader
                    onUpload={(files) => {
                      onFileSelect(files[0]);
                    }}
                    acceptFileTypes=".pdf"
                  >
                    <div className="content">
                      <MdOutlineFileUpload size={40}></MdOutlineFileUpload>
                      <p>Drag and drop here</p>
                      <p>or</p>
                    </div>
                  </FileUploader>
                  {error && <span className="error-text">{error}</span>}
                </>
              ) : (
                <div className="file-details-container">
                  <div>
                    <p style={{ fontWeight: '500' }}>{fileName ?? '---'}</p>
                  </div>
                  {row.attachment_key ? (
                    <OverlayTrigger
                      rootClose
                      trigger={'click'}
                      key="bottom"
                      placement="bottom"
                      overlay={
                        <Popover>
                          <Popover className="text-primary file-options">
                            <ul className="list-popover-menu">
                              <li onClick={() => onClickDelete()} aria-hidden="true">
                                Delete
                              </li>
                              <li onClick={() => onClickDownload()} aria-hidden="true">
                                Download
                              </li>
                            </ul>
                          </Popover>
                        </Popover>
                      }
                    >
                      <div>
                        <Icon
                          icon="more"
                          size={20}
                          className="default"
                          style={{ cursor: 'pointer' }}
                        />
                      </div>
                    </OverlayTrigger>
                  ) : (
                    <></>
                  )}
                </div>
              )}
            </div>
          </Col>
        </ConfirmModal>
      </>
    );
  };

  return <ButtonLink row={rowDetails} voyagesUpdatedCallback={voyagesUpdatedCallback} />;
};
