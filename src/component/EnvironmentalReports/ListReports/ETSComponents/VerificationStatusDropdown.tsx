import React from 'react';
import { Form, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { VERIFICATION_STATUS_DESC, VERIFICATION_STATUS_TYPE } from '../../../../constants/ets';
export const PENDING_VERIFICATION = 'pending-verification';

export const formatVerificatonStatus = (value) => {
  const hoverText = VERIFICATION_STATUS_DESC[value] === 'Pending' ? 'Pending Verification' : '';
  return (
    <>
      {VERIFICATION_STATUS_DESC[value]?.toLowerCase() === "pending" ? (
        <OverlayTrigger
          placement="bottom"
          overlay={
            <Tooltip id="desc_tooltip" className="tooltip pointup">
              {hoverText}
            </Tooltip>
          }
        >
          <span
            className={`verification-status verification-status-${VERIFICATION_STATUS_DESC[value].toLowerCase()}`}
          >
            <span className="verification-status-text">
              {VERIFICATION_STATUS_DESC[value]}
            </span>
          </span>
        </OverlayTrigger>
      ) : (
        <span
          className={`verification-status verification-status-${VERIFICATION_STATUS_DESC[value].toLowerCase()}`}
        >
          <span className="verification-status-text">
            {VERIFICATION_STATUS_DESC[value]}
          </span>
        </span>
      )}
    </>
  );
  
};


export const VerificationStatusDropdown = ({ status, disabled, onVerificationStatusChange }) => {
  return (
    <Form.Control
      as="select"
      onChange={(e) => onVerificationStatusChange(e.target.value)}
      value={status}
      disabled={disabled}
      className="verification-status-dropdown"
    >
      <option value={VERIFICATION_STATUS_TYPE.DATA_NOT_SENT}>
        {VERIFICATION_STATUS_DESC['data-not-sent']}
      </option>
      <option value={VERIFICATION_STATUS_TYPE.VERIFIED}>{VERIFICATION_STATUS_DESC.verified}</option>
    </Form.Control>
  );
};
