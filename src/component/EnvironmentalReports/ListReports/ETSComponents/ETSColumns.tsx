import moment from 'moment';
import { formatFloat, formatValue } from '../../../../util/view-utils';
import { getEUACostHeader, getEUAHeader, getTotalCO2EmittedHeader } from '../../../../util/eu-ets';
import { formatVerificatonStatus } from './VerificationStatusDropdown';
import { CompletedVoyageActionCell } from './CompletedVoyageActionCell';
import { ViewAttachmentCell } from './ViewAttachmentCell';
import { PendingVoyageActionCell } from './PendingVoyageActionCell';
const getETSVoyageColums = (startDate, voyagesUpdatedCallback) => {
  const filterYear = moment.utc(startDate).format('YYYY');

  const voyageCol = [
    {
      Header: 'Voyage No.',
      accessor: (row) => formatValue(row.voyage_id),
      id: 'voyage_no',
      name: 'voyage_no',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Port of Departure',
      id: 'port_departure',
      name: 'port_departure',
      type: 'text',
      accessor: (row) => formatValue(row.port_departure),
      disableSortBy: true,
    },
    {
      Header: 'Port of Arrival',
      accessor: (row) => formatValue(row.port_arrival),
      id: 'port_arrival',
      name: 'port_arrival',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Leg Start / Leg End',
      accessor: (row) => formatValue(row.leg_date),
      id: 'leg_date',
      name: 'leg_date',
      type: 'text',
      maxWidth: 230,
      width: 230,
      disableSortBy: true,
    },
    {
      Header: 'Hour of Departure (UTC)',
      accessor: (row) => formatValue(row.departure_hour),
      id: 'departure_hour',
      name: 'departure_hour',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Hour of Arrival (UTC)',
      accessor: (row) => formatValue(row.arrival_hour),
      id: 'arrival_hour',
      name: 'arrival_hour',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Time Spent at Sea (hr)',
      accessor: (row) => formatValue(row.time_spent_sea),
      id: 'time_spent_sea',
      name: 'time_spent_sea',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Distance (nm)',
      accessor: (row) => formatFloat(row.distance),
      id: 'distance',
      name: 'distance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Cargo Carried (tons)',
      accessor: (row) => formatFloat(row.cargo_carried),
      id: 'cargo_carried',
      name: 'cargo_carried',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Voyage Type (L/B)',
      accessor: (row) => formatValue(row.voyage_type),
      id: 'voyage_type',
      name: 'voyage_type',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'HFO Consumption (tons)',
      accessor: (row) => formatFloat(row.hfo_cons),
      id: 'hfo_cons',
      name: 'hfo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'HFO Emission Factor',
      accessor: (row) => formatValue(row.hfo_emission_factor),
      id: 'hfo_emission_factor',
      name: 'hfo_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'HFO CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.hfo_co2_emitted),
      id: 'hfo_co2_emitted',
      name: 'hfo_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LFO Consumption (tons)',
      accessor: (row) => formatFloat(row.lfo_cons),
      id: 'lfo_cons',
      name: 'lfo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LFO Emission Factor',
      accessor: (row) => formatValue(row.lfo_emission_factor),
      id: 'lfo_emission_factor',
      name: 'lfo_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LFO CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lfo_co2_emitted),
      id: 'lfo_co2_emitted',
      name: 'lfo_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'MGO Consumption (tons)',
      accessor: (row) => formatFloat(row.mgo_cons),
      id: 'mgo_cons',
      name: 'mgo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'MGO Emission Factor',
      accessor: (row) => formatValue(row.mgo_emission_factor),
      id: 'mgo_emission_factor',
      name: 'mgo_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'MGO CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.mgo_co2_emitted),
      id: 'mgo_co2_emitted',
      name: 'mgo_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LNG Consumption (tons)',
      accessor: (row) => formatFloat(row.lng_cons),
      id: 'lng_cons',
      name: 'lng_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LNG Emission Factor',
      accessor: (row) => formatValue(row.lng_emission_factor),
      id: 'lng_emission_factor',
      name: 'lng_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LNG CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lng_co2_emitted),
      id: 'lng_co2_emitted',
      name: 'lng_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel Name',
      accessor: (row) => formatValue(row.other_fuel_name),
      id: 'other_fuel_name',
      name: 'other_fuel_name',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel Consumptions',
      accessor: (row) => formatValue(row.other_fuel_cons),
      id: 'other_fuel_consumption',
      name: 'other_fuel_consumption',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel CO2 Emission Factor',
      accessor: (row) => formatValue(row.other_fuel_co2_emission_factor),
      id: 'other_fuel_co2_emission_factor',
      name: 'other_fuel_co2_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: getTotalCO2EmittedHeader(filterYear),
      accessor: (row) => formatFloat(row.total_co2_emitted),
      id: 'total_co2_emitted',
      name: 'total_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Total Energy Consumed (MJ)',
      accessor: (row) => formatFloat(row.total_energy_consumed),
      id: 'total_energy_consumed',
      name: 'total_energy_consumed',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Average GHG Intensity (gCO2eq/MJ)',
      accessor: (row) => formatFloat(row.average_ghg_intensity),
      id: 'average_ghg_intensity',
      name: 'average_ghg_intensity',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Compliance Balance (tCO2eq)',
      accessor: (row) => formatFloat(row.compliance_balance),
      id: 'compliance_balance',
      name: 'compliance_balance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Transport Work (tn*nm)',
      accessor: (row) => formatFloat(row.transport_work),
      id: 'transport_work',
      name: 'transport_work',
      type: 'text',
      disableSortBy: true,
      width: 140,
      minWidth: 140,
    },
    {
      Header: 'Fuel Consumption per Distance (tn/nm)',
      accessor: (row) => formatFloat(row.fuel_cons_per_distance),
      id: 'fuel_cons_per_distance',
      name: 'fuel_cons_per_distance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Fuel Consumption per Transport Work (gr/tn*nm)',
      accessor: (row) => formatFloat(row.fuel_cons_per_transport_work),
      id: 'fuel_cons_per_transport_work',
      name: 'fuel_cons_per_transport_work',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'CO2 Emissions per Distance (tn/nm)',
      accessor: (row) => formatFloat(row.co2_emitted_per_distance),
      id: 'co2_emitted_per_distance',
      name: 'co2_emitted_per_distance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'CO2 Emissions per Transport Work (g CO2 / tn*nm)',
      accessor: (row) => formatFloat(row.co2_emitted_per_transport_work),
      id: 'co2_emitted_per_transport_work',
      name: 'co2_emitted_per_transport_work',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Boiler Consumption (HFO)',
      accessor: (row) => formatFloat(row.boiler_hfo_cons),
      id: 'boiler_hfo_cons',
      name: 'boiler_hfo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Boiler Consumption (MDO/MGO)',
      accessor: (row) => formatFloat(row.boiler_mgo_cons),
      id: 'boiler_mgo_cons',
      name: 'boiler_mgo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Boiler Consumption (Fuel3)',
      accessor: (row) => formatFloat(row.boiler_fuel3_cons),
      id: 'boiler_fuel3_cons',
      name: 'boiler_fuel3_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Propane) Consumption (tons)',
      accessor: (row) => formatFloat(row.lpg_propane_cons),
      id: 'lpg_propane_cons',
      name: 'lpg_propane_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Propane) Emission Factor',
      accessor: (row) => formatValue(row.lpg_propane_emission_factor),
      id: 'lpg_propane_emission_factor',
      name: 'lpg_propane_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Propane) CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lpg_propane_co2_emitted),
      id: 'lpg_propane_co2_emitted',
      name: 'lpg_propane_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Butane) Consumption (tons) ',
      accessor: (row) => formatFloat(row.lpg_butane_cons),
      id: 'lpg_butane_cons',
      name: 'lpg_butane_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Butane) Emission Factor',
      accessor: (row) => formatValue(row.lpg_butane_emission_factor),
      id: 'lpg_butane_emission_factor',
      name: 'lpg_butane_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Butane) CO2 Emitted (tons) ',
      accessor: (row) => formatFloat(row.lpg_butane_co2_emitted),
      id: 'lpg_butane_co2_emitted',
      name: 'lpg_butane_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Methanol Consumption (tons)',
      accessor: (row) => formatFloat(row.methanol_cons),
      id: 'methanol_cons',
      name: 'methanol_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Methanol Emission Factor',
      accessor: (row) => formatValue(row.methanol_emission_factor),
      id: 'methanol_emission_factor',
      name: 'methanol_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Methanol CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.methanol_co2_emitted),
      id: 'methanol_co2_emitted',
      name: 'methanol_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Ethanol Consumption (tons)',
      accessor: (row) => formatFloat(row.ethanol_cons),
      id: 'ethanol_cons',
      name: 'ethanol_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Ethanol Emission Factor',
      accessor: (row) => formatValue(row.ethanol_emission_factor),
      id: 'ethanol_emission_factor',
      name: 'ethanol_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Ethanol CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.ethanol_co2_emitted),
      id: 'ethanol_co2_emitted',
      name: 'ethanol_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - TtW CO2 Emission Factor',
      accessor: (row) => formatFloat(row.ttw_co2_emission_factor),
      id: 'ttw_co2_emission_factor',
      name: 'ttw_co2_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - EU GHG Intensity (gCO2eq/MJ)',
      accessor: (row) => formatFloat(row.eu_ghg_intensity),
      id: 'eu_ghg_intensity',
      name: 'eu_ghg_intensity',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - EU Lower Calorific Value (MJ/g)',
      accessor: (row) => formatFloat(row.eu_lower_calorific_value),
      id: 'eu_lower_calorific_value',
      name: 'eu_lower_calorific_value',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Parent Fuel',
      accessor: (row) => formatValue(row.parent_fuel),
      id: 'parent_fuel',
      name: 'parent_fuel',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Blend Percentage (%)',
      accessor: (row) => formatFloat(row.biofuel_percentage),
      id: 'biofuel_percentage',
      name: 'biofuel_percentage',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Compliance Balance (tCO2eq)',
      accessor: (row) => formatFloat(row.other_fuel_compliance_balance),
      id: 'other_fuel_compliance_balance',
      name: 'other_fuel_compliance_balance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Blended EU GHG Intensity (gCO2eq/MJ)',
      accessor: (row) => formatFloat(row.blended_eu_ghg_intensity),
      id: 'blended_eu_ghg_intensity',
      name: 'blended_eu_ghg_intensity',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Total Fuel Consumed (tons)',
      accessor: (row) => formatFloat(row.total_fuel_consumed),
      id: 'total_fuel_consumed',
      name: 'total_fuel_consumed',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Verifier Name',
      accessor: (row) => formatValue(row.class_name),
      id: 'class_name',
      name: 'class_name',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: getEUAHeader(filterYear),
      accessor: (row) => formatFloat(row.eua),
      id: 'eua',
      name: 'EUA',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 100,
      width: 100,
    },
    {
      Header: 'Estimate FuelEU Penalty (€)',
      accessor: (row) => (formatFloat(row.estimate_fuel_eu_penalty) === '-0.00' ? '0.00' : formatFloat(row.estimate_fuel_eu_penalty)),
      id: 'estimate_fuel_eu_penalty',
      name: 'estimate_fuel_eu_penalty',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 170,
      width: 150,
    },
    {
      Header: getEUACostHeader(filterYear),
      accessor: (row) => formatFloat(row.eua_cost),
      id: 'estimated_cost',
      name: 'estimated_cost',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 100,
      width: 100,
    },
    {
      Header: 'Verification Status',
      accessor: (row) => formatVerificatonStatus(row.verification_status),
      id: 'verification_status',
      name: 'verification_status',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 100,
      width: 100,
    },
    {
      type: 'item',
      Header: 'Attachment',
      id: 'attachment',
      name: 'attachment',
      accessor: (row) => ViewAttachmentCell(row),
      width: 120,
      minWidth: 120,
      disableSortBy: true,
      sticky: 'right',
    },
    {
      type: 'item',
      Header: 'Action',
      id: 'action',
      name: 'action',
      accessor: (row) => CompletedVoyageActionCell(row, voyagesUpdatedCallback),
      width: 94,
      minWidth: 50,
      disableSortBy: true,
      sticky: 'right',
    },
  ];
  if (['2023', '2024', '2025'].includes(filterYear)) {
    voyageCol.splice(47, 0, {
      Header: 'Base EU Allowance',
      accessor: (row) => formatFloat(row.base_eua),
      id: 'base_eua',
      name: 'base_eua',
      type: 'text',
      disableSortBy: true,
      maxWidth: 200,
      width: 160,
    });
  }
  return voyageCol;
};
const getETSPendingVoyageColumns = (
  startDate,
  roleConfig,
  vesselOwnershipId,
  voyagesUpdatedCallback,
) => {
  const filterYear = moment.utc(startDate).format('YYYY');

  const pendingVoyageCol = [
    {
      Header: 'Voyage No.',
      accessor: (row) => formatValue(row.voyage_id),
      id: 'voyage_no',
      name: 'voyage_no',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Port of Departure',
      id: 'port_departure',
      name: 'port_departure',
      type: 'text',
      accessor: (row) => formatValue(row.port_departure),
      disableSortBy: true,
    },
    {
      Header: 'Port of Arrival',
      accessor: (row) => formatValue(row.port_arrival),
      id: 'port_arrival',
      name: 'port_arrival',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Leg Start / End Date',
      accessor: (row) => formatValue(row.leg_date),
      id: 'leg_date',
      name: 'leg_date',
      type: 'text',
      maxWidth: 230,
      width: 230,
      disableSortBy: true,
    },
    {
      Header: 'Hour of Departure (UTC)',
      accessor: (row) => formatValue(row.departure_hour),
      id: 'departure_hour',
      name: 'departure_hour',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Hour of Arrival (UTC)',
      accessor: (row) => formatValue(row.arrival_hour),
      id: 'arrival_hour',
      name: 'arrival_hour',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Time Spent at Sea (hr)',
      accessor: (row) => formatValue(row.time_spent_sea),
      id: 'time_spent_sea',
      name: 'time_spent_sea',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Distance (nm)',
      accessor: (row) => formatFloat(row.distance),
      id: 'distance',
      name: 'distance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Cargo Carried (tons)',
      accessor: (row) => formatValue(row.cargo_carried),
      id: 'cargo_carried',
      name: 'cargo_carried',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Voyage Type (L/B)',
      accessor: (row) => formatValue(row.voyage_type),
      id: 'voyage_type',
      name: 'voyage_type',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'HFO Consumption (tons)',
      accessor: (row) => formatFloat(row.hfo_cons),
      id: 'hfo_cons',
      name: 'hfo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'HFO Emission Factor',
      accessor: (row) => formatValue(row.hfo_emission_factor),
      id: 'hfo_emission_factor',
      name: 'hfo_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'HFO CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.hfo_co2_emitted),
      id: 'hfo_co2_emitted',
      name: 'hfo_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LFO Consumption (tons)',
      accessor: (row) => formatFloat(row.lfo_cons),
      id: 'lfo_cons',
      name: 'lfo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LFO Emission Factor',
      accessor: (row) => formatValue(row.lfo_emission_factor),
      id: 'lfo_emission_factor',
      name: 'lfo_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LFO CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lfo_co2_emitted),
      id: 'lfo_co2_emitted',
      name: 'lfo_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'MGO Consumption (tons)',
      accessor: (row) => formatFloat(row.mgo_cons),
      id: 'mgo_cons',
      name: 'mgo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'MGO Emission Factor',
      accessor: (row) => formatValue(row.mgo_emission_factor),
      id: 'mgo_emission_factor',
      name: 'mgo_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'MGO CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.mgo_co2_emitted),
      id: 'mgo_co2_emitted',
      name: 'mgo_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LNG Consumption (tons)',
      accessor: (row) => formatFloat(row.lng_cons),
      id: 'lng_cons',
      name: 'lng_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LNG Emission Factor',
      accessor: (row) => formatValue(row.lng_emission_factor),
      id: 'lng_emission_factor',
      name: 'lng_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LNG CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lng_co2_emitted),
      id: 'lng_co2_emitted',
      name: 'lng_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel Name',
      accessor: (row) => formatValue(row.other_fuel_name),
      id: 'other_fuel_name',
      name: 'other_fuel_name',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel Consumptions',
      accessor: (row) => formatValue(row.other_fuel_cons),
      id: 'other_fuel_consumption',
      name: 'other_fuel_consumption',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel CO2 Emission Factor',
      accessor: (row) => formatValue(row.other_fuel_co2_emission_factor),
      id: 'other_fuel_co2_emission_factor',
      name: 'other_fuel_co2_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: getTotalCO2EmittedHeader(filterYear),
      accessor: (row) => formatFloat(row.total_co2_emitted),
      id: 'total_co2_emitted',
      name: 'total_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Total Energy Consumed (MJ)',
      accessor: (row) => formatFloat(row.total_energy_consumed),
      id: 'total_energy_consumed',
      name: 'total_energy_consumed',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Average GHG Intensity (gCO2eq/MJ)',
      accessor: (row) => formatFloat(row.average_ghg_intensity),
      id: 'average_ghg_intensity',
      name: 'average_ghg_intensity',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Compliance Balance (tCO2eq)',
      accessor: (row) => formatFloat(row.compliance_balance),
      id: 'compliance_balance',
      name: 'compliance_balance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Boiler Consumption (HFO)',
      accessor: (row) => formatFloat(row.boiler_hfo_cons),
      id: 'boiler_hfo_cons',
      name: 'boiler_hfo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Boiler Consumption (MDO/MGO)',
      accessor: (row) => formatFloat(row.boiler_mgo_cons),
      id: 'boiler_mgo_cons',
      name: 'boiler_mgo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Boiler Consumption (Fuel3)',
      accessor: (row) => formatFloat(row.boiler_fuel3_cons),
      id: 'boiler_fuel3_cons',
      name: 'boiler_fuel3_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Propane) Consumption (tons)',
      accessor: (row) => formatFloat(row.lpg_propane_cons),
      id: 'lpg_propane_cons',
      name: 'lpg_propane_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Propane) Emission Factor',
      accessor: (row) => formatValue(row.lpg_propane_emission_factor),
      id: 'lpg_propane_emission_factor',
      name: 'lpg_propane_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Propane) CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lpg_propane_co2_emitted),
      id: 'lpg_propane_co2_emitted',
      name: 'lpg_propane_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Butane) Consumption (tons) ',
      accessor: (row) => formatFloat(row.lpg_butane_cons),
      id: 'lpg_butane_cons',
      name: 'lpg_butane_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Butane) Emission Factor',
      accessor: (row) => formatValue(row.lpg_butane_emission_factor),
      id: 'lpg_butane_emission_factor',
      name: 'lpg_butane_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Butane) CO2 Emitted (tons) ',
      accessor: (row) => formatFloat(row.lpg_butane_co2_emitted),
      id: 'lpg_butane_co2_emitted',
      name: 'lpg_butane_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Methanol Consumption (tons)',
      accessor: (row) => formatFloat(row.methanol_cons),
      id: 'methanol_cons',
      name: 'methanol_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Methanol Emission Factor',
      accessor: (row) => formatValue(row.methanol_emission_factor),
      id: 'methanol_emission_factor',
      name: 'methanol_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Methanol CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.methanol_co2_emitted),
      id: 'methanol_co2_emitted',
      name: 'methanol_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Ethanol Consumption (tons)',
      accessor: (row) => formatFloat(row.ethanol_cons),
      id: 'ethanol_cons',
      name: 'ethanol_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Ethanol Emission Factor',
      accessor: (row) => formatValue(row.ethanol_emission_factor),
      id: 'ethanol_emission_factor',
      name: 'ethanol_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Ethanol CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.ethanol_co2_emitted),
      id: 'ethanol_co2_emitted',
      name: 'ethanol_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - TtW CO2 Emission Factor',
      accessor: (row) => formatFloat(row.ttw_co2_emission_factor),
      id: 'ttw_co2_emission_factor',
      name: 'ttw_co2_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - EU GHG Intensity (gCO2eq/MJ)',
      accessor: (row) => formatFloat(row.eu_ghg_intensity),
      id: 'eu_ghg_intensity',
      name: 'eu_ghg_intensity',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - EU Lower Calorific Value (MJ/g)',
      accessor: (row) => formatFloat(row.eu_lower_calorific_value),
      id: 'eu_lower_calorific_value',
      name: 'eu_lower_calorific_value',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Parent Fuel',
      accessor: (row) => formatValue(row.parent_fuel),
      id: 'parent_fuel',
      name: 'parent_fuel',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Blend Percentage (%)',
      accessor: (row) => formatFloat(row.biofuel_percentage),
      id: 'biofuel_percentage',
      name: 'biofuel_percentage',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Compliance Balance (tCO2eq)',
      accessor: (row) => formatFloat(row.other_fuel_compliance_balance),
      id: 'other_fuel_compliance_balance',
      name: 'other_fuel_compliance_balance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Blended EU GHG Intensity (gCO2eq/MJ)',
      accessor: (row) => formatFloat(row.blended_eu_ghg_intensity),
      id: 'blended_eu_ghg_intensity',
      name: 'blended_eu_ghg_intensity',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Total Fuel Consumed (tons)',
      accessor: (row) => formatFloat(row.total_fuel_consumed),
      id: 'total_fuel_consumed',
      name: 'total_fuel_consumed',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Verifier Name',
      accessor: (row) => formatValue(row.class_name),
      id: 'class_name',
      name: 'class_name',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: getEUAHeader(filterYear),
      accessor: (row) => formatFloat(row.eua),
      id: 'eua',
      name: 'EUA',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 100,
      width: 100,
    },
    {
      Header: 'Estimate FuelEU Penalty (€)',
      accessor: (row) => (formatFloat(row.estimate_fuel_eu_penalty) === '-0.00' ? '0.00' : formatFloat(row.estimate_fuel_eu_penalty)),
      id: 'estimate_fuel_eu_penalty',
      name: 'estimate_fuel_eu_penalty',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 170,
      width: 150,
    },
    {
      Header: getEUACostHeader(filterYear),
      accessor: (row) => formatFloat(row.eua_cost),
      id: 'estimated_cost',
      name: 'estimated_cost',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 100,
      width: 100,
    },
  ];
  if (roleConfig.vessel.environmental?.editEuEtsReport) {
    pendingVoyageCol.push({
      type: 'item',
      Header: 'Action',
      id: 'action',
      name: 'action',
      accessor: (row) => PendingVoyageActionCell(row, vesselOwnershipId, voyagesUpdatedCallback),
      width: 94,
      minWidth: 50,
      disableSortBy: true,
      sticky: 'right',
    });
  }

  if (['2023', '2024', '2025'].includes(filterYear)) {
    pendingVoyageCol.splice(27, 0, {
      Header: 'Base EU Allowance',
      accessor: (row) => formatFloat(row.base_eua),
      id: 'base_eua',
      name: 'base_eua',
      type: 'text',
      disableSortBy: true,
      maxWidth: 200,
      width: 160,
    });
  }
  return pendingVoyageCol;
};
const getETSPortColumns = (startDate) => {
  const filterYear = moment.utc(startDate).format('YYYY');

  const portCol = [
    {
      Header: 'Voyage No.',
      accessor: (row) => formatValue(row.voyage_id),
      id: 'voyage_no',
      name: 'voyage_no',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Port of Departure',
      accessor: (row) => formatValue(row.port),
      id: 'port',
      name: 'port',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Arrival in Port (date)',
      accessor: (row) => formatValue(row.eu_port_departure_date),
      id: 'eu_port_arrival_date',
      name: 'eu_port_arrival_date',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Departure from Port (date)',
      accessor: (row) => formatValue(row.eu_port_arrival_date),
      id: 'eu_port_departure_date',
      name: 'eu_port_departure_date',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'HFO Consumption (tons)',
      accessor: (row) => formatFloat(row.hfo_cons),
      id: 'hfo_cons',
      name: 'hfo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'HFO Emission Factor',
      accessor: (row) => formatValue(row.hfo_emission_factor),
      id: 'hfo_emission_factor',
      name: 'hfo_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'HFO CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.hfo_co2_emitted),
      id: 'hfo_co2_emitted',
      name: 'hfo_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LFO Consumption (tons)',
      accessor: (row) => formatFloat(row.lfo_cons),
      id: 'lfo_cons',
      name: 'lfo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LFO Emission Factor',
      accessor: (row) => formatValue(row.lfo_emission_factor),
      id: 'lfo_emission_factor',
      name: 'lfo_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LFO CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lfo_co2_emitted),
      id: 'lfo_co2_emitted',
      name: 'lfo_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'MGO Consumption (tons)',
      accessor: (row) => formatFloat(row.mgo_cons),
      id: 'mgo_cons',
      name: 'mgo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'MGO Emission Factor',
      accessor: (row) => formatValue(row.mgo_emission_factor),
      id: 'mgo_emission_factor',
      name: 'mgo_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'MGO CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.mgo_co2_emitted),
      id: 'mgo_co2_emitted',
      name: 'mgo_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LNG Consumption (tons)',
      accessor: (row) => formatFloat(row.lng_cons),
      id: 'lng_cons',
      name: 'lng_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LNG Emission Factor',
      accessor: (row) => formatValue(row.lng_emission_factor),
      id: 'lng_emission_factor',
      name: 'lng_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LNG CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lng_co2_emitted),
      id: 'lng_co2_emitted',
      name: 'lng_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel Name',
      accessor: (row) => formatValue(row.other_fuel_name),
      id: 'other_fuel_name',
      name: 'other_fuel_name',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel Consumptions',
      accessor: (row) => formatValue(row.other_fuel_cons),
      id: 'other_fuel_consumption',
      name: 'other_fuel_consumption',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel CO2 Emission Factor',
      accessor: (row) => formatValue(row.other_fuel_co2_emission_factor),
      id: 'other_fuel_co2_emission_factor',
      name: 'other_fuel_co2_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: getTotalCO2EmittedHeader(filterYear),
      accessor: (row) => formatFloat(row.total_co2_emitted),
      id: 'total_co2_emitted',
      name: 'total_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Total Energy Consumed (MJ)',
      accessor: (row) => formatFloat(row.total_energy_consumed),
      id: 'total_energy_consumed',
      name: 'total_energy_consumed',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Average GHG Intensity (gCO2eq/MJ)',
      accessor: (row) => formatFloat(row.average_ghg_intensity),
      id: 'average_ghg_intensity',
      name: 'average_ghg_intensity',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Compliance Balance (tCO2eq)',
      accessor: (row) => formatFloat(row.compliance_balance),
      id: 'compliance_balance',
      name: 'compliance_balance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Boiler Consumption (HFO)',
      accessor: (row) => formatFloat(row.boiler_hfo_cons),
      id: 'boiler_hfo_cons',
      name: 'boiler_hfo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Boiler Consumption (MDO/MGO)',
      accessor: (row) => formatFloat(row.boiler_mgo_cons),
      id: 'boiler_mgo_cons',
      name: 'boiler_mgo_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Boiler Consumption (Fuel3)',
      accessor: (row) => formatFloat(row.boiler_fuel3_cons),
      id: 'boiler_fuel3_cons',
      name: 'boiler_fuel3_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Propane) Consumption (tons)',
      accessor: (row) => formatFloat(row.lpg_propane_cons),
      id: 'lpg_propane_cons',
      name: 'lpg_propane_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Propane) Emission Factor',
      accessor: (row) => formatValue(row.lpg_propane_emission_factor),
      id: 'lpg_propane_emission_factor',
      name: 'lpg_propane_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Propane) CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lpg_propane_co2_emitted),
      id: 'lpg_propane_co2_emitted',
      name: 'lpg_propane_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Butane) Consumption (tons)',
      accessor: (row) => formatFloat(row.lpg_butane_cons),
      id: 'lpg_butane_cons',
      name: 'lpg_butane_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Butane) Emission Factor',
      accessor: (row) => formatValue(row.lpg_butane_emission_factor),
      id: 'lpg_butane_emission_factor',
      name: 'lpg_butane_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'LPG (Butane) CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.lpg_butane_co2_emitted),
      id: 'lpg_butane_co2_emitted',
      name: 'lpg_butane_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Methanol Consumption (tons)',
      accessor: (row) => formatFloat(row.methanol_cons),
      id: 'methanol_cons',
      name: 'methanol_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Methanol Emission Factor',
      accessor: (row) => formatValue(row.methanol_emission_factor),
      id: 'methanol_emission_factor',
      name: 'methanol_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Methanol CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.methanol_co2_emitted),
      id: 'methanol_co2_emitted',
      name: 'methanol_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Ethanol Consumption (tons)',
      accessor: (row) => formatFloat(row.ethanol_cons),
      id: 'ethanol_cons',
      name: 'ethanol_cons',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Ethanol Emission Factor',
      accessor: (row) => formatValue(row.ethanol_emission_factor),
      id: 'ethanol_emission_factor',
      name: 'ethanol_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Ethanol CO2 Emitted (tons)',
      accessor: (row) => formatFloat(row.ethanol_co2_emitted),
      id: 'ethanol_co2_emitted',
      name: 'ethanol_co2_emitted',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - TtW CO2 Emission Factor',
      accessor: (row) => formatFloat(row.ttw_co2_emission_factor),
      id: 'ttw_co2_emission_factor',
      name: 'ttw_co2_emission_factor',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - EU GHG Intensity (gCO2eq/MJ)',
      accessor: (row) => formatFloat(row.eu_ghg_intensity),
      id: 'eu_ghg_intensity',
      name: 'eu_ghg_intensity',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - EU Lower Calorific Value (MJ/g)',
      accessor: (row) => formatFloat(row.eu_lower_calorific_value),
      id: 'eu_lower_calorific_value',
      name: 'eu_lower_calorific_value',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Parent Fuel',
      accessor: (row) => formatValue(row.parent_fuel),
      id: 'parent_fuel',
      name: 'parent_fuel',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Blend Percentage (%)',
      accessor: (row) => formatFloat(row.biofuel_percentage),
      id: 'biofuel_percentage',
      name: 'biofuel_percentage',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Compliance Balance (tCO2eq)',
      accessor: (row) => formatFloat(row.other_fuel_compliance_balance),
      id: 'other_fuel_compliance_balance',
      name: 'other_fuel_compliance_balance',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Other Fuel - Blended EU GHG Intensity (gCO2eq/MJ)',
      accessor: (row) => formatFloat(row.blended_eu_ghg_intensity),
      id: 'blended_eu_ghg_intensity',
      name: 'blended_eu_ghg_intensity',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Total Fuel Consumed (tons)',
      accessor: (row) => formatFloat(row.total_fuel_consumed),
      id: 'total_fuel_consumed',
      name: 'total_fuel_consumed',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Verifier Name',
      accessor: (row) => formatValue(row.class_name),
      id: 'class_name',
      name: 'class_name',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: getEUAHeader(filterYear),
      accessor: (row) => formatFloat(row.eua),
      id: 'eua',
      name: 'EUA',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 100,
      width: 100,
    },
    {
      Header: 'Estimate FuelEU Penalty (€)',
      accessor: (row) => (formatFloat(row.estimate_fuel_eu_penalty) === '-0.00' ? '0.00' : formatFloat(row.estimate_fuel_eu_penalty)),
      id: 'estimate_fuel_eu_penalty',
      name: 'estimate_fuel_eu_penalty',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 170,
      width: 150,
    },
    {
      Header: getEUACostHeader(filterYear),
      accessor: (row) => formatFloat(row.eua_cost),
      id: 'estimated_cost',
      name: 'estimated_cost',
      type: 'text',
      disableSortBy: true,
      sticky: 'right',
      maxWidth: 100,
      width: 100,
    },
  ];
  if (['2023', '2024', '2025'].includes(filterYear)) {
    portCol.splice(36, 0, {
      Header: 'Base EU Allowance',
      accessor: (row) => formatFloat(row.base_eua),
      id: 'base_eua',
      name: 'base_eua',
      type: 'text',
      disableSortBy: true,
    });
  }
  return portCol;
};

export default { getETSVoyageColums, getETSPendingVoyageColumns, getETSPortColumns };
