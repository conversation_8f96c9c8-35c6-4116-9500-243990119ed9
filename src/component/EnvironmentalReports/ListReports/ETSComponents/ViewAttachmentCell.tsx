import React from 'react';
import { Link } from 'react-router-dom';
import { base64_encode } from '../../../../util/getURLParams';

const { PARIS2_URL } = process.env;
export const ViewAttachmentCell = (row) => {
  return row.attachment_key ? (
    <Link
      to={{
        pathname: `${PARIS2_URL}/vessel/document?source=eu-ets&path=${base64_encode(
          row.attachment_key,
        )}&id=${row.ets_voyage_id}`,
      }}
      target="_blank"
      className="button-link"
    >
      View
    </Link>
  ) : (
    '---'
  );
};
