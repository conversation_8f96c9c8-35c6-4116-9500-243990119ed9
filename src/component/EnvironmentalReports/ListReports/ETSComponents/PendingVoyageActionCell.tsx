import React, { useState } from 'react';
import { FaTrashAlt } from 'react-icons/fa';
import ConfirmModal from '../../../customComponent/CustomConfirmationModal';
import { deletePendingVoyage } from '../../../../service/vessel-service';

export const PendingVoyageActionCell = (rowDetails, vesselOwnershipId, voyagesUpdatedCallback) => {
  const ButtonLink = ({ row, vesselOwnershipId, voyagesUpdatedCallback }) => {
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    const [loading, setLoading] = useState(false);

    const onDeleteClick = () => {
      setShowConfirmationModal(true);
    };

    const handleVoyageDelete = async () => {
      try {
        setLoading(true);
        await deletePendingVoyage(
          {
            vessel_ownership_id: vesselOwnershipId,
          },
          rowDetails.ets_voyage_id,
        );
        voyagesUpdatedCallback();
        setLoading(false);
        setShowConfirmationModal(false);
      } catch (err) {
        setLoading(false);
      }
    };

    return (
      <>
        <FaTrashAlt className="delete-voyage-icon" onClick={() => onDeleteClick()} />

        <ConfirmModal
          title={'Confirm Delete'}
          content={'Are you sure you want to delete this pending voyage ?'}
          showConfirmModal={showConfirmationModal}
          cancelText="Cancel"
          confirmText="Confirm"
          handleCancel={() => setShowConfirmationModal(false)}
          handleConfirm={() => handleVoyageDelete()}
          isDisabledConfirm={loading}
        ></ConfirmModal>
      </>
    );
  };

  return (
    <ButtonLink
      row={rowDetails}
      vesselOwnershipId={vesselOwnershipId}
      voyagesUpdatedCallback={voyagesUpdatedCallback}
    />
  );
};
