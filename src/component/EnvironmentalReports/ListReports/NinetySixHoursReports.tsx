import React, { useContext, useState, useEffect, useMemo } from 'react';
import { formatDate, formatValue } from '../../../util/view-utils';
import vesselService from '../../../service/vessel-service';
import ErrorAlert from '../../ErrorAlert';
import { Link } from 'react-router-dom';
import { EnvironmentalReportContext } from '../../../context/EnvironmentalReportContext';
import _ from 'lodash';
import CustomTable from '../../customComponent/CustomTable';

const NinetySixHoursReports = ({ loading, setLoading, error, setError }) => {
  const { filterData, ga4EventTrigger = () => {} } = useContext(EnvironmentalReportContext);
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([{ id: 'report_date', desc: true }]);
  const [ninetySixHoursReports, setNinetySixHoursReports] = useState([]);

  const columns = useMemo(
    () => [
      {
        Header: 'No.',
        accessor: (row) => formatValue(row.id),
        sticky: 'left',
        id: 'id',
        name: 'id',
        type: 'text',
        maxWidth: 50,
      },
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link
            onClick={() => {
              ga4EventTrigger(
                'Link to Vessel Details',
                '96Hours Report - link',
                _.get(row, 'vessel_name'),
              );
            }}
            to={`/vessel/ownership/details/${row.vessel_ownership_id}`}
            className="button-link"
          >
            {formatValue(row.vessel_name)}
          </Link>
        ),
        id: 'vessel_name',
        name: 'vessel_name',
        type: 'text',
      },
      {
        Header: 'View Report',
        id: 'viewReport',
        type: 'item',
        accessor: (row) => (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
            aria-hidden="true"
          >
            <Link
              className="button-link"
              onClick={() => {
                ga4EventTrigger(
                  'Link to 96Hours Report Compare',
                  '96Hours Report - link',
                  _.get(row, 'vessel_name'),
                );
              }}
              to={`/vessel/report/environmental/ninety-six-hours/${
                row.vessel_ownership_id
              }/compare?report_date=${formatDate(row.report_date, 'YYYY-MM-DD')}`}
            >
              View
            </Link>
          </div>
        ),
        disableSortBy: true,
      },
      {
        Header: 'Report Submit Date',
        accessor: (row) => formatDate(row.report_date, 'DD MMM YYYY HH:mm'),
        id: 'report_date',
        name: 'report_date',
        type: 'date',
        maxWidth: 120,
      },
    ],
    [],
  );

  const fetchMonthly96hoursReports = async (
    sortData = [{ id: 'report_date', desc: true }],
    pageIndex = 0,
    pageSize = 10,
  ) => {
    setLoading(true);
    setNinetySixHoursReports([]);
    try {
      let pageFilterData = '';
      if (filterData?.startDate || filterData?.endDate) {
        pageFilterData = `report_date=${filterData.startDate ? filterData.startDate : ''},${
          filterData.endDate ? filterData.endDate : ''
        }`;
      }
      const response = await vesselService.get96HoursReportList(
        !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
        {
          sortBy: sortData,
          pageSize: pageSize,
          pageIndex: pageIndex,
          pageMode: 'list',
        },
        pageFilterData,
      );
      setNinetySixHoursReports(response.data.results);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
      setError(null);
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  useEffect(() => {
    setPageIndex(0);
    fetchMonthly96hoursReports(sortData, 0, pageSize);
  }, [filterData]);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchMonthly96hoursReports(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', '96Hours Report - List', value);
    setPageSize(value);
    setPageIndex(0);
    fetchMonthly96hoursReports(sortData, 0, value);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', '96Hours Report - List', value[0]?.id);
    setSortData(value);
    fetchMonthly96hoursReports(value, pageIndex, pageSize);
  };

  return (
    <div className="pt-4">
      {error && <ErrorAlert message={error} />}
      <CustomTable
        column={columns}
        reportData={ninetySixHoursReports}
        tableRef={null}
        isLoading={loading}
        pagination={true}
        pageCount={pageCount}
        totalCount={totalCount}
        setPageNo={onPageIndexChange}
        setPageListSize={onPageSizeChange}
        setSortData={onSortChange}
        pageNo={pageIndex}
        className="drill-table-top-border"
        pageSize={pageSize}
      />
    </div>
  );
};

export default NinetySixHoursReports;
