import React, { useContext, useState, useEffect, useMemo } from 'react';
import { formatNumber, formatValue } from '../../../util/view-utils';
import vesselService from '../../../service/vessel-service';
import ErrorAlert from '../../ErrorAlert';
import { Link } from 'react-router-dom';
import { EnvironmentalReportContext } from '../../../context/EnvironmentalReportContext';
import _ from 'lodash';
import CustomTable from '../../customComponent/CustomTable';
import { Icon } from '../../../styleGuide';
import moment from 'moment';
import { ButtonToolbar, Dropdown } from 'react-bootstrap';
import CustomDropDownItemCheck from '../../customComponent/CustomDropDownItemCheck';

const WasteStreamReport = ({
  loading,
  setLoading,
  error,
  setError,
  selectedColumnIds,
  setSelectedColumnIds,
}) => {
  const { filterData, setExcelData } = useContext(EnvironmentalReportContext);
  const [WasteStreamReport, setWasteStreamReport] = useState([]);
  const [selectedColumns, setSelectedColumns] = useState([]);

  const columns = useMemo(
    () => [
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link to={`/vessel/ownership/details/${row.vessel_ownership_id}`} className="button-link">
            {formatValue(row.vessel_name)}
          </Link>
        ),
        id: 'vessel_name',
        name: 'vessel_name',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Vessel Type',
        id: 'vessel_type',
        type: 'text',
        accessor: (row) => formatValue(row?.vessel_type),
        disableSortBy: true,
      },
      {
        Header: 'DWT',
        accessor: (row) => formatValue(row.dwt, '0.00'),
        id: 'dwt',
        name: 'dwt',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Time Spent Underway (hr)',
        accessor: (row) => formatValue(row.time_spent_underway, '0.00'),
        id: 'time_spent_underway',
        name: 'time_spent_underway',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Time Spent In Port (hr)',
        accessor: (row) => formatValue(row.time_spent_port, '0.00'),
        id: 'time_spent_port',
        name: 'time_spent_port',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Distance Travelled(nm)',
        accessor: (row) => formatValue(row.distance_travelled, '0.00'),
        id: 'distance_travelled',
        name: 'distance_travelled',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Number Of Voyages',
        accessor: (row) => formatNumber(row.voyage_number),
        id: 'voyage_number',
        name: 'voyage_number',
        type: 'number',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Percentage of Laden Voyages',
        accessor: (row) => formatValue(row.laden_voyage_percentage, '0.00'),
        id: 'laden_voyage_percentage',
        name: 'laden_voyage_percentage',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Cargo Carried(MT or M3 For LNG)',
        accessor: (row) => formatValue(row.cargo_carried, '0.00'),
        id: 'cargo_carried',
        name: 'cargo_carried',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Transport Work',
        accessor: (row) => formatValue(row.transport_work, '0.00'),
        id: 'transport_work',
        name: 'transport_work',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Total Fuel Consumed At Sea (MT)',
        accessor: (row) => formatValue(row.fuel_sea_cons, '0.00'),
        id: 'fuel_sea_cons',
        name: 'fuel_sea_cons',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Total Fuel Consumed At Port (MT)',
        accessor: (row) => formatValue(row.fuel_port_cons, '0.00'),
        id: 'fuel_port_cons',
        name: 'fuel_port_cons',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Total CO2 Emitted At Sea (MT)',
        accessor: (row) => formatValue(row.co2_sea_emitted, '0.00'),
        id: 'co2_sea_emitted',
        name: 'co2_sea_emitted',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Total CO2 Emitted At Port (MT)',
        accessor: (row) => formatValue(row.co2_port_emiited, '0.00'),
        id: 'co2_port_emiited',
        name: 'co2_port_emiited',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Total SOX Emitted (MT)',
        accessor: (row) => formatValue(row.sox_emitted, '0.00'),
        id: 'sox_emitted',
        name: 'sox_emitted',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Average Sulphur Content Of Fuel Consumed (%)',
        accessor: (row) => formatValue(row.avg_shulphur_fuel_cons, '0.00'),
        id: 'avg_shulphur_fuel_cons',
        name: 'avg_shulphur_fuel_cons',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'NOX Emitted By Main Engines (MT)',
        accessor: (row) => formatValue(row.nox_emitted_me, '0.00'),
        id: 'nox_emitted_me',
        name: 'nox_emitted_me',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'NOX Emitted By Diesel Generators (MT)',
        accessor: (row) => formatValue(row.nox_emitted_dg, '0.00'),
        id: 'nox_emitted_dg',
        name: 'nox_emitted_dg',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Bilge Generated (M3)',
        accessor: (row) => formatValue(row.bilge_generated, '0.00'),
        id: 'bilge_generated',
        name: 'bilge_generated',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Sludge (%)',
        accessor: (row) => formatValue(row.sludge, '0.00'),
        id: 'sludge',
        name: 'sludge',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Garbage Generation (M3)',
        accessor: (row) => formatValue(row.garbage_generation, '0.00'),
        id: 'garbage_generation',
        name: 'garbage_generation',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Dry Cargo Residues Discharged To Sea (CU.M)',
        accessor: (row) => formatValue(row.cargo_discharged_sea, '0.00'),
        id: 'cargo_discharged_sea',
        name: 'cargo_discharged_sea',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Food Waste Discharged To Sea (CU.M)',
        accessor: (row) => formatValue(row.food_waste_dsicharged_sea, '0.00'),
        id: 'food_waste_dsicharged_sea',
        name: 'food_waste_dsicharged_sea',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Nett Refrigerant Emitted (KGS)',
        accessor: (row) => formatValue(row.refrigerant_emitted, '0.00'),
        id: 'refrigerant_emitted',
        name: 'refrigerant_emitted',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Qty Of Annex 1 Slop oil Discharged To Sea (Ltrs)',
        accessor: (row) => formatValue(row.qtx_annex_discharge_sea, '0.00'),
        id: 'qtx_annex_discharge_sea',
        name: 'qtx_annex_discharge_sea',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
    ],
    [],
  );

  const fetchWasteStreamReport = async () => {
    setLoading(true);
    setWasteStreamReport([]);
    try {
      if (filterData?.startDate && filterData?.endDate) {
        let pageFilterData = '';
        if (filterData?.startDate || filterData?.endDate || filterData?.eu) {
          pageFilterData = `startDate=${filterData.startDate ? filterData.startDate : ''}&endDate=${
            filterData.endDate ? filterData.endDate : ''
          }&eu=${filterData.eu}`;
        }
        const response = await vesselService.getWasteStreamReport(
          !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
          pageFilterData,
        );
        setWasteStreamReport(response.data.results);
        const result = response.data.results[0];
        setExcelData({
          fileName: 'Waste Stream Analysis',
          startDate: {
            year: moment(filterData.startDate).format('YYYY'),
            month: moment(filterData.startDate).format('MMM'),
          },
          endDate: {
            year: moment(filterData.endDate).format('YYYY'),
            month: moment(filterData.endDate).format('MMM'),
          },
          data: [
            {
              column: 'SHIP',
              data: result.vessel_name,
            },
            {
              column: 'SHIP TYPE',
              data: result.vessel_type,
            },
            {
              column: 'DWT',
              data: formatNumber(result.dwt),
            },
            {
              header: 'TIME',
              column: 'TIME SPENT UNDERWAY',
              data: formatNumber(result.time_spent_underway),
            },
            {
              header: 'TIME',
              column: 'TIME SPENT IN PORT',
              data: formatNumber(result.time_spent_port),
            },
            {
              header: 'DISTANCE',
              column: 'DISTANCE TRAVELLED (NM)',
              data: formatNumber(result.distance_travelled),
            },
            {
              header: 'VOYAGES',
              column: 'NUMBER OF VOYAGES',
              data: formatNumber(result.voyage_number),
            },
            {
              header: 'VOYAGES',
              column: 'PERCENTAGE OF LADEN VOYAGES',
              data: formatNumber(result.laden_voyage_percentage),
            },
            {
              header: 'CARGO',
              column: 'CARGO CARRIED (MT OR M3 FOR LNG)',
              data: formatNumber(result.cargo_carried),
            },
            {
              header: 'CARGO',
              column: 'TRANSPORT WORK',
              data: formatNumber(result.transport_work),
            },
            {
              header: 'FUEL',
              column: 'TOTAL FUEL CONSUMED AT SEA (MT)',
              data: formatNumber(result.fuel_sea_cons),
            },

            {
              header: 'FUEL',
              column: ' TOTAL FUEL CONSUMED IN PORT (MT)',
              data: formatNumber(result.fuel_port_cons),
            },
            {
              header: 'CO2',
              column: 'TOTAL CO2 EMITTED AT SEA (MT)',
              data: formatNumber(result.co2_sea_emitted),
            },
            {
              header: 'CO2',
              column: 'TOTAL CO2 EMITTED IN PORT (MT)',
              data: formatNumber(result.co2_port_emiited),
            },
            {
              header: 'SOX',
              column: 'TOTAL SOX EMITTED (MT)',
              data: formatNumber(result.sox_emitted),
            },
            {
              header: 'SOX',
              column: 'AVERAGE SULPHUR CONTENT OF FUEL CONSUMED (%)',
              data: formatNumber(result.avg_shulphur_fuel_cons),
            },
            {
              header: 'NOX',
              column: 'NOX EMITTED BY MAIN ENGINES',
              data: formatNumber(result.nox_emitted_me),
            },
            {
              header: 'NOX',
              column: 'NOX EMITTED BY DIESEL GENERATORS',
              data: formatNumber(result.nox_emitted_dg),
            },
            {
              header: 'BILGE',
              column: 'BILGE GENERATED (M3)',
              data: formatNumber(result.bilge_generated),
            },
            {
              header: 'SLUDGE',
              column: 'SLUDGE %',
              data: formatNumber(result.sludge),
            },
            {
              header: 'GARBAGE - GENERAL',
              column: 'GARBAGE GENERATION (M3)',
              data: formatNumber(result.garbage_generation),
            },
            {
              header: 'GARBAGE - DRY CARGO RESIDUES',
              column: 'DRY CARGO RESIDUES DISCHARGED TO SEA (CU.M)',
              data: formatNumber(result.cargo_discharged_sea),
            },
            {
              header: 'GARBAGE - FOOD WASTE',
              column: 'FOOD WASTE DISCHARGED TO SEA (CU.M)',
              data: formatNumber(result.food_waste_dsicharged_sea),
            },
            {
              header: 'REFRIGERANTS (ODS)',
              column: 'NETT REFRIGERANTS EMITTED',
              data: formatNumber(result.refrigerant_emitted),
            },
            {
              header: 'ANNEX 1 SLOPS',
              column: 'QTY OF ANNEX 1 SLOP OIL DISCHARGED TO SEA',
              data: formatNumber(result.qtx_annex_discharge_sea),
            },
          ],
        });
        setError(null);
      }
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  const onSelectDropDown = (id, checked) => {
    if (selectedColumnIds?.length > 1) {
      if (checked) setSelectedColumnIds([...selectedColumnIds, id]);
      else {
        const listIds = _.remove(selectedColumnIds, function (n) {
          return n != id;
        });
        setSelectedColumnIds(listIds);
      }
    }
  };

  useEffect(() => {
    const columnsData = _.filter(columns, function (o) {
      return _.includes(selectedColumnIds, o.id);
    });
    setSelectedColumns(columnsData);
  }, [selectedColumnIds]);

  useEffect(() => {
    if (filterData?.vessel[0]?.id) {
      fetchWasteStreamReport();
    }
  }, [filterData]);

  return (
    <div className="pt-4">
      {error && <ErrorAlert message={error} />}
      <ButtonToolbar className="pb-4">
        <Dropdown alignRight className="ml-auto">
          <Dropdown.Toggle
            variant="outline-primary"
            id="dropdown-more"
            data-testid="fml-waste-stream-report-table-columns"
          >
            Table Columns
          </Dropdown.Toggle>
          <Dropdown.Menu>
            {columns.map((item) => {
              const checked = _.includes(selectedColumnIds, item.id);
              return (
                <Dropdown.Item
                  key={item.id}
                  as={CustomDropDownItemCheck}
                  checked={checked}
                  onClick={() => onSelectDropDown(item.id, !checked)}
                >
                  {item.showToolTip ? item.name : item.Header}
                </Dropdown.Item>
              );
            })}
          </Dropdown.Menu>
        </Dropdown>
      </ButtonToolbar>
      {filterData?.vessel[0]?.id ? (
        <CustomTable
          column={selectedColumns}
          reportData={WasteStreamReport}
          tableRef={null}
          isLoading={loading}
          pagination={false}
          className="drill-table-top-border"
        />
      ) : (
        <div className="no-result-found mt-5">
          <Icon icon="alert" className="alert-icon-no-search" />
          <div data-testid="fml-no-result">
            <b>No Vessel Selected. Please select a vessel</b>
          </div>
        </div>
      )}
    </div>
  );
};

export default WasteStreamReport;
