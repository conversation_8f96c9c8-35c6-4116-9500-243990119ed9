import React, { useContext, useState, useEffect, useMemo } from 'react';
import { formatValue, formatNumber } from '../../../util/view-utils';
import vesselService from '../../../service/vessel-service';
import ErrorAlert from '../../ErrorAlert';
import { Link } from 'react-router-dom';
import { EnvironmentalReportContext } from '../../../context/EnvironmentalReportContext';
import _ from 'lodash';
import CustomTable from '../../customComponent/CustomTable';
import { Icon } from '../../../styleGuide';
import moment from 'moment';
import CustomTooltip from '../../customComponent/CustomTooltip';
import { ButtonToolbar, Dropdown } from 'react-bootstrap';
import CustomDropDownItemCheck from '../../customComponent/CustomDropDownItemCheck';

const ImoDataReport = ({
  loading,
  setLoading,
  error,
  setError,
  selectedColumnIds,
  setSelectedColumnIds,
}) => {
  const { filterData, setExcelData, ga4EventTrigger = () => {} } = useContext(
    EnvironmentalReportContext,
  );
  const [imoDataReport, setImoDataReport] = useState([]);
  const [selectedColumns, setSelectedColumns] = useState([]);

  const renderHeaderWithTooltip = (headerText, tooltipText) => {
    return (
      <div className="d-flex align-items-center">
        <div>{headerText}</div>
        <CustomTooltip
          content={<Icon icon="alert" size={20} className="alert-icon-no-search" />}
          tooltipText={tooltipText}
          className={'tooltip-header'}
          onToggle={(isOpen) => {
            if (isOpen) {
              ga4EventTrigger('View Tooltip', 'IMO Data Collection Report - Menu', tooltipText);
            }
          }}
        />
      </div>
    );
  };

  const columns = useMemo(
    () => [
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link to={`/vessel/ownership/details/${row.vessel_ownership_id}`} className="button-link">
            {formatValue(row.vessel_name)}
          </Link>
        ),
        id: 'vessel_name',
        name: 'vessel_name',
        type: 'text',
        disableSortBy: true,
      },
      {
        Header: 'Vessel Type',
        id: 'vessel_type',
        type: 'text',
        accessor: (row) => formatValue(row?.vessel_type),
        disableSortBy: true,
      },
      {
        Header: renderHeaderWithTooltip('Ethanol', '(Cf: 1.913)'),
        accessor: (row) => formatValue(row.ethanol, '0.00'),
        id: 'ethanol',
        name: 'Ethanol',
        type: 'text',
        maxWidth: 150,
        disableSortBy: true,
        showToolTip: true,
      },
      {
        Header: renderHeaderWithTooltip('Methanol', '(Cf: 1.375)'),
        accessor: (row) => formatValue(row.methanol, '0.00'),
        id: 'methanol',
        name: 'Methanol',
        type: 'text',
        maxWidth: 150,
        disableSortBy: true,
        showToolTip: true,
      },
      {
        Header: renderHeaderWithTooltip('LNG', '(Cf: 2.750)'),
        accessor: (row) => formatValue(row.lng, '0.00'),
        id: 'lng',
        name: 'LNG',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
        showToolTip: true,
      },
      {
        Header: renderHeaderWithTooltip('LPG(Butane)', '(Cf: 3.030)'),
        accessor: (row) => formatValue(row.lpg_butane, '0.00'),
        id: 'lpg_butane',
        name: 'LPG(Butane)',
        type: 'text',
        maxWidth: 200,
        disableSortBy: true,
        showToolTip: true,
      },
      {
        Header: renderHeaderWithTooltip('LPG(Propane)', ' (Cf: 3.000)'),
        accessor: (row) => formatValue(row.lpg_propane),
        id: 'lpg_propane',
        name: 'LPG(Propane)',
        type: 'text',
        maxWidth: 200,
        disableSortBy: true,
        showToolTip: true,
      },
      {
        Header: renderHeaderWithTooltip('HFO', '(Cf: 3.114)'),
        accessor: (row) => formatValue(row.hfo, '0.00'),
        id: 'hfo',
        name: 'HFO',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
        showToolTip: true,
      },
      {
        Header: renderHeaderWithTooltip('LFO', ' (Cf: 3.151)'),
        accessor: (row) => formatValue(row.lfo, '0.00'),
        id: 'lfo',
        name: 'LFO',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
        showToolTip: true,
      },
      {
        Header: renderHeaderWithTooltip('Diesel/Gas Oil', ' (Cf: 3.206)'),
        accessor: (row) => formatValue(row.diesel_gas, '0.00'),
        id: 'diesel_gas',
        name: 'Diesel/Gas Oil',
        type: 'text',
        maxWidth: 200,
        disableSortBy: true,
        showToolTip: true,
      },
      {
        Header: 'Hours Underway(Hr)',
        accessor: (row) => formatValue(row.underway_hour, '0.00'),
        id: 'underway_hour',
        name: 'underway_hour',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Distance Travelled(nm)',
        accessor: (row) => formatValue(row.distance_travelled, '0.00'),
        id: 'distance_travelled',
        name: 'distance_travelled',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Auxiliary Engine(s)',
        accessor: (row) => formatValue(row.toal_aux_eng_pw, '0.00'),
        id: 'auxiliary_engine',
        name: 'auxiliary_engine',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Main Propulsion Power',
        accessor: (row) => formatValue(row.main_propulsion_pw, '0.00'),
        id: 'main_propulsion_pw',
        name: 'main_propulsion_pw',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Ice Class',
        accessor: (row) => formatValue(row.ice_class),
        id: 'ice_class',
        name: 'ice_class',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'EEDI (gCO2/t.nm)',
        accessor: (row) => formatValue(row.eedi, '0.00'),
        id: 'eedi',
        name: 'eedi',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'DWT',
        accessor: (row) => formatValue(row.dwt, '0.00'),
        id: 'dwt',
        name: 'dwt',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'NT',
        accessor: (row) => formatValue(row.nt, '0.00'),
        id: 'nt',
        name: 'nt',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Gross Tonnage',
        accessor: (row) => formatValue(row.gross_tonnage, '0.00'),
        id: 'gross_tonnage',
        name: 'gross_tonnage',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Imo Number',
        accessor: (row) => formatValue(row.imo_number, '0.00'),
        id: 'imo_number',
        name: 'imo_number',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Method used to measure fuel oil consumption',
        accessor: (row) => formatValue(row.method_fuel_oil_cons),
        id: 'method_fuel_oil_cons',
        name: 'method_fuel_oil_cons',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: '(cf;....)',
        accessor: (row) => formatValue(row.other_fuel3_emission_factor, '0.00'),
        id: 'cf',
        name: 'cf',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Other(....)',
        accessor: (row) => formatValue(row.other_fuel3_consumption, '0.00'),
        id: 'other',
        name: 'other',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
    ],
    [],
  );

  const fetchImoDataReport = async () => {
    setLoading(true);
    setImoDataReport([]);
    try {
      if (filterData?.startDate && filterData?.endDate) {
        let pageFilterData = '';
        if (filterData?.startDate || filterData?.endDate) {
          pageFilterData = `startDate=${filterData.startDate ? filterData.startDate : ''}&endDate=${
            filterData.endDate ? filterData.endDate : ''
          }`;
        }
        const response = await vesselService.getImoDataReport(
          !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
          pageFilterData,
        );
        setImoDataReport(response.data.results);
        const result = response.data.results[0];
        setExcelData({
          fileName: 'IMO DCS REPORTS',
          startDate: {
            year: moment(filterData.startDate).format('YYYY'),
            month: moment(filterData.startDate).format('MMM'),
          },
          endDate: {
            year: moment(filterData.endDate).format('YYYY'),
            month: moment(filterData.endDate).format('MMM'),
          },
          data: [
            {
              column: 'VESSEL',
              data: formatValue(filterData?.vessel[0]?.name),
            },
            {
              column: 'METHOD USED TO MEASURE FUEL OIL CONSUMPTION',
              data: formatValue(result.method_fuel_oil_cons),
            },
            {
              column: '(CF ;…..)',
              data: formatNumber(result.other_fuel3_emission_factor),
            },
            {
              column: 'OTHER(……….)',
              data: formatNumber(result.other_fuel3_consumption),
            },
            {
              column: 'ETHANOL (Cf: 1.913)',
              data: formatNumber(result.ethanol),
            },
            {
              column: 'METHANOL (Cf: 1.375)',
              data: formatNumber(result.methanol),
            },
            {
              column: 'LNG (Cf: 2.750) ',
              data: formatNumber(result.lng),
            },
            {
              column: 'LPG (Butane) (Cf: 3.030)',
              data: formatNumber(result.lpg_butane),
            },
            {
              column: 'LPG (Propane) (Cf: 3.000)',
              data: formatNumber(result.lpg_propane),
            },
            {
              column: 'HFO (Cf: 3.114)',
              data: formatNumber(result.hfo),
            },
            {
              column: 'LFO (Cf: 3.151)',
              data: formatNumber(result.lfo),
            },
            {
              column: 'DIESEL/GAS Oil (Cf: 3.206)',
              data: formatNumber(result.diesel_gas),
            },
            {
              column: 'HOURS UNDERWAY (H)',
              data: formatNumber(result.underway_hour),
            },
            {
              column: 'DISTANCE TRAVELLED (NM)',
              data: formatNumber(result.distance_travelled),
            },
            {
              column: 'AUXILIARY ENGINE(S)',
              data: formatNumber(result.auxiliary_engine),
            },
            {
              column: 'MAIN PROPULSION POWER',
              data: formatNumber(result.main_propulsion_pw),
            },
            {
              column: 'ICE CLASS',
              data: formatValue(result.ice_class),
            },
            {
              column: 'EEDI (gCO2/t.nm)',
              data: formatNumber(result.eedi),
            },
            {
              column: 'DWT',
              data: formatNumber(result.dwt),
            },
            {
              column: 'NT',
              data: formatNumber(result.nt),
            },
            {
              column: 'GROSS TONNAGE',
              data: formatNumber(result.gross_tonnage),
            },
            {
              column: 'SHIP TYPE',
              data: formatValue(result.vessel_type),
            },
            {
              column: 'IMO NUMBER',
              data: formatNumber(result.imo_number),
            },
            {
              column: 'END DATE (DD/MM/YYYY)',
              data: filterData.endDate,
            },
            {
              column: 'START DATE (DD/MM/YYYY)',
              data: filterData.startDate,
            },
          ],
        });
        setError(null);
      }
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  const onSelectDropDown = (id, checked) => {
    if (selectedColumnIds?.length > 1) {
      if (checked) setSelectedColumnIds([...selectedColumnIds, id]);
      else {
        const listIds = _.remove(selectedColumnIds, function (n) {
          return n != id;
        });
        setSelectedColumnIds(listIds);
      }
    }
  };

  useEffect(() => {
    const columnsData = _.filter(columns, function (o) {
      return _.includes(selectedColumnIds, o.id);
    });
    setSelectedColumns(columnsData);
  }, [selectedColumnIds]);

  useEffect(() => {
    if (filterData?.vessel[0]?.id) fetchImoDataReport();
  }, [filterData]);

  return (
    <div className="pt-4">
      {error && <ErrorAlert message={error} />}
      <ButtonToolbar className="pb-4">
        <Dropdown alignRight className="ml-auto">
          <Dropdown.Toggle
            variant="outline-primary"
            id="dropdown-more"
            data-testid="fml-imo-report-table-columns"
          >
            Table Columns
          </Dropdown.Toggle>
          <Dropdown.Menu>
            {columns.map((item) => {
              const checked = _.includes(selectedColumnIds, item.id);
              return (
                <Dropdown.Item
                  key={item.id}
                  as={CustomDropDownItemCheck}
                  checked={checked}
                  onClick={() => onSelectDropDown(item.id, !checked)}
                >
                  {item.showToolTip ? item.name : item.Header}
                </Dropdown.Item>
              );
            })}
          </Dropdown.Menu>
        </Dropdown>
      </ButtonToolbar>
      {filterData?.vessel[0]?.id ? (
        <CustomTable
          column={selectedColumns}
          reportData={imoDataReport}
          tableRef={null}
          isLoading={loading}
          pagination={false}
          className="drill-table-top-border"
        />
      ) : (
        <div className="no-result-found mt-5">
          <Icon icon="alert" className="alert-icon-no-search" />
          <div data-testid="fml-no-result">
            <b>No Vessel Selected. Please select a vessel</b>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImoDataReport;
