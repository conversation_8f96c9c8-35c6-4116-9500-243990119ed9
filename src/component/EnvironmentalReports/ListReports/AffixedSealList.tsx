import moment from 'moment';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Form, OverlayTrigger, Tooltip, Row } from 'react-bootstrap';
import vesselService from '../../../service/vessel-service';
import CustomDatePicker from '../../../component/customComponent/CustomDatePicker';
import { formatDate, formatValue } from '../../../util/view-utils';
import CustomTable from '../../customComponent/CustomTable';
import ErrorAlert from '../../ErrorAlert';
import _ from 'lodash';
import { EnvironmentalReportContext } from '../../../context/EnvironmentalReportContext';

const AffixedSealList = ({ ownershipId }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState();
  const [pageCount, setPageCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [sortData, setSortData] = useState([]);
  const [sealList, setSealList] = useState();
  const [showPresentAffixed, setShowPresentAffixed] = useState(false);
  const [affixedStartDate, setAffixedStartDate] = useState('');
  const [affixedEndDate, setAffixedEndDate] = useState('');
  const [highlightedRows, setHighlightedRows] = useState([]);
  const { vesselList, ga4EventTrigger = () => {} } = useContext(EnvironmentalReportContext);

  const columns = useMemo(
    () => [
      {
        Header: 'Location#',
        accessor: (row) => formatValue(row.location_num),
        sticky: 'left',
        id: 'location_num',
        type: 'text',
      },
      {
        Header: 'Sub-location',
        accessor: (row) => formatValue(row.sublocation),
        id: 'sublocation',
        type: 'text',
      },
      {
        Header: 'Seal#',
        id: 'serial',
        type: 'text',
        accessor: (row) => formatValue(row.serial),
      },
      {
        Header: 'System',
        id: 'system',
        type: 'text',
        accessor: (row) => formatValue(row.system),
        disableSortBy: true,
      },
      {
        Header: 'Description of Seal Location on System',
        id: 'description',
        type: 'text',
        accessor: (row) => formatValue(row.description),
        disableSortBy: true,
      },
      {
        Header: 'Component',
        accessor: (row) => formatValue(row.component),
        id: 'component',
        type: 'text',
        maxWidth: 120,
      },
      {
        Header: 'Component Size',
        accessor: (row) => formatValue(row.component_size),
        id: 'component_size',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'E/R Location (Vertical)',
        accessor: (row) => formatValue(row.location_vert),
        id: 'location_vert',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'E/R Location (Side)',
        accessor: (row) => formatValue(row.location_other),
        id: 'location_other',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
      {
        Header: 'Seal Type',
        accessor: (row) => formatValue(row.seal_type),
        id: 'seal_type',
        type: 'text',
        maxWidth: 120,
      },
      {
        Header: 'Affixed Date',
        accessor: (row) => formatDate(row.affixed_date, 'DD MMM YYYY'),
        id: 'affixed_date',
        type: 'date',
        maxWidth: 120,
      },
      {
        Header: 'Affixed Reason',
        accessor: (row) =>
          row.remark ? (
            <OverlayTrigger
              overlay={
                <Tooltip id="desc_tooltip" className="tooltip">
                  {row.remark}
                </Tooltip>
              }
              placement="bottom"
            >
              <div className="line-text-truncate button-link">{_.get(row, 'affixed_reason')}</div>
            </OverlayTrigger>
          ) : (
            <div className="line-text-truncate">{_.get(row, 'affixed_reason')}</div>
          ),
        id: 'affixed_reason',
        type: 'date',
        maxWidth: 120,
      },
      {
        Header: 'Approved by Master',
        accessor: (row) => (row.approved ? 'Yes' : 'No'),
        id: 'approved',
        type: 'text',
        maxWidth: 120,
        disableSortBy: true,
      },
    ],
    [],
  );

  const fetchSealLog = async (
    sortData = [{ id: 'affixed_date', desc: true }],
    pageIndex = 0,
    pageSize = 10,
  ) => {
    setError(null);
    setLoading(true);
    try {
      let pageFilterData = `mode=${showPresentAffixed ? 'present' : 'affixed'}`;
      if (affixedStartDate !== '' || affixedEndDate !== '') {
        pageFilterData = pageFilterData.concat(
          `&affixed_date=${affixedStartDate},${affixedEndDate}`,
        );
      }
      const response = await vesselService.getSealLog(
        ownershipId,
        {
          sortBy: sortData,
          pageSize: pageSize,
          pageIndex: pageIndex,
        },
        pageFilterData,
      );
      setSealList(response.data.results);
      const oldData = response.data.results.map((item, index) => {
        if (!item.is_present) return index;
      });
      setHighlightedRows([
        { list: _.without(oldData, undefined), class: 'highlight-row-light-blue' },
      ]);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchSealLog(sortData, pageIndex, pageSize);
  }, [showPresentAffixed, affixedStartDate, affixedEndDate]);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchSealLog(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', 'Seal Report - List', value);
    setPageSize(value);
    setPageIndex(0);
    fetchSealLog(sortData, 0, value);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Seal Report - List', value[0]?.id);
    setSortData(value);
    fetchSealLog(value, pageIndex, pageSize);
  };

  const handleCheckboxChange = (event) => {
    ga4EventTrigger(
      'Display Present Affixed',
      'Seal Report Detail - Menu',
      vesselList.filter((vessel) => `${vessel.id}` === ownershipId)[0]?.value,
    );
    setShowPresentAffixed(event.currentTarget.checked);
  };

  const FilterComponent = () => {
    return (
      <>
        <Row>
          <Form.Label className="filter-reports">
            <b>Filter Affixed Seals</b>
          </Form.Label>
        </Row>

        <Row>
          <Form.Group className="reports-filter-textField form-group">
            <Form.Control type="text" placeholder="Seal Affixed Date" disabled />
          </Form.Group>

          <Form.Group className="startDatePicker form-group">
            <CustomDatePicker
              value={affixedStartDate}
              onChange={(event) =>
                setAffixedStartDate(event ? moment(event).format('YYYY-MM-DD') : '')
              }
              dataTestId="fml-affixed-seal-filter-start-date"
              disabled={loading}
            />
          </Form.Group>
          <Form.Group className="mx-2 align-self-center form-group">To</Form.Group>
          <Form.Group className="endDatePicker form-group">
            <CustomDatePicker
              value={affixedEndDate}
              onChange={(event) =>
                setAffixedEndDate(event ? moment(event).format('YYYY-MM-DD') : '')
              }
              dataTestId="fml-affixed-seal-filter-end-date"
              disabled={loading}
            />
          </Form.Group>

          <Form.Group className="reports-filter-checkbox form-group">
            <Form.Check
              type="checkbox"
              label="Display Present Affixed Seals Only"
              data-testid="fml-affixed-seal-display-present"
              checked={showPresentAffixed}
              onChange={(e) => handleCheckboxChange(e)}
            />
          </Form.Group>
        </Row>
      </>
    );
  };

  return (
    <div className="pt-4">
      <span className="pl-1">
        Historical Affixed Seals highlighted in background.
        <span className="text-info font-weight-bold">light blue</span>background.{' '}
      </span>
      <div className="no-print">
        <FilterComponent />
      </div>
      {error && <ErrorAlert message={error} />}
      <CustomTable
        column={columns}
        reportData={sealList}
        tableRef={null}
        isLoading={loading}
        pagination={true}
        pageCount={pageCount}
        totalCount={totalCount}
        setPageNo={onPageIndexChange}
        setPageListSize={onPageSizeChange}
        setSortData={onSortChange}
        pageNo={pageIndex}
        className="drill-table-top-border affixed-table"
        highlightedRows={highlightedRows}
        pageSize={pageSize}
      />
    </div>
  );
};

export default AffixedSealList;
