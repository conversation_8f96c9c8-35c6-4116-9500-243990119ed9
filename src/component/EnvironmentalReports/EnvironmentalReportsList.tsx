import _ from 'lodash';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { ButtonToolbar, Container, Dropdown, Tab } from 'react-bootstrap';
import { useHistory, useParams } from 'react-router-dom';
import { EnvironmentalReportContext } from '../../context/EnvironmentalReportContext';
import {
  etsPendingVoyageReportSelectedIds,
  etsPortReportSelectedIds,
  etsVoyageReportSelectedIds,
  imoSelectedIds,
  mrvPortReportSelectedIds,
  mrvVoyageReportSelectedIds,
  wasteStreamSelectedIds,
} from '../../model/constants';
import { EUETSExcelExport } from '../../util/eu-ets-export/eu-ets-excel-export';
import { exportToImoExcel, exportToWasteAnalysisExcel } from '../../util/excel-export';
import getURLParams from '../../util/getURLParams';
import { exportToMrvExcel } from '../../util/mrv-excel-export';
import TabWrapper from '../TabWrapper';
import EnvironmentalReportsFilter from './EnvironmentalReportsFilter';
import ETSReport from './ListReports/ETSReport';
import ImoDataReport from './ListReports/ImoDataReport';
import MonthlyMarpolReports from './ListReports/MonthlyMarpolReports';
import MrvReport from './ListReports/MrvReport';
import NinetySixHoursReports from './ListReports/NinetySixHoursReports';
import SealManagementList from './ListReports/SealManagementList';
import WasteStreamReport from './ListReports/WasteStreamReport';
import SealManagementListFilter from './SealManagementFilter';
import moment from 'moment';
import EuEtsReportsFilter from './EuEtsReportsFilter';

const environmentalReportsPageTabData = [
  {
    eventKey: 'marpol',
    tabName: 'Monthly MARPOL Report',
  },
  {
    eventKey: 'seal',
    tabName: 'Seal Management',
  },
  {
    eventKey: 'mrv',
    tabName: 'MRV Report',
  },
  {
    eventKey: 'ets',
    tabName: 'EU Emission Reports',
  },
  {
    eventKey: 'waste-stream',
    tabName: 'Waste Stream Analysis',
  },
  {
    eventKey: 'imo-collection',
    tabName: 'IMO Data Collection',
  },
  {
    eventKey: 'ninety-six-hours',
    tabName: '96 Hours',
  },
];

const EnvironmentalReportsList = () => {
  const {
    filterData,
    excelData,
    ga4EventTrigger = () => { },
    roleConfig,
    setFilterData
  } = useContext(EnvironmentalReportContext);
  const history = useHistory();
  const { tab = 'marpol' } = useParams();
  const [activeTab, setActiveTab] = useState(tab);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTabStartTime, setActiveTabStartTime] = useState(0);

  const [selectedColumnIdsImo, setSelectedColumnIdsImo] = useState(imoSelectedIds);

  const [selectedColumnIdsWasteStream, setSelectedColumnIdsWasteStream] =
    useState(wasteStreamSelectedIds);

  const [selectedColumnIdsMrvVoyageReport, setSelectedColumnIdsMrvVoyageReport] = useState(
    mrvVoyageReportSelectedIds,
  );
  const [selectedColumnIdsEtsVoyageReport, setSelectedColumnIdsEtsVoyageReport] = useState(
    etsVoyageReportSelectedIds,
  );

  const [selectedColumnIdsMrvPortReport, setSelectedColumnIdsMrvPortReport] =
    useState(mrvPortReportSelectedIds);

  const [selectedColumnIdsEtsPortReport, setSelectedColumnIdsEtsPortReport] =
    useState(etsPortReportSelectedIds);

  const [selectedColumnIdsEtsPendingVoyageReport, setSelectedColumnIdsEtsPendingVoyageReport] =
    useState(etsPendingVoyageReportSelectedIds);

  const onChangeMrvReportTableColumns = (type, data) => {
    if (type === 'voyage') {
      setSelectedColumnIdsMrvVoyageReport(data);
    }
    if (type === 'port') {
      setSelectedColumnIdsMrvPortReport(data);
    }
  };
  const onChangeEtsReportTableColumns = (type, data) => {
    if (type === 'voyage') {
      setSelectedColumnIdsEtsVoyageReport(data);
    }
    if (type === 'port') {
      setSelectedColumnIdsEtsPortReport(data);
    }
    if (type === 'pending-voyage') {
      setSelectedColumnIdsEtsPendingVoyageReport(data);
    }
  };

  const handleTabSelect = (key) => {
    if (key !== activeTab) {
      if (key === 'ets') {
        setActiveTabStartTime(moment().valueOf());
      } else if (activeTab === "ets") {
          const timeSpentOnEts = (moment.utc(moment().valueOf() - activeTabStartTime).format('HH:mm:ss'));
          ga4EventTrigger('Time Spent', `EU-ETS – Time Spent`, `EU-ETS - ${timeSpentOnEts}`);
        }
      ga4EventTrigger(
        'Switch Tab',
        `Vessel ${environmentalReportsPageTabData.find((item) => item.eventKey === activeTab)?.tabName
        } Report - Menu`,
        environmentalReportsPageTabData.find((item) => item.eventKey === key)?.tabName,
      );
      setActiveTab('');
      setActiveTab(key);
    }
    if (key === 'ets') {
      const startDate = moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
      const endDate = moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD');
      setFilterData({ ...filterData, startDate, endDate });
    }
    const ownershipId = getURLParams('vessel_ownership_id', history.location.search);
    if (ownershipId) {
      return history.push(
        `/vessel/report/environmental/${key}?report_date=${filterData.startDate},${filterData.endDate}&vessel_ownership_id=${ownershipId}`,
      );
    }
    return history.push(
      `/vessel/report/environmental/${key}?report_date=${filterData.startDate},${filterData.endDate}`,
    );
  };

  const updateQuery = useCallback(() => {
    if (!_.isEmpty(filterData)) {
      let queryParam = '';
      queryParam += `&report_date=${filterData?.startDate},${filterData?.endDate}`;
      if (filterData.vessel[0]?.id) {
        queryParam += `&vessel_ownership_id=${filterData.vessel[0]?.id}`;
      }
      if (filterData.dataSource[0]?.value) {
        queryParam += `&datasource=${filterData.dataSource[0]?.value}`;
      }
      history.push(`${history.location.pathname}?${queryParam}`);
    }
  }, [filterData, tab]);

  useEffect(() => {
    updateQuery();
  }, [filterData, tab]);

  const handleExportToExcel = useCallback(() => {
    switch (activeTab) {
      case 'waste-stream':
        exportToWasteAnalysisExcel(excelData);
        break;
      case 'imo-collection':
        exportToImoExcel(excelData);
        break;
      case 'mrv':
        exportToMrvExcel(excelData);
        break;
      case 'ets':
        new EUETSExcelExport(excelData).export();
        ga4EventTrigger('Export to Excel', `EU-ETS – Menu`, `${excelData.vesselName} : ${moment.utc().format('DD MMM YYYY')}`);
        break;
      default:
        console.log('Invalid Tab');
    }
  }, [activeTab, excelData]);

  const handleControlParameter = () => {
    ga4EventTrigger('Environmental Control', `Environmental Report - Menu`, 'Control Parameter');
    return history.push(`/vessel/report/environmental/control-parameter`);
  };

  const EnvironmentalReportButtons = useMemo(() => {
    return (
      <div className="no-print">
        <ButtonToolbar className="pb-4">
          {(roleConfig.params.view ||
            ['waste-stream', 'imo-collection', 'mrv', 'ets'].includes(activeTab)) && (
              <Dropdown align={'end'}>
                <Dropdown.Toggle
                  variant="outline-primary"
                  data-testid="fml-environmental-report-list-more-dropdown"
                  id="dropdown-more"
                >
                  More
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  {activeTab === 'marpol' && roleConfig.params.view && (
                    <Dropdown.Item
                      onClick={handleControlParameter}
                      data-testid="fml-environmental-report-list-more-dropdown-control-parameter"
                    >
                      Control Parameters
                    </Dropdown.Item>
                  )}
                  {['waste-stream', 'imo-collection', 'mrv', 'ets'].includes(activeTab) && (
                    <Dropdown.Item
                      onClick={handleExportToExcel}
                      data-testid="fml-environmental-report-list-more-dropdown-export-excel"
                    >
                      Export to Excel
                    </Dropdown.Item>
                  )}
                </Dropdown.Menu>
              </Dropdown>
            )}
        </ButtonToolbar>
      </div>
    );
  }, [activeTab, excelData]);

  return (
    <Container>
      <Tab.Container activeKey={activeTab} defaultActiveKey="all">
        <div className="d-flex justify-content-between">
          <div className="pb-4 font-weight-bold technical-reports">Environmental Reports</div>
          {EnvironmentalReportButtons}
        </div>

        <div className="no-print tab-wrapper">
          <TabWrapper
            handleTabSelect={handleTabSelect}
            data={environmentalReportsPageTabData}
            step={tab}
            setActiveTab={setActiveTab}
            activeTab={activeTab}
          />
        </div>

        {tab == 'marpol' && (
          <>
            <EnvironmentalReportsFilter loading={loading} />
            <MonthlyMarpolReports
              loading={loading}
              setLoading={setLoading}
              error={error}
              setError={setError}
            />
          </>
        )}
        {tab == 'seal' && (
          <>
            <SealManagementListFilter loading={loading} />
            <SealManagementList
              loading={loading}
              setLoading={setLoading}
              error={error}
              setError={setError}
            />
          </>
        )}
        {tab === 'mrv' && (
          <>
            <EnvironmentalReportsFilter loading={loading} activeTab={activeTab} />
            <MrvReport
              loading={loading}
              setLoading={setLoading}
              error={error}
              setError={setError}
              selectedVoyageColumnIds={selectedColumnIdsMrvVoyageReport}
              selectedPortColumnIds={selectedColumnIdsMrvPortReport}
              onChangeMrvReportTableColumns={onChangeMrvReportTableColumns}
            />
          </>
        )}
        {tab === 'ets' && (
          <>
            <EuEtsReportsFilter loading={loading} />
            <ETSReport
              loading={loading}
              setLoading={setLoading}
              error={error}
              setError={setError}
              selectedVoyageColumnIds={selectedColumnIdsEtsVoyageReport}
              selectedPortColumnIds={selectedColumnIdsEtsPortReport}
              selectedPendingVoyageColumnIds={selectedColumnIdsEtsPendingVoyageReport}
              onChangeEtsReportTableColumns={onChangeEtsReportTableColumns}
              ga4EventTrigger={ga4EventTrigger}
            />
          </>
        )}
        {tab === 'waste-stream' && (
          <>
            <EnvironmentalReportsFilter loading={loading} activeTab={activeTab} />
            <WasteStreamReport
              loading={loading}
              setLoading={setLoading}
              error={error}
              setError={setError}
              selectedColumnIds={selectedColumnIdsWasteStream}
              setSelectedColumnIds={setSelectedColumnIdsWasteStream}
            />
          </>
        )}
        {tab === 'imo-collection' && (
          <>
            <EnvironmentalReportsFilter loading={loading} activeTab={activeTab} />
            <ImoDataReport
              loading={loading}
              setLoading={setLoading}
              error={error}
              setError={setError}
              selectedColumnIds={selectedColumnIdsImo}
              setSelectedColumnIds={setSelectedColumnIdsImo}
            />
          </>
        )}
        {tab === 'ninety-six-hours' && (
          <>
            <EnvironmentalReportsFilter loading={loading} activeTab={activeTab} />
            <NinetySixHoursReports
              loading={loading}
              setLoading={setLoading}
              error={error}
              setError={setError}
            />
          </>
        )}
      </Tab.Container>
    </Container>
  );
};

export default EnvironmentalReportsList;
