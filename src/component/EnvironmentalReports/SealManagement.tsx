import React, { useContext, useMemo, useState } from 'react';
import { ButtonToolbar, Col, Container, Dropdown, Row } from 'react-bootstrap';
import { useHistory, useParams } from 'react-router-dom';
import { EnvironmentalReportContext } from '../../context/EnvironmentalReportContext';
import { BreadcrumbHeader } from '../BreadcrumpHeader';
import TabWrapper from '../TabWrapper';
import AffixedSealList from './ListReports/AffixedSealList';
import SealLogList from './ListReports/SealLogList';
import UnusedSealList from './ListReports/UnusedSealList';

const { PARIS2_URL } = process.env;
const sealMgmtPageTabData = [
  {
    eventKey: 'unused',
    tabName: 'Unused Seals',
  },
  {
    eventKey: 'affixed',
    tabName: 'Affixed Seals',
  },
  {
    eventKey: 'logs',
    tabName: 'Seal Logs',
  },
];
const SealManagement = () => {
  const { vesselList, ga4EventTrigger = () => {} } = useContext(EnvironmentalReportContext);
  const history = useHistory();
  const { ownershipId, tab = 'affixed' } = useParams();
  const [activeTab, setActiveTab] = useState(tab);
  const breadCrumbsItems = useMemo(
    () => [
      {
        title: 'Environmental Reports',
        label: 'To Seal List Page',
        link: `${PARIS2_URL}/vessel/report/environmental/seal`,
      },
      {
        title: `Seal Management • ${
          vesselList.filter((vessel) => `${vessel.id}` === ownershipId)[0]?.value
        }`,
        label: 'Details',
      },
    ],
    [vesselList, ownershipId],
  );

  const handleTabSelect = (key) => {
    if (key !== activeTab) {
      ga4EventTrigger(
        `View ${sealMgmtPageTabData.find((item) => item.eventKey === key)?.tabName} Tab`,
        'Seal Report Detail - Menu',
        sealMgmtPageTabData.find((item) => item.eventKey === key)?.tabName,
      );
      setActiveTab('');
      setActiveTab(key);
    }
    return history.push(`/vessel/report/environmental/seals/${ownershipId}/${key}`);
  };

  const SealManagementButtons = useMemo(() => {
    return (
      <div className="no-print d-flex">
        <ButtonToolbar className="pb-4">
          <Dropdown align={'end'}>
            <Dropdown.Toggle variant="outline-primary" id="dropdown-more">
              More
            </Dropdown.Toggle>
            <Dropdown.Menu>
              <Dropdown.Item
                data-testid="fml-voyage-history-export-excel"
                onClick={() => {
                  ga4EventTrigger(
                    'View Vessel Details',
                    'Seal Report Detail - Menu',
                    vesselList.find((vessel) => `${vessel.id}` === ownershipId)?.value,
                  );
                  history.push(`/vessel/ownership/details/${ownershipId}`);
                }}
              >
                Vessel Details
              </Dropdown.Item>
              <Dropdown.Item
                data-testid="fml-voyage-history-print"
                onClick={() => {
                  ga4EventTrigger(
                    'Print',
                    'Seal Report Detail - Menu',
                    vesselList.find((vessel) => `${vessel.id}` === ownershipId)?.value,
                  );
                  window.print();
                }}
              >
                Print
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </ButtonToolbar>
      </div>
    );
  }, []);

  return (
    <Container>
      <div className="d-flex  justify-content-between">
        <BreadcrumbHeader
          items={breadCrumbsItems}
          activeItem={`Seal Management • ${
            vesselList.find((vessel) => `${vessel.id}` === ownershipId)?.value
          }`}
          onClick={() =>
            ga4EventTrigger(
              'Breadcrumb',
              'Seal Report Detail - Menu',
              vesselList.find((vessel) => `${vessel.id}` === ownershipId)?.value,
            )
          }
        />
        {SealManagementButtons}
      </div>
      <>
        <Row className="no-print tab-wrapper">
          <Col>
            <TabWrapper
              handleTabSelect={handleTabSelect}
              data={sealMgmtPageTabData}
              step={tab}
              setActiveTab={setActiveTab}
              activeTab={activeTab}
            />
          </Col>
        </Row>

        {tab === 'unused' && <UnusedSealList ownershipId={ownershipId} />}
        {tab === 'affixed' && <AffixedSealList ownershipId={ownershipId} />}
        {tab === 'logs' && <SealLogList ownershipId={ownershipId} />}
      </>
    </Container>
  );
};

export default SealManagement;
