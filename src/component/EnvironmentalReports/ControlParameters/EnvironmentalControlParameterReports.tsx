import _ from 'lodash';
import React from 'react';
import { Col, Row, Form } from 'react-bootstrap';
import CustomTable from '../../customComponent/CustomTable';

const EnvironmentalControlParameterReports = ({ columns, controlParameters, title }) => {
  return (
    <div>
      <Row>
        <Form.Label className="technical-reports col-sm-3">
          <b>{title}</b>
        </Form.Label>
      </Row>

      <Row className="no-print">
        <Col>
          <CustomTable column={columns} reportData={[]} tableRef={null} showData={false} />

          {_.keys(controlParameters).map((item, index) => (
            <>
              {!_.isEmpty(controlParameters[item].controlParameter) && (
                <>
                  <div
                    className="filter-heading control-parameter-heading-border"
                    key={'controlParameter' + index}
                  >
                    {controlParameters[item].header}
                  </div>
                  <CustomTable
                    column={columns}
                    className={`control-parameter-table ${
                      _.keys(controlParameters).length === index + 1 &&
                      'control-parameter-table-item'
                    }`}
                    reportData={controlParameters[item].controlParameter}
                    tableRef={null}
                    showHeader={false}
                  />
                </>
              )}
            </>
          ))}
        </Col>
      </Row>
    </div>
  );
};

export default EnvironmentalControlParameterReports;
