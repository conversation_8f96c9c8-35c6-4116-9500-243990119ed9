import _ from 'lodash';
import moment from 'moment';
import { Dash } from '../../../model/utils';
import { formatDate, getHighlightColor } from '../../../util/view-utils';

export const formateStringDate = (currentValue) => {
  if (currentValue?.includes(' ')) {
    const data = currentValue.split(' ');
    return data[0] + ' ' + moment(data[1], 'HHmm').format('HH:mm');
  }
  return Dash;
};

export const GeneralInfoData = (monthlyMarpolReports) => [
  {
    order: 1,
    label: 'Report Submit Date',
    value: formatDate(_.get(monthlyMarpolReports, 'report_date')),
  },
  {
    order: 2,
    label: 'Report Month',
    value: _.get(monthlyMarpolReports, 'month', Dash),
  },
];

export const ResiduesInfoData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Previous Month End ROB',
    value: _.get(monthlyMarpolReports, 'report_json.a.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.a.1', 0),
      parameter,
      'report_json.a.1',
    ),
  },
  {
    order: 2,
    label: 'Total Quantity Incinerated during the Month',
    value: _.get(monthlyMarpolReports, 'report_json.a.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.a.2', 0),
      parameter,
      'report_json.a.2',
    ),
  },
  {
    order: 3,
    label:
      'Quantity of water Evaporated or Drained to Bilge System from Incinerator Waste Oil Tank, if this is recorded in the Oil Record Book under C-12.4',
    value: _.get(monthlyMarpolReports, 'report_json.a.3', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.a.3', 0),
      parameter,
      'report_json.a.3',
    ),
  },
  {
    order: 4,
    label:
      'Total Quantity Disposed by Other Acceptable Means (disposal to residual oil / slop tank, etc.)',
    value: _.get(monthlyMarpolReports, 'report_json.a.4', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.a.4', 0),
      parameter,
      'report_json.a.4',
    ),
  },
  {
    order: 5,
    label: 'Total Qunatity landed Ashore during the Month(with relevant certificate)',
    value: _.get(monthlyMarpolReports, 'report_json.a.5', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.a.5', 0),
      parameter,
      'report_json.a.5',
    ),
  },
  {
    order: 5,
    label: 'Date of Last Landing',
    value: formateStringDate(_.get(monthlyMarpolReports, 'report_json.a.6')),
  },
  {
    order: 6,
    label: 'Port of last Landing',
    value: _.get(monthlyMarpolReports, 'report_json.a.7', Dash),
  },
  {
    order: 7,
    label: 'Total Qunatity Retained Onboard',
    value: _.get(monthlyMarpolReports, 'report_json.a.8', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.a.8', 0),
      parameter,
      'report_json.a.8',
    ),
  },
  {
    order: 8,
    label: 'Calculate Total Sludge Generated in this Month',
    value: _.get(monthlyMarpolReports, 'report_json.a.9', '0.0'),
  },
];

export const BilgeOilyWaterData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Previous Month End ROB',
    value: _.get(monthlyMarpolReports, 'report_json.b.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.b.1', 0),
      parameter,
      'report_json.b.1',
    ),
  },
  {
    order: 2,
    label: 'Total Quantity Pumped out through the Oily Water Seperator during the Month',
    value: _.get(monthlyMarpolReports, 'report_json.b.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.b.2', 0),
      parameter,
      'report_json.b.2',
    ),
  },
  {
    order: 3,
    label: 'Quantity of Water Evaporated from Primary Bilge and Evaporation Tank',
    value: _.get(monthlyMarpolReports, 'report_json.b.3', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.b.3', 0),
      parameter,
      'report_json.b.3',
    ),
  },
  {
    order: 4,
    label:
      'Total Quantity transferred from Bilge Wells to Bilge Tank, Evaporation Tank and Other Tank (such as sludge tank in case of heavy oil contamination)',
    value: _.get(monthlyMarpolReports, 'report_json.b.4', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.b.4', 0),
      parameter,
      'report_json.b.4',
    ),
  },
  {
    order: 5,
    label: 'Quantity Landed Ashore during this Month',
    value: _.get(monthlyMarpolReports, 'report_json.b.5', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.b.5', 0),
      parameter,
      'report_json.b.5',
    ),
  },
  {
    order: 6,
    label: 'Date of Last Landing',
    value: formateStringDate(_.get(monthlyMarpolReports, 'report_json.b.6')),
  },
  {
    order: 7,
    label: 'Port of Last Landing',
    value: _.get(monthlyMarpolReports, 'report_json.b.7', Dash),
  },
  {
    order: 8,
    label: 'Total Quantity Retained Onboard (Bilge tank + Primary Bilge Tank + Evaporation Tank)',
    value: _.get(monthlyMarpolReports, 'report_json.b.8', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.b.8', 0),
      parameter,
      'report_json.b.8',
    ),
  },
  {
    order: 9,
    label: 'Quantity of Water Disposed Through IBTS / Clean Drain Tank',
    value: _.get(monthlyMarpolReports, 'report_json.b.9', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.b.9', 0),
      parameter,
      'report_json.b.9',
    ),
  },
];

export const GarbageData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Previous Month End ROB (Not incl. dry cargo residues)',
    value: _.get(monthlyMarpolReports, 'report_json.c1.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.c1.1', 0),
      parameter,
      'report_json.c1.1',
    ),
  },
  {
    order: 2,
    label: 'Quantity Landed Ashore during this Month (NOT incl. dry cargo residues)',
    value: _.get(monthlyMarpolReports, 'report_json.c1.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.c1.2', 0),
      parameter,
      'report_json.c1.2',
    ),
  },
  {
    order: 3,
    label: 'Date of Last Landing',
    value: formateStringDate(_.get(monthlyMarpolReports, 'report_json.c1.3')),
  },
  {
    order: 4,
    label: 'Port of Last Landing',
    value: _.get(monthlyMarpolReports, 'report_json.c1.4', Dash),
  },
  {
    order: 5,
    label: 'Quantity incinerated During this Month',
    value: _.get(monthlyMarpolReports, 'report_json.c1.5', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.c1.5', 0),
      parameter,
      'report_json.c1.5',
    ),
  },
  {
    order: 6,
    label: 'Total Quantity Retained Onboard',
    value: _.get(monthlyMarpolReports, 'report_json.c1.6', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.c1.6', 0),
      parameter,
      'report_json.c1.6',
    ),
  },
  {
    order: 7,
    label: 'Quantity of Garbage Generated (auto calculation)',
    value: _.get(monthlyMarpolReports, 'report_json.c1.7', '0.0'),
  },
  {
    order: 8,
    label: 'Quantity of Food Waste Discharged to sea',
    value: _.get(monthlyMarpolReports, 'report_json.c1.8', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.c1.8', 0),
      parameter,
      'report_json.c1.8',
    ),
  },
  {
    order: 9,
    label: 'Quanity of Dry Cargo Residues Discharged to Sea (dry bulk ships only)',
    value: _.get(monthlyMarpolReports, 'report_json.c1.9', 0.0),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.c1.9', 0),
      parameter,
      'report_json.c1.9',
    ),
  },
];

export const bottledDrinkingWaterData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'ROB at Month Beginning of Plastic Bottled Drinking Water',
    value: _.get(monthlyMarpolReports, 'report_json.c2.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.c2.1', 0),
      parameter,
      'report_json.c2.1',
    ),
  },
  {
    order: 2,
    label: 'Bottled Water Purchased / Added During the Month',
    value: _.get(monthlyMarpolReports, 'report_json.c2.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.c2.2', 0),
      parameter,
      'report_json.c2.2',
    ),
  },
  {
    order: 3,
    label: 'ROB at Month End of Plastic Bottled Drinking Water',
    value: _.get(monthlyMarpolReports, 'report_json.c2.3', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.c2.3', 0),
      parameter,
      'report_json.c2.3',
    ),
  },
];

export const refrigerantsData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Quantity of Refrigerants Added (charged) to System in the Month',
    value: _.get(monthlyMarpolReports, 'report_json.d.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.d.1', 0),
      parameter,
      'report_json.d.1',
    ),
  },
  {
    order: 2,
    label: 'Quanity of Refrigerants Removed (recovered by vaccum pump) from System in the Month',
    value: _.get(monthlyMarpolReports, 'report_json.d.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.d.2', 0),
      parameter,
      'report_json.d.2',
    ),
  },
  {
    order: 3,
    label: 'Refrigerants Emitted to Atmosphere (auto calculation)',
    value: _.get(monthlyMarpolReports, 'report_json.d.3', '0.0'),
  },
];

export const marpolAnnexSlopOilData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label:
      'Quantity of Annex 1 Slop Oil Discharged to Sea (quantity from ODME printout - ONLY oil; NOT include wash water)',
    value: _.get(monthlyMarpolReports, 'report_json.e.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.e.1', 0),
      parameter,
      'report_json.e.1',
    ),
  },
];

export const graywaterData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Untreated Graywater (if GrayWater discharges directly overboard)',
    value: _.get(monthlyMarpolReports, 'report_json.f.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.f.1', 0),
      parameter,
      'report_json.f.1',
    ),
  },
  {
    order: 2,
    label: 'Treated Graywater (if Graywater discharges into Sewage Treatment Plant)',
    value: _.get(monthlyMarpolReports, 'report_json.f.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.f.2', 0),
      parameter,
      'report_json.f.2',
    ),
  },
];

export const oilyWaterSeparatorData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Coalescer Filters (Min ROB - 1)',
    value: _.get(monthlyMarpolReports, 'report_json.g.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.g.1', 0),
      parameter,
      'report_json.g.1',
    ),
  },
  {
    order: 2,
    label: 'Solenoid Valve (Oil Discharge Valve) (Min ROB 1)',
    value: _.get(monthlyMarpolReports, 'report_json.g.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.g.2', 0),
      parameter,
      'report_json.g.2',
    ),
  },
  {
    order: 3,
    label: 'Oil Level Probe (Min ROB - 1)',
    value: _.get(monthlyMarpolReports, 'report_json.g.3', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.g.3', 0),
      parameter,
      'report_json.g.3',
    ),
  },
];

export const bilgePumpSparesData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Stator (For Screw Type Pumps) (Min ROB - 1) (PC)',
    value: _.get(monthlyMarpolReports, 'report_json.h.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.h.1', 0),
      parameter,
      'report_json.h.1',
    ),
  },
  {
    order: 2,
    label: 'V-Belt (If Applicable) (Min ROB - 1) (Set)',
    value: _.get(monthlyMarpolReports, 'report_json.h.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.h.2', 0),
      parameter,
      'report_json.h.2',
    ),
  },
  {
    order: 3,
    label: 'Bearings (Min ROB - 1) (Set)',
    value: _.get(monthlyMarpolReports, 'report_json.h.3', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.h.3', 0),
      parameter,
      'report_json.h.3',
    ),
  },
  {
    order: 4,
    label: 'Bucket Rings (Reciprocating Pumps Only) (Min ROB 1) (Set)    ',
    value: _.get(monthlyMarpolReports, 'report_json.h.4', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.h.4', 0),
      parameter,
      'report_json.h.4',
    ),
  },
  {
    order: 5,
    label: 'Suction and Delivery Valves (Reciprocating Pumps Only) (Min ROB- 1) (Set)    ',
    value: _.get(monthlyMarpolReports, 'report_json.h.5', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.h.5', 0),
      parameter,
      'report_json.h.5',
    ),
  },
];

export const oilContentMonitorData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Sensor Glass Tube (Min ROB - 1)',
    value: _.get(monthlyMarpolReports, 'report_json.i.1', 0.0),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.i.1', 0),
      parameter,
      'report_json.i.1',
    ),
  },
  {
    order: 2,
    label: 'Cleaning Kit (Min ROB - 1) (Set)',
    value: _.get(monthlyMarpolReports, 'report_json.i.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.i.2', 0),
      parameter,
      'report_json.i.2',
    ),
  },
];

export const incineratorData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Thermocouple (Min ROB - 1) (PC)',
    value: _.get(monthlyMarpolReports, 'report_json.j.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.j.1', 0),
      parameter,
      'report_json.j.1',
    ),
  },
  {
    order: 2,
    label: 'V-belts for Fan (Min ROB - 1) (Set)',
    value: _.get(monthlyMarpolReports, 'report_json.j.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.j.2', 0),
      parameter,
      'report_json.j.2',
    ),
  },
  {
    order: 3,
    label: 'V-belts for Burner (Min ROB - 1) (Set)',
    value: _.get(monthlyMarpolReports, 'report_json.j.3', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.j.3', 0),
      parameter,
      'report_json.j.3',
    ),
  },
  {
    order: 4,
    label: 'Burner Atomiser (Min ROB - 1) (PC)',
    value: _.get(monthlyMarpolReports, 'report_json.j.4', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.j.4', 0),
      parameter,
      'report_json.j.4',
    ),
  },
  {
    order: 5,
    label: 'Diesel Oil Nozzle (Min ROB 1) (PC)',
    value: _.get(monthlyMarpolReports, 'report_json.j.5', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.j.5', 0),
      parameter,
      'report_json.j.5',
    ),
  },
];

export const sludgePumpData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Stator (Min ROB - 1) (PC)',
    value: _.get(monthlyMarpolReports, 'report_json.k.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.k.1', 0),
      parameter,
      'report_json.k.1',
    ),
  },
  {
    order: 2,
    label: 'V-Belt (Min ROB 1) (Set)',
    value: _.get(monthlyMarpolReports, 'report_json.k.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.k.2', 0),
      parameter,
      'report_json.k.2',
    ),
  },
  {
    order: 3,
    label: 'Bearings (Min ROB - 1) (Set)',
    value: _.get(monthlyMarpolReports, 'report_json.k.3', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.k.3', 0),
      parameter,
      'report_json.k.3',
    ),
  },
];

export const sewageTreatmentPlantData = (monthlyMarpolReports, parameter) => [
  {
    order: 1,
    label: 'Chlorinated Tablets (Min ROB - 1) (KG)',
    value: _.get(monthlyMarpolReports, 'report_json.l.1', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.l.1', 0),
      parameter,
      'report_json.l.1',
    ),
  },
  {
    order: 2,
    label: 'V-Belt for Blower (Min ROB - 1) (Set)',
    value: _.get(monthlyMarpolReports, 'report_json.l.2', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.l.2', 0),
      parameter,
      'report_json.l.2',
    ),
  },
  {
    order: 3,
    label: 'Chlorine Testing Kit (Min ROB for 1 Month)',
    value: _.get(monthlyMarpolReports, 'report_json.l.3', '0.0'),
    text_color: getHighlightColor(
      _.get(monthlyMarpolReports, 'report_json.l.3', 0),
      parameter,
      'report_json.l.3',
    ),
  },
];
