import React, { useContext, useRef, useState } from 'react';
import { Col, Form, Row, Tab } from 'react-bootstrap';
import { useDebouncedCallback } from 'use-debounce';
import { EnvironmentalReportContext } from '../../context/EnvironmentalReportContext';
import VesselDropDown from '../TechnicalReports/VesselDropDown';

const SealManagementListFilter = ({ loading }) => {
  const {
    vesselList,
    filterData,
    setFilterData,
    ga4EventTrigger = () => {},
  } = useContext(EnvironmentalReportContext);
  const [selectedVessel, setSelectedVessel] = useState(filterData?.vessel);
  const vesselRef = useRef(null);
  const onInputChange = useDebouncedCallback((value, name) => {
    if (value) {
      ga4EventTrigger('Search Seal Number', 'Seal Report - Filter', value);
    }
    setFilterData({
      ...filterData,
      [name]: value || '',
    });
  }, 700);

  const handleDropdownChange = (event) => {
    setSelectedVessel(event);
    setFilterData({ ...filterData, vessel: event });
  };

  const handleVesselClear = () => {
    vesselRef.current?.clear();
    setSelectedVessel(null);
    setFilterData({ ...filterData, vessel: [] });
  };

  return (
    <Tab.Container>
      <Row className="form-row">
        <Form.Label className="filter-reports">
          <b>Filter Seal Management Reports</b>
        </Form.Label>
      </Row>

      <Row className="form-row">
        <Col className="reports-filter-textField">
          <Form.Group className="form-group">
            <Form.Control type="text" placeholder="Vessel Name" disabled />
          </Form.Group>
        </Col>
        <Col className="reports-filter-textField">
          <Form.Group className={`dropdown ${loading && 'disabled'} form-group`}>
            <VesselDropDown
              onChange={handleDropdownChange}
              dropdownData={vesselList}
              labelKey="value"
              selectedItem={selectedVessel}
              handleClear={handleVesselClear}
              placeholder="All Vessels"
              dataTestId="fml-seal-management-vessel"
            />
          </Form.Group>
        </Col>
        <Form.Group className="reports-filter-textField form-group">
          <Form.Control type="text" placeholder="Seal Number" disabled />
        </Form.Group>
        <Form.Group className="reports-filter-textField form-group">
          <Form.Control
            type="text"
            data-testid="fml-seal-management-seal-number"
            onChange={(e) => onInputChange(e.target.value, 'serial')}
            disabled={loading}
          />
        </Form.Group>
      </Row>
    </Tab.Container>
  );
};

export default SealManagementListFilter;
