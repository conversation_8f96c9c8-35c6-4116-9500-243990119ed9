import React, { useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { Tab, Form, Row, Alert } from 'react-bootstrap';
import CustomDatePicker from '../customComponent/CustomDatePicker';
import moment from 'moment';
import VesselDropDown from '../TechnicalReports/VesselDropDown';
import { EnvironmentalReportContext } from '../../context/EnvironmentalReportContext';
import { EU_BANNER_CHECK } from '../../model/constants';
import vesselService from '../../service/vessel-service';

const DATE_ERROR_TEXT = 'Please provide a valid date';
const getETSEndDatesForInclusion = (startDate) => {
  const dates = [];
  if (!startDate) return dates;
  const day = moment(startDate).get('D');
  const daysInMonth = moment(startDate).daysInMonth();
  for (let index = day; index < daysInMonth; index++) {
    dates.push(moment(startDate).startOf('month').add(index, 'days').toDate());
  }

  const nextMonth = moment(startDate).add(1, 'month');
  let month = nextMonth.get('month');
  const startOfYear = moment(startDate).startOf('year');
  for (month; month < 12; month++) {
    const lastDayOfMonth = moment(startOfYear).add(month, 'month').endOf('month').toDate();
    dates.push(lastDayOfMonth);
  }
  return dates;
};

const checkIfDateIsPartOfAnArray = (date, dates) => {
  return !!dates.find((item) => {
    return item.getTime() == date.getTime();
  });
};

const EuEtsReportsFilter = ({ loading }) => {
  const { vesselList, filterData, setFilterData, bannerInfo, vendorDataSourceOptions, setVendorDataSourceOptions, ga4EventTrigger } =
    useContext(EnvironmentalReportContext);
  const [errors, setErrors] = useState({
    startDate: !filterData.startDate ? DATE_ERROR_TEXT : undefined,
    endDate: !filterData.endDate ? DATE_ERROR_TEXT : undefined,
  });
  const [includeDates, setIncludeDates] = useState(
    getETSEndDatesForInclusion(filterData?.startDate),
  );
  useEffect(() => {
    if (filterData?.startDate && filterData?.endDate) {
      const startDateYear = moment(filterData.startDate).get('year');
      const endDateYear = moment(filterData.endDate).get('year');
      const isValidEndDate = checkIfDateIsPartOfAnArray(
        moment(filterData.endDate).startOf('day').toDate(),
        includeDates,
      );
      if (startDateYear !== endDateYear && !isValidEndDate) {
        setFilterData({
          ...filterData,
          endDate: '',
        });
        setErrors({ ...errors, endDate: DATE_ERROR_TEXT });
      }
    }
  }, [filterData, includeDates]);

  useEffect(() => {
    const fetchVendorDataSourceOptions = async () => {
      const startDate = new Date(filterData.startDate);
      const comparisonYear = new Date('2025-01-01'); // Start of the year 2025
      const in2025orLater = startDate >= comparisonYear;
      const options = {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        };
      if (filterData?.vessel[0]?.id && in2025orLater) {
        try {
          const { data: vendor_data_source_configs } =
            await vesselService.getReportEnabledDatasourceConfigs(filterData?.vessel[0]?.id);
          if (vendor_data_source_configs.length > 0) {
            const vendorDataSources = vendor_data_source_configs.map((item) => {
              const date = new Date(item.last_sync_at);
              const formattedDate = date.toLocaleString('en-GB', options).replace(',', '');
              return {
                value: item.datasource_code,
                name: item.datasource_code,
                isMainSource: item.is_main_source,
                lastSyncAt: formattedDate,
              };
            });

            setVendorDataSourceOptions([
              ...vendorDataSources,
              {
                value: 'PARIS',
                label: 'PARIS',
                isMainSource: false,
              },
            ]);
            // Auto select first option only if no data source is selected
            if (filterData.dataSource.length === 0) {
              setFilterData((prev) => ({
                ...prev,
                dataSource: vendorDataSources.length > 0 ? [vendorDataSources[0]] : [],
              }));
            }

          }

        } catch (error) {
          console.error(error);
        }
      } else {
            setFilterData((prev) => ({
                ...prev,
                dataSource: [],
          }));
      }
    };

    fetchVendorDataSourceOptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterData.vessel, filterData.startDate]);

  const onChangeStartDate = async (date) => {
    const newStartDate = date ? moment(date).format('YYYY-MM-DD') : '';

    setFilterData({
      ...filterData,
      startDate: newStartDate,
      endDate: '',
    });
    if (newStartDate) {
      setIncludeDates(getETSEndDatesForInclusion(newStartDate));
      setErrors({ ...errors, startDate: '', endDate: DATE_ERROR_TEXT });
    } else {
      setErrors({ ...errors, startDate: DATE_ERROR_TEXT });
      setIncludeDates([]);
    }
  };

  const onChangeEndDate = async (date) => {
    const newEndDate = date ? moment(date).format('YYYY-MM-DD') : '';
    setFilterData({
      ...filterData,
      endDate: newEndDate,
    });
    if (newEndDate) {
      setErrors({ ...errors, endDate: '' });
    } else {
      setErrors({ ...errors, endDate: DATE_ERROR_TEXT });
    }
  };

  const handleDataSourceChange = useCallback((event) => {
    setFilterData(prev => ({ ...prev, dataSource: event }));
    ga4EventTrigger(
      'Click',
      'EU-ETS – Data Source',
      event ? event[0].value : 'NAVTOR'
    );
  }, [setFilterData]);

  const handleDataSourceClear = useCallback(() => {
    setFilterData(prev => ({ ...prev, dataSource: [] }));
  }, [setFilterData]);

  const handleDropdownChange = useCallback((event) => {
    setFilterData((prev) => ({ ...prev, vessel: event }));
    if (vendorDataSourceOptions.length === 0) {
      setFilterData((prev) => ({ ...prev, dataSource: [] }));
    }
  }, [setFilterData]);

  const handleVesselClear = useCallback(() => {
    setFilterData(prev => ({ ...prev, vessel: [] }));
  }, [setFilterData]);
  // Memoize expensive computations
  const showEUBannerOrNot = (dateString) => {
    const date = new Date(dateString);
    const comparisonDate = new Date(EU_BANNER_CHECK.BANNER_DATE);
    return ((date >= comparisonDate) && bannerInfo);
  };

  const isStartOfYear = useMemo(() => {
    return filterData.startDate && moment(filterData.startDate).month() === 0;
  }, [filterData.startDate]);

  const isEndDateDisabled = useMemo(() => {
    return loading || !filterData.startDate;
  }, [loading, filterData.startDate]);

  return (
    <>
      <Tab.Container>
        <Row className="form-row">
          <Form.Label className="filter-reports">
            <b>Filter Reports</b>
          </Form.Label>
        </Row>
        {filterData.startDate && showEUBannerOrNot(filterData.startDate) && (
          <Row>
            <Alert
              variant="info"
              id=""
              style={{ width: '100%', marginLeft: '15px', marginRight: '15px' }}
            >
              As of January 1, 2025, Norway, Iceland, and Liechtenstein have not implemented FuelEU
              Maritime and should be considered non-EU entities, thus please exclude from the
              report's FuelEU calculations manually.
            </Alert>
          </Row>
        )}

        <Row className="form-row">
          <Form.Group className="reports-filter-textField form-group">
            <Form.Control type="text" placeholder="Report Submit Date" disabled />
          </Form.Group>

        <Form.Group className="startDatePicker form-group">
          <CustomDatePicker
            value={filterData.startDate}
            onChange={(event) => onChangeStartDate(event)}
            dataTestId="fml-environmental-report-filter-start-date"
            disabled={loading}
          />
          <div className="validate-error">{errors.startDate}</div>
        </Form.Group>
        <Form.Group className=" datePickerRange form-group">To</Form.Group>
        <Form.Group className="endDatePicker form-group z-index-110">
          <CustomDatePicker
            value={filterData.endDate}
            onChange={(event) => onChangeEndDate(event)}
            dataTestId="fml-environmental-report-filter-end-date"
            disabled={isEndDateDisabled}
            customProps={{
              includeDates: includeDates,
            }}
          />
          <div className="validate-error">{errors.endDate}</div>
        </Form.Group>

          <Form.Group className="reports-filter-textField form-group">
            <Form.Control type="text" placeholder="Vessel Name" disabled />
          </Form.Group>
          <Form.Group className={`vessel-dropdown ${loading && 'disabled'} form-group`}>
            <VesselDropDown
              onChange={handleDropdownChange}
              dropdownData={vesselList}
              labelKey="value"
              selectedItem={filterData.vessel}
              handleClear={handleVesselClear}
              placeholder="All Vessels"
              dataTestId="fml-environmental-report-filter-vessel"
            />
          </Form.Group>
          {vendorDataSourceOptions.length > 0 && (
            <>
              <Form.Group className="reports-filter-textField form-group">
                <Form.Control type="text" placeholder="Data Source" disabled />
              </Form.Group>
              <Form.Group className={`vessel-dropdown form-group ${loading && 'disabled'}`}>
                <VesselDropDown
                  onChange={handleDataSourceChange}
                  dropdownData={vendorDataSourceOptions}
                  labelKey="value"
                  selectedItem={filterData.dataSource}
                  handleClear={handleDataSourceClear}
                  placeholder="Data Source"
                  dataTestId="fml-environmental-report-filter-data-source"

                />
              </Form.Group>
            </>
          )}
        </Row>
      </Tab.Container>
      {isStartOfYear ? (
        <span className="proration-note-filter">
          (Note: Fuel consumed, distance traveled, and time spent at sea figures have been pro-rated
          for cross-year voyages in accordance with the reporting period for ETS, effective from
          January 1, 2024, UTC 00:00.)
        </span>
      ) : (
        <></>
      )}
    </>
  );
};

export default EuEtsReportsFilter;
