import React from 'react';
import { Typeahead } from 'react-bootstrap-typeahead';
import PropTypes from 'prop-types';

/**
 * This code reference component paris2-web-seafarer/CountryNationalityDropDownControl
 *
 * change is made to make country dropdown from vessel stored value of country alpha2 code/name
 */
const CountryNationalityDropDownControl = ({
  dropDownValues,
  name,
  onInputChange,
  selectedValue,
  disabled,
  isInvalid,
  placeholder = 'Please select',
  onTypeAheadInputValueChange,
}) => {
  const onChange = (option) => {
    const countryName = option.length > 0 ? option[0].value : undefined;
    const result = { target: { name: name, value: countryName }, targetOption: option };
    onInputChange(result);
  };

  const getOptionByCountryName = (countryName) => {
    const result = (dropDownValues ?? []).filter((item) => {
      return countryName && item.value
        ? item.value.toLowerCase().trim() === countryName.toLowerCase().trim()
        : false;
    });
    return result.length > 0 ? result[0] : undefined;
  };
  const selected = getOptionByCountryName(selectedValue);

  function getFlag(isoCode) {
    return typeof String.fromCodePoint !== 'undefined'
      ? isoCode
          .toUpperCase()
          .replace(/./g, (char) => String.fromCodePoint(char.charCodeAt(0) + 127397))
      : isoCode;
  }

  const getLabel = (option) => {
    const title = option.value ?? option;
    const code = option.alpha2_code;
    if (code) {
      const flag = getFlag(code);
      return `${flag} ${title}`;
    }
    return title;
  };

  const onChangeInputText = (text, event) => {
    onTypeAheadInputValueChange(name, text);
  };

  return (
    <Typeahead
      inputProps={{ 'data-testid': 'fml-editVessel-' + name }}
      id={name}
      labelKey={getLabel}
      onChange={onChange}
      options={dropDownValues}
      placeholder={placeholder}
      selected={selected ? [selected] : []}
      disabled={disabled}
      isInvalid={isInvalid}
      onInputChange={onChangeInputText}
    />
  );
};

CountryNationalityDropDownControl.propTypes = {
  dropDownValues: PropTypes.object,
  name: PropTypes.string,
  onInputChange: PropTypes.func,
  selectedValue: PropTypes.string,
  disabled: PropTypes.bool,
  isInvalid: PropTypes.bool,
  placeholder: PropTypes.string,
  onTypeAheadInputValueChange: PropTypes.func,
};

export { CountryNationalityDropDownControl };
