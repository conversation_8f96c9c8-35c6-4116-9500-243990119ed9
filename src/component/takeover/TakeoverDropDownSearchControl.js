import React from 'react';
import { Typeahead } from 'react-bootstrap-typeahead';
import PropTypes from 'prop-types';

const TakeoverDropDownSearchControl = ({
  dropDownValues,
  name,
  onInputChange,
  selectedValue,
  disabled,
  isInvalid,
  onTypeAheadInputValueChange = () => {},
  onFocus = () => {},
  isLoading = false,
  shouldReturnDropdownValues = false,
}) => {
  const options = (dropDownValues ?? []).map((item) => {
    return item.value ?? item.emission_type;
  });

  const onChange = (value) => {
    const eventData = { target: { name: name, value: getIdOfSelectedOption(value[0]) } };

    onInputChange(eventData, shouldReturnDropdownValues ? dropDownValues : undefined);
  };

  const getIdOfSelectedOption = (value) => {
    const result = getDataOfSelectedOption(value);
    return result.length > 0 ? result[0].id : undefined;
  };

  const getDataOfSelectedOption = (value) => {
    return (dropDownValues ?? []).filter((item) => {
      return (
        (item.value && item.value === value) || (item.emission_type && item.emission_type === value)
      );
    });
  };

  const getOptionById = (id) => {
    const result = (dropDownValues ?? []).filter((item) => {
      return item.id === id;
    });
    return result.length > 0 ? result[0].value || result[0].emission_type : undefined;
  };

  const handleSelected = () => {
    const value = getOptionById(selectedValue);
    return value ? [value] : [];
  };

  const onChangeInputText = (text, event) => {
    onTypeAheadInputValueChange(name, text);
  };

  return (
    <Typeahead
      paginate={true}
      id={name}
      inputProps={{ 'data-testid': 'fml-editVessel-' + name }}
      labelKey={name}
      onChange={onChange}
      options={options.sort()}
      placeholder="Please select"
      selected={handleSelected()}
      disabled={disabled}
      isInvalid={isInvalid}
      onInputChange={onChangeInputText}
      onFocus={onFocus}
      isLoading={isLoading}
    />
  );
};

TakeoverDropDownSearchControl.propTypes = {
  dropDownValues: PropTypes.object,
  name: PropTypes.string,
  onInputChange: PropTypes.func,
  selectedValue: PropTypes.string,
  disabled: PropTypes.bool,
  isInvalid: PropTypes.bool,
  onTypeAheadInputValueChange: PropTypes.func,
  onFocus: PropTypes.func,
  isLoading: PropTypes.bool,
  shouldReturnDropdownValues: PropTypes.bool,
};

export default TakeoverDropDownSearchControl;
