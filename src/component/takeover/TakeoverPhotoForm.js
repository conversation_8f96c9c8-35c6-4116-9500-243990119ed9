import React, { useState, useEffect } from 'react';
import { Form, Figure, Row, Col } from 'react-bootstrap';
import _ from 'lodash';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import NoPhoto from '../../../public/icons/no-photo.svg';
import vesselService from '../../service/vessel-service';
import PhotoUploadModal from '../photoUpload/PhotoUploadModal';
import PropTypes from 'prop-types';

const Result = (props) => {
  const { vessel, onSubmitVesselOnly, onCustomInputChange } = props;
  const { images = [] } = vessel;
  const sortedPhotos = images.slice().sort((a, b) => a.order - b.order);
  const photoPaths = sortedPhotos.map((p) => p.path);
  const captions = sortedPhotos.map((p) => p.caption);

  const [photoUrlMap, setPhotoUrlMap] = useState({});
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [lightboxIsOpen, setLightboxIsOpen] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        const response = await vesselService.requestVesselDownloadUrls(photoPaths);
        if (response.data) {
          setPhotoUrlMap(response.data);
        }
      } catch (error) {}
    })();
  }, []);

  return (
    <div className="take_over_page">
      <Row className="mt-5 mb-3 ml-0">
        <h6>PHOTOS</h6>
      </Row>
      {images.length > 0 ? (
        <Form.Group className="form-group">
          <Row className="ml-0">
            <PhotoUploadModal
              label="Edit / Upload Photos"
              images={images}
              photoUrlMap={photoUrlMap}
              onCustomInputChange={onCustomInputChange}
              setPhotoUrlMap={setPhotoUrlMap}
              onSubmitVesselOnly={onSubmitVesselOnly}
            />
          </Row>
          {_.chunk(sortedPhotos, 6).map((r, rIdx) => (
            <Row data-testid="fml-editVessel-photoContainer" className="mt-4" key={rIdx}>
              {r.map((c, cIdx) => (
                <Col className="d-flex justify-content-center" md={2} key={cIdx}>
                  <div className="text-center">
                    <img
                      style={{ maxWidth: '100%', maxHeight: '140px', cursor: 'pointer' }}
                      src={photoUrlMap[photoPaths[rIdx * 6 + cIdx]] || NoPhoto}
                      onClick={() => {
                        setLightboxIndex(rIdx * 6 + cIdx);
                        setLightboxIsOpen(true);
                      }}
                      aria-hidden="true"
                    />
                    <div className="p-2">{c.caption}</div>
                  </div>
                </Col>
              ))}
            </Row>
          ))}
          {lightboxIsOpen && (
            <Lightbox
              reactModalStyle={{ overlay: { zIndex: 2000 } }}
              mainSrc={photoUrlMap[photoPaths[lightboxIndex]]}
              nextSrc={photoUrlMap[photoPaths[(lightboxIndex + 1) % photoPaths.length]]}
              prevSrc={
                photoUrlMap[photoPaths[(lightboxIndex + photoPaths.length - 1) % photoPaths.length]]
              }
              onCloseRequest={() => {
                setLightboxIsOpen(false);
              }}
              onMovePrevRequest={() => {
                setLightboxIndex((lightboxIndex + photoPaths.length - 1) % photoPaths.length);
              }}
              onMoveNextRequest={() => {
                setLightboxIndex((lightboxIndex + 1) % photoPaths.length);
              }}
              imageCaption={captions[lightboxIndex]}
            />
          )}
        </Form.Group>
      ) : (
        <Form.Group className="form-group">
          <div className="mt-5 d-flex justify-content-center">
            <Figure>
              <Figure.Image className="d-block mx-auto" width={90} height={90} src={NoPhoto} />
              <Figure.Caption className="text-center">Nothing here right now</Figure.Caption>
            </Figure>
          </div>
          <div className="mt-2 d-flex justify-content-center">
            <PhotoUploadModal
              label="Upload Photos"
              images={sortedPhotos}
              photoUrlMap={photoUrlMap}
              onCustomInputChange={onCustomInputChange}
              setPhotoUrlMap={setPhotoUrlMap}
              onSubmitVesselOnly={onSubmitVesselOnly}
            />
          </div>
        </Form.Group>
      )}
    </div>
  );
};

Result.propTypes = {
  vessel: PropTypes.object,
  onSubmitVesselOnly: PropTypes.func,
  onCustomInputChange: PropTypes.func,
};

export default Result;
