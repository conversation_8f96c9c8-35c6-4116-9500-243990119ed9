import React from 'react';
import { Col, Form, InputGroup } from 'react-bootstrap';
import { TRIAL_TYPES } from '../../constants/trial-data';
import PropTypes from 'prop-types';

const ShopTrialInputRow = ({ shopTrialErrors, shopTrial, onChange }) => {
  const handleChange = (event) => onChange(shopTrial.id, event, TRIAL_TYPES.shop_trials);
  return (
    <>
      <Form.Group className="form-group" as={Col} md="3">
        <InputGroup>
          <Form.Control
            placeholder=""
            name="load"
            onChange={handleChange}
            type="text"
            isInvalid={!!shopTrialErrors.load}
            value={isNaN(shopTrial.load) ? '' : shopTrial.load}
          />
          <InputGroup.Text className="unit-of-measure">%</InputGroup.Text>
          {!!shopTrialErrors.load && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              Please provide Load.
            </Form.Control.Feedback>
          )}
        </InputGroup>
      </Form.Group>
      <Form.Group className="form-group" as={Col} md={{ span: 3, offset: 1 }}>
        <Form.Control
          placeholder=""
          isInvalid={!!shopTrialErrors.corrected_sfoc}
          name="corrected_sfoc"
          onChange={handleChange}
          value={isNaN(shopTrial.corrected_sfoc) ? '' : shopTrial.corrected_sfoc}
        />
        {!!shopTrialErrors.corrected_sfoc && (
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            Please provide Corrected SFOC.
          </Form.Control.Feedback>
        )}
      </Form.Group>
    </>
  );
};

ShopTrialInputRow.propTypes = {
  shopTrialErrors: PropTypes.object,
  shopTrial: PropTypes.object,
  onChange: PropTypes.func,
};

export { ShopTrialInputRow };
