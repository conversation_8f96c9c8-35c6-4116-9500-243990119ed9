import React from 'react';
import { Form, Col, Row } from 'react-bootstrap';
import PropTypes from 'prop-types';

const Result = (props) => {
  const { vessel, onInputChange } = props;

  return (
    <Form className={props.isAllApproved ? 'required take_over_page h6' : 'take_over_page h6'}>
      <Row className="mt-5 mb-3 ml-0">
        <h6>OFFICE DATA</h6>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Vessel Short Code</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-vesselShortCode"
            placeholder=""
            name="vessel_short_code"
            onChange={onInputChange}
            value={vessel.vessel_short_code}
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Vessel Tel FAC Code</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-vesselTelFacCode"
            placeholder=""
            name="vessel_tec_fac_code"
            onChange={onInputChange}
            value={vessel.vessel_tec_fac_code}
          />
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Vessel Account Code (OLD)</Form.Label>
          <Form.Control
            disabled
            data-testid="fml-editVessel-vesselAccountCodeOld"
            placeholder=""
            name="vessel_account_code"
            onChange={onInputChange}
            value={vessel.vessel_account_code}
          />
        </Form.Group>
      </Row>
    </Form>
  );
};

Result.propTypes = {
  vessel: PropTypes.object,
  onInputChange: PropTypes.func,
  isAllApproved: PropTypes.bool,
};

export default Result;
