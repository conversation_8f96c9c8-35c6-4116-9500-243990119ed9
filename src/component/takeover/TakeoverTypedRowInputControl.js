import React, { useState } from 'react';
import { Col, Form, Button, Alert, Row } from 'react-bootstrap';
import TakeoverDropDownControl from './TakeoverDropDownControl';
import TakeoverInputArrayControl from './TakeoverInputArrayControl';
import _ from 'lodash';
import PropTypes from 'prop-types';

import styleGuide from '../../styleGuide';
const { Icon } = styleGuide;

const TakeoverTypedRowInputControl = (props) => {
  const {
    arrayId,
    dropDownName,
    textName,
    onInputArrayChange,
    onInputArrayRemoveRow,
    vessel,
    errors,
    options,
    labels,
    alert,
  } = props;

  const [minRow, setMinRow] = useState(1);

  const addNewRow = (event) => {
    const arrayData = vessel && vessel[arrayId] ? vessel[arrayId] : [];
    setMinRow(arrayData.length + 1);
  };

  const RemoveButton = ({ onRemoveItem }) => (
    <Icon icon="remove" size={30} className="remove_icon" onClick={onRemoveItem} />
  );

  return (
    <>
      <Row>
        {labels.map((label) => (
          <Form.Label key={label} className="mb-2" as={Col} md={5}>
            {label}
          </Form.Label>
        ))}
      </Row>
      {alert ? (
        <Row>
          <Alert variant={'primary'}>{alert}</Alert>
        </Row>
      ) : null}
      <TakeoverInputArrayControl
        onInputArrayChange={onInputArrayChange}
        onInputArrayRemoveRow={onInputArrayRemoveRow}
        vessel={vessel}
        arrayId={arrayId}
        minRow={minRow}
        onRow={({ rowId, rowData, onRowInputChange, onRemoveRow }) => {
          const onRemoveItem = (event) => {
            onRemoveRow(event);
            const arrayData = vessel && vessel[arrayId] ? vessel[arrayId] : [];
            setMinRow(arrayData.length);
          };

          const error = errors[arrayId] && errors[arrayId][rowId];
          const dropDownError = error && typeof error === 'object' ? error.dropDown : error;
          const textError = error && typeof error === 'object' ? error.text : error;

          return (
            <Row className="form-row" key={rowId}>
              <Form.Group className="form-group" as={Col} md={5}>
                <TakeoverDropDownControl
                  label=""
                  name={dropDownName}
                  vessel={rowData}
                  onInputChange={onRowInputChange}
                  error={dropDownError}
                  options={_.orderBy(options, 'value', 'asc')}
                />
                <Form.Control.Feedback type="invalid">{dropDownError}</Form.Control.Feedback>
              </Form.Group>
              <Form.Group className="form-group" as={Col} md={5}>
                <Form.Control
                  type="text"
                  name={textName}
                  value={rowData[textName] || ''}
                  onChange={onRowInputChange}
                  isInvalid={!!textError}
                  data-testid={'fml-editVessel-' + textName}
                />
                <Form.Control.Feedback type="invalid">{textError}</Form.Control.Feedback>
              </Form.Group>
              <Form.Group className="form-group" as={Col} md={2}>
                {rowId != 0 ? <RemoveButton onRemoveItem={onRemoveItem} /> : null}
              </Form.Group>
            </Row>
          );
        }}
      />
      <Button data-testid="fml-editVessel-addButton" variant="outline-primary" onClick={addNewRow}>
        Add
      </Button>
    </>
  );
};

TakeoverTypedRowInputControl.propTypes = {
  arrayId: PropTypes.string,
  dropDownName: PropTypes.string,
  textName: PropTypes.string,
  onInputArrayChange: PropTypes.func,
  onInputArrayRemoveRow: PropTypes.func,
  vessel: PropTypes.object,
  errors: PropTypes.object,
  options: PropTypes.object,
  labels: PropTypes.object,
  alert: PropTypes.string,
};

export default TakeoverTypedRowInputControl;
