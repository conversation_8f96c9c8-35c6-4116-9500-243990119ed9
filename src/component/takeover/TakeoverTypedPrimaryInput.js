/* eslint-disable react/prop-types */
import React from 'react';
import { Col, Form, Row } from 'react-bootstrap';
import PropTypes from 'prop-types';

export const TakeOverTypedPrimaryInput = ({
  name,
  value,
  error,
  onInputChange,
  typeId,
  typeLabel,
  header,
}) => {
  return (
    <>
      <Row>
        <Form.Label className="mb-2" as={Col} md={5}>
          {header}
        </Form.Label>
      </Row>
      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={5}>
          <Form.Control as="select" value={typeId} disabled={true}>
            <option value={typeId}>{typeLabel}</option>
          </Form.Control>
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={5}>
          <Form.Control
            type="text"
            name={name}
            value={value}
            onChange={onInputChange}
            isInvalid={!!error}
          />
          <Form.Control.Feedback type="invalid">{error}</Form.Control.Feedback>
        </Form.Group>
      </Row>
    </>
  );
};

TakeOverTypedPrimaryInput.propTypes = {
  name: PropTypes.string,
  value: PropTypes.string,
  error: PropTypes.string,
  onInputChange: PropTypes.func,
  typeId: PropTypes.string,
  typeLabel: PropTypes.string,
  header: PropTypes.string,
};
