import React, { useState, useEffect } from 'react';
import TakeoverDropDownSearchControl from './TakeoverDropDownSearchControl';
import { Form, Col, Row } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import LNG_ENGINE_CATEGORY from '../../constants/lng-type';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import moment from 'moment';
import PropTypes from 'prop-types';

const MiscView = (props) => {
  const { vessel, dropDownData, onInputChange, onDateChange } = props;
  const {
    miscEngines,
    euVerifiers,
    miscRegisteredOwners,
    miscManagers,
    miscQis,
    miscSalvages,
    miscOperators,
    miscClassifications,
    miscClassificationSocietys,
    miscOsros,
    miscMediaResponses,
    miscManagementTypes,
    miscOtherContactss,
    hmUnderwriters = [],
    piClubs = [],
    ihmProviders = [],
  } = dropDownData;

  useEffect(() => {
    if (vessel?.ihm_inspection_date || !vessel.ihm_provider_id) {
      setIhmInspectionDateDisabled(true);
    } else {
      setIhmInspectionDateDisabled(false);
    }

    if (vessel.ihm_provider_id) {
      const ihmProviderNA = ihmProviders.find((p) => p.id === vessel.ihm_provider_id);
      if (ihmProviderNA && ihmProviderNA.value === 'Not Applicable') {
        setIhmInspectionDate(new Date());
        setIhmInspectionDateDisabled(true);
      } else if (vessel.ihm_inspection_date) {
        setIhmInspectionDate(new Date(vessel.ihm_inspection_date));
      }
    } else {
      setIhmInspectionDate(null);
    }
  }, [vessel, ihmProviders]);

  const [ihmInspectionDate, setIhmInspectionDate] = useState(null);
  const [ihmInspectionDateDisabled, setIhmInspectionDateDisabled] = useState(false);

  const onIhmInspectionDateChange = (date) => {
    setIhmInspectionDate(date);
    changeDateForInspectionDate(date, 'ihm_inspection_date');
  };

  function changeDateForInspectionDate(date, key) {
    let dateValue = null;
    if (date) {
      dateValue = moment(date).format('DD MMM YYYY');
    }
    onDateChange(key, dateValue);
  }

  return (
    <Form className={props.isAllApproved ? 'required take_over_page h6' : 'take_over_page h6'}>
      <Row className="mt-5 mb-3 ml-0">
        <h6>MISCELLANEOUS</h6>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Engine</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_engine_id'}
            selectedValue={vessel.misc_engine_id}
            dropDownValues={miscEngines}
            onInputChange={onInputChange}
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>DWT</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-dwt"
            placeholder=""
            name="dwt"
            onChange={onInputChange}
            value={vessel.dwt}
          />
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Power(kW)</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-power"
            placeholder=""
            name="bhp"
            onChange={onInputChange}
            value={vessel.bhp}
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Engine Consumes LNG?</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-isEngineConsumesLNG"
            as="select"
            name="is_engine_consume_lng"
            value={vessel.is_engine_consume_lng?.toString()}
            onChange={onInputChange}
          >
            <option value="">Please select</option>
            <option value="true">Yes</option>
            <option value="false">No</option>
          </Form.Control>
        </Form.Group>

        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>LNG Engine Category (Main Engine)</Form.Label>
          <TakeoverDropDownSearchControl
            name={'lng_engine_category_main_engine'}
            selectedValue={vessel.lng_engine_category_main_engine}
            dropDownValues={LNG_ENGINE_CATEGORY}
            disabled={vessel.is_engine_consume_lng?.toString() != 'true'}
            onInputChange={onInputChange}
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>LNG Engine Category (Diesel Generator)</Form.Label>
          <TakeoverDropDownSearchControl
            name={'lng_engine_category_diesel_generator'}
            selectedValue={vessel.lng_engine_category_diesel_generator}
            dropDownValues={LNG_ENGINE_CATEGORY}
            disabled={vessel.is_engine_consume_lng?.toString() != 'true'}
            onInputChange={onInputChange}
          />
        </Form.Group>
      </Row>

      <hr className="dashed_line" />

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>Registered Owner</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_registered_owner_id'}
            selectedValue={vessel.misc_registered_owner_id}
            dropDownValues={miscRegisteredOwners}
            onInputChange={onInputChange}
            disabled={vessel.status === 'active'}
          />
          {vessel.status === 'active' && (
            <Form.Text className="form-text ">
              To change this field, it needs to be done by Business Team through the{' '}
              {vessel.isOwnershipChangePending ? (
                'change ownership'
              ) : (
                <Link to={`/vessel/ownership/${vessel.id}`} className="change-ownership-link">
                  change ownership
                </Link>
              )}{' '}
              process.
            </Form.Text>
          )}
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Operator</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_operator_id'}
            selectedValue={vessel.misc_operator_id}
            dropDownValues={miscOperators}
            onInputChange={onInputChange}
          />
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>Manager</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_manager_id'}
            selectedValue={vessel.misc_manager_id}
            dropDownValues={miscManagers}
            onInputChange={onInputChange}
          />
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Is Manning Manager</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-isManningManager"
            as="select"
            name="is_manning_manager"
            value={vessel.is_manning_manager}
            onChange={onInputChange}
          >
            <option value="">Please select</option>
            <option>yes</option>
            <option>no</option>
          </Form.Control>
        </Form.Group>
      </Row>

      <hr className="dashed_line" />

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>Classification</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_classification_id'}
            selectedValue={vessel.misc_classification_id}
            dropDownValues={miscClassifications}
            onInputChange={onInputChange}
          />
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Classification Society (Emergency Response Assistance)</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_classification_society_id'}
            selectedValue={vessel.misc_classification_society_id}
            dropDownValues={miscClassificationSocietys}
            onInputChange={onInputChange}
          />
        </Form.Group>
      </Row>
      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>EU Verifier</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_classification_euets_verifier_id'}
            selectedValue={vessel.misc_classification_euets_verifier_id}
            dropDownValues={euVerifiers}
            onInputChange={onInputChange}
            disabled={vessel.misc_classification_euets_verifier}
          />
          <Form.Text className="form-group">
            {vessel.misc_classification_euets_verifier ? (
              <>
                Please contact the EU-ETS team via{' '}
                <a className="change-ownership-link" href="mailto:<EMAIL>">
                  <EMAIL>
                </a>
              </>
            ) : (
              'If selected you cannot change it after clicking Save'
            )}
          </Form.Text>
        </Form.Group>
      </Row>

      <hr className="dashed_line" />

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>QI</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_qi_id'}
            selectedValue={vessel.misc_qi_id}
            dropDownValues={miscQis}
            onInputChange={onInputChange}
          />
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>OSRO</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_osro_id'}
            selectedValue={vessel.misc_osro_id}
            dropDownValues={miscOsros}
            onInputChange={onInputChange}
          />
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>Salvage</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_salvage_id'}
            selectedValue={vessel.misc_salvage_id}
            dropDownValues={miscSalvages}
            onInputChange={onInputChange}
          />
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Media Response</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_media_response_id'}
            selectedValue={vessel.misc_media_response_id}
            dropDownValues={miscMediaResponses}
            onInputChange={onInputChange}
          />
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>Other Contacts</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_other_contacts_id'}
            selectedValue={vessel.misc_other_contacts_id}
            dropDownValues={miscOtherContactss}
            onInputChange={onInputChange}
          />
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Management Type</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_management_type_id'}
            selectedValue={vessel.misc_management_type_id}
            dropDownValues={miscManagementTypes}
            onInputChange={onInputChange}
          />
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>US Visa Required</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-usVisaRequired"
            as="select"
            name="us_visa_required"
            value={vessel.us_visa_required}
            onChange={onInputChange}
          >
            <option value="">Please select</option>
            <option>yes</option>
            <option>no</option>
          </Form.Control>
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>P & I Club</Form.Label>
          <TakeoverDropDownSearchControl
            name={'p_i_club_id'}
            selectedValue={vessel.p_i_club_id}
            dropDownValues={piClubs}
            onInputChange={onInputChange}
          />
        </Form.Group>
      </Row>
      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>H & M Underwriter</Form.Label>
          <TakeoverDropDownSearchControl
            name={'h_m_underwriter_id'}
            selectedValue={vessel.h_m_underwriter_id}
            dropDownValues={hmUnderwriters}
            onInputChange={onInputChange}
          />
        </Form.Group>
      </Row>
      <hr className="dashed_line" />

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>IHM Provider</Form.Label>
          <TakeoverDropDownSearchControl
            name={'ihm_provider_id'}
            selectedValue={vessel.ihm_provider_id}
            dropDownValues={ihmProviders}
            onInputChange={(e) => {
              if (e.target.value === 3) {
                onIhmInspectionDateChange(new Date());
              } else {
                onIhmInspectionDateChange(null);
                setIhmInspectionDateDisabled(false);
              }
              onInputChange(e);
            }}
          />
        </Form.Group>
      </Row>
      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 5 }}>
          <Form.Label>IHM Initial Inspection Date</Form.Label>
          <DatePicker
            name={'ihm_inspection_date'}
            isClearable
            selected={ihmInspectionDate}
            onChange={onIhmInspectionDateChange}
            customInput={<input type="text" />}
            placeholderText="Please select"
            dateFormat="d MMM yyyy"
            disabled={ihmInspectionDateDisabled}
          />
        </Form.Group>
      </Row>
    </Form>
  );
};

MiscView.propTypes = {
  vessel: PropTypes.object,
  dropDownData: PropTypes.object,
  onInputChange: PropTypes.func,
  onDateChange: PropTypes.func,
  ihm_inspection_date: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
  ihm_provider_id: PropTypes.string,
  isAllApproved: PropTypes.boolean,
};

export default MiscView;
