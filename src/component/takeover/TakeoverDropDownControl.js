import React from 'react';
import { Form } from 'react-bootstrap';
import PropTypes from 'prop-types';

const TakeOverDropDownControl = ({ label, name, vessel, onInputChange, error, options }) => {
  let value = vessel[name];

  if (value === undefined) {
    const objectName = name.replace('_id', '');
    const objectValue = vessel[objectName];

    if (objectValue) {
      value = objectValue.id;
    } else {
      value = '';
    }
  }

  return (
    <>
      {label && <Form.Label>{label}</Form.Label>}
      <Form.Control
        data-testid={'fml-editVessel-' + name}
        as="select"
        name={name}
        value={value}
        onChange={onInputChange}
        isInvalid={!!error}
      >
        <option value="">Please Select</option>
        {options.map(({ id, value }) => (
          <option value={id} key={id}>
            {value}
          </option>
        ))}
      </Form.Control>
    </>
  );
};

TakeOverDropDownControl.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string,
  vessel: PropTypes.object,
  onInputChange: PropTypes.func,
  error: PropTypes.object,
  options: PropTypes.object,
};

export default TakeOverDropDownControl;
