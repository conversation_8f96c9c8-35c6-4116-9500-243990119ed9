import React, { useState, useEffect } from 'react';
import { Form, Col, Row } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import TakeoverDropDownSearchControl from './TakeoverDropDownSearchControl';
import { CUSTOM_USER_GROUPS, TECH_GROUP } from '../../model/constants';
import moment from 'moment';
import { Field } from 'formik';
import { IMO_NUMBER_REGEXP } from '../../constants/regexp';
import PropTypes from 'prop-types';

const BasicForm = (props) => {
  const {
    vessel,
    dropDownData,
    errors,
    onInputChange,
    handleBlur,
    onDateChange,
    isAllApproved,
    userMemberships,
    onVesselChange,
  } = props;
  const { owners, vesselServiceStatuss, vesselTypes, techgroups, emissionTypes } = dropDownData;
  const [dateOfDelivery, setDateOfDelivery] = useState(null);
  const [yearOfDelivery, setYearOfDelivery] = useState(null);
  const [dateOfTakeover, setDateOfTakeover] = useState(null);
  const [expectedDateOfTakeover, setExpectedDateOfTakeover] = useState(null);
  const [dateOfHandover, setDateOfHandover] = useState(null);
  const [dateDeliveryType, setDateDeliveryType] = useState('date');
  const [techGroup, setTechGroup] = useState(undefined);
  const IN_SERVICE_STATUS_VALUE = 2;

  useEffect(() => {
    setupDates();
    (async () => {
      try {
        setUserTechGroupData();
      } catch (error) {
        console.error(`keycloak service error while getting superintendents. Error: ${error}`);
      }
    })();
  }, []);

  const setUserTechGroupData = () => {
    const userTechGroup = getUserTechGroup(userMemberships);
    if (userTechGroup) {
      onVesselChange('techgroup', userTechGroup);
    }
    setTechGroup(userTechGroup);
  };

  const getUserTechGroup = (userMemberships) => {
    const userPositionInTechGroup = () => {
      const techGroupMemberShip = userMemberships.find(
        (membership) =>
          membership.departmentGroup === TECH_GROUP &&
          !CUSTOM_USER_GROUPS.includes(membership.positionGroup),
      );
      return techGroupMemberShip ? techGroupMemberShip.positionGroup : undefined;
    };
    if (vessel.id || vessel?.techgroup) return vessel.techgroup;
    else return userMemberships ? userPositionInTechGroup() : undefined;
  };

  const onTechGroupChange = (event) => {
    onInputChange(event);
  };

  function setupDates() {
    const dateString = vessel.date_of_delivery;
    if (dateString) {
      const date = new Date(dateString);
      setDateOfDelivery(date);
      setDateDeliveryType('date');
    }

    const yearSring = vessel.year_of_delivery;
    if (yearSring) {
      const date = new Date(yearSring);
      setYearOfDelivery(date);
      setDateDeliveryType('year');
    }

    const dateString2 = vessel.date_of_takeover;
    if (dateString2) {
      const date = new Date(dateString2);
      setDateOfTakeover(date);
    }

    if (vessel.expected_date_of_takeover) {
      const date = new Date(vessel.expected_date_of_takeover);
      setExpectedDateOfTakeover(date);
    }

    if (vessel.date_of_handover) {
      const date = new Date(vessel.date_of_handover);
      setDateOfHandover(date);
    }
  }

  const onDateOfTakeoverChange = (date) => {
    setDateOfTakeover(date);
    changeDateForKey(date, 'date_of_takeover');
  };

  const onExpectedDateOfTakeoverChange = (date) => {
    setExpectedDateOfTakeover(date);
    changeDateForKey(date, 'expected_date_of_takeover');
  };

  const onDateOfHandoverChange = (date) => {
    setDateOfHandover(date);
    changeDateForKey(date, 'date_of_handover');
  };

  function changeDateForKey(date, key) {
    let dateValue = null;
    if (date) {
      // dateValue = date.toISOString();
      // just extract the date
      // use the same date str ('DD MMM YYYY') format as seafarer to avoid confusion
      dateValue = moment(date).format('DD MMM YYYY');
    }
    onDateChange(key, dateValue);
  }

  const YearDateDeliveryField = (props) => {
    const isYear = dateDeliveryType === 'year';

    const onDateDeliveryTypeChange = (event) => {
      const value = event.target.value;
      setDateDeliveryType(value);
      setDateOfDelivery(null);
      setYearOfDelivery(null);
    };

    const onChange = (date) => {
      if (isYear) {
        setYearOfDelivery(date);
        setDateOfDelivery(null);
        changeDateForKey(date, 'year_of_delivery');
        changeDateForKey(null, 'date_of_delivery');
      } else {
        setYearOfDelivery(null);
        setDateOfDelivery(date);
        changeDateForKey(null, 'year_of_delivery');
        changeDateForKey(date, 'date_of_delivery');
      }
    };

    const DateField = () => {
      return (
        <DatePicker
          isClearable
          selected={dateOfDelivery}
          onChange={onChange}
          placeholderText="Please select"
          customInput={<input data-testid="basic-date-of-delivery" type="text" />}
          dateFormat="d MMM yyyy"
        />
      );
    };

    const YearField = () => {
      return (
        <DatePicker
          isClearable
          showYearPicker
          data-testid="basic-date-of-delivery"
          customInput={<input data-testid="basic-date-of-delivery" type="text" />}
          selected={yearOfDelivery}
          onChange={onChange}
          placeholderText="YYYY"
          dateFormat="yyyy"
        />
      );
    };

    return (
      <Form.Group className="form-group" as={Col} md="3">
        <Form.Label>Year Built/ Date of Delivery</Form.Label>
        <Row>
          <Form.Group className="form-group" as={Col} md={5}>
            <Form.Control
              as="select"
              value={dateDeliveryType}
              name="date_delivery_type"
              onChange={onDateDeliveryTypeChange}
              onBlur={handleBlur}
            >
              <option value="">Select</option>
              <option value="year">Year Built</option>
              <option value="date">Date of Delivery</option>
            </Form.Control>
          </Form.Group>{' '}
          <Form.Group className="form-group" as={Col}>
            {isYear ? <YearField /> : <DateField />}
          </Form.Group>
        </Row>
      </Form.Group>
    );
  };

  const validateImoNumber = (value) => {
    if (value) {
      if (!IMO_NUMBER_REGEXP.test(value)) return 'must allow 7 digits number only';
    } else {
      if (vessel.vessel_service_status_id === 2) return 'Please enter IMO number';
    }
  };

  const isAllApprovedOrInService = () => {
    return isAllApproved || vessel.vessel_service_status_id == IN_SERVICE_STATUS_VALUE;
  };
  return (
    <div className={`take_over_page h6 ${isAllApproved ? 'required' : ''}`}>
      <Row className="mt-5 mb-3 ml-0 form-row">
        <h6>BASIC</h6>
      </Row>
      <Field as="div" name="imo_number" validate={validateImoNumber} />
      <Form.Group className="pb-4 form-group">
        <h6 className="pl-0" as="legend" sm={12}>
          This Vessel is
        </h6>
        <Row className="col-md-5 pl-1 different default-font-weight form-row">
          {vesselServiceStatuss.map(({ id, value }) => (
            <Form.Check
              style={{ fontWeight: '400' }}
              inline
              type="radio"
              label={value}
              value={id}
              name="vessel_service_status_id"
              id={`custom-radio-${id}`}
              key={id}
              checked={id === parseInt(vessel.vessel_service_status_id)}
              onChange={onInputChange}
              disabled={!!vessel.id}
            />
          ))}
        </Row>
      </Form.Group>
      <span> *Required Fields </span>

      <hr className="dashed_line" />

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md={{ span: 3 }}>
          <Form.Label>Vessel Name</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-vesselName"
            type="text"
            name="name"
            value={vessel.name}
            onChange={onInputChange}
            isInvalid={!!errors.name}
          />
          <Form.Control.Feedback type="invalid">Please enter vessel name.</Form.Control.Feedback>
        </Form.Group>

        <Form.Group
          className={`form-group ${isAllApprovedOrInService() ? 'required' : ''}`}
          as={Col}
          md={{ span: 3, offset: 1 }}
        >
          <Form.Label>IMO Number</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-imoNumber"
            placeholder=""
            name="imo_number"
            value={vessel.imo_number}
            onChange={onInputChange}
            disabled={vessel.vessel_service_status_id == IN_SERVICE_STATUS_VALUE && vessel.id}
            isInvalid={!!errors.imo_number}
          />
          <Form.Control.Feedback type="invalid">{errors.imo_number}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group className="required form-group" as={Col} md={{ span: 3, offset: 1 }}>
          <Form.Label>Owners</Form.Label>
          <TakeoverDropDownSearchControl
            name={'owner_id'}
            selectedValue={vessel.owner_id}
            dropDownValues={owners}
            disabled={!!vessel.id}
            onBlur={handleBlur}
            isInvalid={!!errors.owner_id}
            onInputChange={onInputChange}
          />
          <Form.Control.Feedback type="invalid">Please select owners.</Form.Control.Feedback>
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="required form-group" as={Col} md="3">
          <Form.Label>Vessel Type</Form.Label>
          <TakeoverDropDownSearchControl
            name={'vessel_type_id'}
            selectedValue={vessel.vessel_type_id}
            dropDownValues={vesselTypes}
            disabled={!!vessel.id}
            onBlur={handleBlur}
            isInvalid={!!errors.vessel_type_id}
            onInputChange={onInputChange}
          />
          <Form.Control.Feedback type="invalid">Please select vessel type.</Form.Control.Feedback>
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 3, offset: 1 }}>
          <Form.Label>Emission Type</Form.Label>
          <TakeoverDropDownSearchControl
            name="emission_type_id"
            selectedValue={vessel.emission_type_id}
            dropDownValues={emissionTypes}
            onBlur={handleBlur}
            isInvalid={!!errors.emission_type_id}
            onInputChange={onInputChange}
          />
          <Form.Control.Feedback type="invalid">Please select emission type.</Form.Control.Feedback>
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 3, offset: 1 }}>
          <Form.Label>Shipyard</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-shipyard"
            type="text"
            name="shipyard_text"
            value={vessel ? vessel.shipyard_text : ''}
            onChange={onInputChange}
            isInvalid={!!errors.shipyard_text}
          />
        </Form.Group>
      </Row>
      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="3">
          <Form.Label>Vessel Hull Number</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-vesselHullNumber"
            type="text"
            name="vessel_hull_number"
            value={vessel.vessel_hull_number ? vessel.vessel_hull_number : ''}
            onChange={onInputChange}
            isInvalid={!!errors.vessel_hull_number}
          />
          <Form.Control.Feedback type="invalid">Please select Hull number.</Form.Control.Feedback>
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 3, offset: 1 }}>
          <Form.Label>Expected Date of Takeover</Form.Label>
          <DatePicker
            data-testid="basic-expected-date-of-takeover"
            isClearable
            selected={expectedDateOfTakeover}
            onChange={onExpectedDateOfTakeoverChange}
            customInput={<input data-testid="basic-expected-date-of-takeover" type="text" />}
            placeholderText="Please select"
            dateFormat="d MMM yyyy"
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 3, offset: 1 }}>
          <Form.Label>Date of Takeover</Form.Label>
          <DatePicker
            isClearable={!vessel || !vessel.id} // only clearable when the user is creating a new vessel, after it's been set on an existing vessel, it cannot be cleared
            selected={dateOfTakeover}
            onChange={onDateOfTakeoverChange}
            data-testid="basic-date-of-takeover"
            customInput={<input data-testid="basic-date-of-takeover" type="text" />}
            placeholderText="Please select"
            dateFormat="d MMM yyyy"
            disabled={true}
            maxDate={new Date()}
          />
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="3">
          <Form.Label>Date of Handover</Form.Label>
          <DatePicker
            isClearable={!vessel || !vessel.id} // only clearable when the user is creating a new vessel, after it's been set on an existing vessel, it cannot be cleared
            selected={dateOfHandover}
            onChange={onDateOfHandoverChange}
            placeholderText="Please select"
            data-testid="basic-date-of-handover"
            customInput={<input data-testid="basic-date-of-handover" type="text" />}
            dateFormat="d MMM yyyy"
            disabled={true}
            maxDate={new Date()}
          />
        </Form.Group>
        {!vessel.id && (
          <Form.Group as={Col} className="required form-group" md={{ span: 3, offset: 1 }}>
            <Form.Label>Tech Group</Form.Label>
            <Form.Control
              as="select"
              value={techGroup}
              name="techgroup"
              onChange={(event) => onTechGroupChange(event)}
              onBlur={handleBlur}
              data-testid="techgroup-dropdown"
              isInvalid={!!errors.techgroup}
            >
              <option value="">Please select</option>
              {techgroups.map(({ id, value }) => (
                <option value={id} key={id}>
                  {value}
                </option>
              ))}
            </Form.Control>
            <Form.Control.Feedback type="invalid">Please select Tech Group</Form.Control.Feedback>
          </Form.Group>
        )}
      </Row>

      <Row className="form-row">
        <YearDateDeliveryField />
      </Row>
    </div>
  );
};

BasicForm.propTypes = {
  vessel: PropTypes.object,
  errors: PropTypes.object,
  dropDownData: PropTypes.object,
  onInputChange: PropTypes.func,
  handleBlur: PropTypes.func,
  onDateChange: PropTypes.func,
  isAllApproved: PropTypes.bool,
  userMemberships: PropTypes.object,
  onVesselChange: PropTypes.func,
};

export default BasicForm;
