import moment from 'moment';
import React, { useCallback, useMemo, useState } from 'react';
import { Form, Col, InputGroup, Button, Row } from 'react-bootstrap';
import { TRIAL_TYPES } from '../../constants/trial-data';
import { Icon } from '../../styleGuide';
import { buildSeaTrialDataByDate } from '../../util/trial-data-utils';
import { FormLabelHeader, FormTrialSectionHeader } from '../trial-data/FormLabelHeader';
import { ArchivedSeaTrial } from './ArchivedSeaTrial';
import { SeaTrialRowInput } from './SeaTrial';
import { ShopTrialInputRow } from './ShopTrial';
import './styles/takeover-trial.scss';
import PropTypes from 'prop-types';

const isSeaTrialCreatedBeforeThisDay = (seaTrials) => {
  return seaTrials.length > 0 && moment(seaTrials[0].created_at).isBefore(Date.UTC(), 'day');
};

const TrialAddButton = ({ onClick, trialType }) => {
  const handleClick = () => onClick(trialType);
  return (
    <Button
      variant="outline-primary"
      className="mt-3"
      style={{ minWidth: '8.5rem' }}
      onClick={handleClick}
    >
      Add
    </Button>
  );
};

TrialAddButton.propTypes = {
  onClick: PropTypes.func,
  trialType: PropTypes.string,
};

const TakeoverTrialForm = (props) => {
  const {
    vessel,
    onInputChange,
    onTrialRowInput,
    onAddMoreTrial,
    onRemoveTrial,
    errors,
    onAddSeaTrialSection,
    onUndoSeaTrialAddSection,
  } = props;
  const shopTrials = vessel.shop_trials ?? [];
  const seaTrials = vessel.sea_trials ?? [];
  const [seaTrialsToArchive, setSeaTrialsToArchive] = useState([]);

  const handleAddMore = useCallback(
    (trialType) => {
      const trialData = vessel[trialType] ?? [];
      const lastRowTrial = trialData[trialData.length - 1];
      if (Object.keys(lastRowTrial).length === 1) return;
      onAddMoreTrial(trialType);
    },
    [onAddMoreTrial, vessel],
  );

  const hasSeaTrialHistory = seaTrials.some((seaTrial) => seaTrial.created_at !== null);
  const activeSeaTrial = seaTrials.filter((seaTrial) => seaTrial.created_at !== null);
  const vesselID = vessel.id ?? null;
  const isEditingTrial = hasSeaTrialHistory && vesselID;

  const handleAddNewSetOfSeaTrial = useCallback(() => {
    setSeaTrialsToArchive(activeSeaTrial);
    onAddSeaTrialSection();
  }, [seaTrials, setSeaTrialsToArchive]);

  const handleCancelNewSetTrial = useCallback(() => {
    onUndoSeaTrialAddSection(activeSeaTrial);
    setSeaTrialsToArchive([]);
  }, [setSeaTrialsToArchive]);

  const seaTrialArchivesFormatted = useMemo(
    () => buildSeaTrialDataByDate(seaTrialsToArchive),
    [seaTrialsToArchive],
  );
  console.log('seaTrialArchivesFormatted:', seaTrialArchivesFormatted);

  return (
    <div
      className={`${props.isAllApproved ? 'required take_over_page h6' : 'take_over_page h6'} mt-5`}
    >
      <span> *Required Fields </span>

      <hr className="dashed_line" />
      <FormTrialSectionHeader title="Maximum continuous rating (MCR) of engine" />
      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="3">
          <Form.Label>MCR KW*</Form.Label>
          <InputGroup>
            <Form.Control
              placeholder=""
              name="maximum_continuous_rating_kw"
              onChange={onInputChange}
              type="text"
              isInvalid={!!errors.maximum_continuous_rating_kw}
              value={
                isNaN(vessel.maximum_continuous_rating_kw)
                  ? ''
                  : vessel.maximum_continuous_rating_kw
              }
            />
            <InputGroup.Text className="unit-of-measure">kw</InputGroup.Text>
            {!!errors.maximum_continuous_rating_kw && (
              <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
                Please provide Maximum Continuous Rating KW
              </Form.Control.Feedback>
            )}
          </InputGroup>
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 3, offset: 1 }}>
          <Form.Label>MCR RPM*</Form.Label>
          <InputGroup>
            <Form.Control
              placeholder=""
              name="maximum_continuous_rating_rpm"
              onChange={onInputChange}
              isInvalid={!!errors.maximum_continuous_rating_rpm}
              value={
                isNaN(vessel.maximum_continuous_rating_rpm)
                  ? ''
                  : vessel.maximum_continuous_rating_rpm
              }
            />
            <InputGroup.Text className="unit-of-measure">rpm</InputGroup.Text>
            {!!errors.maximum_continuous_rating_rpm && (
              <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
                Please provide Maximum Continuous Rating RPM
              </Form.Control.Feedback>
            )}
          </InputGroup>
        </Form.Group>
      </Row>

      <hr className="dashed_line" />
      <FormTrialSectionHeader title="Shop Trial Data" />
      <FormLabelHeader
        gridOption={{ span: 3, offset: 1 }}
        labels={['% Load*', 'Corrected SFOC*']}
      />

      {shopTrials.map((shopTrial, index) => {
        let shopTrialErrors = {};
        if (errors && errors.shop_trials && errors.shop_trials.length) {
          shopTrialErrors = errors.shop_trials[index] ?? {};
        }
        const onRemove = () => onRemoveTrial(shopTrial.id, TRIAL_TYPES.shop_trials);
        return (
          <Row className="form-row" key={shopTrial.id}>
            <Col md="1" className="flex-center">
              <h6 style={{ color: '#1F4A70' }}>{index + 1}</h6>
            </Col>
            <ShopTrialInputRow
              key={shopTrial.id}
              shopTrial={shopTrial}
              onChange={onTrialRowInput}
              shopTrialErrors={shopTrialErrors}
            />
            {index !== 0 && (
              <Col md="1" className="remove_icon">
                <Icon icon="remove" size={29.5} onClick={onRemove} />
              </Col>
            )}
          </Row>
        );
      })}
      <TrialAddButton onClick={handleAddMore} trialType={TRIAL_TYPES.shop_trials} />

      <hr className="dashed_line" />
      <FormTrialSectionHeader title="Sea Trial Data" />
      {isEditingTrial && isSeaTrialCreatedBeforeThisDay(activeSeaTrial) && (
        <Row className="mb-4 ml-0" style={{ alignItems: 'center' }}>
          {seaTrialsToArchive.length === 0 && (
            <Col md="2">
              <h6 style={{ textTransform: 'uppercase', color: '#1F4A70' }}>
                (Created on {moment(activeSeaTrial[0].created_at).format('DD MMM YYYY')})
              </h6>
            </Col>
          )}
          <Col md="2">
            {seaTrialsToArchive.length ? (
              <Button
                variant="outline-primary"
                style={{ minWidth: '5.5rem' }}
                onClick={handleCancelNewSetTrial}
              >
                Cancel
              </Button>
            ) : (
              <Button
                variant="secondary"
                style={{ minWidth: '8.5rem' }}
                onClick={handleAddNewSetOfSeaTrial}
              >
                Add Another Sea Trial Data Set
              </Button>
            )}
          </Col>
        </Row>
      )}
      <FormLabelHeader
        extraCol={<Col md="1" />}
        labels={['% Load*', 'Corrected SFOC*', 'KW*', 'RPM*', 'Speed*', 'Tons/Hr*']}
      />

      {seaTrials.map((seaTrial, index) => {
        let seaTrialErrors = {};
        if (errors && errors.sea_trials && errors.sea_trials.length) {
          seaTrialErrors = errors.sea_trials[index] ?? {};
        }
        const onRemove = () => onRemoveTrial(seaTrial.id, TRIAL_TYPES.sea_trials);
        return (
          <Row className="form-row" key={seaTrial.id}>
            <Col md="1" className="flex-center">
              <h6 style={{ color: '#1F4A70' }}>{index + 1}</h6>
            </Col>
            <SeaTrialRowInput
              seaTrial={seaTrial}
              seaTrialErrors={seaTrialErrors}
              onChange={onTrialRowInput}
            />
            <Col md="1" className="remove_icon">
              {index !== 0 && <Icon icon="remove" size={29.5} onClick={onRemove} />}
            </Col>
          </Row>
        );
      })}
      <TrialAddButton onClick={handleAddMore} trialType={TRIAL_TYPES.sea_trials} />
      {Object.entries(seaTrialArchivesFormatted).map(([key, value]) => {
        return (
          <div key={key}>
            <hr className="dashed_line" />
            <FormTrialSectionHeader title={`(CREATED ON ${key})`} />
            <FormLabelHeader
              extraCol={<Col md="1" />}
              labels={['% Load*', 'Corrected SFOC*', 'KW*', 'RPM*', 'Speed*', 'Tons/Hr*']}
            />
            {value.map((seaTrial, index) => {
              return (
                <Row key={seaTrial.id}>
                  <Col md="1" className="flex-center">
                    <h6 style={{ color: '#1F4A70' }}>{index + 1}</h6>
                  </Col>
                  <ArchivedSeaTrial seaTrial={seaTrial} />
                </Row>
              );
            })}
          </div>
        );
      })}
    </div>
  );
};

TakeoverTrialForm.propTypes = {
  vessel: PropTypes.array,
  onInputChange: PropTypes.func,
  onTrialRowInput: PropTypes.func,
  onAddMoreTrial: PropTypes.func,
  onRemoveTrial: PropTypes.func,
  errors: PropTypes.func,
  onAddSeaTrialSection: PropTypes.func,
  onUndoSeaTrialAddSection: PropTypes.func,
  isAllApproved: PropTypes.boolean,
};

export default TakeoverTrialForm;
