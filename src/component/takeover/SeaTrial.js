import React from 'react';
import { Col, Form, InputGroup } from 'react-bootstrap';
import { TRIAL_TYPES } from '../../constants/trial-data';
import PropTypes from 'prop-types';

const SeaTrialRowInput = ({ seaTrial, seaTrialErrors, onChange }) => {
  const handleChange = (event) => onChange(seaTrial.id, event, TRIAL_TYPES.sea_trials);
  return (
    <>
      <Form.Group className="form-group" as={Col}>
        <InputGroup>
          <Form.Control
            placeholder=""
            name="load"
            isInvalid={!!seaTrialErrors.load}
            onChange={handleChange}
            type="text"
            value={isNaN(seaTrial.load) ? '' : seaTrial.load}
          />
          <InputGroup.Text className="unit-of-measure">%</InputGroup.Text>
          {!!seaTrialErrors.load && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              Please provide Load
            </Form.Control.Feedback>
          )}
        </InputGroup>
      </Form.Group>
      <Form.Group className="form-group" as={Col}>
        <Form.Control
          placeholder=""
          name="corrected_sfoc"
          isInvalid={!!seaTrialErrors.corrected_sfoc}
          onChange={handleChange}
          value={isNaN(seaTrial.corrected_sfoc) ? '' : seaTrial.corrected_sfoc}
        />
        {!!seaTrialErrors.corrected_sfoc && (
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            Please provide Corrected SFOC
          </Form.Control.Feedback>
        )}
      </Form.Group>
      <Form.Group className="form-group" as={Col}>
        <Form.Control
          placeholder=""
          name="kw"
          isInvalid={!!seaTrialErrors.kw}
          onChange={handleChange}
          value={isNaN(seaTrial.kw) ? '' : seaTrial.kw}
        />
        {!!seaTrialErrors.kw && (
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            Please provide Kilowatt
          </Form.Control.Feedback>
        )}
      </Form.Group>
      <Form.Group className="form-group" as={Col}>
        <Form.Control
          placeholder=""
          name="rpm"
          isInvalid={!!seaTrialErrors.rpm}
          onChange={handleChange}
          value={isNaN(seaTrial.rpm) ? '' : seaTrial.rpm}
        />
        {!!seaTrialErrors.rpm && (
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            Please provide RPM
          </Form.Control.Feedback>
        )}
      </Form.Group>
      <Form.Group className="form-group" as={Col}>
        <Form.Control
          placeholder=""
          name="speed"
          isInvalid={!!seaTrialErrors.speed}
          onChange={handleChange}
          value={isNaN(seaTrial.speed) ? '' : seaTrial.speed}
        />
        {!!seaTrialErrors.speed && (
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            Please provide Speed
          </Form.Control.Feedback>
        )}
      </Form.Group>
      <Form.Group className="form-group" as={Col}>
        <Form.Control
          placeholder=""
          name="tons_per_hour"
          isInvalid={!!seaTrialErrors.tons_per_hour}
          onChange={handleChange}
          value={isNaN(seaTrial.tons_per_hour) ? '' : seaTrial.tons_per_hour}
        />
        {!!seaTrialErrors.tons_per_hour && (
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            Please provide Tons Per Hour
          </Form.Control.Feedback>
        )}
      </Form.Group>
    </>
  );
};

SeaTrialRowInput.propTypes = {
  seaTrial: PropTypes.object,
  seaTrialErrors: PropTypes.object,
  onChange: PropTypes.func,
};

export { SeaTrialRowInput };
