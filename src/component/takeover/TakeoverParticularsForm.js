import React from 'react';
import { Form, Col, InputGroup, Row } from 'react-bootstrap';
import TakeoverTypedRowInputControl from './TakeoverTypedRowInputControl';
import TakeoverDropDownSearchControl from './TakeoverDropDownSearchControl';
import { Field } from 'formik';
import PropTypes from 'prop-types';

function validateNotation(value) {
  if (!value) return null;

  const errors = value.map(({ notation, vessel_class_id }) => {
    if (notation && !vessel_class_id) {
      return {
        dropDown: 'Please select Class',
        message: 'Please select Class for Notation',
      };
    }
    if (vessel_class_id && !notation) {
      return {
        text: 'Please enter Notation',
        message: 'Please enter Notation for Class',
      };
    }
    return null;
  });
  return errors.filter((e) => e).length === 0 ? null : errors;
}

const Result = (props) => {
  const { vessel, onInputChange, onInputArrayChange, onInputArrayRemoveRow, errors, dropDownData } =
    props;

  const { vesselClasss = [], miscFlagIspss } = dropDownData;
  return (
    <Form className={props.isAllApproved ? 'required take_over_page h6' : 'take_over_page h6'}>
      <Row className="mt-5 mb-3 ml-0">
        <h6>PARTICULARS</h6>
      </Row>

      <Row className="form-row">
        <Field as="div" name="class_notations" validate={validateNotation} />
        <Form.Group className="form-group" as={Col} md="5">
          <TakeoverTypedRowInputControl
            labels={['Class', 'Notation']}
            arrayId="class_notations"
            dropDownName="vessel_class_id"
            textName="notation"
            onInputArrayChange={onInputArrayChange}
            onInputArrayRemoveRow={onInputArrayRemoveRow}
            vessel={vessel}
            errors={errors}
            options={vesselClasss}
          />
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Flag (ISPS)</Form.Label>
          <TakeoverDropDownSearchControl
            name={'flag_isps_id'}
            selectedValue={vessel.flag_isps_id}
            dropDownValues={miscFlagIspss}
            onInputChange={onInputChange}
          />
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Life Boat Capacity</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-liftBoatCapacity"
            placeholder=""
            name="life_boat_capacity"
            onChange={onInputChange}
            type="text"
            value={isNaN(vessel.life_boat_capacity) ? '' : vessel.life_boat_capacity}
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Length O.A.</Form.Label>
          <InputGroup>
            <Form.Control
              data-testid="fml-editVessel-lengthQA"
              placeholder=""
              name="length_oa"
              onChange={onInputChange}
              value={isNaN(vessel.length_oa) ? '' : vessel.length_oa}
            />
            <InputGroup.Text className="unit-of-measure">m</InputGroup.Text>
          </InputGroup>
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Length B.P.</Form.Label>
          <InputGroup>
            <Form.Control
              data-testid="fml-editVessel-lengthBP"
              placeholder=""
              name="length_bp"
              onChange={onInputChange}
              value={isNaN(vessel.length_bp) ? '' : vessel.length_bp}
            />
            <InputGroup.Text className="unit-of-measure">m</InputGroup.Text>
          </InputGroup>
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Depth</Form.Label>
          <InputGroup>
            <Form.Control
              data-testid="fml-editVessel-depth"
              placeholder=""
              name="depth"
              onChange={onInputChange}
              value={isNaN(vessel.depth) ? '' : vessel.depth}
            />
            <InputGroup.Text className="unit-of-measure">m</InputGroup.Text>
          </InputGroup>
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Breadth (Extreme)</Form.Label>
          <InputGroup>
            <Form.Control
              data-testid="fml-editVessel-breadth"
              placeholder=""
              name="breadth_extreme"
              onChange={onInputChange}
              value={isNaN(vessel.breadth_extreme) ? '' : vessel.breadth_extreme}
            />
            <InputGroup.Text className="unit-of-measure">m</InputGroup.Text>
          </InputGroup>
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Summer Draft</Form.Label>
          <InputGroup>
            <Form.Control
              data-testid="fml-editVessel-summerDraft"
              placeholder=""
              name="summer_draft"
              onChange={onInputChange}
              value={isNaN(vessel.summer_draft) ? '' : vessel.summer_draft}
            />
            <InputGroup.Text className="unit-of-measure">m</InputGroup.Text>
          </InputGroup>
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Summer DWT</Form.Label>
          <InputGroup>
            <Form.Control
              data-testid="fml-editVessel-summerDWT"
              placeholder=""
              name="summer_dwt"
              onChange={onInputChange}
              value={isNaN(vessel.summer_dwt) ? '' : vessel.summer_dwt}
            />
            <InputGroup.Text className="unit-of-measure">mt</InputGroup.Text>
          </InputGroup>
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>International GRT</Form.Label>
          <InputGroup>
            <Form.Control
              data-testid="fml-editVessel-internationalGRT"
              placeholder=""
              name="international_grt"
              onChange={onInputChange}
              value={isNaN(vessel.international_grt) ? '' : vessel.international_grt}
            />
            <InputGroup.Text className="unit-of-measure">mt</InputGroup.Text>
          </InputGroup>
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>International NRT</Form.Label>
          <InputGroup>
            <Form.Control
              data-testid="fml-editVessel-internationalNRT"
              placeholder=""
              name="international_nrt"
              onChange={onInputChange}
              value={isNaN(vessel.international_nrt) ? '' : vessel.international_nrt}
            />
            <InputGroup.Text className="unit-of-measure">mt</InputGroup.Text>
          </InputGroup>
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Service Speed</Form.Label>
          <InputGroup>
            <Form.Control
              data-testid="fml-editVessel-serviceSpeed"
              placeholder=""
              name="service_speed"
              onChange={onInputChange}
              value={isNaN(vessel.service_speed) ? '' : vessel.service_speed}
            />
            <InputGroup.Text className="unit-of-measure">knots</InputGroup.Text>
          </InputGroup>
        </Form.Group>
      </Row>
    </Form>
  );
};

Result.propTypes = {
  vessel: PropTypes.object,
  onInputChange: PropTypes.func,
  onInputArrayChange: PropTypes.func,
  onInputArrayRemoveRow: PropTypes.func,
  errors: PropTypes.object,
  dropDownData: PropTypes.object,
  isAllApproved: PropTypes.bool,
};

export default Result;
