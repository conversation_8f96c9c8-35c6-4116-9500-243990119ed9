import React from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';

const Result = ({ link, title, isActive, vesselId, ownershipId, onValidateLink, eventTracker }) => {
  const classStyle = isActive ? 'active' : '';
  const onClick = (event) => {
    eventTracker('tabNavigation', title);
    if (!onValidateLink(link)) {
      event.preventDefault();
    }
  };

  return (
    <Link
      data-testid="fml-editVessel-navigation"
      to={
        vesselId
          ? `/vessel/${vesselId}/${ownershipId}/takeover/${link}`
          : `/vessel/takeover/${link}`
      }
      onClick={onClick}
    >
      <div className={`tab_navigation__title ${classStyle}`}>{title}</div>
      <div className={`tab_navigation__progress_line ${classStyle}`}></div>
      <div className={`tab_navigation__progress_dot ${classStyle}`}></div>
    </Link>
  );
};

Result.propTypes = {
  link: PropTypes.string,
  title: PropTypes.string,
  isActive: PropTypes.string,
  vesselId: PropTypes.string,
  ownershipId: PropTypes.string,
  onValidateLink: PropTypes.func,
  eventTracker: PropTypes.func,
};

export default Result;
