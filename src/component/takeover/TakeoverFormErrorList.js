import React from 'react';
import { Alert } from 'react-bootstrap';
import PropTypes from 'prop-types';

const ERROR_DESCRIPTIONS = {
  owner_id: 'Please select Owners',
  vessel_type_id: 'Please select Vessel Type',
  techgroup: 'Please select Tech Group',
  // Trial related fields
  maximum_continuous_rating_kw: 'Please provide Maximum Continuous Rating KW',
  maximum_continuous_rating_rpm: 'Please provide Maximum Continuous Rating RPM',
  shop_trials: 'Please complete all fields for Shop Trial',
  sea_trials: 'Please complete all fields for Sea Trial',
  flag_id: 'Please select the valid option for Flag-Office',
  flag_country: 'Please select the valid option for Flag-Country',
  wages_treatment: 'Please enter Wages Treatment',
  has_portage_bill: 'Please enter Portage bill Module',
  lng_engine_category_diesel_generator: 'Please select LNG Engine Category',
  lng_engine_category_main_engine: 'Please select LNG Engine Category',
};

const fieldLabels = {
  dwt: 'DWT',
  bhp: 'Power(kW)',
  vessel_account_code: 'Vessel Account Code',
  vessel_account_code_new: 'Vessel Account Code New',
};

const TakeoverErrorsList = ({ errors }) => {
  if (!errors || Object.keys(errors).length === 0) {
    return null;
  }

  let allErrors = getErrorsList(errors);

  if (allErrors.length === 0) {
    return null;
  }

  function getCleanedArray(array) {
    if (array === undefined) {
      return [];
    }
    return array.filter(function (element) {
      return element != null;
    });
  }

  function getErrorsList(dictionary) {
    if (
      Object.hasOwn(dictionary, 'lng_engine_category_main_engine') &&
      Object.hasOwn(dictionary, 'lng_engine_category_diesel_generator')
    ) {
      delete dictionary['lng_engine_category_main_engine'];
    }
    let allErrors = Object.keys(dictionary).map((key) => {
      let error = dictionary[key];
      if (Array.isArray(error) && error.length > 0) {
        error = error.find((e) => e);
      }

      let errorMessage = error && typeof error === 'object' ? error.message : error;

      if (errorMessage && errorMessage.includes('must be a `number` type')) {
        errorMessage = `${fieldLabels[key]} must be a number`;
      }

      if (ERROR_DESCRIPTIONS[key]) {
        errorMessage = ERROR_DESCRIPTIONS[key];
      }
      return errorMessage;
    });
    allErrors = getCleanedArray(allErrors);
    return allErrors;
  }

  return (
    <Alert className="takeover_error_list" variant="danger">
      <Alert.Heading>Correct the following:</Alert.Heading>
      <ul>
        {allErrors.map((error) => {
          return <li key={error}>{error}</li>;
        })}
      </ul>
    </Alert>
  );
};

TakeoverErrorsList.propTypes = {
  errors: PropTypes.object,
};

export default TakeoverErrorsList;
