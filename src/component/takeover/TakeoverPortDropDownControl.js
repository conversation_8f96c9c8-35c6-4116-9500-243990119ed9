/* eslint-disable react/prop-types */
import React from 'react';
import { Typeahead } from 'react-bootstrap-typeahead';
import PropTypes from 'prop-types';

const TakeoverPortDropDownControl = ({
  dropDownValues,
  name,
  labelKey,
  onInputChange,
  selectedValue,
  disabled,
  isInvalid,
}) => {
  let options = dropDownValues ?? [];

  if (selectedValue && !options.includes(selectedValue)) {
    options = [selectedValue, ...options];
  }

  const onChange = (value) => {
    const result = {
      target: { name, value: value.length > 0 ? value[0] : null },
    };
    onInputChange(result);
  };

  const selected = selectedValue ? [selectedValue] : [];
  return (
    <Typeahead
      inputProps={{ 'data-testid': 'fml-editVessel-' + name }}
      id={name}
      labelKey={labelKey}
      onChange={onChange}
      options={options}
      placeholder="Please select"
      selected={selected}
      disabled={disabled}
      isInvalid={isInvalid}
    />
  );
};

TakeoverPortDropDownControl.propTypes = {
  dropDownValues: PropTypes.object,
  name: PropTypes.string,
  labelKey: PropTypes.string,
  onInputChange: PropTypes.func,
  selectedValue: PropTypes.string,
  disabled: PropTypes.bool,
  isInvalid: PropTypes.bool,
};

export { TakeoverPortDropDownControl };
