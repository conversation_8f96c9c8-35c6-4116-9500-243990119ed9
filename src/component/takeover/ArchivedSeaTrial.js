import React from 'react';
import { Col, Form } from 'react-bootstrap';
import PropTypes from 'prop-types';

const ArchivedSeaTrial = ({ seaTrial }) => (
  <>
    <Form.Group className="form-group" as={Col}>
      <Form.Control
        disabled
        name="load"
        type="text"
        value={isNaN(seaTrial.load) ? '' : seaTrial.load}
      />
    </Form.Group>
    <Form.Group className="form-group" as={Col}>
      <Form.Control
        disabled
        name="corrected_sfoc"
        value={isNaN(seaTrial.corrected_sfoc) ? '' : seaTrial.corrected_sfoc}
      />
    </Form.Group>
    <Form.Group className="form-group" as={Col}>
      <Form.Control disabled name="kw" value={isNaN(seaTrial.kw) ? '' : seaTrial.kw} />
    </Form.Group>
    <Form.Group className="form-group" as={Col}>
      <Form.Control disabled name="rpm" value={isNaN(seaTrial.rpm) ? '' : seaTrial.rpm} />
    </Form.Group>
    <Form.Group className="form-group" as={Col}>
      <Form.Control disabled name="speed" value={isNaN(seaTrial.speed) ? '' : seaTrial.speed} />
    </Form.Group>
    <Form.Group className="form-group" as={Col}>
      <Form.Control
        disabled
        name="tons_per_hour"
        value={isNaN(seaTrial.tons_per_hour) ? '' : seaTrial.tons_per_hour}
      />
    </Form.Group>
  </>
);

ArchivedSeaTrial.propTypes = {
  seaTrial: PropTypes.object,
};

export { ArchivedSeaTrial };
