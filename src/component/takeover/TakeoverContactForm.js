/* eslint-disable react/prop-types */
import React from 'react';
import { Col, Row, Alert } from 'react-bootstrap';
import { Field } from 'formik';

import TakeoverTypedRowInputControl from './TakeoverTypedRowInputControl';
import { TakeOverTypedPrimaryInput } from './TakeoverTypedPrimaryInput';
import { EMAIL_REGEXP, SAT_C_PHONE_REGEXP, DEFAULT_PHONE_REGEXP } from '../../constants/regexp';
import PropTypes from 'prop-types';

const PHONE_TYPE_SAT_C = '6';

function validateEmail(value) {
  if (!value) return null;
  const errors = value.map(({ email, email_type_id }) => {
    if (!email && !email_type_id) return null;
    if (email && !email_type_id) {
      return {
        dropDown: 'Pleaase select Email type',
        message: 'Please select Email type for Email address',
      };
    }
    if (email_type_id && !email) {
      return null;
    }
    if (!EMAIL_REGEXP.test(email)) {
      return {
        text: 'Invalid email address',
        message: 'Invalid email address',
      };
    }
    return null;
  });
  return errors.filter((e) => e).length === 0 ? null : errors;
}

function validatePhone(value) {
  if (!value) return null;
  const errors = value.map(({ phone_type_id, phone_number }) => {
    if (phone_number && !phone_type_id) {
      return {
        dropDown: 'Pleaase select phone number type',
        message: 'Please select phone number type for phone number',
      };
    }
    if (phone_type_id && !phone_number) {
      return {
        text: 'Please enter phone number',
        message: 'Please enter phone number for phone number type',
      };
    }
    const phoneRegexp =
      Number(phone_type_id) === Number(PHONE_TYPE_SAT_C)
        ? SAT_C_PHONE_REGEXP
        : DEFAULT_PHONE_REGEXP;
    if (!phoneRegexp.test(phone_number)) {
      return {
        text: 'Invalid phone number',
        message: 'Invalid phone number',
      };
    }
    return null;
  });
  return errors.filter((e) => e).length === 0 ? null : errors;
}

const TakeOverContactForm = (props) => {
  const { vessel, dropDownData, errors, onInputArrayChange, onInputChange, onInputArrayRemoveRow } =
    props;
  const { phoneTypes = [], emailTypes = [] } = dropDownData;

  const emailType = emailTypes ? emailTypes[0] : { id: 0, value: '' };
  const satcEmailType = emailTypes ? emailTypes[1] : { id: 0, value: '' };
  return (
    <div className={props.isAllApproved ? 'required take_over_page h6' : 'take_over_page h6'}>
      <Row className="mt-5 mb-3 ml-0">
        <Alert className="takeover_contact_info" variant="primary">
          <Alert.Heading>
            We only synchronise the Primary Email address and the Primary SAT Email address to PARIS
            1.0.
          </Alert.Heading>
          <p>
            This is a system limitation from PARIS 1.0, not PARIS 2.0. You can enter as many email
            addresses as you want, they will just not be synchronised to the old PARIS 1.0 system.
          </p>
        </Alert>
      </Row>
      <Field as="div" name="other_emails" validate={validateEmail} />
      <Field as="div" name="phones" validate={validatePhone} />
      <Row>
        <Col md={6}>
          <TakeOverTypedPrimaryInput
            name="primary_email"
            value={vessel.primary_email}
            error={errors.primary_email}
            onInputChange={onInputChange}
            typeId={emailType.id}
            typeLabel={emailType.value}
            header="Primary E-Mail address"
          />
          <TakeOverTypedPrimaryInput
            name="primary_satc_email"
            value={vessel.primary_satc_email}
            error={errors.primary_satc_email}
            onInputChange={onInputChange}
            typeId={satcEmailType.id}
            typeLabel={satcEmailType.value}
            header="Primary Sat C E-Mail address"
          />
          <TakeoverTypedRowInputControl
            arrayId="other_emails"
            labels={['Other Emails']}
            dropDownName="email_type_id"
            textName="email"
            onInputArrayChange={onInputArrayChange}
            onInputArrayRemoveRow={onInputArrayRemoveRow}
            vessel={vessel}
            errors={errors}
            options={emailTypes}
          />
        </Col>

        <Col md={6}>
          <TakeoverTypedRowInputControl
            arrayId="phones"
            labels={['Phone']}
            dropDownName="phone_type_id"
            textName="phone_number"
            onInputArrayChange={onInputArrayChange}
            onInputArrayRemoveRow={onInputArrayRemoveRow}
            vessel={vessel}
            errors={errors}
            options={phoneTypes}
          />
        </Col>
      </Row>
    </div>
  );
};

TakeOverContactForm.propTypes = {
  vessel: PropTypes.object,
  dropDownData: PropTypes.object,
  errors: PropTypes.object,
  onInputArrayChange: PropTypes.func,
  onInputChange: PropTypes.func,
  onInputArrayRemoveRow: PropTypes.func,
  isAllApproved: PropTypes.bool,
};

export default TakeOverContactForm;
