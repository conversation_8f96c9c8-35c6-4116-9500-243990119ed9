import { VesselContext } from '../../context/VesselContext';
import React, { useContext } from 'react';
import { Col, Form, Row } from 'react-bootstrap';
import TakeoverDropDownSearchControl from './TakeoverDropDownSearchControl';
import { PAY_CALCULATION_DROPDOWN } from '../../model/constants';
import PropTypes from 'prop-types';

const TakeoverFinancialForm = (props) => {
  const { vessel, onInputChange, errors, dropDownData } = props;
  const { roleConfig } = useContext(VesselContext);
  const isEditable = !!(vessel && vessel.id) && roleConfig.financial.edit;
  const { miscCurrencys } = dropDownData;

  return (
    <div className={props.isAllApproved ? 'required take_over_page h6' : 'take_over_page h6'}>
      <Row className="mt-5 mb-3 ml-0">
        <h6>PORTAGE BILL RELATED</h6>
      </Row>
      <div className="p-2 text-dark  required-field-text">
        <p className="text-danger"> Important fields below</p>
        <p>
          {isEditable
            ? 'These fields are important for Portage Bill, if you do not understand the impact do not change them.'
            : 'These fields are important for Portage Bill, please contact "Finance and Accounts" if you need to make any changes.'}
        </p>
      </div>

      <Row className="form-row">
        <Form.Group
          as={Col}
          md={{ span: 5 }}
          className={`${isEditable ? 'required form-group' : ''} form-group`}
        >
          <Form.Label>Wages Treatment</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-wagesTreatment"
            as="select"
            name="wages_treatment"
            value={vessel.wages_treatment}
            onChange={onInputChange}
            disabled={!(isEditable || roleConfig?.financial?.payCalculationEdit)}
            placeholder="Please Select"
          >
            <option value="fixed">Fixed Wages</option>
            <option value="normal">Normal Wages</option>
            <option value="pool">Wages Pool</option>
          </Form.Control>
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Accumulation Wages</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-accumulationWages"
            as="select"
            name="wages_accumulation"
            value={vessel.wages_accumulation}
            onChange={onInputChange}
            disabled={true}
            placeholder="Please Select"
          >
            <option>yes</option>
            <option>no</option>
          </Form.Control>
          {isEditable && (
            <p className="financial_info">
              To change "Accumulation wages" please send an email to{' '}
              <a href="mailto:<EMAIL>"><EMAIL></a> with
              Capt. Aga's approval.
            </p>
          )}
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group
          as={Col}
          md={{ span: 5 }}
          className={`${isEditable ? 'required' : ''} form-group`}
        >
          <Form.Label>Portage bill module</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-hasPortageBill"
            as="select"
            name="has_portage_bill"
            value={vessel.has_portage_bill}
            onChange={onInputChange}
            disabled={!isEditable}
            placeholder="Please Select"
          >
            <option>yes</option>
            <option>no</option>
          </Form.Control>
        </Form.Group>
        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Vessel Account Code</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-vesselAccountCodeNew"
            type="text"
            name="vessel_account_code_new"
            value={vessel.vessel_account_code_new ? vessel.vessel_account_code_new : ''}
            isInvalid={!!errors.vessel_account_code_new}
            disabled={!isEditable}
            onChange={onInputChange}
          />
          <Form.Control.Feedback type="invalid">
            {errors.vessel_account_code_new}
          </Form.Control.Feedback>
        </Form.Group>
      </Row>

      <Row className="form-row">
        <Form.Group className="form-group" as={Col} md="5">
          <Form.Label>Accounting Currency</Form.Label>
          <TakeoverDropDownSearchControl
            name={'misc_currency_id'}
            selectedValue={vessel.misc_currency_id}
            dropDownValues={miscCurrencys}
            onInputChange={onInputChange}
            disabled={
              !roleConfig?.financial?.payCalculationEdit ||
              (typeof vessel?.status !== 'undefined' && vessel?.status !== 'draft')
            }
          />
        </Form.Group>

        <Form.Group className="form-group" as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Pay Calculation Method</Form.Label>
          <Form.Control
            data-testid="fml-editVessel-payCalculationMethod"
            as="select"
            name="pay_calculation_method"
            value={vessel.pay_calculation_method}
            onChange={onInputChange}
            disabled={!roleConfig?.financial?.payCalculationEdit}
            placeholder="Please Select"
          >
            {Object.entries(PAY_CALCULATION_DROPDOWN).map(([value, label]) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </Form.Control>
          <p className="financial_info">
            Pay Calculation Method will be applicable from the next Portage Bill
          </p>
        </Form.Group>
      </Row>
    </div>
  );
};

TakeoverFinancialForm.propTypes = {
  vessel: PropTypes.object,
  onInputChange: PropTypes.func,
  errors: PropTypes.object,
  dropDownData: PropTypes.object,
  isAllApproved: PropTypes.bool,
};

export default TakeoverFinancialForm;
