import React, { useRef } from 'react';
import { Form } from 'react-bootstrap';
import { useDrag, useDrop } from 'react-dnd';
import styleGuide from '../../styleGuide';
import PropTypes from 'prop-types';
const { Icon } = styleGuide;

const type = 'Image'; // Need to pass which type element can be draggable

const Image = ({ image, index, moveImage, deleteImage, handleShow, onTextChange }) => {
  const ref = useRef(null);

  const [, drop] = useDrop({
    accept: type,
    hover(item) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;
      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }
      // Move the content
      moveImage(dragIndex, hoverIndex);
      // Update the index for dragged item directly to avoid flickering when half dragged
      item.index = hoverIndex;
    },
  });

  const [, drag] = useDrag({
    type: type,
    item: { id: image.id, index },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  // initialize drag and drop into the element
  drag(drop(ref));

  const thumb = {
    display: 'inline-flex',
    marginTop: 35,
    marginLeft: 10,
    marginRight: 10,
    boxSizing: 'border-box',
  };
  const thumbInner = {};
  const img = {
    display: 'block',
    maxWidth: 140,
    maxHeight: 140,
    cursor: 'move',
  };

  return (
    <div style={thumb}>
      <div style={thumbInner}>
        <img
          ref={ref}
          style={img}
          src={image.preview}
          onClick={(e) => {
            e.stopPropagation();
          }}
          aria-hidden="true"
        />
        {handleShow && (
          <div style={{ paddingTop: 5 }}>
            <Form.Control
              style={{ maxWidth: 140, textAlign: 'center' }}
              type="text"
              name={`tf${index}`}
              value={image.caption || ''}
              onChange={(e) => {
                onTextChange(index, e.target.value);
              }}
            />
          </div>
        )}
        <div className="mt-2 text-center">
          <Icon
            icon="remove"
            size={30}
            alt={image.id}
            style={{ cursor: 'pointer' }}
            onClick={(e) => {
              if (handleShow) {
                handleShow(index);
              } else {
                deleteImage(e, index);
              }
            }}
          />
        </div>
      </div>
    </div>
  );
};

Image.propTypes = {
  image: PropTypes.object,
  index: PropTypes.number,
  moveImage: PropTypes.func,
  deleteImage: PropTypes.func,
  handleShow: PropTypes.func,
  onTextChange: PropTypes.func,
};

const ImageList = ({ images, moveImage, deleteImage, handleShow, onTextChange }) => {
  const thumbsContainer = {
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
  };

  const renderImage = (image, index) => {
    return (
      <Image
        image={image}
        index={index}
        key={image.id}
        moveImage={moveImage}
        deleteImage={deleteImage}
        handleShow={handleShow}
        onTextChange={onTextChange}
      />
    );
  };

  return <aside style={thumbsContainer}>{images.map(renderImage)}</aside>;
};

ImageList.propTypes = {
  images: PropTypes.object,
  moveImage: PropTypes.func,
  deleteImage: PropTypes.func,
  handleShow: PropTypes.func,
  onTextChange: PropTypes.func,
};

export default ImageList;
