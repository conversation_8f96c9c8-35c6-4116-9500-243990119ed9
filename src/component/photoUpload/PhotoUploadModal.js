/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import Resizer from 'react-image-file-resizer';
import Spinner from '../Spinner';
import axios from 'axios';
import '../../../node_modules/react-image-lightbox/style.css';
import vesselService from '../../service/vessel-service';
import Dropzone from './Dropzone';
import Lowerzone from './Lowerzone';
import PropTypes from 'prop-types';

function PhotoUploadModal({
  vesselId,
  label,
  images,
  photoUrlMap,
  onCustomInputChange,
  setPhotoUrlMap,
  onSubmitVesselOnly,
}) {
  const [dropzoneFiles, setDropzoneFiles] = useState([]);
  const [lowerzoneFiles, setLowerzoneFiles] = useState([]);
  const [show, setShow] = useState(false);
  const [loading, setLoading] = useState(false);
  const handleShow = () => setShow(true);
  const handleClose = () => {
    // clear dropzone
    setDropzoneFiles([]);

    setShow(false);
  };

  const updateVessel = async (newList) => {
    // set vessel state
    onCustomInputChange('images', newList);
    // save to db
    onSubmitVesselOnly();
  };

  const resizeFile = (file) =>
    new Promise((resolve) => {
      Resizer.imageFileResizer(
        file,
        720,
        720,
        'JPEG',
        100,
        0,
        (uri) => {
          resolve(uri);
        },
        'file',
      );
    });

  const handleConfirm = async () => {
    // upload to s3
    const requestUploadItems = dropzoneFiles.map((f) => ({
      id: f.id,
      key: f.key,
      contentType: f.type,
      file: f,
    }));
    let uploadResult = [];
    try {
      setLoading(true);
      const fileNames = requestUploadItems.map(({ key }) => key);
      const response = await vesselService.requestVesselUploadUrls(fileNames);
      if (response.data.results) {
        uploadResult = await Promise.all(
          response.data.results.map(async (urlItem, index) => {
            const { fileName: key, url, path } = urlItem;
            try {
              const formData = new FormData();
              const fileData = requestUploadItems[index].file;
              const resizedFile = await resizeFile(fileData);
              formData.append('file', resizedFile);

              const options = {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              };

              await axios.post(url, formData, options);

              return {
                key,
                result: 'success',
                imageID: requestUploadItems[index].id,
                path,
              };
            } catch (error) {
              console.log(error);
              return {
                key,
                result: 'failed',
                error,
              };
            }
          }),
        );
      }
    } catch (error) {
      console.error(error);
    }
    // update database
    const newList = [
      ...lowerzoneFiles.map((f, idx) => {
        return {
          order: idx,
          path: f.path,
          caption: f.caption,
        };
      }),
      ...dropzoneFiles.map((f2, idx2) => {
        console.log('idx2', idx2, uploadResult[idx2]);
        return {
          order: idx2,
          path: uploadResult[idx2] ? uploadResult[idx2].path : '',
          caption: f2.caption,
        };
      }),
    ];

    // refresh photoUrlMap
    try {
      const response = await vesselService.requestVesselDownloadUrls(
        uploadResult.map(({ path }) => path),
      );
      if (response.data) {
        setPhotoUrlMap({
          ...photoUrlMap,
          ...response.data,
        });
      }
    } catch (error) {
      console.log(error);
    }

    // clear dropzone
    setDropzoneFiles([]);
    setShow(false);

    updateVessel(newList);
    setLoading(false);
  };

  return (
    <>
      <Button
        data-testid={'fml-editVessel-' + label}
        onClick={handleShow}
        variant="outline-primary"
      >
        {label}
      </Button>
      <Modal
        show={show}
        onHide={handleClose}
        size="xl"
        aria-labelledby="contained-modal-title-vcenter"
        centered
      >
        <Modal.Header>
          <Modal.Title>Upload Photos</Modal.Title>
        </Modal.Header>
        {loading ? (
          <div className="photo-upload-spinner-container">
            <Spinner />
          </div>
        ) : (
          <>
            <Modal.Body>
              <Dropzone files={dropzoneFiles} setFiles={setDropzoneFiles} />
              {images.length > 0 && (
                <>
                  <Modal.Title className="mt-4 mb-2">Edit Uploaded Photos</Modal.Title>
                  <Lowerzone
                    files={lowerzoneFiles}
                    setFiles={setLowerzoneFiles}
                    images={images}
                    photoUrlMap={photoUrlMap}
                    updateVessel={updateVessel}
                  />
                </>
              )}
            </Modal.Body>
            <Modal.Footer>
              <Button data-testid="fml-editVessel-cancel" variant="primary" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                data-testid="fml-editVessel-confirm"
                variant="secondary"
                onClick={handleConfirm}
                disabled={dropzoneFiles.length === 0 && lowerzoneFiles.length === 0}
              >
                Confirm
              </Button>
            </Modal.Footer>
          </>
        )}
      </Modal>
    </>
  );
}

PhotoUploadModal.propTypes = {
  vesselId: PropTypes.string,
  label: PropTypes.string,
  images: PropTypes.object,
  photoUrlMap: PropTypes.object,
  onCustomInputChange: PropTypes.func,
  setPhotoUrlMap: PropTypes.func,
  onSubmitVesselOnly: PropTypes.func,
};

export default PhotoUploadModal;
