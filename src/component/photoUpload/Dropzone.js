/* eslint-disable react/prop-types */
import React, { useEffect, useCallback, useMemo } from 'react';
import { useDropzone } from 'react-dropzone';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TouchBackend } from 'react-dnd-touch-backend';
import update from 'immutability-helper';
import { v4 as uuidv4 } from 'uuid';
import ImageList from './ImageList';
import _ from 'lodash';
import PropTypes from 'prop-types';

// Reference:
// https://blog.logrocket.com/drag-and-drop-in-react/
// https://github.com/learnwithparam/logrocket-drag-and-drop

const backendForDND = _.has(window, 'ontouchstart') ? TouchBackend : HTML5Backend;

export default function Dropzone({ files, setFiles }) {
  const moveImage = (dragIndex, hoverIndex) => {
    const draggedImage = files[dragIndex];
    setFiles(
      update(files, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, draggedImage],
        ],
      }),
    );
  };

  const deleteImage = (e, index) => {
    e.stopPropagation();
    setFiles(
      update(files, {
        $splice: [[index, 1]],
      }),
    );
  };

  useEffect(
    () => () => {
      // Make sure to revoke the data uris to avoid memory leaks
      files.forEach((file) => URL.revokeObjectURL(file.preview));
    },
    [files],
  );

  const baseStyle = {
    backgroundColor: 'rgba(0, 145, 184, 0.05)',
    color: '#1f4a70',
    borderRadius: 4,
    padding: '60px',
    outline: 'none',
    cursor: 'pointer',
  };

  const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } = useDropzone({
    onDrop: useCallback((acceptedFiles) => {
      setFiles(
        acceptedFiles.map((file) => {
          let extension;
          if (file.type === 'image/jpeg') {
            extension = 'jpg';
          } else if (file.type === 'image/png') {
            extension = 'png';
          } else if (file.type === 'image/gif') {
            extension = 'gif';
          }
          const id = uuidv4();
          return Object.assign(file, {
            id,
            key: `${id}.${extension}`,
            preview: URL.createObjectURL(file),
            caption: null,
          });
        }),
      );
    }, []),
    accept: 'image/*',
  });

  const style = useMemo(
    () => ({
      ...baseStyle,
    }),
    [isDragActive, isDragReject, isDragAccept],
  );

  return (
    <div {...getRootProps({ style })}>
      <input {...getInputProps()} />
      <div data-testid="fml-editVessel-clickOrDropFilesHereToUpload" className="text-center">
        <b>Click or drop files here to upload</b>
        <br />
        <br />
        (Support format: jpg, jpeg, png, gif)
      </div>
      <DndProvider backend={backendForDND}>
        <ImageList images={files} moveImage={moveImage} deleteImage={deleteImage} />
      </DndProvider>
    </div>
  );
}

Dropzone.propTypes = {
  files: PropTypes.object,
  setFiles: PropTypes.func,
};
