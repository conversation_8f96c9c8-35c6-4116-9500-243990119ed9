/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import { Button, Modal } from 'react-bootstrap';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TouchBackend } from 'react-dnd-touch-backend';
import update from 'immutability-helper';
import ImageList from './ImageList';
import _ from 'lodash';
import PropTypes from 'prop-types';

const backendForDND = _.has(window, 'ontouchstart') ? TouchBackend : HTML5Backend;

export default function Lowerzone({ files, setFiles, images, photoUrlMap, updateVessel }) {
  const [show, setShow] = useState(false);
  const [deleteIdx, setDeleteIdx] = useState(-1);
  const handleShow = (idx) => {
    setDeleteIdx(idx);
    setShow(true);
  };
  const handleClose = () => {
    setShow(false);
  };
  const handleConfirm = () => {
    let newList = [];
    let newFiles = [];
    if (deleteIdx >= 0) {
      newFiles = update(files, {
        $splice: [[deleteIdx, 1]],
      });
      newList = newFiles.map((f, idx) => {
        const pathParts = f.path.split('/');
        const key = pathParts[pathParts.length - 1];
        return {
          id: key,
          key,
          path: f.path,
          order: idx,
          caption: f.caption,
        };
      });
    }

    updateVessel(newList);
    setFiles(newFiles);
    setShow(false);
  };

  const moveImage = (dragIndex, hoverIndex) => {
    const draggedImage = files[dragIndex];
    setFiles(
      update(files, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, draggedImage],
        ],
      }),
    );
  };

  const deleteImage = (e, index) => {
    e.stopPropagation();
    setFiles(
      update(files, {
        $splice: [[index, 1]],
      }),
    );
  };

  const onTextChange = (index, value) => {
    const arrayData = [...files];
    arrayData[index].caption = value;
    setFiles(arrayData);
  };

  useEffect(() => {
    setFiles(
      images
        .sort((a, b) => a.order - b.order)
        .map((p) => {
          const pathParts = p.path.split('/');
          const key = pathParts[pathParts.length - 1];
          return {
            id: key,
            path: p.path,
            key,
            preview: photoUrlMap[p.path],
            caption: p.caption,
          };
        }),
    );
  }, []);

  useEffect(
    () => () => {
      // trigger when close modal

      // Make sure to revoke the data uris to avoid memory leaks
      files.forEach((file) => URL.revokeObjectURL(file.preview));
    },
    [files],
  );

  const baseStyle = {
    backgroundColor: 'rgba(0, 145, 184, 0.05)',
    color: '#1f4a70',
    borderRadius: 4,
    padding: '20px 60px',
    outline: 'none',
  };

  return (
    <div style={baseStyle}>
      <Button
        onClick={() => {
          handleShow(-1);
        }}
        variant="outline-primary"
      >
        Delete all
      </Button>
      <div className="mt-2">To reorder photos, click and drag photos</div>
      <DndProvider backend={backendForDND}>
        <ImageList
          images={files}
          moveImage={moveImage}
          deleteImage={deleteImage}
          handleShow={handleShow}
          onTextChange={onTextChange}
        />
      </DndProvider>
      <Modal
        show={show}
        onHide={handleClose}
        aria-labelledby="contained-modal-title-vcenter"
        centered
      >
        <Modal.Header>
          <Modal.Title>Confirm Deleting Photos?</Modal.Title>
        </Modal.Header>
        <Modal.Body>Are you confirm to delete photos?</Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="secondary" onClick={handleConfirm}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

Lowerzone.propTypes = {
  files: PropTypes.object,
  setFiles: PropTypes.func,
  images: PropTypes.object,
  photoUrlMap: PropTypes.object,
  updateVessel: PropTypes.func,
};
