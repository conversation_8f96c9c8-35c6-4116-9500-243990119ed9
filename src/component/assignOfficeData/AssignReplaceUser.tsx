import React, { useContext, useEffect, useState } from 'react';
import AssignFieldDialog from './AssignFieldDialog';
import ConfirmModal from '../customComponent/CustomConfirmationModal';
import { VesselContext } from '../../context/VesselContext';
import { Row, Col, Form } from 'react-bootstrap';
import _ from 'lodash';
import { ASSIGNUSERSTATUS, SUPERINTENDENT_ONBOARDING_GROUP, USERTYPEOPTIONS, POD_MANAGER_SUB_ROLES, ROLES } from '../../constants/assign-user';
import AssignRoleDialog from './AssignRoleDialog';
import vesselService from '../../service/vessel-service';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import { DetailContext } from '../../context/DetailContext';

const AssignReplaceUser = () => {
  const { assignUserActionStatus, setAssignUserActionStatus } = useContext(VesselContext);
  const { ASSIGN_STATUS, UPDATED_STATUS, CONFIRM_CHANGES, VESSEL_ASSIGN } = ASSIGNUSERSTATUS;
  const [loading, setLoading] = useState(false);
  const [hasUserTickedConfirmation, setHasUserTickedConfirmation] = useState(false);
  const [oscPilot, setOSCPilot] = useState(false);
  const [oscPilotReplaceUser, setOSCPilotReplaceUser] = useState(false);
  const [isSuptdOnBoarding, setIsSuptdOnBoarding] = useState(false)
  const [error, setError] = useState();
  const [modalTitle, setModalTitle] = useState('Confirm Staff Assignment')
  const { ga4EventTrigger = () => { } } = useContext(DetailContext);

  const handleOSCPilotUpdateReplaceUser = (val) => {
    setOSCPilot(val);
    setOSCPilotReplaceUser(val);
  }

  useEffect(() => {
    const data = assignUserActionStatus?.data;
    const status = assignUserActionStatus?.status;
    const role = assignUserActionStatus?.role;

    const isTechGroupModal = data?.tech_group_modal === true;

    // Always reset first
    setOSCPilot(false);
    setOSCPilotReplaceUser(false);
    setModalTitle('Role Assignment'); // default

    // Handle OSC pilot vessels
    const isOSCVessel = !!data?.osc_pilot;

    if (role === 'tech_group') {
      if (isTechGroupModal && status === 'vessel_assign') {
        setModalTitle('Tech Group Assignment');
      } else if (isTechGroupModal && status === 'confirm_changes') {
        setModalTitle('Confirm Tech Group Changes');
      }

      if (isOSCVessel) {
        setOSCPilot(true);
        setOSCPilotReplaceUser(true);
      }

      // Also allow Confirm Staff Assignment inside tech_group
      if (assignUserActionStatus?.staff_data && assignUserActionStatus.label !== 'tech group') {
        setModalTitle('Confirm Staff Assignment');
      }
    } else {
      // Fix for other roles
      if (assignUserActionStatus?.staff_data) {
        setModalTitle('Confirm Staff Assignment');
      } else {
        setModalTitle('Role Assignment');
      }
    }

    setIsSuptdOnBoarding(assignUserActionStatus?.new_staff?.position === SUPERINTENDENT_ONBOARDING_GROUP);
  }, [assignUserActionStatus])

  useEffect(() => {
    if (isSuptdOnBoarding) {
      setError('The superintendent is not available for selection as they are currently in the onboarding process')
    }
  }, [isSuptdOnBoarding])

  const handleCancelAssignDialog = () => {
    setAssignUserActionStatus();
    setError('');
    setHasUserTickedConfirmation(false);
    setTimeout(() => {
      const element = document.getElementById('desc_tooltip').children;
      element[0].remove(); // this removes the first element
      element[0].remove(); // we remove 0th element again because the element previously at index 1, will be moved to zero after previous line is executed
    }, 250);
  };

  const handleConfirmChanges = async () => {
    try {
      setError('');
      setLoading(true);
      if (assignUserActionStatus.type === 'change_user') {
        const staffData = [];
        assignUserActionStatus.updateVesselList.forEach((item) => {
          const vesselData = _.omit(item, ['id', 'name', 'osc_pilot']);
          Object.keys(vesselData).forEach((eachItem) => {
            const mappedStaff = assignUserActionStatus.staff_list.find(
              (staff) => staff.full_name === vesselData[eachItem],
            );
            const curr_staff_id =
              eachItem === 'tech_group'
                ? assignUserActionStatus.curr_staff.full_name
                : assignUserActionStatus.curr_staff.id;
            if (mappedStaff && mappedStaff.id === curr_staff_id) {
              vesselData[eachItem] = assignUserActionStatus.new_staff.id
                ? assignUserActionStatus.new_staff.id
                : null;
            }
          });
          staffData.push({
            ownership_id: Number(item.id),
            ...(assignUserActionStatus.data.subrole === 'tech_group' && { superintendent: null }),
            ...vesselData,
          });

          if(assignUserActionStatus?.role == 'tech_group'){
            ga4EventTrigger('FML Staff Relationship', `${item?.name} - ${item?.tech_group} (new)`, 'Change Tech Group');
          }
          if(assignUserActionStatus?.role == 'superintendent'){
            ga4EventTrigger('FML Staff Relationship', `${item?.name} - ${assignUserActionStatus?.new_staff?.full_name} (new)`, 'Replace Superintendent');
          }
        });
        await vesselService.assignStaffToVessel(staffData);
        setAssignUserActionStatus({ status: UPDATED_STATUS });
      } else if (assignUserActionStatus.action === ASSIGN_STATUS) {
        if (POD_MANAGER_SUB_ROLES.includes(assignUserActionStatus.data.subrole)) {
          ga4EventTrigger('Clicks the Confirm', `${assignUserActionStatus?.label}`, 'Click');
        }

        if(assignUserActionStatus?.role == 'superintendent'){
            const vesselInformation = await vesselService.getOwnershipVessel(assignUserActionStatus.data.ownershipid);
            ga4EventTrigger('FML Staff Relationship', `${vesselInformation?.data?.name} - ${assignUserActionStatus?.new_staff?.full_name} (new)`, 'Assign Superintendent');
        }

        const staffData = [
          {
            ownership_id: Number(assignUserActionStatus.data.ownershipid),
            [assignUserActionStatus.data.subrole]: assignUserActionStatus.new_staff.id,
            ...(assignUserActionStatus.data.subrole === 'tech_group' && { superintendent: null }),
          },
        ];

        await vesselService.assignStaffToVessel(staffData);

        setAssignUserActionStatus({ status: UPDATED_STATUS });
      }
      setLoading(false);
      setHasUserTickedConfirmation(false);
    } catch (error) {
      console.log(error);
      setError('Something went wrong while updating staff details. Please try later.');
      setLoading(false);
    }
  };

  const assignDataPerson = (data) => {
    return _.isEmpty(data) || _.isEmpty(data?.full_name) ? (
      <p className="font-weight-bold">(Not Assigned)</p>
    ) : (
      <>
        <p className="m-0 font-weight-bold text-break">{data?.full_name}</p>
        <p className="m-0 font-weight-bold text-break">{data?.email}</p>
      </>
    );
  };

  const checkUserAssignRole = (data, username, role) => {
    if (ROLES.includes(role)) {
      if (data[`${role}1`] === username) {
        return '- Primary';
      } else if (data[`${role}2`] === username) {
        return '- Secondary';
      }
    } else if (['buyer', 'buyer_lead', 'buyer_senior_lead'].includes(role)) {
      if (data[`${role}`] === username) {
        return ` - ${USERTYPEOPTIONS.find((item) => item.id === role)?.value}`;
      }
    }
    return '';
  };

  return (
    <div>
      <AssignFieldDialog
        title={assignUserActionStatus?.status === ASSIGN_STATUS ? 'Assign New' : 'Replace'}
        showModal={ASSIGN_STATUS === assignUserActionStatus?.status}
        handleModalCancel={handleCancelAssignDialog}
      />

      <AssignRoleDialog
        showModal={assignUserActionStatus?.status === VESSEL_ASSIGN}
        title={modalTitle}
        handleModalCancel={handleCancelAssignDialog}
        oscPilot={oscPilot}
        setOSCPilot={handleOSCPilotUpdateReplaceUser}
      />

      <ConfirmModal
        showConfirmModal={assignUserActionStatus?.status === CONFIRM_CHANGES}
        title={modalTitle}
        isDisabledConfirm={loading || isSuptdOnBoarding || (oscPilotReplaceUser && !hasUserTickedConfirmation)}
        isDisabledCancel={loading}
        confirmButtonTooltipText={(oscPilotReplaceUser && !hasUserTickedConfirmation) ? 'Confirm approval statement' : ''}
        content={
          <CustomOverlayLoader active={loading}>
            <div>
              {error && <div className='alert-danger mb-3' style={{ marginTop: '-20px', padding: '10px 20px', color: '#dc3545', marginRight: '20px' }}>{error}</div>}
              <Row>
                <Col className="font-weight-bold">Current</Col>
                <Col className="font-weight-bold">New</Col>
              </Row>
              <hr />
              <Row>
                <Col md={4}>{assignDataPerson(assignUserActionStatus?.curr_staff)}</Col>
                <Col md={2} className="font-weight-bold">
                  {'->'}
                </Col>
                <Col md={6}>{assignDataPerson(assignUserActionStatus?.new_staff)}</Col>
              </Row>
              <hr />

              {!_.isEmpty(assignUserActionStatus?.updateVesselList) && (
                <>
                  <p>Changes will apply for the following</p>
                  <ul>
                    {assignUserActionStatus.updateVesselList.map((item, index) => (
                      <li key={item.name + index}>
                        {item.name}
                        {checkUserAssignRole(
                          item,
                          assignUserActionStatus?.curr_staff?.full_name,
                          assignUserActionStatus?.role,
                        )}
                      </li>
                    ))}
                  </ul>
                </>
              )}
              {oscPilotReplaceUser && <Row className="m-0 tech-group-osc-hint">
                <Form.Check
                  type={'checkbox'}
                  id={`osc-checkbox`}
                  checked={hasUserTickedConfirmation}
                  onChange={() => {
                    setHasUserTickedConfirmation(!hasUserTickedConfirmation);
                  }}
                  label={<span>I confirm that I have consulted Procurement and Digital & IT. <span style={{ fontWeight: 500 }}>They have given me approval to change the Tech Group of the selected vessel(s).</span></span>}
                />
              </Row>}
              {assignUserActionStatus?.role === 'superintendent' && (<Row>
                <Form.Check
                  type={'checkbox'}
                  id={`HRTemplate-checkbox`}
                  label={
                    <span>
                      New Vessel Assignment Information Form{' '}
                      <a
                        target="_blank"
                        href="https://fleetship.sharepoint.com/sites/PARIS2/Shared%20Documents/Forms/AllItems.aspx?id=%2Fsites%2FPARIS2%2FShared%20Documents%2FVessel%20v2%2FNew%20Vessel%20Assignment%20Information%20Form%2FNew%20Vessel%20Assignment%20Information%20Form%5FMar%202024%2Epdf&parent=%2Fsites%2FPARIS2%2FShared%20Documents%2FVessel%20v2%2FNew%20Vessel%20Assignment%20Information%20Form"
                      >
                        (HR template)
                      </a>
                      has been signed and submitted{' '}
                      <a
                        target="_blank"
                        href="https://fleetship.sharepoint.com/sites/PARIS2/Shared%20Documents/Forms/AllItems.aspx?csf=1&web=1&e=oGeUfh&CID=d0c6f23d%2D2af4%2D4bc8%2D9c59%2D14b88f5ce8ed&FolderCTID=0x012000044877DA7AD6254182FB944FC49C4901&id=%2Fsites%2FPARIS2%2FShared%20Documents%2FVessel%20v2%2FNew%20Vessel%20Assignment%20Information%20Form%2FSubmission%20Folder"
                      >
                        here
                      </a>
                    </span>
                  }
                />
              </Row>)}
            </div>
          </CustomOverlayLoader>
        }
        handleCancel={handleCancelAssignDialog}
        handleConfirm={handleConfirmChanges}
        confirmText="Confirm Changes"
      />
    </div>
  );
};

export default AssignReplaceUser;
