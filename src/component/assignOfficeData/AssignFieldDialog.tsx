import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Button, Modal, Form, Row, Col } from 'react-bootstrap';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';
import CustomTable from '../customComponent/CustomTable';
import vesselService from '../../service/vessel-service';
import { VesselContext } from '../../context/VesselContext';
import _ from 'lodash';
import { ASSIGNUSERSTATUS, POD_MANAGER_GROUP, ROLES } from '../../constants/assign-user';

const AssignFieldDialog = ({ title = '', showModal = false, handleModalCancel = () => { } }) => {
  const { assignUserActionStatus, setAssignUserActionStatus } = useContext(VesselContext);
  const { CONFIRM_CHANGES, ASSIGN_STATUS } = ASSIGNUSERSTATUS;
  const [userOptions, setUserOptions] = useState([]);
  const [selectedUser, setSelectedUser] = useState([]);
  const [loading, setLoading] = useState(false);
  const [staffVesselList, setStaffVesselList] = useState([]);
  const [sortData, setSortData] = useState();
  const [error, setError] = useState();

  const columns = useMemo(() => {
    const columnData = [
      {
        Header: 'Vessel',
        accessor: (row) => <div>{`${row.name} (${row.id})`}</div>,
        id: 'name',
        name: 'vessel_name',
        type: 'text',
        maxWidth: 60,
      },
    ];
    if (ROLES.includes(assignUserActionStatus?.role)) {
      columnData.push(
        ...[
          {
            Header: 'Primary',
            accessor: (row) => row[assignUserActionStatus?.role + '1'],
            id: 'primary_email',
            name: 'primary_email',
            type: 'text',
            maxWidth: 50,
          },
          {
            Header: 'Secondary',
            accessor: (row) => row[assignUserActionStatus?.role + '2'],
            id: 'secondary_email',
            name: 'secondary_email',
            type: 'text',
            maxWidth: 50,
          },
        ],
      );
    } else if (
      ['buyer', 'buyer_lead', 'buyer_senior_lead'].includes(assignUserActionStatus?.role)
    ) {
      columnData.push(
        ...[
          {
            Header: 'Buyer',
            accessor: (row) => row.buyer,
            id: 'buyer',
            name: 'buyer',
            type: 'text',
            maxWidth: 50,
          },
          {
            Header: 'Buyer Lead',
            accessor: (row) => row.buyer_lead,
            id: 'buyer_lead',
            name: 'buyer_lead',
            type: 'text',
            maxWidth: 50,
          },
          {
            Header: 'Buyer Senior Lead',
            accessor: (row) => row.buyer_senior_lead,
            id: 'buyer_senior_lead',
            name: 'buyer_senior_lead',
            type: 'text',
            maxWidth: 50,
          },
        ],
      );
    }
    return columnData;
  }, [assignUserActionStatus]);

  const fetchStaffVesselList = async (data) => {
    try {
      setError('');
      const response = await vesselService.getAssignedStaffVessel(data);
      if (data.role === 'tech_group') {
        response.data.map((item) => (item.id = item.full_name));
      }
      setStaffVesselList(response.data);
      const filteredSupdt = response.data.filter(
        (item) => item.subgroup_name === assignUserActionStatus.data?.tech_group,
      );
      const formatOption = (item) => ({
        ...item,
        value: item.email ? `${item.full_name} (${item.email})` : `${item.full_name}`
      });
      let options = response.data.map(formatOption);
      if (data.role === 'superintendent') {
        options = filteredSupdt.map(formatOption);
      }
      setUserOptions(_.sortBy(options, [(staff) => staff.full_name.toLowerCase()]));
      if (
        assignUserActionStatus.action === ASSIGNUSERSTATUS.VESSEL_ASSIGN &&
        !_.isEmpty(assignUserActionStatus.staff_data)
      ) {
        setSelectedUser(assignUserActionStatus.staff_data.user);
      }
      setLoading(false);
    } catch (error) {
      console.log(error);
      setError('Something went wrong while fetching staff. Please try later.');
      setLoading(false);
    }
  };

  const handleModalSubmit = () => {
    const statusData = { ...assignUserActionStatus };
    statusData.status = CONFIRM_CHANGES;
    statusData.new_staff = selectedUser[0];
    statusData.action = statusData.action ?? ASSIGN_STATUS;
    statusData.staff_data = { vessels: handleTableData };
    statusData.staff_list = userOptions;
    if (POD_MANAGER_GROUP.includes(assignUserActionStatus.role)) {
      statusData.curr_staff = assignUserActionStatus.data;
    }
    setAssignUserActionStatus(statusData);
  };

  const handleTableData = useMemo(() => {
    if (
      !_.isEmpty(assignUserActionStatus) &&
      assignUserActionStatus.status === ASSIGNUSERSTATUS.ASSIGN_STATUS
    ) {
      let vesselList = [];

      if (!_.isEmpty(selectedUser)) {
        if (
          !_.isEmpty(assignUserActionStatus.staff_data?.user) &&
          selectedUser[0].id === assignUserActionStatus.staff_data.user[0].id
        ) {
          vesselList = { vessel_ownerships: assignUserActionStatus.staff_data.vessels };
        } else {
          vesselList = staffVesselList.find((item) => item.id === selectedUser[0].id);
        }
      }

      if (!_.isEmpty(vesselList)) {
        let data = vesselList?.vessel_ownerships;
        if (!_.isEmpty(sortData)) {
          data = sortData[0]?.desc
            ? _.sortBy(data, [sortData[0]?.id]).reverse()
            : _.sortBy(data, [sortData[0]?.id]);
        }
        return data;
      } else return [];
    } else return [];
  }, [selectedUser, staffVesselList, sortData, assignUserActionStatus]);

  useEffect(() => {
    if (
      !_.isEmpty(assignUserActionStatus) &&
      assignUserActionStatus.status === ASSIGNUSERSTATUS.ASSIGN_STATUS
    ) {
      setLoading(true);
      setSelectedUser([]);
      fetchStaffVesselList({
        id: assignUserActionStatus.data.ownershipid,
        role: assignUserActionStatus.role,
      });
    }
  }, [assignUserActionStatus]);

  return (
    <Modal
      aria-labelledby="contained-modal-title-vcenter"
      centered
      show={showModal}
      backdrop="static"
      data-testid="fml-assign-field-dialog"
      size={POD_MANAGER_GROUP.includes(assignUserActionStatus?.role) ? 'lg' : undefined}
    >
      <CustomOverlayLoader active={loading}>
        <Modal.Header>
          <Modal.Title>{`${title} ${(assignUserActionStatus?.label === 'QHSE Manager' || POD_MANAGER_GROUP.includes(assignUserActionStatus?.role)) ? assignUserActionStatus?.label : _.startCase(assignUserActionStatus?.label)}`}</Modal.Title>
        </Modal.Header>

        <Form className="form-main-control">
          <Modal.Body>
            <Row>
              <Col md={12}>
                {error && <p className="validate-error">{error}</p>}
                <Form.Group>
                  <Form.Label className="from-label">Please Select</Form.Label>
                  <CustomTypeAhead
                    id="basic-typeahead-single"
                    labelKey="value"
                    name="office"
                    placeholder="Please select"
                    inputProps={{ 'data-testid': 'fml-assign-field-select-user' }}
                    multiple={false}
                    options={userOptions}
                    showDropDownIcon={true}
                    clearOnFocus={true}
                    selected={selectedUser}
                    onChange={(e) => setSelectedUser(e)}
                    handleClear={() => setSelectedUser([])}
                  />
                </Form.Group>
                {!_.isEmpty(handleTableData) && (
                  <div className="fs-13-label">
                    {` This ${assignUserActionStatus?.role === 'tech_group' ? 'tech group' : 'person'
                      } is already responsible for ${_.get(selectedUser, '[0].active_vo', 0)} active and ${_.get(selectedUser, '[0].new_takover_vo', 0)} new takeover vessels`}
                  </div>
                )}
              </Col>
              <Col md={12}>
                {!_.isEmpty(handleTableData) && (
                  <CustomTable
                    column={columns}
                    reportData={handleTableData}
                    tableRef={null}
                    isLoading={false}
                    className="vessel-certificate-header assign-user-table custom-table-scroll"
                    setSortData={setSortData}
                  />
                )}
              </Col>
              {assignUserActionStatus?.type === 'change_user' && (
                <Col md={12}>
                  {!_.isEmpty(assignUserActionStatus.updateVesselList) && (
                    <>
                      <p>Changes will apply for the following</p>
                      <CustomTable
                        column={columns}
                        reportData={assignUserActionStatus.updateVesselList}
                        tableRef={null}
                        isLoading={false}
                        className="vessel-certificate-header assign-user-table custom-table-scroll"
                        setSortData={setSortData}
                      />
                    </>
                  )}
                </Col>
              )}
            </Row>
          </Modal.Body>
        </Form>

        <Modal.Footer>
          <Button
            variant="primary"
            data-testid="fml-assign-field-dialog-cancel-btn"
            onClick={handleModalCancel}
          >
            Cancel
          </Button>
          <Button
            variant="secondary"
            data-testid="fml-assign-field-dialog-submit-btn"
            onClick={handleModalSubmit}
            disabled={_.isEmpty(selectedUser)}
          >
            Next
          </Button>
        </Modal.Footer>
      </CustomOverlayLoader>
    </Modal>
  );
};

export default AssignFieldDialog;
