import React, { useContext, useEffect, useState, useMemo, useCallback } from 'react';
import { Button, Modal, Form, Row, Col } from 'react-bootstrap';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import CustomTable from '../customComponent/CustomTable';
import { VesselContext } from '../../context/VesselContext';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';
import vesselService from '../../service/vessel-service';
import _ from 'lodash';
import { ASSIGNUSERSTATUS, USERTYPEOPTIONS, ROLES } from '../../constants/assign-user';

const AssignRoleDialog = ({ showModal = false, title, handleModalCancel, oscPilot = false, setOSCPilot }) => {
  const { assignUserActionStatus, setAssignUserActionStatus } = useContext(VesselContext);
  const { VESSEL_ASSIGN, ASSIGN_STATUS } = ASSIGNUSERSTATUS;
  const [userOptions, setUserOptions] = useState([]);
  const [selectedUser, setSelectedUser] = useState([]);
  const [loading, setLoading] = useState(false);
  const [staffVesselList, setStaffVesselList] = useState([]);
  const [userType, setUserType] = useState([]);
  const [sortData, setSortData] = useState();
  const [initialAssigned, setInitialAssigned] = useState([]);
  const [updateVesselList, setUpdateVesselList] = useState([]);
  const [selectAll, setSelectAll] = useState({});
  const [error, setError] = useState();
  const modalTitle = title ? title : 'Role Assignment';

  const handleTableData = useMemo(() => {
    let vesselList = [];
    if (!_.isEmpty(selectedUser)) {
      if (assignUserActionStatus.role !== 'tech_group') {
        vesselList = staffVesselList.find((item) => item.id === selectedUser[0].id);
      } else {
        vesselList = staffVesselList.find((item) => item.full_name === selectedUser[0]?.full_name);
        vesselList?.vessel_ownerships?.map(
          (item) => (item.tech_group = selectedUser[0]?.full_name),
        );
      }
    }

    if (!_.isEmpty(vesselList)) {
      let data = vesselList?.vessel_ownerships;
      if (!_.isEmpty(sortData)) {
        data = sortData[0]?.desc
          ? _.sortBy(data, [sortData[0]?.id]).reverse()
          : _.sortBy(data, [sortData[0]?.id]);
      }
      setInitialAssigned(data);
      setSelectAll({ ...selectAll, [assignUserActionStatus.role]: data.length === 1 });
      const updateList = data
        .filter((item) => item.id === assignUserActionStatus.data.ownershipid)
        .reduce((map, item) => {
          let cloneData = _.cloneDeep(item);
          Object.keys(item).forEach((key) => {
            if (!['id', 'name', assignUserActionStatus.data.subrole, 'osc_pilot'].includes(key)) {
              cloneData = _.omit(cloneData, key);
            }
          });
          map.push(cloneData);
          return map;
        }, []);
      setUpdateVesselList(updateList);
      return data;
    } else {
      setInitialAssigned([]);
      setUpdateVesselList([]);
      return [];
    }
  }, [selectedUser, staffVesselList, sortData]);

  const getChecked = (row, role) =>
    row[role] === selectedUser[0]?.full_name &&
    updateVesselList.map((item) => item.id).includes(row.id) &&
    updateVesselList.find((item) => item.id === row.id)[role];

  const columns = useMemo(() => {
    const columnData = [
      {
        Header: 'Vessel',
        accessor: (row) => <div>{`${row.name} (${row.id})`}</div>,
        id: 'name',
        name: 'vessel_name',
        type: 'text',
        maxWidth: 60,
      },
    ];
    if (ROLES.includes(userType[0]?.id)) {
      columnData.push(
        ...[
          {
            Header: (
              <Form.Check
                type={'checkbox'}
                id={`default-checkbox`}
                checked={selectAll[`${userType[0]?.id}1`]}
                onChange={() => {
                  onSelectAll(`${userType[0]?.id}1`, userType[0]?.id);
                }}
                label="Primary"
              />
            ),
            accessor: (row) =>
              row[`${userType[0]?.id}1`] && (
                <Form.Check
                  type={'checkbox'}
                  id={`default-checkbox`}
                  checked={getChecked(row, `${userType[0]?.id}1`)}
                  disabled={row[userType[0]?.id + '1'] !== selectedUser[0]?.full_name}
                  onChange={(e) => onCheckChange(row, e.target.checked, `${userType[0]?.id}1`)}
                  label={row[`${userType[0]?.id}1`]}
                />
              ),
            id: `${userType[0]?.id}1`,
            name: 'primary_email',
            type: 'text',
            maxWidth: 50,
            disableSortBy: true,
          },
          {
            Header: (
              <Form.Check
                type={'checkbox'}
                id={`default-checkbox`}
                checked={selectAll[`${userType[0]?.id}2`]}
                onChange={(e) => {
                  onSelectAll(`${userType[0]?.id}2`, userType[0]?.id);
                }}
                label="Secondary"
              />
            ),
            accessor: (row) =>
              row[`${userType[0]?.id}2`] && (
                <Form.Check
                  type={'checkbox'}
                  id={`default-checkbox`}
                  checked={getChecked(row, `${userType[0]?.id}2`)}
                  disabled={row[`${userType[0]?.id}2`] !== selectedUser[0]?.full_name}
                  onChange={(e) => onCheckChange(row, e.target.checked, `${userType[0]?.id}2`)}
                  label={row[`${userType[0]?.id}2`]}
                />
              ),
            id: `${userType[0]?.id}2`,
            name: 'secondary_email',
            type: 'text',
            maxWidth: 50,
            disableSortBy: true,
          },
        ],
      );
    } else if (['buyer', 'buyer_lead', 'buyer_senior_lead'].includes(userType[0]?.id)) {
      columnData.push(
        ...[
          {
            Header: (
              <Form.Check
                type={'checkbox'}
                id={`default-checkbox`}
                checked={selectAll.buyer}
                onChange={() => {
                  onSelectAll('buyer', 'buyer');
                }}
                label="Buyer"
              />
            ),
            accessor: (row) =>
              row.buyer && (
                <Form.Check
                  type={'checkbox'}
                  id={`default-checkbox`}
                  checked={getChecked(row, 'buyer')}
                  disabled={row.buyer !== selectedUser[0]?.full_name}
                  onChange={(e) => onCheckChange(row, e.target.checked, 'buyer')}
                  label={row.buyer}
                />
              ),
            id: 'buyer',
            name: 'buyer',
            type: 'text',
            maxWidth: 50,
            disableSortBy: true,
          },
          {
            Header: (
              <Form.Check
                type={'checkbox'}
                id={`default-checkbox`}
                checked={selectAll.buyer_lead}
                onChange={(e) => {
                  onSelectAll('buyer_lead', 'buyer');
                }}
                label="Buyer Lead"
              />
            ),
            accessor: (row) =>
              row.buyer_lead && (
                <Form.Check
                  type={'checkbox'}
                  id={`default-checkbox`}
                  checked={getChecked(row, 'buyer_lead')}
                  disabled={row.buyer_lead !== selectedUser[0]?.full_name}
                  onChange={(e) => onCheckChange(row, e.target.checked, 'buyer_lead')}
                  label={row.buyer_lead}
                />
              ),
            id: 'buyer_lead',
            name: 'buyer_lead',
            type: 'text',
            maxWidth: 50,
            disableSortBy: true,
          },
          {
            Header: (
              <Form.Check
                type={'checkbox'}
                id={`default-checkbox`}
                checked={selectAll.buyer_senior_lead}
                onChange={(e) => {
                  onSelectAll('buyer_senior_lead', 'buyer');
                }}
                label="Buyer Senior Lead"
              />
            ),
            accessor: (row) =>
              row.buyer_senior_lead && (
                <Form.Check
                  type={'checkbox'}
                  id={`default-checkbox`}
                  checked={getChecked(row, 'buyer_senior_lead')}
                  disabled={row.buyer_senior_lead !== selectedUser[0]?.full_name}
                  onChange={(e) => onCheckChange(row, e.target.checked, 'buyer_senior_lead')}
                  label={row.buyer_senior_lead}
                />
              ),
            id: 'buyer_senior_lead',
            name: 'buyer_senior_lead',
            type: 'text',
            maxWidth: 50,
            disableSortBy: true,
          },
        ],
      );
    } else {
      // included userType[0]?.id === 'tech_group'
      columnData.push({
        Header: (
          <Form.Check
            type={'checkbox'}
            id={`default-checkbox`}
            checked={selectAll[userType[0]?.id]}
            onChange={(e) => {
              onSelectAll(userType[0]?.id);
            }}
            label="Tech Group"
          />
        ),
        accessor: (row) =>
          row[userType[0]?.id] && (
            <Form.Check
              type={'checkbox'}
              id={`default-checkbox`}
              checked={
                row[userType[0]?.id] === selectedUser[0]?.full_name &&
                updateVesselList.map((item) => item.id).includes(row.id)
              }
              disabled={row[userType[0]?.id] !== selectedUser[0]?.full_name}
              onChange={(e) => onCheckChange(row, e.target.checked, userType[0]?.id)}
              label={row[userType[0]?.id]}
            />
          ),
        id: `${userType[0]?.id} `,
        name: 'primary_email',
        type: 'text',
        maxWidth: 50,
        disableSortBy: true,
      });
    }
    return columnData;
  }, [userType, selectedUser, updateVesselList, handleTableData, selectAll]);

  const onCheckChange = (row, isChecked, userRole) => {
    let updatedList = [...updateVesselList];
    if (isChecked) {
      let updatedItem = _.cloneDeep(row);
      if (!updatedList.map((item) => item.id).includes(updatedItem.id)) {
        Object.keys(row).forEach((key) => {
          if (!['id', 'name', userRole, 'osc_pilot'].includes(key)) {
            updatedItem = _.omit(updatedItem, key);
          }
        });
      } else {
        const existingRow = updatedList.find(({ id }) => id === row.id) ?? {};
        const allowedKeys = [...Object.keys(existingRow), userRole];
        Object.keys(row).forEach((key) => {
          if (!allowedKeys.includes(key)) {
            updatedItem = _.omit(updatedItem, key);
          }
        });
        updatedList = _.remove(updatedList, ({ id }) => id !== updatedItem.id);
      }
      updatedList.push(updatedItem);
      setUpdateVesselList(updatedList);
    } else if (updatedList.map((item) => item.id).includes(row.id)) {
      let updatedItem = updatedList.find(({ id }) => id === row.id);
      updatedItem = _.omit(updatedItem, userRole);
      updatedItem = _.omit(updatedItem, 'osc_pilot');
      _.remove(updatedList, (n) => _.isEqual(n.id, row.id));
      if (Object.keys(updatedItem).length !== 2) {
        updatedList.push(updatedItem);
      }
      setUpdateVesselList(updatedList);
    }

    let oscPilotInUpdatedList = false;
    updatedList.forEach((item) => {
      if (item.osc_pilot) {
        oscPilotInUpdatedList = true;
      }
    });
    if (oscPilotInUpdatedList) {
      setOSCPilot(true);
    } else {
      setOSCPilot(false);
    }
    if (checkSelectedAll(userRole, updatedList)) {
      setSelectAll({ ...selectAll, [userRole]: true });
    } else {
      setSelectAll({ ...selectAll, [userRole]: false });
    }
  };

  const checkSelectedAll = useCallback(
    (type, updateList) => {
      const listTableData = handleTableData.filter(
        (item) => item[type] === selectedUser[0]?.full_name,
      );
      const listIntialData = updateList.filter((item) => item[type] === selectedUser[0]?.full_name);

      return listIntialData.length === listTableData.length;
    },
    [handleTableData, selectedUser],
  );

  const onSelectAll = (role, type = '') => {
    if (checkSelectedAll(role, updateVesselList)) {
      const updateList = handleTableData.reduce((map, item) => {
        let cloneData = _.cloneDeep(item);
        if (
          (ROLES.includes(type) &&
            ((item[`${type}1`] === item[`${type}2`] &&
              (selectAll[`${type}1` === role ? `${type}2` : `${type}1`] ||
                item.id === assignUserActionStatus.data.ownershipid)) ||
              item[role] !== selectedUser[0]?.full_name)) ||
          (role === 'buyer' &&
            (((item.buyer === item.buyer_lead || item.buyer === item.buyer_senior_lead) &&
              (selectAll.buyer_lead ||
                selectAll.buyer_senior_lead ||
                item.id === assignUserActionStatus.data.ownershipid)) ||
              item[role] !== selectedUser[0]?.full_name)) ||
          (role === 'buyer_lead' &&
            (((item.buyer === item.buyer_lead || item.buyer_senior_lead === item.lead_buyer) &&
              (selectAll.buyer ||
                selectAll.buyer_senior_lead ||
                item.id === assignUserActionStatus.data.ownershipid)) ||
              item[role] !== selectedUser[0]?.full_name)) ||
          (role === 'buyer_senior_lead' &&
            (((item.buyer === item.buyer_senior_lead ||
              item.buyer_senior_lead === item.lead_buyer) &&
              (selectAll.buyer_lead ||
                selectAll.buyer ||
                item.id === assignUserActionStatus.data.ownershipid)) ||
              item[role] !== selectedUser[0]?.full_name))
        ) {
          cloneData = _.omit(cloneData, role);
          map.push(cloneData);
        } else if (item[role] !== selectedUser[0]?.full_name) {
          map.push(item);
        }
        return map;
      }, []);
      setUpdateVesselList(_.intersectionBy(updateList, updateVesselList, 'id'));

      setSelectAll({ ...selectAll, [role]: false });
    } else {
      const data = [
        ...handleTableData.filter((item) => item[role] === selectedUser[0]?.full_name),
        ...updateVesselList,
      ];
      let updateList = data.reduce((map, item) => {
        let cloneData = _.cloneDeep(item);
        Object.keys(item).forEach((key) => {
          if (!['id', 'name', role, 'osc_pilot'].includes(key)) {
            if (!item.id === assignUserActionStatus.data.ownershipid || !selectAll[key]) {
              cloneData = _.omit(cloneData, key);
            }
          }
        });
        map.push(cloneData);
        return map;
      }, []);
      setUpdateVesselList(_.uniqBy(updateList, 'id'));

      setSelectAll({ ...selectAll, [role]: true });
    }
  };

  const fetchStaffVesselList = async (data) => {
    try {
      setError('');
      const response = await vesselService.getAssignedStaffVessel(data);
      setStaffVesselList(response.data);
      const options =
        data.role === 'tech_group'
          ? response.data.map((item) => {
            return { value: item.full_name, ...item, id: item.full_name };
          })
          : response.data.map((item) => {
            return { value: item.full_name + (item.email != null ? ` (  ${item.email}  )` : ''), ...item };
          });
      setUserOptions(options);

      const user = data.role === 'tech_group'
        ? options.find((item) => item.full_name === assignUserActionStatus.data.full_name)
        : options.find((item) => item.id === assignUserActionStatus.data.id);
      if (['buyer', 'buyer_lead', 'buyer_senior_lead'].includes(data.role)) {
        setSelectAll({
          buyer: false,
          buyer_lead: false,
          buyer_senior_lead: false,
        });
      } else if (ROLES.includes(data.role)) {
        setSelectAll({
          [`${data.role}1`]: false,
          [`${data.role}2`]: false,
        });
      } else {
        setSelectAll({
          [`${data.role}`]: false,
        });
      }
      setSelectedUser(user ? [user] : []);
      setLoading(false);
    } catch (error) {
      console.log(error);
      setError('Something went wrong while fetching staff. Please try later.');
      setLoading(false);
    }
  };

  const handleUserSelect = (e) => {
    setUserType(e);
    if (!_.isEmpty(e)) {
      setLoading(true);
      fetchStaffVesselList({ id: assignUserActionStatus.data.ownershipid, role: e[0].id });
    } else {
      setUserOptions([]);
      setStaffVesselList([]);
      setSelectedUser([]);
    }
  };

  const handleModalSubmit = () => {
    const statusData = { ...assignUserActionStatus };

    statusData.status = ASSIGN_STATUS;
    statusData.staff_data = { user: selectedUser, vessels: initialAssigned };
    statusData.action = VESSEL_ASSIGN;
    statusData.role = userType[0]?.id;
    statusData.curr_staff = assignUserActionStatus.data;
    statusData.updateVesselList = updateVesselList;
    statusData.type = 'change_user';
    setAssignUserActionStatus(statusData);
  };

  const handleCancel = () => {
    setInitialAssigned([]);
    setError('');
    handleModalCancel();
  };

  useEffect(() => {
    if (
      !_.isEmpty(assignUserActionStatus) &&
      assignUserActionStatus.status === ASSIGNUSERSTATUS.VESSEL_ASSIGN
    ) {
      setLoading(true);
      fetchStaffVesselList({
        id: assignUserActionStatus.data.ownershipid,
        role: assignUserActionStatus?.role,
      });
      setUserType([USERTYPEOPTIONS.find((item) => item.id === assignUserActionStatus?.role)]);
    }
  }, [assignUserActionStatus]);

  return (
    <Modal
      aria-labelledby="contained-modal-title-vcenter"
      backdrop="static"
      data-testid="fml-assign-role-dialog"
      centered
      show={showModal}
    >
      <CustomOverlayLoader active={loading}>
        <Modal.Header>
          <Modal.Title>{modalTitle}</Modal.Title>
        </Modal.Header>

        <Form className="form-main-control">
          <Modal.Body>
            <Row>
              <Col md={12}>
                {error && <p className="validate-error">{error}</p>}
                {oscPilot && <Row className="m-0">
                  <div className="tech-group-osc-hint">The selected vessel(s) is on Oracle Supply Chain (OSC). Please consult with Procurement and Digital & IT before changing the vessel(s) Tech Group</div>
                </Row>}
                <Row className="m-0">
                  <Col className="p-0">
                    <Form.Group>
                      <CustomTypeAhead
                        id="basic-typeahead-single"
                        labelKey="value"
                        name="type"
                        placeholder="Please select"
                        inputProps={{ 'data-testid': 'fml-assign-role-select-user-type' }}
                        multiple={false}
                        options={USERTYPEOPTIONS}
                        showDropDownIcon={true}
                        clearOnFocus={true}
                        selected={userType}
                        disabled={true}
                        onChange={(e) => handleUserSelect(e)}
                        handleClear={() => {
                          setUserType([]);
                          setUserOptions([]);
                          setStaffVesselList([]);
                          setSelectedUser([]);
                        }}
                      />
                    </Form.Group>
                  </Col>
                  <Col className={` ml-2 p-0`}>
                    <Form.Group>
                      <CustomTypeAhead
                        id="basic-typeahead-single"
                        labelKey="value"
                        name="assigned user"
                        placeholder="Please select"
                        inputProps={{ 'data-testid': 'fml-assign-role-select-user' }}
                        multiple={false}
                        options={userOptions}
                        showDropDownIcon={true}
                        clearOnFocus={true}
                        selected={selectedUser}
                        disabled={true}
                        onChange={(e) => setSelectedUser(e)}
                        handleClear={() => setSelectedUser([])}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                {!_.isEmpty(handleTableData) && (
                  <div className="fs-13-label">
                    {` This ${assignUserActionStatus?.role === 'tech_group' ? 'tech group' : 'person'
                      } is already responsible for ${_.get(selectedUser, '[0].active_vo', 0)} active and ${_.get(selectedUser, '[0].new_takover_vo', 0)} new takeover vessels`}
                  </div>
                )}
              </Col>
              <Col>
                {!_.isEmpty(handleTableData) && (
                  <CustomTable
                    column={columns}
                    reportData={handleTableData}
                    tableRef={null}
                    isLoading={false}
                    setSortData={setSortData}
                    className="vessel-certificate-header assign-user-table custom-table-scroll"
                  />
                )}
              </Col>
            </Row>
          </Modal.Body>
        </Form>

        <Modal.Footer>
          <Button
            variant="primary"
            data-testid="fml-assign-role-dialog-cancel-btn"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            variant="secondary"
            data-testid="fml-assign-role-dialog-submit-btn"
            onClick={handleModalSubmit}
            disabled={_.isEmpty(updateVesselList)}
          >
            Next
          </Button>
        </Modal.Footer>
      </CustomOverlayLoader>
    </Modal>
  );
};

export default AssignRoleDialog;
