import React from 'react';
import { Table } from 'react-bootstrap';
import PropTypes from 'prop-types';

const ResponsiveTable = ({ columns = [], tableBody }) => (
  <Table responsive bordered={false}>
    <thead>
      <tr>
        {columns.map((column) => (
          <th className="table-header" key={column}>
            {column}
          </th>
        ))}
      </tr>
    </thead>
    <tbody>{tableBody}</tbody>
  </Table>
);

ResponsiveTable.propTypes = {
  columns: PropTypes.array,
  tableBody: PropTypes.object,
};

const TrialContainer = ({ title, body }) => (
  <div className="trial-row-container">
    <span className="title">{title}</span>
    {body}
  </div>
);

TrialContainer.propTypes = {
  title: PropTypes.string,
  body: PropTypes.object,
};

const SeaTrialContainer = ({ title, body }) => (
  <div>
    <span className="sea-trial-date">{title}</span>
    {body}
  </div>
);

SeaTrialContainer.propTypes = {
  title: PropTypes.string,
  body: PropTypes.object,
};

export { ResponsiveTable, TrialContainer, SeaTrialContainer };
