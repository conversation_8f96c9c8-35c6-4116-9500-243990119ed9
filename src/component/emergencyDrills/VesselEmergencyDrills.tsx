/* eslint-disable react/prop-types */
import _ from 'lodash';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { Button, Form, Modal, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { Link, useHistory } from 'react-router-dom';
import INTERVAL_UNIT from '../../constants/interval-unit';
import { DetailContext } from '../../context/DetailContext';
import httpService from '../../service/http-service';
import vesselService from '../../service/vessel-service';
import { Icon } from '../../styleGuide';
import {
  formatDate,
  formatValue,
  getDueDateTextColor,
  isEllipsisActive,
} from '../../util/view-utils';
import CustomTable from '../customComponent/CustomTable';
import { useIsMount } from '../../util/useIsMount';
import CustomCopyModal from '../customComponent/CustomCopyModal';
import ConfirmModal from '../customComponent/CustomConfirmationModal';
import ErrorAlert from '../ErrorAlert';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';

const VesselEmergencyDrills = ({ vesselLists, ownershipId, setDrillExcelData }) => {
  const [emergencyDrillList, setEmergencyDrillList] = useState([]);
  const [showCopyDrillModal, setShowCopyDrillModal] = useState(false);
  const [showConfirmCopyDrillModal, setShowConfirmCopyDrillModal] = useState(false);
  const [showCancelAssignDrillModal, setShowCancelAssignDrillModal] = useState(false);
  const [error, setError] = useState('');
  const [showNoVesselPage, setShowNoVesselPage] = useState(false);
  const [selectedVessel, setSelectedVessel] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedDrills, setSelectedDrills] = useState([]);
  const [sortData, setSortData] = useState([]);
  const history = useHistory();
  const {
    vesselName,
    ga4EventTrigger = () => {},
    roleConfig,
    handleError = () => {},
  } = useContext(DetailContext);
  const isMount = useIsMount();
  const [showHover, setShowHover] = useState({});

  const onHoverRemark = (index) => {
    if (isEllipsisActive(index)) {
      setShowHover({ [`a${index}`]: true });
    }
  };

  const handleCheckboxChange = (checked, id) => {
    let drillList = [...selectedDrills];
    if (checked) {
      drillList.push(id);
    } else {
      drillList = drillList.filter((item) => item != id);
    }
    setSelectedDrills(drillList);
  };

  const columns = [
    {
      Header: (
        <div className="d-flex align-items-center">
          <Form.Check
            type={'checkbox'}
            id={`default-checkbox`}
            checked={emergencyDrillList.length === selectedDrills.length}
            onChange={(e) =>
              setSelectedDrills(e.target.checked ? _.map(emergencyDrillList, 'id') : [])
            }
            className="drill-assign-check"
          />
          {roleConfig?.drills.assign && (
            <Icon
              icon="Delete"
              size={20}
              className="delete-icon"
              onClick={() => {
                if (selectedDrills?.length > 0) {
                  setShowCancelAssignDrillModal(true);
                  ga4EventTrigger('Delete Drills', 'Emergency Drills - Menu', vesselName);
                }
              }}
            />
          )}
        </div>
      ),
      accessor: (row) => (
        <Form.Check
          type={'checkbox'}
          id={`default-checkbox`}
          checked={selectedDrills.includes(row?.id)}
          onChange={(e) => handleCheckboxChange(e.target.checked, row?.id)}
        />
      ),
      id: 'trash',
      name: 'trash',
      type: 'item',
      disableSortBy: true,
    },
    {
      Header: 'No.',
      accessor: (row, index) => index + 1,
      id: 'id',
      name: 'no',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Assigned Drills',
      accessor: (row) => (
        <Link
          to={`/vessel/emergency-drills/history/${ownershipId}?drill_id=${_.get(row, 'drill_id')}`}
          className="button-link"
          onClick={() =>
            ga4EventTrigger(
              'Assigned Drills Link',
              'Emergency Drills - Menu',
              formatValue(_.get(row, 'vessel_drill.name')),
            )
          }
        >
          {formatValue(_.get(row, 'vessel_drill.name'))}
        </Link>
      ),
      id: 'name',
      name: 'drills',
      type: 'text',
    },
    {
      Header: 'Due Date',
      id: 'due_date',
      type: 'item',
      accessor: (row) =>
        row?.due_date ? (
          <p className={getDueDateTextColor(row?.due_date)}>{formatDate(row?.due_date)}</p>
        ) : (
          <p className="text-warning">Not Done</p>
        ),
    },

    {
      Header: 'Description',
      accessor: (row) => (
        <OverlayTrigger
          overlay={
            <Tooltip id="desc_tooltip" className="tooltip">
              {_.get(row, 'vessel_drill.description')}
            </Tooltip>
          }
          placement="bottom"
        >
          <div className="line-text-truncate">{_.get(row, 'vessel_drill.description')}</div>
        </OverlayTrigger>
      ),
      id: 'description',
      name: 'description',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Last Done Date',
      accessor: (row) => formatDate(_.get(row, 'vessel_drill_history.drill_date')),
      id: 'drill_date',
      name: 'last_done_date',
      type: 'date',
    },
    {
      Header: 'Period',
      accessor: (row) => (
        <div className='"text-capitalize"'>
          {_.get(row, 'vessel_drill.interval') +
            ' ' +
            INTERVAL_UNIT[_.get(row, 'vessel_drill.interval_unit')]}
        </div>
      ),
      id: 'interval_days',
      name: 'period',
      type: 'item',
    },
    {
      Header: 'Remarks',
      accessor: (row, index) => (
        <div>
          {showHover[`a${index}`] ? (
            <OverlayTrigger
              overlay={
                <Tooltip id="desc_tooltip" className="tooltip">
                  {formatValue(_.get(row, 'vessel_drill_history.remark'))}
                </Tooltip>
              }
              placement="bottom"
            >
              <div
                id={index}
                onMouseLeave={() => {
                  setShowHover({});
                }}
                className="line-text-truncate"
              >
                {formatValue(_.get(row, 'vessel_drill_history.remark'))}
              </div>
            </OverlayTrigger>
          ) : (
            <div
              id={index}
              onMouseOver={() => onHoverRemark(index)}
              onFocus={() => {}}
              onMouseLeave={() => {
                setShowHover({});
              }}
              aria-hidden="true"
              className="line-text-truncate"
            >
              {formatValue(_.get(row, 'vessel_drill_history.remark'))}
            </div>
          )}
        </div>
      ),
      id: 'remarks',
      name: 'remarks',
      type: 'text',
      disableSortBy: true,
    },
  ];

  const handleCopy = () => {
    setShowConfirmCopyDrillModal(true);
    setShowCopyDrillModal(false);
  };

  const handleCancel = () => {
    setShowConfirmCopyDrillModal(false);
    setSelectedVessel([]);
  };

  const handleConfirmCancelAssignDrills = useCallback(async () => {
    ga4EventTrigger('Confirm Cancel Drills', 'Emergency Drills - Menu', vesselName);
    setShowCancelAssignDrillModal(false);
    setLoading(true);
    try {
      await vesselService.unAssignDrills(ownershipId, selectedDrills);
      fetchAssignedEmergencyDrills();
      setSelectedDrills([]);
    } catch (error) {
      console.log('Unable to  cancel Assign drill', error);
      setLoading(false);
    }
  }, [selectedDrills]);

  const handleConfirmCopyDrill = useCallback(async () => {
    ga4EventTrigger('Confirm Copy Drills', 'Emergency Drills - Menu', vesselName);
    setShowConfirmCopyDrillModal(false);
    setLoading(true);
    try {
      await vesselService.assignDrills(ownershipId, {
        vessel_id: selectedVessel[0]?.id,
      });
      fetchAssignedEmergencyDrills();
    } catch (error) {
      const { status, data } = error.response;
      if (status === 400) {
        setError(data.error.response.error);
      } else {
        console.log('Unable to  cancel Assign drill', error);
      }
      setLoading(false);
    }
  }, [selectedVessel]);
  const DrillButtons = useMemo(() => {
    return (
      <div>
        <Button
          size="sm"
          variant="outline-primary"
          className="mr-2"
          onClick={() => {
            ga4EventTrigger('Assign Drills', 'Emergency Drills - Menu', vesselName);
            history.push(`/vessel/ownership/details/${ownershipId}/assign-drills`);
          }}
          data-testid="fml-vesselEmergencyDrills-assignDrills"
          hidden={!roleConfig?.drills.assign}
        >
          Assign Drills
        </Button>
        <Button
          size="sm"
          variant="outline-primary"
          className="mr-2"
          disabled={emergencyDrillList?.length === 0}
          data-testid="fml-vesselEmergencyDrills-drillHistory"
          onClick={() => {
            ga4EventTrigger('View Drill History', 'Emergency Drills - Menu', vesselName);
            history.push(`/vessel/emergency-drills/history/${ownershipId}`);
          }}
        >
          Drill History
        </Button>
      </div>
    );
  }, [emergencyDrillList]);

  const ConfirmCancelDrillModal = useMemo(() => {
    return (
      <Modal
        aria-labelledby="contained-modal-title-vcenter"
        centered
        show={showCancelAssignDrillModal}
        onHide={() => setShowCancelAssignDrillModal(false)}
      >
        <Modal.Header>
          <Modal.Title>Confirm Cancelling Assigned Drill ?</Modal.Title>
        </Modal.Header>
        <Modal.Footer>
          <Button variant="primary" onClick={() => setShowCancelAssignDrillModal(false)}>
            Cancel
          </Button>
          <Button variant="secondary" onClick={handleConfirmCancelAssignDrills}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    );
  }, [showCancelAssignDrillModal, selectedVessel]);

  const fetchAssignedEmergencyDrills = useCallback(async () => {
    setLoading(true);
    try {
      const response = await vesselService.getAssignedEmergencyDrillList(sortData, ownershipId);
      setEmergencyDrillList(response.data);
      setShowNoVesselPage(_.isEmpty(response.data));
      setDrillExcelData({
        columns: [
          {
            Header: 'No.',
            id: 'drill_id',
            name: 'drill_id',
          },
          {
            Header: 'Assigned Drills',
            id: 'name',
            name: 'name',
          },
          {
            Header: 'Due Date',
            id: 'due_date',
            name: 'due_date',
          },
          {
            Header: 'Description',
            id: 'vessel_drill.description',
            name: 'description',
          },
          {
            Header: 'Last Done Date',
            id: 'drill_date',
            name: 'last_date',
          },
          {
            Header: 'Period',
            id: 'interval',
            name: 'period',
          },
          {
            Header: 'Remarks',
            id: 'remarks',
            name: 'remarks',
          },
        ],
        jsonData: response.data,
        title: 'EMERGENCY DRILL',
      });
    } catch (error) {
      handleError(error.response);
      if (httpService.axios.isCancel(error)) {
        return;
      }
      console.log('get drills failed', error);
    }
    setLoading(false);
  }, [sortData]);

  useEffect(() => {
    !isMount && ga4EventTrigger('Sorting', 'Emergency Drills - Menu', sortData[0]?.id);
    fetchAssignedEmergencyDrills();
  }, [sortData]);

  return (
    roleConfig?.drills.view && (
      <div>
        <div className="d-flex  justify-content-between">
          <div className="font-weight-bold p-2">ASSIGNED DRILLS</div>
          {DrillButtons}
        </div>
        {error && <ErrorAlert message={error} />}
        {!showNoVesselPage ? (
          <>
            <CustomTable
              column={columns}
              reportData={emergencyDrillList}
              tableRef={null}
              isLoading={loading}
              setSortData={setSortData}
            />
            {ConfirmCancelDrillModal}
          </>
        ) : (
          <CustomOverlayLoader active={loading}>
            <div className="text-center mt-5">
              <Icon icon="alert" className="alert-icon-no-search" />
              <div className="font-weight-bold">No Drill Records</div>
              {roleConfig?.drills.assign && (
                <Button
                  size="sm"
                  variant="outline-primary"
                  onClick={() => {
                    ga4EventTrigger('Copy Drills', 'Emergency Drills - Menu', vesselName);
                    setError('');
                    setSelectedVessel([]);
                    setShowCopyDrillModal(true);
                  }}
                  data-testid="fml-vesselEmergencyDrills-copyVessel"
                >
                  Copy from Other Vessel
                </Button>
              )}

              <CustomCopyModal
                showCopyModal={showCopyDrillModal}
                setShowCopyModal={setShowCopyDrillModal}
                handleCopy={handleCopy}
                selectedCopyVessel={selectedVessel}
                setSelectedCopyVessel={setSelectedVessel}
                vesselLists={vesselLists}
                modalHeading="Emergency Drills"
              />
              <ConfirmModal
                showConfirmModal={showConfirmCopyDrillModal}
                setShowConfirmModal={setShowConfirmCopyDrillModal}
                title={`Confirm Copying Emergency Drills ?`}
                confirmText={'Confirm'}
                handleCancel={handleCancel}
                handleConfirm={handleConfirmCopyDrill}
              />
            </div>
          </CustomOverlayLoader>
        )}
      </div>
    )
  );
};

export default VesselEmergencyDrills;
