import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { formatValue } from '../../util/view-utils';
import CustomTable from '../customComponent/CustomTable';
import { Button, Form, OverlayTrigger, Tooltip, Col, Row, Container, Modal } from 'react-bootstrap';
import vesselService from '../../service/vessel-service';
import { BreadcrumbHeader } from '../BreadcrumpHeader';
import styleGuide from '../../styleGuide';
import INTERVAL_UNIT from '../../constants/interval-unit';
import VESSEL_TYPE from '../../constants/vessel-type';
import _ from 'lodash';
import { useHistory } from 'react-router-dom';
import { useIsMount } from '../../util/useIsMount';
const { PARIS2_URL } = process.env;
const { Icon } = styleGuide;

const AssignDrills = ({ ownershipId, ownershipName, ga4EventTrigger }) => {
  const history = useHistory();
  const [drillList, setDrillList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sortData, setSortData] = useState([]);
  const [selectedDrills, setSelectedDrills] = useState([]);
  const [showValidationModal, setShowValidationModal] = useState(false);
  const isMount = useIsMount();

  const handleCheckboxChange = (checked, id) => {
    let drills = [...selectedDrills];
    if (checked) {
      drills.push(id);
    } else {
      drills = drills.filter((item) => item != id);
    }
    setSelectedDrills(drills);
  };

  const columns = [
    {
      Header: (
        <Form.Check
          type={'checkbox'}
          id={`default-checkbox`}
          checked={drillList?.length !== 0 && drillList?.length === selectedDrills.length}
          onChange={(e) => setSelectedDrills(e.target.checked ? _.map(drillList, 'id') : [])}
          className="drill-assign-check"
        />
      ),
      accessor: (row) => (
        <Form.Check
          type={'checkbox'}
          id={`default-checkbox`}
          size={'large'}
          checked={selectedDrills?.includes(row?.id)}
          onChange={(e) => handleCheckboxChange(e.target.checked, row?.id)}
        />
      ),
      id: 'add',
      name: 'add',
      type: 'item',
      maxWidth: 30,
      disableSortBy: true,
    },
    {
      Header: 'Drills',
      accessor: (row) => <div className="line-text-truncate">{formatValue(row?.name)}</div>,
      id: 'name',
      name: 'drills',
      type: 'text',
    },
    {
      Header: 'Applicable To',
      id: 'vessel_type',
      type: 'item',
      accessor: (row) => row.parsed_type?.map((val) => VESSEL_TYPE[val] || val).join(','),
    },
    {
      Header: 'Description',
      accessor: (row) => (
        <OverlayTrigger
          overlay={
            <Tooltip id="desc_tooltip" className="tooltip">
              {row.description}
            </Tooltip>
          }
          placement="bottom"
        >
          <div className="line-text-truncate">{row?.description}</div>
        </OverlayTrigger>
      ),
      id: 'description',
      name: 'description',
      type: 'text',
      disableSortBy: true,
    },
    {
      Header: 'Drill Period',
      accessor: (row) => (
        <div className='"text-capitalize"'>
          {row?.interval + ' ' + INTERVAL_UNIT[row?.interval_unit]}
        </div>
      ),
      id: 'period',
      name: 'period',
      type: 'item',
      disableSortBy: true,
    },
  ];

  const fetchDrillList = useCallback(async () => {
    setLoading(true);
    try {
      const responseDrillList = await vesselService.getEmergencyDrillList(
        sortData,
        `inactive=false`,
      );
      const responseAssignedDrill = await vesselService.getAssignedEmergencyDrillList(
        [],
        ownershipId,
      );

      const data = responseDrillList?.data.filter((itemList) => {
        return !responseAssignedDrill?.data.some((assignList) => {
          return itemList.id === assignList.drill_id;
        });
      });
      setDrillList(data);
    } catch (error) {
      console.log('Unable to fetch drill List', error);
    }
    setLoading(false);
  }, [sortData]);

  const validationCheckboxModal = useMemo(() => {
    return (
      <Modal
        aria-labelledby="contained-modal-title-vcenter"
        centered
        show={showValidationModal}
        onHide={() => setShowValidationModal(false)}
      >
        <Modal.Header>
          <Modal.Title>
            Select checkboxes from the list in order to assign drills to vessel
          </Modal.Title>
        </Modal.Header>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowValidationModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    );
  }, [showValidationModal]);

  const handleAssign = useCallback(async () => {
    ga4EventTrigger('Confirm Assign Drills', 'Assign Drills - Menu', ownershipName);
    if (_.isEmpty(selectedDrills)) {
      setShowValidationModal(true);
    } else {
      setLoading(true);
      try {
        await vesselService.assignDrills(ownershipId, selectedDrills);
        history.push(`/vessel/ownership/details/${ownershipId}/emergency-drills`);
      } catch (error) {
        console.log('Unable to Assign drill', error);
      }
      setLoading(false);
    }
  }, [ownershipId, selectedDrills]);
  useEffect(() => {
    !isMount && ga4EventTrigger('Sorting', 'Assign Drills - Menu', sortData[0]?.id);
    fetchDrillList();
  }, [sortData]);

  const breadCrumbsItems = useMemo(
    () => [
      { title: 'Vessel', label: 'To List Page', link: `${PARIS2_URL}/vessel` },
      {
        title: <p>{ownershipName}</p>,
        label: 'Details',
        link: `${PARIS2_URL}/vessel/ownership/details/${ownershipId}`,
      },
      { title: 'Assign Drills', label: 'To Assign Drill', link: '#' },
    ],
    [ownershipId, ownershipName],
  );
  return (
    <Container>
      <Row className="details-breadcrumb">
        <Col className="col-md-auto align-header">
          <BreadcrumbHeader items={breadCrumbsItems} activeItem={ownershipName} />
        </Col>
        <Col className="d-flex justify-content-end">
          <Icon
            icon="close"
            className="cursor-pointer"
            size={20}
            onClick={() =>
              history.push(`/vessel/ownership/details/${ownershipId}/emergency-drills`)
            }
          />
        </Col>
      </Row>
      <CustomTable
        column={columns}
        className="assign-drill-table"
        reportData={drillList}
        tableRef={null}
        isLoading={loading}
        setSortData={setSortData}
      />
      {validationCheckboxModal}
      <div className="fixed-bottom d-flex justify-content-center w-100 p-3 bg-light ">
        <Button
          variant="secondary"
          disabled={loading}
          onClick={() => handleAssign()}
          data-testid="fml-assignDrills-assignDrillsToVessel"
        >
          Assign Drills to Vessel
        </Button>
      </div>
    </Container>
  );
};

export default AssignDrills;
