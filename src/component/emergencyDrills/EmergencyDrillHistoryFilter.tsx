import React from 'react';
import moment from 'moment';
import { Form, Row } from 'react-bootstrap';
import VesselDropDown from '../TechnicalReports/VesselDropDown';
import CustomDatePicker from '../../component/customComponent/CustomDatePicker';

const EmergencyDrillHistoryFilter = ({
  drillDropdownData,
  filterData,
  setFilterData,
  setPageIndex,
  loading,
  ga4EventTrigger,
}) => {
  const onInputChange = async (date, name) => {
    const changedDate = date ? moment(date).format('YYYY-MM-DD') : '';
    ga4EventTrigger('Filter Last Done', 'Drill History - Filter', `${name} - ${changedDate}`);
    setFilterData({
      ...filterData,
      [name]: changedDate,
    });
    setPageIndex(0);
  };

  const handleDropdownChange = (event) => {
    ga4EventTrigger('Filter Drill Title', 'Drill History - Filter', event[0]?.value);
    setFilterData({ ...filterData, drill: event });
    setPageIndex(0);
  };

  const handleClear = () => {
    if (filterData?.drill?.length > 0) {
      setFilterData({ ...filterData, drill: [] });
    }
  };

  return (
    <div className="no-print">
      <Row>
        <Form.Label className="filter-reports">
          <b>Filter Drill History</b>
        </Form.Label>
      </Row>

      <Row>
        <Form.Group className="reports-filter-textField form-group">
          <Form.Control type="text" placeholder="Last Done Date" disabled />
        </Form.Group>

        <Form.Group className="startDatePicker form-group">
          <CustomDatePicker
            value={filterData.startDate}
            onChange={(event) => onInputChange(event, 'startDate')}
            dataTestId="fml-drillHistory-startDate"
            disabled={loading}
            customProps={{ maxDate: new Date() }}
          />
        </Form.Group>

        <Form.Group className=" datePickerRange form-group">To</Form.Group>

        <Form.Group className="endDatePicker form-group">
          <CustomDatePicker
            value={filterData.endDate}
            onChange={(event) => onInputChange(event, 'endDate')}
            dataTestId="fml-drillHistory-endDate"
            disabled={loading}
            customProps={{ maxDate: new Date() }}
          />
        </Form.Group>

        <Form.Group className="reports-filter-textField form-group">
          <Form.Control type="text" placeholder="Drills" disabled />
        </Form.Group>

        <Form.Group className={`vessel-dropdown ${loading && 'disabled'} form-group`}>
          <VesselDropDown
            onChange={handleDropdownChange}
            dropdownData={drillDropdownData}
            labelKey="value"
            selectedItem={filterData.drill}
            handleClear={handleClear}
            placeholder="All"
            dataTestId="fml-emergencyDrill-dropdown"
          />
        </Form.Group>
      </Row>
    </div>
  );
};

export default EmergencyDrillHistoryFilter;
