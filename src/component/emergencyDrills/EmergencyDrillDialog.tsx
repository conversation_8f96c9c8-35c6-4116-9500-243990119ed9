import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Col, Button, Modal, Form, Row, ButtonToolbar } from 'react-bootstrap';
import { SlashCircle, ChevronExpand } from 'react-bootstrap-icons';
import INTERVAL_UNIT from '../../constants/interval-unit';
import VESSEL_TYPE from '../../constants/vessel-type';
import vesselService from '../../service/vessel-service';
import MultiSelectDropDownControl from '../ItineraryList/MultiSelectDropDownControl';
import { formatDate, getTimeDiffWithUTC } from '../../util/view-utils';
import ErrorAlert from '../ErrorAlert';
import _ from 'lodash';

const EmergencyDrillDialog = ({
  cancelButtonHandler,
  showEmergencyDrillModal,
  rowId,
  setShowEmergencyDrillModal,
  fetchDrillList,
  isLoading,
  setLoading,
  ga4EventTrigger,
}) => {
  const [name, setName] = useState('');
  const [selectedVesselType, setSelectedVesselType] = useState([]);
  const [interval, setInterval] = useState('');
  const [selectedIntervalUnit, setSelectedIntervalUnit] = useState('DAY');
  const [inactive, setInactive] = useState(false);
  const [description, setDescription] = useState(null);
  const [validateForm, setValidateForm] = useState(false);
  const [showConfirmDrillModal, setShowConfirmDrillModal] = useState(false);
  const [isDisabled, setIsDisabled] = useState(true);
  const [invalidMessage, setInvalidMessage] = useState('');
  const [error, setError] = useState(null);
  const multipleDropdownRef = useRef(null);
  const [isDisabledConfirm, setIsDisabledConfirm] = useState(false);

  const intervalUnit = Object.entries(INTERVAL_UNIT).map(([id, value]) => ({
    id: id,
    value: value,
  }));

  const vesselType = Object.keys(VESSEL_TYPE).map((vessel) => {
    return { id: vessel, value: VESSEL_TYPE[vessel] };
  });

  const formValidate = (event) => {
    const form = event.currentTarget;
    return selectedVesselType.length > 0 && form.checkValidity();
  };

  const resetfields = () => {
    setName('');
    setSelectedVesselType([]);
    setInterval('');
    setSelectedIntervalUnit('DAY');
    setInactive(false);
    setDescription(null);
    setValidateForm(false);
    cancelButtonHandler();
  };

  const handleVesselType = (e) => {
    setSelectedVesselType(e.target.value || []);
    setIsDisabled(false);
  };

  const handleInterval = (event, type) => {
    const intervalUnit = type === 'unit' ? event : selectedIntervalUnit;
    const intervalValue = type === 'value' ? event : interval;
    switch (intervalUnit) {
      case 'DAY':
        if (!_.inRange(intervalValue, 1, 31)) {
          setInvalidMessage('Value must be between 1 to 30');
        } else {
          setInvalidMessage('');
        }
        break;
      case 'MONTH':
        if (!_.inRange(intervalValue, 1, 12)) {
          setInvalidMessage('Value must be between 1 to 11');
        } else {
          setInvalidMessage('');
        }
        break;
      case 'YEAR':
        if (!_.inRange(intervalValue, 1, 6)) {
          setInvalidMessage('Value must be between 1 to 5');
        } else {
          setInvalidMessage('');
        }
        break;
      default:
        setInvalidMessage('');
    }
    setInterval(intervalValue);
    setSelectedIntervalUnit(intervalUnit);
    setIsDisabled(false);
  };

  const handleInactiveButton = (event) => {
    event?.preventDefault();
    setInactive(!inactive);
    setIsDisabled(false);
  };

  const handleName = (event) => {
    setName(event.target.value);
    setIsDisabled(false);
  };

  const handleDescription = (e) => {
    setDescription(e.target.value);
    setIsDisabled(false);
  };

  const handleCancelButton = () => {
    setIsDisabled(true);
    cancelButtonHandler();
    resetfields();
    setInvalidMessage('');
  };

  const handleSubmit = async (event) => {
    event.stopPropagation();
    event.preventDefault();
    if (_.isEmpty(interval)) {
      setInvalidMessage('Drill Period is required');
    }
    if (formValidate(event) && !invalidMessage) {
      if (rowId) {
        handleConfirm();
        setShowEmergencyDrillModal(false);
      } else {
        setShowEmergencyDrillModal(false);
        setShowConfirmDrillModal(true);
      }
      setIsDisabled(true);
    } else {
      setValidateForm(true);
      setShowConfirmDrillModal(false);
    }
  };

  const handleConfirm = async () => {
    try {
      ga4EventTrigger(
        'Confirm Define New Drill',
        'Drill Master List - Menu',
        'Confirm Define New Drill',
      );
      const data = {
        name: name,
        vessel_type: selectedVesselType,
        interval_unit: selectedIntervalUnit,
        interval: Number(interval),
        inactive: inactive,
        description: description,
      };
      setLoading(true);
      setIsDisabledConfirm(true);
      rowId
        ? await vesselService.patchDrill(rowId.id, data)
        : await vesselService.createDrill(data);
      fetchDrillList();
      setShowConfirmDrillModal(false);
      setIsDisabledConfirm(false);
      setError(null);
      resetfields();
      setInvalidMessage('');
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
      setLoading(false);
    }
  };

  const handleCancelConfirmModal = () => {
    setShowConfirmDrillModal(false);
    resetfields();
    setIsDisabledConfirm(false);
    setError(null);
  };

  useEffect(() => {
    (async () => {
      if (rowId?.name) {
        setName(rowId.name);
        setSelectedVesselType(rowId.parsed_type || []);
        setInterval(rowId.interval);
        setSelectedIntervalUnit(rowId.interval_unit);
        setInactive(rowId.inactive);
        setDescription(rowId.description);
      }
    })();
  }, [rowId]);

  const ConfirmDrillModal = useMemo(() => {
    return (
      <Modal
        aria-labelledby="contained-modal-title-vcenter"
        centered
        show={showConfirmDrillModal}
        onHide={() => setShowConfirmDrillModal(false)}
      >
        <Modal.Header className="d-block">
          <span>{error && <ErrorAlert message={error} />}</span>
          <Modal.Title>Confirm Defining New Emergency Drill?</Modal.Title>
        </Modal.Header>
        <Modal.Footer>
          <Button variant="primary" onClick={() => handleCancelConfirmModal()}>
            Cancel
          </Button>
          <Button variant="secondary" onClick={() => handleConfirm()} disabled={isDisabledConfirm}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    );
  }, [showConfirmDrillModal, error, isDisabledConfirm]);

  return (
    <>
      <Modal
        id="emergency-drill-popup"
        show={showEmergencyDrillModal}
        aria-labelledby="emergency-drill-modal"
        centered
        size="lg"
        backdrop="static"
      >
        <Modal.Header>
          <Modal.Title style={{ borderBottom: '0' }}>
            {!rowId ? 'Define New Emergency Drill' : 'Edit Emergency Drill'}
            <div className="text-danger h6">
              {rowId &&
                'Note: Editing this Emergency Drill will apply to vessels have already assigned with this drill.'}
            </div>
            <div className="required-field-text">* Required fields</div>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <>
            {
              <Form
                noValidate
                validated={validateForm}
                onSubmit={(e) => handleSubmit(e)}
                className="form-main-control"
              >
                <Row>
                  <Col className="col-6 pr-4">
                    <Form.Group className="form-group">
                      <Form.Label className="from-label">Drill*</Form.Label>
                      <Form.Control
                        type="text"
                        data-testid="fml-emergencyDrillDialog-drill"
                        required
                        name="name"
                        className="form-main-control"
                        value={name}
                        onChange={(e) => handleName(e)}
                      />
                      <Form.Control.Feedback type="invalid">
                        Drill is required field.
                      </Form.Control.Feedback>
                    </Form.Group>

                    <Form.Group className="form-group">
                      <Form.Label className="from-label">Applicable To*</Form.Label>
                      <div className="position-relative">
                        <MultiSelectDropDownControl
                          multipleDropdownRef={multipleDropdownRef}
                          name="vessel_type"
                          data-testid="fml-emergencyDrillDialog-applicableTo"
                          selectedValue={selectedVesselType}
                          dropDownValues={vesselType}
                          onInputChange={(e) => handleVesselType(e)}
                          multiple={true}
                          isInvalid={false}
                          testID={'dropdown-control'}
                        />
                        <ButtonToolbar className="vessel-type-angle-icon">
                          <ChevronExpand
                            onClick={() => {
                              multipleDropdownRef.current.toggleMenu();
                            }}
                          />
                        </ButtonToolbar>
                      </div>
                      {selectedVesselType?.length === 0 && validateForm && (
                        <p className="validate-error">Applicable to is required field.</p>
                      )}
                    </Form.Group>

                    <Form.Group className="form-group">
                      <Form.Label className="from-label">Drill Period*</Form.Label>
                      <Row>
                        <Col>
                          <Form.Control
                            name="interval"
                            data-testid="fml-emergencyDrillDialog-interval"
                            type="number"
                            className="form-main-control"
                            max={30}
                            min={1}
                            value={interval}
                            onChange={(e) => handleInterval(e.target.value, 'value')}
                          />
                          <div>
                            <p className="validate-error">{invalidMessage}</p>
                          </div>
                        </Col>
                        <Col className="position-relative">
                          <Form.Control
                            as="select"
                            name="interval_unit"
                            value={selectedIntervalUnit}
                            onChange={(e) => handleInterval(e.target.value, 'unit')}
                            data-testid="fml-emergencyDrillDialog-intervalUnit"
                            className="form-select"
                          >
                            {intervalUnit &&
                              intervalUnit.map(({ id, value }) => (
                                <option className="font-size-large" value={id} key={id}>
                                  {value}
                                </option>
                              ))}
                          </Form.Control>
                        </Col>
                      </Row>
                    </Form.Group>

                    {rowId && (
                      <Form.Label className="edit-label pt-2">
                        Last Edited by {rowId.updated_by} on{' '}
                        {formatDate(rowId.updated_at, 'DD MMM YYYY HH:mm')} {getTimeDiffWithUTC()}
                      </Form.Label>
                    )}
                  </Col>

                  <Col className="col-6 pl-4">
                    {rowId && (
                      <Form.Group className="form-group">
                        <Col>
                          <Row>
                            <Form.Label className="from-label">Mark as inactive</Form.Label>
                          </Row>
                          <Row className="align-items-center">
                            <SlashCircle
                              color={inactive ? 'red' : 'grey'}
                              data-testid="fml-emergencyDrillDialog-inactive"
                              size={20}
                              onClick={(event) => handleInactiveButton(event)}
                              name="inactive"
                              value={inactive}
                            />
                            <p className="pl-1 m-0">Inactive</p>
                          </Row>
                        </Col>
                      </Form.Group>
                    )}
                    <Form.Group className="form-group">
                      <Form.Label className="from-label">Description</Form.Label>
                      <Form.Control
                        as="textarea"
                        data-testid="fml-emergencyDrillDialog-description"
                        rows="3"
                        maxLength={200}
                        name="description"
                        className="form-main-control"
                        value={description}
                        onChange={(e) => handleDescription(e)}
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <hr className="modal-border-line" />

                <Row>
                  <Modal.Footer style={{ borderTop: '0', width: '100%' }}>
                    <div className="ml-auto">
                      <Button
                        variant="primary"
                        data-testid="fml-emergencyDrillDialog-cancel"
                        className="m-2"
                        onClick={() => handleCancelButton()}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="secondary"
                        data-testid="fml-emergencyDrillDialog-save"
                        type="submit"
                        disabled={isDisabled}
                      >
                        Save
                      </Button>
                    </div>
                  </Modal.Footer>
                </Row>
              </Form>
            }
          </>
        </Modal.Body>
      </Modal>
      {ConfirmDrillModal}
    </>
  );
};

export default React.memo(EmergencyDrillDialog);
