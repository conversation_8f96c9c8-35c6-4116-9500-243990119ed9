import React, { useContext } from 'react';
import { <PERSON><PERSON>, Col, Container, Row } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { formatDate, formatValue, parseDMS } from '../util/view-utils';
import { DetailContext } from '../context/DetailContext';
import { Icon } from '../styleGuide';
import PropTypes from 'prop-types';

const ShipReport = ({
  lastPosition,
  lastItinerary,
  lastReport,
  master,
  chiefEngineer,
  ownershipId,
  ga4EventTrigger,
  vesselData,
  history,
}) => {
  const { setShowFutureItinerary, roleConfig } = useContext(DetailContext);
  const routeToCrewList = (vesselId) => {
    ga4EventTrigger('Crew List from Vessel', 'Link', 'Vessel Details');
    history.push(`/seafarer/crew-list/vessel/${vesselId}`);
  };

  const routeToSeafarer = (seafarerId) => {
    history.push(`/seafarer/details/${seafarerId}/general`);
  };

  const Person = ({ label, name, isClickable }) => {
    const nameFormat = (full_name) =>
      full_name && `${full_name.first_name || ''} ${full_name.last_name || ''}`;
    return (
      <Col md="6">
        <div className="ship-report-wrapper__bold">{label}</div>
        {isClickable ? (
          <Button onClick={() => routeToSeafarer(name?.id)} variant="link" className="pl-0">
            <div className="ship-report-wrapper__underline ship-report-wrapper__link">
              {nameFormat(name)}
            </div>
          </Button>
        ) : (
          <div className="pl-0 ship-report-wrapper__crew-name">
            <span>{nameFormat(name)}</span>
          </div>
        )}
      </Col>
    );
  };

  Person.propTypes = {
    label: PropTypes.string,
    name: PropTypes.object,
    isClickable: PropTypes.func,
  };

  const LastPosition = () => (
    <Row className="ship-report-wrapper__second-row">
      <Col>
        <div className="ship-report-wrapper__bold">Last Position</div>
        <div className="ship-report-wrapper__underline">
          <a
            target="_blank"
            href={`http://maps.google.com/maps?q=${lastPosition.lat}+${lastPosition.lon}`}
            rel="noreferrer"
            data-testid="fml-vessel-details-last-position"
            onClick={() =>
              ga4EventTrigger(
                'View position report coordinates',
                'Vessel Details - Link',
                'Vessel Details',
              )
            }
          >
            {lastPosition.position}
          </a>
        </div>
        <div>{formatDate(lastPosition.time, 'DD MMM YYYY HH:mm')}</div>
        <div>(StratumFive)</div>
      </Col>
    </Row>
  );

  LastPosition.propTypes = {
    lastPosition: PropTypes.shape({
      lat: PropTypes.number,
      lon: PropTypes.number,
      position: PropTypes.string,
      time: PropTypes.string,
    }),
  };

  const LastReport = () => {
    const report = lastReport[0];
    const reportType =
      report.report_type.charAt(0).toUpperCase() + report.report_type.toLowerCase().slice(1);
    return (
      <Row className="ship-report-wrapper__third-row">
        <Col>
          <div className="ship-report-wrapper__bold">{reportType} Report</div>
          <div>{formatDate(report.gmt, 'DD MMM YYYY')}</div>
          <div>
            {formatDate(report.smt, 'HH:mm')} (SMT) / {formatDate(report.gmt, 'HH:mm')} (GMT)
          </div>
          <div>
            <div>LOCATION: {reportType}</div>
            <div>
              POSITION:{' '}
              <div className="ship-report-wrapper__underline">
                {report.report_json.general.latitude && report.report_json.general.longitude ? (
                  <a
                    target="_blank"
                    href={`http://maps.google.com/maps?q=${parseDMS({
                      latitude: report.report_json.general.latitude,
                      longitude: report.report_json.general.longitude,
                    })}`}
                    rel="noreferrer"
                    data-testid="fml-vessel-details-position-link"
                    onClick={() =>
                      ga4EventTrigger(
                        'View position report coordinates',
                        'Vessel Details - Link',
                        'Vessel Details',
                      )
                    }
                  >
                    {report.report_json.general.latitude + report.report_json.general.longitude}
                  </a>
                ) : (
                  '- - -'
                )}
              </div>
            </div>
            <div>SPEED: {formatValue(report.report_json.general.average_speed)}</div>
            {reportType === 'Sea' ? (
              <>
                <div>ETA: {formatDate(report.report_json.general.eta, 'DD MMM YYYY HH:mm')}</div>
                <div>NEXT PORT: {formatValue(report.report_json.general.next_port)}</div>
              </>
            ) : (
              <>
                <div>ETC: {formatDate(report.report_json.general.etd, 'DD MMM YYYY HH:mm')}</div>
                <div>PORT: {formatValue(report.report_json.general.port)}</div>
              </>
            )}
          </div>
          <div className="ship-report-wrapper__underline">
            <Link
              data-testid="fml-vessel-details-view-position-report"
              to={`/vessel/report/technical/position/${ownershipId}/compare?gmt=${formatDate(
                report.gmt,
                'YYYY-MM-DD',
              )}`}
              onClick={() =>
                ga4EventTrigger('View position report', 'Vessel Details - Link', 'Vessel Details')
              }
            >
              View report
            </Link>
          </div>
        </Col>
      </Row>
    );
  };

  const Location = ({
    itinerary: { estimated_arrival, estimated_berth, estimated_departure, country, port, berth },
  }) => (
    <div className="ship-report-wrapper__location">
      <div className="ship-report-wrapper__bold">
        &rarr; {country} - {port} ({berth})
      </div>
      <div>ETA: {formatDate(estimated_arrival, 'DD MMM YYYY HH:mm')}</div>
      <div>ETB: {formatDate(estimated_berth, 'DD MMM YYYY HH:mm')}</div>
      <div>ETD: {formatDate(estimated_departure, 'DD MMM YYYY HH:mm')}</div>
    </div>
  );

  Location.propTypes = {
    itinerary: PropTypes.object,
  };

  const handleFutureItineraries = () => {
    ga4EventTrigger('View Future Itinerary', 'Vessel Details - Link', 'Vessel Details');
    setShowFutureItinerary(true);
  };

  const LastItinerary = () => (
    <Row className="ship-report-wrapper__fourth-row">
      <div className="ship-report-wrapper__bold">Itinerary</div>
      {lastItinerary.map((itinerary) => (
        <Location key={itinerary.id} itinerary={itinerary} />
      ))}
      <div className="ship-report-wrapper__underline">
        <Link
          data-testid="fml-vessel-details-view-future-itinerary"
          to={`/vessel/ownership/details/${ownershipId}/itinerary`}
          onClick={handleFutureItineraries}
        >
          View All Future Itinerary
        </Link>
      </div>
    </Row>
  );

  const Alert = ({ text, color = 'red' }) => (
    <div className="alert-wrapper" style={{ color: color }}>
      <Icon icon="alert" className="alert-icon-no-search" size={24} color={color} />
      <p className="mt-4 mb-0">{text}</p>
    </div>
  );

  Alert.propTypes = {
    text: PropTypes.string,
    color: PropTypes.string,
  };

  const SurveyAndCerificate = () => {
    const { certificate_due, certificate_due_30, certificate_due_60 } = vesselData;
    const checkCertificateDue = () => {
      if (Number(certificate_due) !== 0) {
        return <Alert text="Surveys Overdue" color="red" />;
      } else if (Number(certificate_due_30) !== 0) {
        return <Alert text="Surveys due within 30 days" color="orange" />;
      } else if (Number(certificate_due_60) !== 0) {
        return <Alert text="Surveys due within 60 days" color="green" />;
      } else {
        return <p className="my-2">No due Surveys/Certificates due within 60 days </p>;
      }
    };
    return (
      <div className="ship-report-wrapper__third-row border-top border-white mt-2">
        <div className="ship-report-wrapper__bold"> Surveys and Certificates</div>
        {checkCertificateDue()}
        <div className="ship-report-wrapper__underline">
          <Link
            data-testid="fml-vessel-details-view-surveys-and-certificates"
            to={`/vessel/ownership/details/${ownershipId}/certificates`}
          >
            View Surveys and Certificates
          </Link>
        </div>
      </div>
    );
  };

  return (
    <Container className="ship-report-wrapper">
      {(roleConfig.seafarerGeneralView ||
        roleConfig.seafarerViewCrewList ||
        roleConfig.vessel.view) && (
        <Row className="ship-report-wrapper__first-row">
          <Person label="Master" name={master} isClickable={roleConfig.seafarerGeneralView} />
          <Person
            label="Chief Engineer"
            name={chiefEngineer}
            isClickable={roleConfig.seafarerGeneralView}
          />
        </Row>
      )}
      <Row>
        <Col md="6">
          {roleConfig.seafarerViewCrewList && (
            <Row className="ship-report-wrapper__second-row">
              <Button
                onClick={() => routeToCrewList(vesselData?.vessel?.id)}
                variant="link"
                className="pl-3"
              >
                <div className="ship-report-wrapper__underline ship-report-wrapper__link">
                  Crew List
                </div>
              </Button>
            </Row>
          )}

          <LastPosition />
          {lastReport?.length > 0 ? (
            <LastReport />
          ) : (
            <Alert text="Currently no position report for this vessel." />
          )}
        </Col>
        <Col md="6">
          {lastItinerary?.length > 0 ? (
            <LastItinerary />
          ) : (
            <Alert text="No future itinerary records with ETA and ETD found." />
          )}
        </Col>
        <Col md="6">
          <SurveyAndCerificate />
        </Col>
      </Row>
    </Container>
  );
};

ShipReport.propTypes = {
  lastPosition: PropTypes.object,
  lastItinerary: PropTypes.object,
  lastReport: PropTypes.object,
  master: PropTypes.string,
  chiefEngineer: PropTypes.string,
  ownershipId: PropTypes.string,
  ga4EventTrigger: PropTypes.shape({
    ...PropTypes.objectOf(PropTypes.func),
  }),
  vesselData: PropTypes.object,
  history: PropTypes.array,
};

export default ShipReport;
