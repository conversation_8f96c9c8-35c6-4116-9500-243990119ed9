import { QUERY_TYPE_LIKE, QUERY_TYPE_RANGE } from '../../../util/search-types';
import React, { useCallback, useMemo, useRef, useState, useContext } from 'react';
import { Col, Row, Tab } from 'react-bootstrap';
import httpService from '../../../service/http-service';
import vesselService from '../../../service/vessel-service';
import { formatDate, formatValue } from '../../../util/view-utils';
import TechnicalReportTable from '../TechnicalReportTable';
import { Link } from 'react-router-dom';
import _ from 'lodash';
import { TechnicalReportContext } from '../../../context/TechnicalReportContext';
import ErrorAlert from '../../ErrorAlert';

const SearchTypes = [
  {
    type: 'id',
    name: 'No.',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'id',
  },
  {
    type: 'vessel_ownership_id',
    name: 'Vess<PERSON>',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'name',
  },
  {
    type: 'report_type',
    name: 'Report Type',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'report_type',
  },
  {
    type: 'smt',
    name: 'Report Date Time(SMT)',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'smt',
  },
  {
    type: 'gmt',
    name: 'Report Date Time(GMT)',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'gmt',
  },
];

const VoyageReports = ({
  setIsAccessDenied,
  loading = false,
  setLoading = () => {},
  ga4EventTrigger,
}) => {
  const tableRef = useRef(null);
  const { filterData, loadingOptions, error, setError } = useContext(TechnicalReportContext);
  const [voyageReports, setVoyageReports] = useState([]);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, settotalCount] = useState(0);
  const columns = useMemo(
    () => [
      {
        Header: 'No.',
        accessor: (row) => formatValue(row.id),
        sticky: 'left',
        id: 'voyageReport.id',
        name: 'no',
        type: 'text',
        maxWidth: 50,
      },
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link
            to={`/vessel/ownership/details/${row.vessel_ownership_id}`}
            className="button-link"
            onClick={() => ga4EventTrigger('View Vessel', 'Vessel Voyage Report - Link', row.name)}
          >
            {formatValue(row.name)}
          </Link>
        ),
        id: 'voyageReport.vessel',
        name: 'vessel',
        type: 'text',
      },
      {
        Header: 'View Report',
        id: 'voyageReport.viewReport',
        type: 'item',
        accessor: (row) => (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
            aria-hidden="true"
          >
            <Link
              className="button-link"
              to={`/vessel/report/technical/voyage/${
                row.vessel_ownership_id
              }/compare?gmt=${formatDate(row.gmt, 'YYYY-MM-DD')}`}
              onClick={() =>
                ga4EventTrigger(
                  'View Voyage Report',
                  'Vessel Voyage Report - Link',
                  `${row.name} and ${formatDate(row.gmt, 'DD MMM YYYY')}`,
                )
              }
            >
              View
            </Link>
          </div>
        ),
        disableSortBy: true,
      },
      {
        Header: 'Report Type',
        accessor: (row) => formatValue(row.report_type),
        id: 'voyageReport.report_type',
        name: 'reportType',
        type: 'text',
      },
      {
        Header: 'Report Date Time(SMT)',
        accessor: (row) => formatDate(row.smt, 'DD MMM YYYY HH:mm'),
        id: 'voyageReport.smt',
        name: 'smt',
        type: 'date',
        maxWidth: 120,
      },
      {
        Header: 'Report Date Time(GMT)',
        accessor: (row) => formatDate(row.gmt, 'DD MMM YYYY HH:mm'),
        id: 'voyageReport.gmt',
        name: 'gmt',
        type: 'date',
        maxWidth: 120,
      },
    ],
    [],
  );
  const getSortByParams = (paginationParams) => {
    const sortByObj = [...paginationParams.sortBy];
    const searchId = SearchTypes.filter(
      (type) => columns.find((item) => item.id === sortByObj[0].id)?.Header == type.name,
    )
      .map((type) => type.queryKey)
      .toString();
    const formObj = [
      {
        id: searchId,
        desc: sortByObj[0].desc,
      },
    ];
    paginationParams = { ...paginationParams, sortBy: formObj };
    return paginationParams;
  };

  const fetchData = useCallback(
    async (paginationParams) => {
      setLoading(true);
      try {
        setVoyageReports([]);
        if (paginationParams.sortBy.length) {
          paginationParams = getSortByParams(paginationParams);
        }

        let pageFilterData = '';
        if (filterData.startDate || filterData.endDate) {
          pageFilterData = `gmt=${filterData.startDate},${filterData.endDate}`;
        }

        const response = await vesselService.getTechnicalReports(
          'voyage',
          !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
          paginationParams,
          pageFilterData,
        );
        setVoyageReports(response.data.results);
        const total = response.data.total;
        const pageSize = paginationParams.pageSize;
        setPageCount(Math.ceil(total / pageSize));
        settotalCount(total);
        setError(null);
      } catch (error) {
        if (error?.message.includes('403')) {
          setIsAccessDenied(true);
        }
        if (error?.code === 'ERR_NETWORK') {
          setError('oops, request timeout, please apply filter to see results');
        }
        if (httpService.axios.isCancel(error)) {
          return;
        }
        console.log('get vessel failed', error);
      }
      setLoading(false);
    },
    [filterData],
  );

  const eventTracker = (type, value) => {
    if (type === 'Pagination') {
      ga4EventTrigger('Change Row Numbers', 'Vessel Voyage Report - List', value);
    }
    if (type === 'Sorting') {
      ga4EventTrigger('Sort', 'Vessel Voyage Report - List', value);
    }
  };

  return (
    <Tab.Container>
      {error && <ErrorAlert message={error} />}
      <Row className="no-print">
        <Col>
          <TechnicalReportTable
            column={columns}
            reportData={voyageReports}
            fetchData={fetchData}
            tableRef={tableRef}
            isLoading={loading || loadingOptions}
            pageCount={pageCount}
            totalCount={totalCount}
            eventTracker={eventTracker}
          />
        </Col>
      </Row>
    </Tab.Container>
  );
};

export default VoyageReports;
