import _ from 'lodash';
import moment from 'moment';
import React, { useEffect, useState, useContext } from 'react';
import { Link, useHistory } from 'react-router-dom';
import { Dash } from '../../../model/utils';
import vesselService from '../../../service/vessel-service';
import { formatDate, formatValue } from '../../../util/view-utils';
import CustomTable from '../../customComponent/CustomTable';
import ErrorAlert from '../../ErrorAlert';
import VoyageHistoryFilter from '../VoyageHistoryFilter';
import { OverlayTrigger, Tooltip, Button } from 'react-bootstrap';
import { TechnicalReportContext } from '../../../context/TechnicalReportContext';
import { getMonthDateRange } from '../../../util/date';

const VoyageHistory = () => {
  const {
    filterVoyageData,
    setFilterVoyageData,
    setExcelDownloadedData,
    setExcelColumns,
    filterVoyageOptions,
    loadingOptions,
    ga4EventTrigger = () => {},
  } = useContext(TechnicalReportContext);
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const [voyageHistory, setVoyageHistory] = useState();
  const [sortData, setSortData] = useState([{ id: 'smt', desc: true }]);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState(null);
  const [linkCountryPortTriggered, setLinkCountryPortTriggered] = useState(false);
  const columns = [
    {
      Header: 'Date Time (SMT)',
      accessor: (row) => formatDate(_.get(row, 'smt'), 'DD MMM YYYY HH:mm'),
      id: 'smt',
      name: 'smt',
      type: 'date',
      maxWidth: 150,
    },
    {
      Header: 'Vessel',
      accessor: (row) =>
        _.get(row, 'vessel_name') ? (
          <Link
            to={`/vessel/ownership/details/${row.vessel_ownership_id}`}
            onClick={() =>
              ga4EventTrigger(
                'Link to Vessel Details',
                'Voyage History - Link',
                formatValue(_.get(row, 'vessel_name')),
              )
            }
            className="button-link"
          >
            {formatValue(_.get(row, 'vessel_name'))}
          </Link>
        ) : (
          Dash
        ),
      id: 'vessel_name',
      name: 'vessel_name',
      type: 'text',
      maxWidth: 150,
    },
    {
      Header: 'Type',
      type: 'item',
      accessor: (row) => formatValue(_.get(row, 'report_type')),
      id: 'report_type',
      name: 'report_type',
    },
    {
      Header: 'Port',
      type: 'item',
      accessor: (row) =>
        _.get(row, 'port') ? (
          <div
            className="button-link"
            onClick={() => {
              ga4EventTrigger(
                'Link to Port Filtering',
                'Voyage History - Link',
                formatValue(_.get(row, 'port')),
              );
              setFilterVoyageData({
                ...filterVoyageData,
                port: [{ id: row.port, value: row.port }],
                country: [checkCountryOptions(row?.country)],
              });
              setLinkCountryPortTriggered(true);
            }}
            aria-hidden="true"
          >
            {formatValue(_.get(row, 'port'))}
          </div>
        ) : (
          Dash
        ),
      id: 'port',
      name: 'port',
    },
    {
      Header: 'Country',
      type: 'item',
      accessor: (row) =>
        _.get(row, 'country') ? (
          <div
            className="button-link"
            onClick={() => {
              ga4EventTrigger(
                'Link to Country Filtering',
                'Voyage History - Link',
                formatValue(_.get(row, 'port')),
              );
              setFilterVoyageData({
                ...filterVoyageData,
                country: [checkCountryOptions(row?.country)],
              });
              setLinkCountryPortTriggered(true);
            }}
            aria-hidden="true"
          >
            {formatValue(_.get(row, 'country'))}
          </div>
        ) : (
          Dash
        ),
      id: 'country',
      name: 'country',
    },
    {
      Header: 'Avg Speed (Knots)',
      type: 'number',
      accessor: (row) => _.get(row, 'avergae_speed'),
      id: 'avergae_speed',
      name: 'avergae_speed',
      disableSortBy: true,
    },
    {
      Header: 'Cargo Loaded & Discharged',
      type: 'item',
      accessor: (row) => formatValue(_.get(row, 'cargo_type')),
      id: 'cargo_type',
      name: 'cargo_type',
      disableSortBy: true,
      maxWidth: 150,
    },
    {
      Header: 'Cargo Loaded Qty (MT)',
      type: 'number',
      accessor: (row) => formatValue(_.get(row, 'cargo_loaded')),
      id: 'cargo_loaded',
      name: 'cargo_loaded',
      disableSortBy: true,
    },
    {
      Header: 'Cargo Discharged Qty (MT)',
      type: 'number',
      accessor: (row) => formatValue(_.get(row, 'cargo_discharged')),
      id: 'cargo_discharged',
      name: 'cargo_discharged',
      disableSortBy: true,
    },
    {
      Header: 'Reason for port call',
      type: 'item',
      accessor: (row) => formatValue(_.get(row, 'port_call_reason')),
      id: 'port_call_reason',
      name: 'port_call_reason',
      disableSortBy: true,
    },
  ];

  const checkCountryOptions = (item) => {
    const coutryData = filterVoyageOptions?.country?.find((i) => i.value === item);
    if (coutryData) {
      return coutryData;
    }
  };

  const fetchVoyageHistory = async (
    sortData = [{ id: 'smt', desc: true }],
    pageIndex = 0,
    pageSize = 10,
    params = {},
  ) => {
    setLoading(true);
    try {
      const response = await vesselService.getVoyageHistory(
        {
          sortBy: sortData,
          pageSize: pageSize,
          pageIndex: pageIndex,
        },
        params,
      );
      setVoyageHistory(response.data.results);
      setExcelDownloadedData(response.data.results);
      setExcelColumns(columns);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
      setError(null);
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchVoyageHistory(sortData, value, pageSize, formatForFilter());
  };

  const onPageSizeChange = (value) => {
    ga4EventTrigger('Number of Rows', 'Voyage History - List', value);
    setPageSize(value);
    setPageIndex(0);
    fetchVoyageHistory(sortData, 0, value, formatForFilter());
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Voyage History - List', value[0]?.id);
    setSortData(value);
    fetchVoyageHistory(value, pageIndex, pageSize, formatForFilter());
  };

  useEffect(() => {
    if (!loadingOptions && filterVoyageData) {
      setPageIndex(0);
      fetchVoyageHistory(sortData, 0, pageSize, formatForFilter());
    }
  }, [filterVoyageData, loadingOptions]);

  const formatForFilter = () => {
    if (!_.isEmpty(filterVoyageData)) {
      const params = {
        vessel_ownership_id: filterVoyageData?.vessel && filterVoyageData?.vessel[0]?.id,
        year: filterVoyageData?.year && moment(filterVoyageData?.year).format('YYYY'),
        month: filterVoyageData?.month,
        country: filterVoyageData?.country && filterVoyageData?.country[0]?.value,
        port: filterVoyageData?.port && filterVoyageData?.port[0]?.value,
      };

      return _.omitBy(params, (v) => v == null);
    }
  };

  return (
    <div className="pt-4">
      <div className="d-flex justify-content-end">
        <OverlayTrigger
          overlay={
            <Tooltip id="desc_tooltip" className="tooltip">
              Select vessel, year and month in filters to see vessel movement.
            </Tooltip>
          }
          placement="bottom"
        >
          <div>
            <Button
              variant="outline-primary"
              data-testid="fml-voyage-history-movement"
              className={`  mr-2 ${
                _.isEmpty(filterVoyageData?.vessel) ||
                !filterVoyageData?.month ||
                !filterVoyageData?.year
                  ? 'movement-button'
                  : ''
              }`}
              onClick={() => {
                ga4EventTrigger(
                  'Voyage Movement Map',
                  'Voyage History - Menu',
                  filterVoyageData?.vessel ? filterVoyageData?.vessel[0]?.name : 'All Vessels',
                );
                const dateRange = getMonthDateRange(moment(filterVoyageData?.year).format('YYYY'),filterVoyageData?.month)
                history.push(
                  `/vessel/report/technical/voyage-movement?vessel_ownership_id=${filterVoyageData?.vessel[0]?.id}&gmt=${dateRange.join(',')}`);
              }}
              disabled={
                !_.isEmpty(filterVoyageData?.vessel) &&
                filterVoyageData?.month &&
                filterVoyageData?.year &&
                _.isEmpty(voyageHistory)
              }
            >
              Movement
            </Button>
          </div>
        </OverlayTrigger>
      </div>

      {error && <ErrorAlert message={error} />}
      <VoyageHistoryFilter
        linkCountryPortTriggered={linkCountryPortTriggered}
        setLinkCountryPortTriggered={setLinkCountryPortTriggered}
      />
      <CustomTable
        column={columns}
        reportData={voyageHistory}
        tableRef={null}
        isLoading={loading || loadingOptions}
        pagination={true}
        pageCount={pageCount}
        totalCount={totalCount}
        setPageNo={onPageIndexChange}
        setPageListSize={onPageSizeChange}
        setSortData={onSortChange}
        pageNo={pageIndex}
        className="drill-table-top-border"
        pageSize={pageSize}
      />
    </div>
  );
};

export default VoyageHistory;
