import { QUERY_TYPE_LIKE, QUERY_TYPE_RANGE } from '../../../util/search-types';
import React, { useCallback, useMemo, useRef, useState, useContext } from 'react';
import { Col, Row, Tab } from 'react-bootstrap';
import httpService from '../../../service/http-service';
import vesselService from '../../../service/vessel-service';
import { formatDate, formatValue } from '../../../util/view-utils';
import TechnicalReportTable from '../TechnicalReportTable';
import { Link } from 'react-router-dom';
import _ from 'lodash';
import { TechnicalReportContext } from '../../../context/TechnicalReportContext';
import ErrorAlert from '../../ErrorAlert';

const SearchTypes = [
  {
    type: 'id',
    name: 'No.',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'id',
  },
  {
    type: 'vessel_ownership_id',
    name: 'Vess<PERSON>',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'name',
  },
  {
    type: 'report_type',
    name: 'Report Type',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'report_type',
  },
  {
    type: 'smt',
    name: 'Report Date Time(SMT)',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'smt',
  },
  {
    type: 'gmt',
    name: 'Report Date Time(GMT)',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'gmt',
  },
];

const PositionReports = ({
  setIsAccessDenied,
  loading = false,
  setLoading = () => {},
  ga4EventTrigger,
}) => {
  const tableRef = useRef(null);
  const { filterData, loadingOptions, error, setError } = useContext(TechnicalReportContext);
  const [postionReports, setPostionReports] = useState([]);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, settotalCount] = useState(0);

  const columns = useMemo(
    () => [
      {
        Header: 'No.',
        accessor: (row) => formatValue(row.id),
        sticky: 'left',
        id: 'position.id',
        name: 'no',
        type: 'text',
        maxWidth: 50,
      },
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link
            to={`/vessel/ownership/details/${row.vessel_ownership_id}`}
            className="button-link"
            onClick={() =>
              ga4EventTrigger('View Vessel', 'Vessel Position Report - Link', row.name)
            }
          >
            {formatValue(row.name)}
          </Link>
        ),
        id: 'position.vessel',
        name: 'vessel',
        type: 'text',
      },
      {
        Header: 'View Report',
        id: 'position.viewReport',
        type: 'item',
        accessor: (row) => (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
            aria-hidden="true"
          >
            <Link
              className="button-link"
              to={`/vessel/report/technical/position/${
                row.vessel_ownership_id
              }/compare?gmt=${formatDate(row.gmt, 'YYYY-MM-DD')}`}
              onClick={() =>
                ga4EventTrigger(
                  'View Position Report',
                  'Vessel Position Report - Link',
                  `${row.name} and ${formatDate(row.gmt, 'DD MMM YYYY')}`,
                )
              }
            >
              View
            </Link>
          </div>
        ),
        sticky: 'left',
        disableSortBy: true,
      },
      {
        Header: 'Report Type',
        accessor: (row) => formatValue(row.report_type),
        id: 'position.report_type',
        name: 'reportType',
        type: 'text',
      },
      {
        Header: 'Report Date Time(SMT)',
        accessor: (row) => formatDate(row.smt, 'DD MMM YYYY HH:mm'),
        id: 'position.smt',
        name: 'smt',
        type: 'date',
        maxWidth: 120,
      },
      {
        Header: 'Report Date Time(GMT)',
        accessor: (row) => formatDate(row.gmt, 'DD MMM YYYY HH:mm'),
        id: 'position.gmt',
        name: 'gmt',
        type: 'date',
        maxWidth: 120,
      },
    ],
    [],
  );
  const getSortByParams = (paginationParams) => {
    const sortByObj = [...paginationParams.sortBy];
    const searchId = SearchTypes.filter(
      (type) => columns.find((item) => item.id === sortByObj[0].id)?.Header == type.name,
    )
      .map((type) => type.queryKey)
      .toString();
    const formObj = [
      {
        id: searchId,
        desc: sortByObj[0].desc,
      },
    ];
    paginationParams = { ...paginationParams, sortBy: formObj };
    return paginationParams;
  };

  const fetchData = useCallback(
    async (paginationParams) => {
      setLoading(true);
      try {
        setPostionReports([]);
        if (paginationParams.sortBy.length) {
          paginationParams = getSortByParams(paginationParams);
        }

        let pageFilterData = '';
        if (filterData.startDate || filterData.endDate) {
          pageFilterData = `gmt=${filterData.startDate},${filterData.endDate}`;
        }

        const response = await vesselService.getTechnicalReports(
          'position',
          !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
          paginationParams,
          pageFilterData,
        );
        setPostionReports(response.data.results);
        const total = response.data.total;
        const pageSize = paginationParams.pageSize;
        setPageCount(Math.ceil(total / pageSize));
        settotalCount(total);
        setError(null);
      } catch (error) {
        if (error?.message.includes('403')) {
          setIsAccessDenied(true);
        }
        if (error?.code === 'ERR_NETWORK') {
          setError('oops, request timeout, please apply filter to see results');
        }
        if (httpService.axios.isCancel(error)) {
          return;
        }
        console.log('get vessel failed', error);
      }
      setLoading(false);
    },
    [filterData],
  );

  const eventTracker = (type, value) => {
    if (type === 'Pagination') {
      ga4EventTrigger('Change Row Numbers', 'Vessel Position Report - List', value);
    }
    if (type === 'Sorting') {
      ga4EventTrigger('Sort', 'Vessel Position Report - List', value);
    }
  };

  return (
    <Tab.Container>
      <Row className="no-print">
        <Col>
          {error && <ErrorAlert message={error} />}
          <TechnicalReportTable
            column={columns}
            reportData={postionReports}
            fetchData={fetchData}
            tableRef={tableRef}
            isLoading={loading || loadingOptions}
            pageCount={pageCount}
            totalCount={totalCount}
            eventTracker={eventTracker}
          />
        </Col>
      </Row>
    </Tab.Container>
  );
};

export default PositionReports;
