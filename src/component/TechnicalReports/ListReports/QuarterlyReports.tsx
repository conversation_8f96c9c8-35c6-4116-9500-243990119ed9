import { QUERY_TYPE_LIKE, QUERY_TYPE_RANGE } from '../../../util/search-types';
import React, { useCallback, useMemo, useRef, useState, useContext } from 'react';
import { Col, Row, Tab } from 'react-bootstrap';
import httpService from '../../../service/http-service';
import vesselService from '../../../service/vessel-service';
import { formatDate, formatValue } from '../../../util/view-utils';
import TechnicalReportTable from '../TechnicalReportTable';
import { Link } from 'react-router-dom';
import _ from 'lodash';
import { TechnicalReportContext } from '../../../context/TechnicalReportContext';
import ErrorAlert from '../../ErrorAlert';

const SearchTypes = [
  {
    type: 'id',
    name: 'No.',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'id',
  },
  {
    type: 'vessel_ownership_id',
    name: 'Vessel',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'name',
  },
  {
    type: 'report_quarter',
    name: 'Report Quarter',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'quarter',
  },
  {
    type: 'report_submit_date',
    name: 'Report Submit Date',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'report_date',
  },
];

const QuarterlyReports = ({
  setIsAccessDenied,
  loading = false,
  setLoading = () => {},
  ga4EventTrigger,
}) => {
  const tableRef = useRef(null);
  const { filterData, loadingOptions, error, setError } = useContext(TechnicalReportContext);
  const [quarterlyReports, setQuarterlyReports] = useState([]);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, settotalCount] = useState(0);
  const columns = useMemo(
    () => [
      {
        Header: 'No.',
        accessor: (row) => formatValue(row.id),
        sticky: 'left',
        id: 'quarterlyReport.id',
        name: 'no',
        type: 'text',
        maxWidth: 50,
      },
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link
            to={`/vessel/ownership/details/${row.vessel_ownership_id}`}
            className="button-link"
            onClick={() =>
              ga4EventTrigger('View Vessel', 'Vessel Quarterly Report - Link', row.name)
            }
          >
            {formatValue(row.name)}
          </Link>
        ),
        id: 'quarterlyReport.vessel',
        name: 'vessel',
        type: 'text',
      },
      {
        Header: 'View Report',
        id: 'quarterlyReport.viewReport',
        type: 'item',
        accessor: (row) => (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
            aria-hidden="true"
          >
            <Link
              className="button-link"
              to={`/vessel/report/technical/quarterly/${
                row.vessel_ownership_id
              }/compare?report_date=${formatDate(row.report_date, 'YYYY-MM-DD')}`}
              onClick={() =>
                ga4EventTrigger(
                  'View Quarterly Report',
                  'Vessel Quarterly Report - Link',
                  `${row.name} and ${formatDate(row.report_date, 'DD MMM YYYY')}`,
                )
              }
            >
              View
            </Link>
          </div>
        ),
        sticky: 'left',
        disableSortBy: true,
      },
      {
        Header: 'Report Quarter',
        accessor: (row) =>
          row.quarter
            ? formatDate(row.quarter, 'Qo') + ' Quarter ' + formatDate(row.quarter, 'YYYY')
            : row.report_json.qtr,
        id: 'quarterlyReport.report_quarter',
        name: 'report_quarter',
        type: 'date',
        maxWidth: 120,
      },
      {
        Header: 'Report Submit Date',
        accessor: (row) => formatDate(row.report_date),
        id: 'quarterlyReport.report_submit_date',
        name: 'report_submit_date',
        type: 'date',
        maxWidth: 120,
      },
    ],
    [],
  );
  const getSortByParams = (paginationParams) => {
    const sortByObj = [...paginationParams.sortBy];
    const searchId = SearchTypes.filter(
      (type) => columns.find((item) => item.id === sortByObj[0].id)?.Header == type.name,
    )
      .map((type) => type.queryKey)
      .toString();
    const formObj = [
      {
        id: searchId,
        desc: sortByObj[0].desc,
      },
    ];
    paginationParams = { ...paginationParams, sortBy: formObj };
    return paginationParams;
  };

  const fetchData = useCallback(
    async (paginationParams) => {
      setLoading(true);
      try {
        setQuarterlyReports([]);
        if (paginationParams.sortBy.length) {
          paginationParams = getSortByParams(paginationParams);
        }

        let pageFilterData = '';
        if (filterData.startDate || filterData.endDate) {
          pageFilterData = `report_date=${filterData.startDate},${filterData.endDate}`;
        }

        const response = await vesselService.getTechnicalReports(
          'quarterly',
          !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
          paginationParams,
          pageFilterData,
        );
        setQuarterlyReports(response.data.results);
        const total = response.data.total;
        const pageSize = paginationParams.pageSize;
        setPageCount(Math.ceil(total / pageSize));
        settotalCount(total);
        setError(null);
      } catch (error) {
        if (error?.message.includes('403')) {
          setIsAccessDenied(true);
        }
        if (error?.code === 'ERR_NETWORK') {
          setError('oops, request timeout, please apply filter to see results');
        }
        if (httpService.axios.isCancel(error)) {
          return;
        }
        console.log('get vessel failed', error);
      }
      setLoading(false);
    },
    [filterData],
  );

  const eventTracker = (type, value) => {
    if (type === 'Pagination') {
      ga4EventTrigger('Change Row Numbers', 'Vessel Quarterly Report - List', value);
    }
    if (type === 'Sorting') {
      ga4EventTrigger('Sort', 'Vessel Quarterly Report - List', value);
    }
  };

  return (
    <Tab.Container>
      {error && <ErrorAlert message={error} />}
      <Row className="no-print">
        <Col>
          <TechnicalReportTable
            column={columns}
            reportData={quarterlyReports}
            fetchData={fetchData}
            tableRef={tableRef}
            isLoading={loading || loadingOptions}
            pageCount={pageCount}
            totalCount={totalCount}
            eventTracker={eventTracker}
          />
        </Col>
      </Row>
    </Tab.Container>
  );
};

export default QuarterlyReports;
