import { QUERY_TYPE_LIKE, QUERY_TYPE_RANGE } from '../../../util/search-types';
import React, { useCallback, useMemo, useRef, useState, useContext } from 'react';
import { Col, Row, Tab } from 'react-bootstrap';
import httpService from '../../../service/http-service';
import vesselService from '../../../service/vessel-service';
import { formatDate, formatValue, parseDMS } from '../../../util/view-utils';
import TechnicalReportTable from '../TechnicalReportTable';
import { Link } from 'react-router-dom';
import _ from 'lodash';
import { TechnicalReportContext } from '../../../context/TechnicalReportContext';
import ErrorAlert from '../../ErrorAlert';

const SearchTypes = [
  {
    type: 'id',
    name: 'No.',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'id',
  },
  {
    type: 'vessel_ownership_id',
    name: 'Vessel',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'name',
  },
  {
    type: 'report_date',
    name: 'Report Submit Date',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'report_date',
  },
];

const PerformanceReports = ({
  setIsAccessDenied,
  loading = false,
  setLoading = () => {},
  ga4EventTrigger,
}) => {
  const tableRef = useRef(null);
  const { filterData, loadingOptions, error, setError } = useContext(TechnicalReportContext);
  const [meReports, setMeReports] = useState([]);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, settotalCount] = useState(0);
  const columns = useMemo(
    () => [
      {
        Header: 'No.',
        accessor: (row) => formatValue(row.id),
        sticky: 'left',
        id: 'meReport.id',
        name: 'no',
        type: 'text',
        maxWidth: 30,
      },
      {
        Header: 'Vessel',
        accessor: (row) => (
          <Link
            to={`/vessel/ownership/details/${row.vessel_ownership_id}`}
            className="button-link"
            onClick={() =>
              ga4EventTrigger('View Vessel', 'Vessel Performance Report - Link', row.name)
            }
          >
            {formatValue(row.name)}
          </Link>
        ),
        id: 'meReport.vessel',
        name: 'vessel',
        type: 'text',
        maxWidth: 60,
      },
      {
        Header: 'View Report',
        id: 'meReport.viewReport',
        type: 'item',
        maxWidth: 40,
        accessor: (row) => (
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
            aria-hidden="true"
          >
            <Link
              className="button-link"
              to={`/vessel/report/technical/performance/${
                row.vessel_ownership_id
              }/compare?report_date=${formatDate(row.report_date, 'YYYY-MM-DD')}`}
              onClick={() =>
                ga4EventTrigger(
                  'View Performance Report',
                  'Vessel Performance Report - Link',
                  `${row.name} and ${formatDate(row.report_date, 'DD MMM YYYY')}`,
                )
              }
            >
              View
            </Link>
          </div>
        ),
        disableSortBy: true,
      },
      {
        Header: 'Report Submit Date',
        accessor: (row) => formatDate(row.report_date),
        id: 'meReport.report_date',
        name: 'report_date',
        type: 'date',
        maxWidth: 80,
      },
      {
        Header: 'Next ETA',
        accessor: (row) => formatDate(row.report_json.eta, 'DD MMM YYYY HH:mm '),
        id: 'meReport.next_eta',
        name: 'next_eta',
        type: 'date',
        disableSortBy: true,
        maxWidth: 80,
      },
      {
        Header: 'Geo Position',
        accessor: ({report_json: {position}, name, report_date}) => (
          <div className="ship-report-wrapper__underline">
            {position ? (
              <a
                className="button-link"
                target="_blank"
                href={`http://maps.google.com/maps?q=${parseDMS({
                  latitude: `${position.slice(0,2)}.${position.slice(2,4)}.0.${position.slice(4,5)}`,
                  longitude: `${position.slice(5,8)}.${position.slice(8,10)}.0.${position.slice(10,11)}`
                })}`}
                rel="noreferrer"
                onClick={() =>
                  ga4EventTrigger(
                    'View Position Report Coordinates',
                    'Vessel Performance Report - Link',
                    `${name} and ${formatDate(report_date, 'DD MMM YYYY')}`,
                  )
                }
              >
                {position}
              </a>
            ) : (
              '- - -'
            )}
          </div>
        ),
        id: 'meReport.geo_position',
        name: 'geo_position',
        type: 'item',
        maxWidth: 60,
        disableSortBy: true,
      },

      {
        Header: 'Speed',
        accessor: (row) => row.report_json.speed,
        id: 'meReport.speed',
        name: 'speed',
        type: 'text',
        maxWidth: 30,
        disableSortBy: true,
      },

      {
        Header: 'Remarks',
        accessor: (row) => <div className="text-truncate">{row.report_json.remarks}</div>,
        id: 'meReport.remarks',
        name: 'remarks',
        type: 'text',
        disableSortBy: true,
        maxWidth: 300,
      },
    ],
    [],
  );
  const getSortByParams = (paginationParams) => {
    const sortByObj = [...paginationParams.sortBy];
    const searchId = SearchTypes.filter(
      (type) => columns.find((item) => item.id === sortByObj[0].id)?.Header == type.name,
    )
      .map((type) => type.queryKey)
      .toString();
    const formObj = [
      {
        id: searchId,
        desc: sortByObj[0].desc,
      },
    ];
    paginationParams = { ...paginationParams, sortBy: formObj };
    return paginationParams;
  };

  const fetchData = useCallback(
    async (paginationParams) => {
      setLoading(true);
      try {
        setMeReports([]);
        if (paginationParams.sortBy.length) {
          paginationParams = getSortByParams(paginationParams);
        }

        let pageFilterData = '';
        if (filterData.startDate || filterData.endDate) {
          pageFilterData = `report_date=${filterData.startDate},${filterData.endDate}`;
        }

        const response = await vesselService.getTechnicalReports(
          'performance',
          !_.isEmpty(filterData) && filterData?.vessel[0]?.id,
          paginationParams,
          pageFilterData,
        );
        setMeReports(response.data.results);
        const total = response.data.total;
        const pageSize = paginationParams.pageSize;
        setPageCount(Math.ceil(total / pageSize));
        settotalCount(total);
        setError(null);
      } catch (error) {
        if (error?.message.includes('403')) {
          setIsAccessDenied(true);
        }
        if (error?.code === 'ERR_NETWORK') {
          setError('oops, request timeout, please apply filter to see results');
        }
        if (httpService.axios.isCancel(error)) {
          return;
        }
        console.log('get vessel failed', error);
      }
      setLoading(false);
    },
    [filterData],
  );

  const eventTracker = (type, value) => {
    if (type === 'Pagination') {
      ga4EventTrigger('Change Row Numbers', 'Vessel Performance Report - List', value);
    }
    if (type === 'Sorting') {
      ga4EventTrigger('Sort', 'Vessel Performance Report - List', value);
    }
  };

  return (
    <Tab.Container>
      {error && <ErrorAlert message={error} />}
      <Row className="no-print">
        <Col>
          <TechnicalReportTable
            column={columns}
            reportData={meReports}
            fetchData={fetchData}
            tableRef={tableRef}
            isLoading={loading || loadingOptions}
            pageCount={pageCount}
            totalCount={totalCount}
            eventTracker={eventTracker}
          />
        </Col>
      </Row>
    </Tab.Container>
  );
};

export default PerformanceReports;
