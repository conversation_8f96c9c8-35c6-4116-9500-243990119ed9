import React, { useContext, useEffect, useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import CustomFormBuilder from '../../customComponent/CustomFormBuilder';
import _ from 'lodash';
import { VesselContext } from '../../../context/VesselContext';
import CustomOverlayLoader from '../../customComponent/CustomOverlayLoader';
import { getPortsByCountry } from '../../../service/reference-service';
import vesselService from '../../../service/vessel-service';
import ErrorAlert from '../../ErrorAlert';
import { formatNumber, formatValue } from '../../../util/view-utils';
import moment from 'moment';
import { validateForm } from '../../../util/validateFormFields';
import { TOTAL_CARGO_ERROR_MESSAGES } from '../../../constants/error-messages';
import { bdnFuels } from '../../../constants/bdn-fuels';
const CustomEditModal = ({ handleModalSubmit, report }) => {
  const {
    vesselName,
    editData,
    showEditModal,
    setShowEditModal,
    compareReportData,
    countries,
    ga4EventTrigger = () => {},
    reportVersion = 1,
  } = useContext(VesselContext);
  const [formGenerateData, setFormGenerateData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState({});
  const [validationError, setValidationError] = useState();
  const [errors, setErrors] = useState({});
  const [isEditable, setIsEditable] = useState(false);
  const [showCard, setShowCard] = useState(true);

  const fetchFlagPorts = async (value) => {
    try {
      setLoading(true);
      const getPortsResponse = await getPortsByCountry(`countryCode=${value?.id}`);
      const options = getPortsResponse?.data?.ports?.map((item) => ({
        id: item.city_code,
        value: item.name,
      }));
      setLoading(false);
      return options;
    } catch (error) {
      setLoading(false);
      return [];
    }
  };

  const intialFormGenerate = async () => {
    const formEditData = { ...editData };
    const compareData = compareReportData[formEditData.group_label][editData?.reportNum - 1];
    const relatedData = [];
    if (['CARGODISCHARGED', 'CARGOLOADED'].includes(formEditData.name)) {
      formEditData.showInlineText = `Once you click ‘Save’, the system will automatically compute the total cargo weight`;
    }
    if (formEditData.validation_json.combination) formEditData.value = formEditData.original_value;
    if (formEditData.validation_json.subType === 'position') {
      formEditData.validation_json.latitude = formEditData.value.substring(0, 5);
      formEditData.validation_json.longitude = formEditData.value.substring(5);
    }
    if (formEditData.validation_json.subType === 'gmt') {
      formEditData.validation_json.smt = compareData?.find(
        (listParams) => listParams.name === 'SMT',
      )?.value;
    }
    if (
      formEditData.validation_json.related &&
      formEditData.validation_json.subType != 'countryport'
    ) {
      formEditData.validation_json.related?.map((relItem) => {
        if (['fuel_sulphur_content', 'rob'].includes(formEditData.group_label)) {
          const compareData = compareReportData['consumption'][editData?.reportNum - 1];
          if (compareData?.some((compItem) => compItem.name === relItem)) {
            relatedData.push(compareData?.find((compItem) => compItem.name === relItem));
          }
          const robCompData = compareReportData['rob'][editData?.reportNum - 1];
          if (robCompData?.some((compItem) => compItem.name === relItem)) {
            relatedData.push(robCompData?.find((compItem) => compItem.name === relItem));
          }
          if (formEditData.name === 'OTHERFUEL3' || formEditData.name === 'OTHERFUEL3WEIGHT') {
            const compareData = compareReportData['fuel_sulphur_content'][editData?.reportNum - 1];
            if (compareData?.some((compItem) => compItem.name === relItem)) {
              let otherFuelTypeForSulphur = compareData?.find(
                (compItem) => compItem.name === relItem,
              );
              relatedData.push(otherFuelTypeForSulphur);
            }
          }
        } else if (bdnFuels.includes(formEditData.value)) {
          const compareData = compareReportData[formEditData.group_label][editData?.reportNum - 1];
          relatedData.push(
            compareData?.find(
              (compItem) =>
                compItem.name === relItem &&
                (compItem.path_v3.includes('bdn') ||
                  compItem.name.includes('CONSUMED') ||
                  compItem.name.includes('APPLICABLE')),
            ),
          );
        } else {
          const compareData = compareReportData[formEditData.group_label][editData?.reportNum - 1];
          relatedData.push(compareData?.find((compItem) => compItem.name === relItem));
          if (relItem.includes('BOILERFUEL3TYPE')) {
            relatedData.push(
              compareData?.find((compItem) => compItem?.path_v3?.includes('boiler.bdn.fuel_name')),
            );
          }
        }
      });
    }
    if (formEditData.validation_json.type === 'typeahead') {
      if (formEditData.validation_json.subType === 'countryport') {
        if (
          ['COUNTRY', 'OTHERNEXTCOUNTRY', 'LASTCOUNTRY', 'NEXTCOUNTRY'].includes(formEditData.name)
        ) {
          const countryData = countries.find((item) => item.value === formEditData.value);
          formEditData.validation_json.countryValue = countryData ? [countryData] : [];
          formEditData.validation_json.countryLabel = formEditData.label;
          const portAttributeData = formEditData.validation_json.related?.map((relItem) => {
            const data = compareData?.find((compItem) => compItem.name === relItem);
            return data;
          });

          formEditData.validation_json.countryPortInfo = [
            formEditData.value,
            portAttributeData[0].value,
          ].join(', ');

          if (countryData) {
            const portOptions = await fetchFlagPorts(countryData);
            const portData =
              !_.isEmpty(portOptions) &&
              portOptions?.find((item) => item.value === portAttributeData[0].value);
            formEditData.validation_json.portValue = portData ? [portData] : [];
            formEditData.validation_json.portLabel = portAttributeData[0].label;
          }
        }

        if (['PORT', 'OTHERNEXTPORT', 'LASTPORT', 'NEXTPORT'].includes(formEditData.name)) {
          const countryAttributes = formEditData.validation_json.related?.map((relItem) => {
            const data = compareData?.find((compItem) => compItem.name === relItem);
            return data;
          });
          formEditData.validation_json.countryPortInfo = [
            countryAttributes[0].value,
            formEditData.value,
          ].join(', ');

          const countryData = countries.find((item) => item.value === countryAttributes[0].value);

          formEditData.validation_json.countryValue = countryData ? [countryData] : [];
          formEditData.validation_json.countryLabel = countryAttributes[0].label;
          if (countryData) {
            const portOptions = await fetchFlagPorts(countryData);
            const portData =
              !_.isEmpty(portOptions) &&
              portOptions?.find((item) => item.value === formEditData.value);

            formEditData.validation_json.portValue = portData ? [portData] : [];
            formEditData.validation_json.portLabel = formEditData.label;
          }
        }
      } else {
        formEditData.validation_json.options = formEditData.validation_json.enum.map((enumItem) => {
          return { id: enumItem, value: enumItem };
        });
      }
    }

    const relatedKeys = [
      'CEAPPLICABLE',
      'IGGAPPLICABLE',
      'CEAPPLICABLE_RO',
      'IGGAPPLICABLE_RO',
      'CEAPPLICABLE_IC',
      'IGGAPPLICABLE_IC',
    ];

    const hasNoValue = relatedKeys.some(
      (key) =>
        formEditData.validation_json?.related?.includes(key) &&
        compareData.find((item) => item.name === key && item.value === 'No'),
    );

    if (hasNoValue) {
      formEditData.validation_json.disabled = true;
    } else {
      formEditData.validation_json.disabled = false;
      if (
        (formEditData.path?.includes('cargo_engine') ||
          formEditData.path_v2?.includes('cargo_engine') ||
          formEditData.path?.includes('inert_gas_generator') ||
          formEditData.path_v2?.includes('inert_gas_generator') ||
          formEditData.path?.includes('cons.ce') ||
          formEditData.path_v2?.includes('cons.ce') ||
          formEditData.path?.includes('cons.igg') ||
          formEditData.path?.includes('cons.igg')) &&
        !formEditData.name?.includes('CEAPPLICABLE') &&
        !formEditData.name?.includes('IGGAPPLICABLE')
      ) {
        const applicableData = compareData.find(
          (item) =>
            item.group_label === formEditData.group_label &&
            relatedKeys.some(
              (key) => formEditData.validation_json.related.includes(key) && item.name === key,
            ),
        );
        if (applicableData?.value === 'No') {
          formEditData.validation_json.disabled = true;
        }
      } else if (
        (formEditData.name.includes('CEAPPLICABLE') ||
          formEditData.name.includes('IGGAPPLICABLE')) &&
        (formEditData.group_label === 'consumption_rescue_operations' ||
          formEditData.group_label === 'consumption_ice_conditions')
      ) {
        formEditData.validation_json.disabled = true;
      }
    }

    relatedData?.map((relList) => {
      const hasNoValue = relatedKeys.some(
        (key) =>
          relList.validation_json?.related?.includes(key) &&
          compareData.find((item) => item.name === key && item.value === 'No'),
      );
      if (hasNoValue) {
        relList.validation_json.disabled = true;
      } else if (relList.path_v3?.includes('bdn')) {
        relList.validation_json.disabled = true;
      } else if (
        relList.group_label === 'consumption_rescue_operations' &&
        (relList.name === 'CEAPPLICABLE_RO' || relList.name === 'IGGAPPLICABLE_RO')
      ) {
        relList.validation_json.disabled = true;
      } else if (
        relList.group_label === 'consumption_ice_conditions' &&
        (relList.name === 'CEAPPLICABLE_IC' || relList.name === 'IGGAPPLICABLE_IC')
      ) {
        relList.validation_json.disabled = true;
      } else {
        relList.validation_json.disabled = false;
      }

      if (formEditData.validation_json.combination) relList.value = relList.original_value;
      if (formEditData.validation_json.subType === 'weightedAvg') {
        relList.validation_json.related = relList.validation_json.related?.filter(
          (rel) => !rel.includes('APPLICABLE'),
        );
        relList.validation_json.disabled = relList.validation_json.subType !== 'weightedAvg';
      }
      //For Fuel Sulphur Content in Voyage Report, only Weighted Fuel3 and Fuel3 Type don't need to disable
      if (
        formEditData.validation_json.disableOther &&
        !['OTHERFUEL3WEIGHT', 'OTHERFUEL3', 'OTHERFUEL3TYPE'].includes(relList.name)
      ) {
        relList.validation_json.disabled = formEditData.validation_json.disableOther;
      }
      if (
        relList.validation_json.type === 'typeahead' &&
        formEditData.validation_json.subType !== 'countryport'
      ) {
        relList.validation_json.options = relList.validation_json.enum.map((enumItem) => {
          return { id: enumItem, value: enumItem };
        });
      }
    });

    const fieldRadioRelation = [];
    const relatedFormData = _.union(relatedData, [formEditData])?.map((relList) => {
      if (
        ['radio', 'typeahead'].includes(relList.validation_json.type) &&
        relList.validation_json.isParent
      ) {
        fieldRadioRelation.push(relList);
      }

      return {
        ...relList,
        label: relList.edit_label,
      };
    });
    if (!_.isEmpty(fieldRadioRelation)) {
      fieldRadioRelation.forEach((item) => {
        const indexParentRelation = _.findIndex(relatedFormData, (o) => {
          return o.name === item.name;
        });
        if (item.validation_json.related) {
          item.validation_json.related.map((eachRelated) => {
            const indexRelation = _.findIndex(relatedFormData, (o) => {
              return o.name === eachRelated;
            });
            if (indexRelation !== -1 && indexParentRelation !== -1) {
              relatedFormData[indexParentRelation].validation_json.radioRel = relatedFormData[
                indexParentRelation
              ].validation_json.radioRel
                ? [
                    ...relatedFormData[indexParentRelation].validation_json.radioRel,
                    {
                      path:
                        relatedFormData[indexRelation].version === 1
                          ? relatedFormData[indexRelation].path
                          : relatedFormData[indexRelation].version === 2
                          ? relatedFormData[indexRelation].path_v2
                          : relatedFormData[indexRelation].path_v3,
                      type: relatedFormData[indexRelation].validation_json.type,
                    },
                  ]
                : [
                    {
                      path:
                        relatedFormData[indexRelation].version === 1
                          ? relatedFormData[indexRelation].path
                          : relatedFormData[indexRelation].version === 2
                          ? relatedFormData[indexRelation].path_v2
                          : relatedFormData[indexRelation].path_v3,
                      type: relatedFormData[indexRelation].validation_json.type,
                    },
                  ];
              relatedFormData[indexRelation].validation_json.pathRel = item.path_v3
                ? item.path_v3
                : item.path_v2
                ? item.path_v2
                : item.path;
              relatedFormData[indexRelation].validation_json.pathRelType =
                item.validation_json.type;
            }
          });
        }
      });
    }

    setFormGenerateData(
      formEditData.validation_json.subType !== 'weightedAvg'
        ? _.orderBy(
            _.orderBy(relatedFormData, [
              (o) => {
                return o.validation_json.isParent;
              },
            ]),
            [
              (o2) => {
                return o2.order;
              },
            ],
          )
        : relatedFormData,
    );
  };

  useEffect(() => {
    if (!_.isEmpty(editData)) {
      intialFormGenerate();
    }
  }, [editData]);

  const extractShowInlineText = (relatedFormData) => {
    return relatedFormData.reduce((result, obj) => {
      if (obj.hasOwnProperty('showInlineText')) {
        result.push(obj.showInlineText);
      }
      return result;
    }, []);
  };
  const getValueByType = (item) => {
    const { path, validation_json, name, path_v2, path_v3 } = item;
    const effectivePath =
      path_v3 && path_v3.trim() !== ''
        ? path_v3
        : path_v2 && path_v2.trim() !== ''
        ? path_v2
        : path;
    switch (validation_json.type) {
      case 'textarea':
      case 'input':
        return formatValue(form[effectivePath], '');
      case 'number':
        return formatNumber(form[effectivePath], 0);
      case 'typeahead':
        if (['PORT', 'OTHERNEXTPORT', 'LASTPORT', 'NEXTPORT'].includes(name)) {
          return form[path][0]?.value;
        } else if (['COUNTRY', 'OTHERNEXTCOUNTRY', 'LASTCOUNTRY', 'NEXTCOUNTRY'].includes(name)) {
          return form[path][0]?.id;
        } else {
          return form[effectivePath][0]?.id;
        }
      case 'coordinate':
        if (validation_json.subType === 'position') {
          let position = '';
          Object.keys(form).forEach((key) => {
            if (['latitude-1', 'latitude-2', 'longitude-2'].includes(key)) {
              position = position.concat(form[key].padStart(2, '0'));
            } else if (key === 'longitude-1') {
              position = position.concat(form[key].padStart(3, '0'));
            } else {
              position = position.concat(form[key]);
            }
          });
          return position;
        }
        return Object.values(form).join(':');
      case 'datetime':
        if (validation_json.subType === 'gmt') {
          if (form.gmt_symbol === '+') {
            return moment(validation_json.smt)
              .add(form.gmt_hrs, 'hours')
              .add(form.gmt_mins, 'minutes')
              .format('YYYY-MM-DD HH:mm:ss');
          } else {
            return moment(validation_json.smt)
              .subtract(form.gmt_hrs, 'hours')
              .subtract(form.gmt_mins, 'minutes')
              .format('YYYY-MM-DD HH:mm:ss');
          }
        } else {
          return form[effectivePath]
            ? moment(form[effectivePath]).format('YYYY-MM-DD HH:mm:ss')
            : null;
        }
      case 'radio':
        return form[effectivePath] === 'Yes' ? 1 : 0;
      case 'time':
        return [form.gmt_hrs, form.gmt_mins].join(':');
      default:
        return '';
    }
  };

  function makeEditObj(editData, updatedFormGeneratedData) {
    return editData.map((item) => {
      const relatedArray = item.validation_json?.related;
      const relatedItemName =
        Array.isArray(relatedArray) && relatedArray.length > 0 ? relatedArray[0] : undefined;

      const relatedItem = relatedItemName
        ? updatedFormGeneratedData.find((data) => data.name === relatedItemName)
        : undefined;

      const valueToCheck =
        form[relatedItem?.path_v3]?.[0]?.value !== ''
          ? form[relatedItem?.path_v3]?.[0]?.value
          : form[relatedItem?.path_v2]?.[0]?.value !== ''
          ? form[relatedItem?.path_v2]?.[0]?.value
          : form[relatedItem?.path]?.[0]?.value;

      if (valueToCheck !== 'Fuel not listed') {
        if (valueToCheck === 'N/A') {
          if (
            item.name.includes('FUEL3TYPE') ||
            item.name.includes('FUEL3EMISSION') ||
            item.name.includes('FUEL3CONSUMED') ||
            item.name.includes('FUEL3CHCONSUMED')
          ) {
            return {
              report_attribute_id: item.report_attribute_id,
              value: item.validation_json.type === 'number' ? 0 : '',
            };
          }
        } else if (item.name.includes('FUEL3TYPE') || item.name.includes('FUEL3EMISSION')) {
          return {
            report_attribute_id: item.report_attribute_id,
            value: item.validation_json.type === 'number' ? 0 : '',
          };
        } else if (item.version !== 1 || !['BUNKERFUEL3', 'OTHERFUEL3'].includes(item.name)) {
          return {
            report_attribute_id: item.report_attribute_id,
            value: getValueByType(item),
          };
        } else {
          // do nothing
        }
      } else {
        return {
          report_attribute_id: item.report_attribute_id,
          value: getValueByType(item),
        };
      }
    });
  }

  const handleConfirm = async (e) => {
    ga4EventTrigger('Save', `Edit Vessel ${report} Report`, editData.edit_label);
    e.preventDefault();
    e.stopPropagation();
    try {
      const formEditData = { ...editData };
      const compareData = compareReportData[formEditData.group_label][editData?.reportNum - 1];
      const updatedFormGeneratedData = [];
      let filteredFormGenerateData = formGenerateData;

      if (
        filteredFormGenerateData.some(
          (item) => item.name === 'OTHERFUEL3' || item.name === 'OTHERFUEL3WEIGHT',
        )
      ) {
        filteredFormGenerateData = formGenerateData.filter(
          (data) =>
            data.name === 'OTHERFUEL3' ||
            data.name === 'OTHERFUEL3TYPE' ||
            data.name === 'OTHERFUEL3WEIGHT',
        );
      }
      filteredFormGenerateData.forEach((item) => {
        if (item.validation_json.subType === 'countryport') {
          const portAttributeData = item.validation_json?.related?.map((relItem) => {
            const data = compareData?.find((compItem) => compItem.name === relItem);
            return data;
          });
          updatedFormGeneratedData.push(portAttributeData[0]);
        }
        if (!item.path_v3 || !item.path_v3.includes('bdn.')) {
          updatedFormGeneratedData.push(item);
        }
      });

      updatedFormGeneratedData.forEach((item) => {
        if (['PORT', 'OTHERNEXTPORT', 'LASTPORT', 'NEXTPORT'].includes(item.name)) {
          item.path = 'port';
        }
        if (['COUNTRY', 'OTHERNEXTCOUNTRY', 'LASTCOUNTRY', 'NEXTCOUNTRY'].includes(item.name)) {
          item.path = 'country';
        }
      });
      let updateErrorlist = {};
      const parentRadio = updatedFormGeneratedData.find(
        (item) => item?.validation_json.type === 'radio' && item?.validation_json.isParent,
      );

      if (parentRadio && form[parentRadio.path] === 'No') {
        setErrors({});
      } else {
        updatedFormGeneratedData.forEach((item) => {
          const effectivePath =
            item.path_v3 && item.path_v3.trim() !== ''
              ? item.path_v3
              : item.path_v2 && item.path_v2.trim() !== ''
              ? item.path_v2
              : item.path;
          if (item.validation_json.subType === 'countryport') {
            updateErrorlist = {
              ...validateForm(
                item.validation_json.subType,
                item,
                updateErrorlist,
                form[item.path],
                item.path,
              ),
            };
          } else if (item.validation_json.type === 'coordinate') {
            if (item.validation_json.subType === 'position') {
              updateErrorlist = {
                ...validateForm(
                  'degree',
                  {
                    label: 'latitude-degree',
                    validation_json: {
                      precision: 2,
                      min: 1,
                      max: 89,
                    },
                  },
                  updateErrorlist,
                  form['latitude-1'],
                  'latitude-1',
                ),
                ...validateForm(
                  'minute',
                  {
                    label: 'latitude-minute',
                    validation_json: { precision: 2, min: 0, max: 59 },
                  },
                  updateErrorlist,
                  form['latitude-2'],
                  'latitude-2',
                ),
                ...validateForm(
                  'degree',
                  {
                    label: 'longitude-degree',
                    validation_json: {
                      precision: 2,
                      min: 0,
                      max: 179,
                    },
                  },
                  updateErrorlist,
                  form['longitude-1'],
                  'longitude-1',
                ),
                ...validateForm(
                  'minute',
                  {
                    label: 'longitude-minute',
                    validation_json: { precision: 2, min: 0, max: 59 },
                  },
                  updateErrorlist,
                  form['longitude-2'],
                  'longitude-2',
                ),
              };
            } else {
              updateErrorlist = {
                ...validateForm(
                  'degree',
                  {
                    label: `${item.label}-degree`,
                    validation_json: {
                      precision: 2,
                      min: 0,
                      max: item.validation_json.subType === 'latitude' ? 89 : 179,
                    },
                  },
                  updateErrorlist,
                  form[`${item.validation_json.subType}-1`],
                  `${item.validation_json.subType}-1`,
                ),
                ...validateForm(
                  'minute',
                  {
                    label: `${item.label}-minute`,
                    validation_json: { precision: 2, min: 0, max: 59 },
                  },
                  updateErrorlist,
                  form[`${item.validation_json.subType}-2`],
                  `${item.validation_json.subType}-2`,
                ),
              };
            }
          } else if (
            item.edit_label?.includes('Other Fuel Type') ||
            item.name.includes('FUEL3EMISSION')
          ) {
            if (
              form[
                updatedFormGeneratedData.find(
                  (data) => data.name === item.validation_json.related[0],
                ).path
              ]?.[0]?.value === 'Fuel not listed'
            ) {
              updateErrorlist = {
                ...validateForm(
                  item.validation_json.type,
                  item,
                  updateErrorlist,
                  form[effectivePath],
                ),
              };
            }
          } else {
            if (item.version !== 1 || !['BUNKERFUEL3', 'OTHERFUEL3'].includes(item.name)) {
              updateErrorlist = {
                ...validateForm(
                  item.validation_json.type,
                  item,
                  updateErrorlist,
                  form[effectivePath],
                ),
              };
            }
          }
        });

        setErrors(updateErrorlist);
      }

      if (_.isEmpty(updateErrorlist)) {
        if (isEditable) {
          const editDataArr = updatedFormGeneratedData.filter((item) => {
            if (reportVersion === 1) {
              return !(item.name.includes('FUEL3TYPE') || item.name.includes('FUEL3EMISSION'));
            }
            return true;
          });
          const eachObj = makeEditObj(editDataArr, updatedFormGeneratedData);

          setLoading(true);
          await vesselService.patchCompareReportField(
            report,
            editData.id,
            eachObj.filter((obj) => obj),
          );
          handleModalSubmit();
          handleCancel();
        } else {
          setValidationError('Please update the value before saving.');
        }
      }
    } catch (error) {
      const errorMessages = {
        LOAD_CARGO_OUT_OF_BOUND: {
          key: 'report_json.cargo.load',
          message: TOTAL_CARGO_ERROR_MESSAGES.EXCEED_MAXIMUM_LIMIT,
        },
        DIS_CARGO_OUT_OF_BOUND: {
          key: 'report_json.cargo.dis',
          message: TOTAL_CARGO_ERROR_MESSAGES.EXCEED_MAXIMUM_LIMIT,
        },
        LOAD_CARGO_OUT_OF_BOUND_SDWT: {
          key: 'report_json.cargo.load',
          message: TOTAL_CARGO_ERROR_MESSAGES.EXCEED_SWDT,
        },
        DIS_CARGO_OUT_OF_BOUND_SDWT: {
          key: 'report_json.cargo.dis',
          message: TOTAL_CARGO_ERROR_MESSAGES.EXCEED_SWDT,
        },
      };

      const errorResponseData = error?.response?.data;
      const errorStatus = error?.response?.status;

      if (errorMessages[errorResponseData]) {
        const { key, message } = errorMessages[errorResponseData];
        setErrors({ [key]: message });
      } else if (errorStatus === 400) {
        setValidationError(`${errorResponseData}. Please try again.`);
      }
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setShowEditModal(false);
    setIsEditable(false);
    setForm({});
    setValidationError();
    setErrors({});
    setFormGenerateData([]);
    setLoading(false);
  };

  useEffect(() => {
    const listener = (event) => {
      if (event.code === 'Enter' || event.code === 'NumpadEnter') {
        handleConfirm(event);
      }
    };
    document.addEventListener('keydown', listener);
    return () => {
      document.removeEventListener('keydown', listener);
    };
  }, [editData, form]);

  return (
    <Modal
      aria-labelledby="contained-modal-title-vcenter"
      data-testid="fml-custom-edit-modal"
      centered
      show={showEditModal}
      dialogClassName="custom-info-edit-modal"
    >
      <Modal.Header>
        <Modal.Title>{`Edit Report ${editData?.reportNum} for ${vesselName}`}</Modal.Title>
      </Modal.Header>
      {validationError && <ErrorAlert message={validationError} />}
      <CustomOverlayLoader active={loading}>
        <Modal.Body>
          <CustomFormBuilder
            form={form}
            setForm={setForm}
            formData={formGenerateData}
            setLoading={setLoading}
            setValidationError={setValidationError}
            errors={errors}
            setErrors={setErrors}
            setIsEditable={setIsEditable}
            reportVersion={reportVersion}
            showCard={showCard}
            setShowCard={setShowCard}
          />
          {!Object.keys(errors).length && extractShowInlineText(formGenerateData).length ? (
            <p className="compare-reports-inline-text">
              {extractShowInlineText(formGenerateData).join(' ')}
            </p>
          ) : (
            <></>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            data-testid="fml-custom-info-modal-cancel-button"
            onClick={handleCancel}
            className={'compare-reports-cancel-btn'}
          >
            Cancel
          </Button>
          <Button
            variant="secondary"
            data-testid="fml-custom-info-modal-confirm-button"
            onClick={handleConfirm}
            disabled={!_.isEmpty(errors)}
            className={!_.isEmpty(errors) ? 'compare-reports-disabled-save-btn' : ''}
          >
            Save
          </Button>
        </Modal.Footer>
      </CustomOverlayLoader>
    </Modal>
  );
};
export default CustomEditModal;
