import React from 'react';
import { But<PERSON>, ButtonToolbar, Dropdown } from 'react-bootstrap';

const CompareToolBar = (props) => {
  const handleButtonClick = (type) => {
    if (type === 'next') {
      if (props.prev > 0) {
        props.setPrev(props.prev - 1);
      } else {
        props.setNext(props.next + 1);
      }
    }
    if (type === 'prev') {
      if (props.next > 0) {
        props.setNext(props.next - 1);
      } else {
        props.setPrev(props.prev + 1);
      }
    }
  };

  return (
    <ButtonToolbar className="toolbar-allignment no-print">
      <Button
        className="mr-2"
        variant="outline-primary"
        disabled={props.prevDisabled}
        onClick={() => handleButtonClick('prev')}
        data-testid="fml-compare-report-prev-button"
      >
        Prev
      </Button>
      <Button
        className="mr-2"
        variant="outline-primary"
        disabled={props.nextDisabled}
        onClick={() => handleButtonClick('next')}
        data-testid="fml-compare-report-next-button"
      >
        Next
      </Button>
      <Dropdown alignRight>
        <Dropdown.Toggle
          variant="outline-primary"
          id="dropdown-more"
          data-testid="fml-compare-report-more-dropdown"
        >
          More
        </Dropdown.Toggle>
        <Dropdown.Menu>
          <Dropdown.Item
            onClick={props.handleVesselDetails}
            data-testid="fml-compare-report-vessel-details-button"
          >
            Vessel Details
          </Dropdown.Item>
          {!['ninety-six-hours'].includes(props.reportTab)?<Dropdown.Item
            onClick={props.handleExportExcel}
            data-testid="fml-compare-report-export-excel"
          >
            Export to Excel
          </Dropdown.Item>:''}
        </Dropdown.Menu>
      </Dropdown>
    </ButtonToolbar>
  );
};

export default CompareToolBar;
