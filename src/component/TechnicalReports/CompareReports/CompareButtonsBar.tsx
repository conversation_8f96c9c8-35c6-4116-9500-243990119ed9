import React from 'react';
import { But<PERSON>, ButtonToolbar } from 'react-bootstrap';

const CompareButtonsBar = (props) => {
  return (
    <ButtonToolbar className="no-print">
      {props.buttons.map((button) => (
        <Button
          size="sm"
          variant="outline-primary"
          className="mr-2"
          key={button.id}
          onClick={button.onClick}
          data-testid={`fml-compare-report-${button.text}-button`}
        >
          {button.text}
        </Button>
      ))}
    </ButtonToolbar>
  );
};

export default CompareButtonsBar;
