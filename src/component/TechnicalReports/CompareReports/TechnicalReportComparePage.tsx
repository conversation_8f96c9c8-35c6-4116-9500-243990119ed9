import _, { get, isNumber } from 'lodash';
import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON>, Col, Container, Row } from 'react-bootstrap';
import { useHistory, useParams } from 'react-router-dom';
import httpService from '../../../service/http-service';
import vesselService from '../../../service/vessel-service';
import { exportTableToExcel } from '../../../util/excel-export';
import getURLParams from '../../../util/getURLParams';
import {
  formatDate,
  formatNumber,
  formatValue,
  getEmailMessage,
  getHighlightColor,
  reportCols,
  transpose,
} from '../../../util/view-utils';
import ScrollArrow from '../../BackToTopButton';
import { BreadcrumbHeader } from '../../BreadcrumpHeader';
import NoResult from '../../NoResult';
import Spinner from '../../Spinner';
import CompareTable from './CompareTable';
import CompareToolBar from './CompareToolBar';
import { SendEmailModal } from '../../SendEmailModal';
import CustomEditModal from './CustomEditModal';
import { VesselContext } from '../../../context/VesselContext';
import jsPDF from 'jspdf';
import { footerSection, headerSection } from '../../../util/pdf-utils';
import AlertModal from '../../customComponent/AlertModal';
import { Dash } from '../../../model/utils';
import { formateStringDate } from '../../EnvironmentalReports/DataMapper/MonthlyMarpolReportsDataMapper';

const TechnicalReportComparePage = ({
  name,
  reportTab,
  setIsAccessDenied,
  controlParameters,
  userEmail = '',
  order: mapping,
  dateLabel,
}) => {
  const { ownershipId, vesselOwnership } = useParams();
  const tableRef = useRef(null);
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const [technicalReports, setTechnicalReports] = useState([]);
  const [nextDisabled, setNextDisabled] = useState(false);
  const [prevDisabled, setPrevDisabled] = useState(false);
  const [prev, setPrev] = useState(0);
  const [next, setNext] = useState(
    sessionStorage.getItem('comparePage') ? parseInt(sessionStorage.getItem('comparePage')) : 0,
  );
  sessionStorage.removeItem('comparePage');
  const [isSendEmailModalVisible, setIsSendEmailModalVisible] = useState(false);
  const [emailContent, setEmailContent] = useState({});
  const {
    setCompareReportData,
    setVesselName,
    ga4EventTrigger = () => {},
    vesselList,
    roleConfig,
  } = useContext(VesselContext);
  const [reportDatMapper, setReportDataMapper] = useState();
  const [data, setData] = useState([]);
  const [order, setOrder] = useState(mapping);

  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const handleClose = () => {
    setShowAlert(false);
    setAlertMessage('');
  };
  const handleShowAlert = (message) => {
    setShowAlert(true);
    setAlertMessage(message);
  };
  function fetchPathAsPerReportVersion(obj, version) {
    let path;
    switch (version) {
      case 1:
        path = obj.path;
        break;
      case 2:
        path = obj.path_v2;
        break;
      case 3:
        path = obj.path_v3;
        break;
      default:
        path = obj.path;
    }

    return path;
  }
  function fetchEditPathAsPerReportVersion(obj, version) {
    let editPath;
    switch (version) {
      case 1:
        editPath = obj.validation_json.edit_path;
        break;
      case 2:
        editPath = obj.validation_json.edit_path_v2;
        break;
      case 3:
        editPath = obj.validation_json.edit_path_v3;
        break;
      default:
        editPath = obj.validation_json.edit_path;
    }

    return editPath;
  }
  function updateForVersion1(obj) {
    const nonEditableFields = [
      'hours',
      'report_json.bunker.lfo',
      'report_json.bunker.fuel3_type',
      'report_json.bunker.fuel3_desc',
      'report_json.cons.lfo.weighted_avg',
      'report_json.cons.fuel3.fuel3_desc',
      'report_json.cons.me.avg',
      'report_json.cons.ge.avg',
      'report_json.cons.boiler.avg',
      'report_json.cons.hfo.total',
      'report_json.cons.hfo.avg',
      'report_json.cons.mgo.total',
      'report_json.cons.mgo.avg',
      'report_json.cons.lfo.total',
      'report_json.cons.lfo.avg',
      'report_json.cons.fuel3.total',
      'report_json.rob.hfo_basis',
      'report_json.rob.mgo_basis',
      'report_json.rob.lfo',
      'report_json.rob.lfo_basis',
      'report_json.rob.fuel_basis',
      'report_json.other.avg_speed',
      'report_json.other.revolution',
      'report_json.other.rpm',
      'report_json.other.me_distance',
      'report_json.other.average_slip',
      'report_json.current.hrs_since_last_report',
      'report_type',
      'edit_report_json.dist.grounddist_ro',
      'report_json.dist.grounddist_ro',
      'edit_report_json.dist.grounddist_ic',
      'report_json.dist.grounddist_ic',
      'report_json.cargo.load_dis_flag',
      'report_json.cargo.sts_flag',
      'edit_report_json.cargo.refrigerated_containers',
      'report_json.cargo.refrigerated_containers',
      'report_json.cons.ce.hfo',
      'report_json.cons.ce.lfo',
      'report_json.cons.ce.fuel3_desc',
      'report_json.cons.ce.fuel3_type',
      'report_json.cons.ce.fuel3_emmission',
      'report_json.cons.ce.cons',
      'report_json.cons.igg.hfo',
      'report_json.cons.igg.lfo',
      'report_json.cons.igg.fuel3',
      'report_json.cons.igg.fuel3_desc',
      'report_json.cons.igg.fuel3_type',
      'report_json.cons.igg.fuel3_emmission',
      'report_json.cons.igg.fuel3_ch_consumed',
      'report_json.cons.ch.lfo',
      'report_json.cons.ch.fuel3_desc',
      'report_json.cons.ch.fuel3_type',
      'report_json.cons.ch.fuel3_emission',
      'report_json.general.grounddist_ro',
      'report_json.general.grounddist_ic',
      'report_json.consumption.cargo_engine.hfo',
      'report_json.consumption.cargo_engine.lfo',
      'report_json.consumption.cargo_engine.fuel3',
      'report_json.consumption.cargo_engine.fuel3_type',
      'report_json.consumption.cargo_engine.fuel3_emmission',
      'report_json.consumption.cargo_engine.fuel3_ch_consumed',
      'report_json.consumption.inert_gas_generator.hfo',
      'report_json.consumption.inert_gas_generator.lfo',
      'report_json.consumption.inert_gas_generator.fuel3',
      'report_json.consumption.inert_gas_generator.fuel3_type',
      'report_json.consumption.inert_gas_generator.fuel3_emmission',
      'report_json.consumption.inert_gas_generator.fuel3_ch_consumed',
      'report_json.consumption.cargo_heating.lfo',
      'report_json.consumption.cargo_heating.fuel3',
      'report_json.consumption.cargo_heating.fuel3_type',
      'report_json.consumption.cargo_heating.fuel3_emission',
      'report_json.rob.ultra_low_hfo',
      'report_json.rob.very_low_hfo',
      'report_json.rob.ultra_low_mgo',
      'report_json.rob.ultra_low_lfo',
      'report_json.rob.very_low_lfo',
      'report_json.rob.high_lfo',
      'report_json.bunker.ultra_low_hfo',
      'report_json.bunker.very_low_hfo',
      'report_json.bunker.ultra_low_mgo',
      'report_json.bunker.ultra_low_lfo',
      'report_json.bunker.very_low_lfo',
      'report_json.bunker.high_lfo',
      'report_json.other.on_shore_pw_supplied',
      'report_json.other.on_shore_pw_duration',
      'report_json.other.on_shore_pw_supplied_zero_emission_tech',
    ];

    const groupLabels = [
      'consumption_rescue_operations',
      'consumption_ice_conditions',
      'consumption_cargo_pumps',
      'consumption_refrigerated_containers',
      'consumption_cargo_cooling',
    ];

    if (
      nonEditableFields.includes(obj.validation_json.edit_path_v2) ||
      nonEditableFields.includes(obj.path_v2) ||
      nonEditableFields.includes(obj.path_v3) ||
      groupLabels.includes(obj.group_label)
    ) {
      return false;
    } else if (obj?.validation_json?.isEditable !== undefined) {
      return obj.validation_json.isEditable;
    } else {
      return true;
    }
  }

  function updateForVersion2(obj) {
    const nonEditableFields = [
      'report_json.rob.ultra_low_hfo',
      'report_json.rob.very_low_hfo',
      // 'report_json.rob.high_hfo',
      'report_json.rob.ultra_low_mgo',
      'report_json.rob.ultra_low_lfo',
      'report_json.rob.very_low_lfo',
      // 'report_json.rob.high_lfo',
      'report_json.bunker.ultra_low_hfo',
      'report_json.bunker.very_low_hfo',
      // 'report_json.bunker.high_hfo',
      'report_json.bunker.ultra_low_mgo',
      'report_json.bunker.ultra_low_lfo',
      'report_json.bunker.very_low_lfo',
      // 'report_json.bunker.high_lfo',
      'report_json.other.on_shore_pw_supplied',
      'report_json.other.on_shore_pw_duration',
      'report_json.other.on_shore_pw_supplied_zero_emission_tech',
    ];

    if (nonEditableFields.includes(obj.path_v3)) {
      return false;
    } else if (obj?.validation_json?.isEditable !== undefined) {
      return obj.validation_json.isEditable;
    } else {
      return true;
    }
  }

  function getRelated(obj) {
    return obj.validation_json.related
      ? reportDatMapper.find((item) => item.name === obj.validation_json.related[0])
      : undefined;
  }

  function createCombinationItem(item, obj, related) {
    const path = fetchPathAsPerReportVersion(obj, item.version);

    const newCombinationItem = _.cloneDeep({
      ...obj,
      report_attribute_id: obj.id,
      value: `${getValueByType(item, path, obj.validation_json.type)}/${
        related && getValueByType(item, related.path, related.validation_json.type)
      }`,
      original_value: getValueByType(item, path, obj.validation_json.type),
      text_color: getHighlightColor(_.get(item, path, 0), controlParameters, path),
    });

    return newCombinationItem;
  }

  function createSingleItem(item, obj) {
    const path = fetchPathAsPerReportVersion(obj, item.version);
    const editPath = fetchEditPathAsPerReportVersion(obj, item.version);

    const newSingleItem = _.cloneDeep({
      ...obj,
      report_attribute_id: obj.id,
      version: item.version,
      value:
        !obj.validation_json.header &&
        (CUSTOM_VALUES?.[reportTab]?.[path]?.(item, obj) ||
          getValueByType(
            item,
            obj.validation_json.type === 'total' ? obj.validation_json.formula : path,
            obj.validation_json.type,
            obj.validation_json,
            obj.validation_json.nullable,
          ) ||
          ''),
      editValue:
        editPath &&
        (getValueByType(
          item,
          obj.validation_json.type === 'total' ? obj.validation_json.formula : editPath,
          obj.validation_json.type,
        ) ||
          ''),
      text_color: CUSTOM_FIELDS?.[reportTab]?.[path]
        ? CUSTOM_FIELDS[reportTab][path](item, obj)
        : getHighlightColor(_.get(item, path, 0), controlParameters, path),
    });

    return newSingleItem;
  }

  function isExcludedForVesselType(singleItem, vesselTypes) {
    let excludedConditions = [];
    let includedConditions = [];

    // Define the conditions for each vessel type
    const conditions = {
      tanker: {
        excluded: [
          (name) => name.startsWith('consumption_cargo_pumps'),
          (name) => name.startsWith('consumption_refrigerated_containers'),
          (name) => name.startsWith('consumption_cargo_cooling'),
        ],
      },
      chemical_tanker: {
        included: [(name) => name.startsWith('consumption_cargo_pumps')],
        excluded: [
          (name) =>
            ['consumption_refrigerated_containers', 'consumption_cargo_cooling'].some((prefix) =>
              name.startsWith(prefix),
            ) ||
            ['cargo_heating', 'cargo_engine', 'inert_gas_generator', 'ch', 'ce', 'igg'].some(
              (cond) => singleItem.path?.includes(cond) || singleItem.path_v2?.includes(cond),
            ) ||
            ['Cargo Heating', 'Cargo Engine', 'Inert Gas Engine'].some((label) =>
              singleItem.label?.includes(label),
            ) ||
            name === singleItem.name,
        ],
      },
      container_ship: {
        included: [(name) => name.startsWith('consumption_refrigerated_containers')],
        excluded: [
          (name) =>
            ['consumption_cargo_pumps', 'consumption_cargo_cooling'].some((prefix) =>
              name.startsWith(prefix),
            ) ||
            ['cargo_heating', 'cargo_engine', 'inert_gas_generator', 'ch', 'ce', 'igg'].some(
              (cond) => singleItem.path?.includes(cond) || singleItem.path_v2?.includes(cond),
            ) ||
            ['Cargo Heating', 'Cargo Engine', 'Inert Gas Engine'].some((label) =>
              singleItem.label?.includes(label),
            ) ||
            name === singleItem.name,
        ],
      },
      gas_and_lng_ship: {
        included: [(name) => name.startsWith('consumption_cargo_cooling')],
        excluded: [
          (name) =>
            ['consumption_cargo_pumps', 'consumption_refrigerated_containers'].some((prefix) =>
              name.startsWith(prefix),
            ) ||
            ['cargo_heating', 'cargo_engine', 'inert_gas_generator', 'ch', 'ce', 'igg'].some(
              (cond) => singleItem.path?.includes(cond) || singleItem.path_v2?.includes(cond),
            ) ||
            ['Cargo Heating', 'Cargo Engine', 'Inert Gas Engine'].some((label) =>
              singleItem.label?.includes(label),
            ) ||
            name === singleItem.name,
        ],
      },
      oil_tanker: {
        included: [(name) => name.startsWith('consumption_cargo_pumps')],
        excluded: [
          (name) =>
            ['consumption_refrigerated_containers', 'consumption_cargo_cooling'].some((prefix) =>
              name.startsWith(prefix),
            ) ||
            ['cargo_heating', 'cargo_engine', 'inert_gas_generator', 'ch', 'ce', 'igg'].some(
              (cond) => singleItem.path?.includes(cond) || singleItem.path_v2?.includes(cond),
            ) ||
            ['Cargo Heating', 'Cargo Engine', 'Inert Gas Engine'].some((label) =>
              singleItem.label?.includes(label),
            ) ||
            name === singleItem.name,
        ],
      },
    };

    vesselTypes.forEach((vesselType) => {
      if (conditions[vesselType]) {
        excludedConditions = [...excludedConditions, ...(conditions[vesselType].excluded || [])];
        includedConditions = [...includedConditions, ...(conditions[vesselType].included || [])];
      }
    });

    // Remove excluded conditions that overlap with included conditions
    excludedConditions = excludedConditions.filter(
      (excludeCond) =>
        !includedConditions.some(
          (includeCond) => includeCond.toString() === excludeCond.toString(),
        ),
    );

    let newOrder = order.filter(({ name }) => includedConditions.some((condition) => condition(name)));
    newOrder = order.filter(({ name }) => !excludedConditions.some((condition) => condition(name)));

    setOrder(newOrder);
    return false;
  }

  useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        const response = await vesselService.getReportDataMapper(reportTab, ownershipId);
        setReportDataMapper(response.data.result);
      } catch (e) {
        setLoading(false);
        console.log(e, 'Unable to load attributes');
      }
    })();
  }, []);

  const getValueByType = (item, path, type, validationJson, nullable = false) => {
    const value = _.get(item, path);
    switch (type) {
      case 'number':
        if (nullable) return value;
        return formatNumber(value, undefined, validationJson?.precision);
      case 'datetime':
        return value?.includes(' ') ? formateStringDate(value) : formatDate(value, 'DD MMM YYYY HH:mm');
      case 'date':
        return formatDate(value, 'DD MMM YYYY');
      case 'radio':
        if(!path) return Dash;
        return Number(value) === 1 ? 'Yes' : 'No';
      case 'total':
        const totalFormula = _.sumBy(path.split('+'), (sumItem) => Number(_.get(item, sumItem, 0)));
        return formatNumber(totalFormula);
      default:
        if (nullable) return value;
        return formatValue(value);
    }
  };
  const CUSTOM_FIELDS = {
    position: {
      'report_json.stratumfive.distance': (item) => {
        const variation = get(item, 'report_json.stratumfive.variation');
        if (!isNumber(variation) || !variation) return;
        if (variation > 5) {
          return 'text-danger-color';
        } else if (variation > 2) {
          return 'text-warning-color';
        } else {
          return 'text-success-color';
        }
      },
    },
    voyage: {
      'report_json.stratumfive.distance': (item) => {
        const variation = get(item, 'report_json.stratumfive.variation');
        const parsedVariation = variation ? parseInt(variation) : null;

        if (!isNumber(parsedVariation)) return;
        if (parsedVariation > 5) {
          return 'text-danger-color';
        } else if (parsedVariation > 2) {
          return 'text-warning';
        } else {
          return 'text-success';
        }
      },
    },
  };
  const CUSTOM_VALUES = {
    position: {
      'report_json.stratumfive.has_variation': (item, attribute) => {
        const value = get(item, attribute.path);
        if (value === 'Yes') {
          return (
            <>
              Yes{' '}
              <Button
                size="sm"
                onClick={() => {
                  if (parseInt(next) > 0) {
                    sessionStorage.setItem('comparePage', next);
                  }
                  history.push(
                    `/vessel/report/technical/voyage-movement?vessel_ownership_id=${item.vessel_ownership_id}&gmt=,${item.gmt}&stratum-feed=true`,
                  );
                }}
                variant="outline-primary"
              >
                Compare Voyage
              </Button>
            </>
          );
        }
        return value;
      },
    },
    voyage: {
      'report_json.stratumfive.distance': (item, attribute) => {
        const value = get(item, attribute.path);
        return parseInt(value);
      },
      'report_json.stratumfive.has_variation': (item, attribute) => {
        const value = get(item, attribute.path);
        const reportDeparture = item?.report_json?.stratumfive?.previous_report_gmt
          ? item.report_json.stratumfive.previous_report_gmt
          : '';
        if (value === 'Yes') {
          return (
            <>
              {value}{' '}
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => {
                  if (parseInt(next) > 0) {
                    sessionStorage.setItem('comparePage', next);
                  }
                  history.push(
                    `/vessel/report/technical/voyage-movement?vessel_ownership_id=${item.vessel_ownership_id}&gmt=${reportDeparture},${item.gmt}&stratum-feed=true&report-type=voyage`,
                  );
                }}
              >
                Compare Voyage
              </Button>
            </>
          );
        }
        return value;
      },
    },
  };

  useEffect(() => {
    if (!_.isEmpty(reportDatMapper) && !_.isEmpty(technicalReports) && !_.isEmpty(vesselList)) {
      const mapperData = _.cloneDeep(reportDatMapper);
      const groupedJson = {};
      const vesselTypes = reportDatMapper[0]?.vessel_type_group ?? null;

      order.forEach((item) => {
        groupedJson[item.name] = _.filter(mapperData, {
          group_label: item.name,
        });
      });
      const groupedJsonOriginal = _.cloneDeep(groupedJson);

      Object.keys(groupedJson).forEach((eachItem) => {
        const records = technicalReports.map((item) => {
          const createdItems = groupedJson[eachItem]
            .map((obj) => {
              if (item.version < 2) {
                obj.validation_json.isEditable = updateForVersion1(obj);
              } else if (item.version === 2) {
                obj.validation_json.isEditable = updateForVersion2(obj);
              } else {
                obj.validation_json.isEditable = groupedJsonOriginal[eachItem]?.find(
                  (et) => et.name === obj.name,
                ).validation_json.isEditable;
              }

              const related = getRelated(obj);
              let singleItem;

              if (obj.validation_json.combination) {
                singleItem = createCombinationItem(item, obj, related);
              } else {
                singleItem = createSingleItem(item, obj);
              }

              if (vesselTypes && isExcludedForVesselType(singleItem, vesselTypes)) {
                return null;
              }
              return singleItem;
            })
            .filter(Boolean);

          // LFO and Fuel3 value adjustment for version 1 reports
          createdItems.forEach((singleItem) => {
            if (
              singleItem.group_label === 'consumption' ||
              singleItem.group_label === 'consumption_cargo_cooling' ||
              singleItem.group_label === 'consumption_cargo_pumps' ||
              singleItem.group_label === 'consumption_refrigerated_containers' ||
              singleItem.group_label === 'consumption_rescue_operations' ||
              singleItem.group_label === 'consumption_ice_conditions'
            ) {
              if (singleItem.name.includes('LFO') && item.version < 2) {
                const pathV2Pattern = singleItem.path_v2.split('.').slice(-3, -1).join('.');

                const fuel3LFOObj = createdItems
                  .flat()
                  .find(
                    (record) =>
                      record.label === 'Fuel3 Type' &&
                      record.group_label === singleItem.group_label &&
                      record.path_v2.includes(pathV2Pattern) &&
                      record.value === 'Light Fuel Oil',
                  );
                const v2PatternSplit = pathV2Pattern.split('.');
                const lastPath = v2PatternSplit[v2PatternSplit.length - 1];
                const firstPath = v2PatternSplit[0];
                let fuelValue = 'fuel3';
                if (['DEPARTURE', 'ARRIVAL'].includes(technicalReports[0].report_type)) {
                  fuelValue = 'fuel3_desc';
                }
                let fuel3LFOCHObj;
                if (['ch', 'cargo_heating'].includes(lastPath)) {
                  fuel3LFOCHObj = createdItems
                    .flat()
                    .find(
                      (record) =>
                        record.label === 'Fuel3 Type' &&
                        record.group_label === singleItem.group_label &&
                        record.path_v2.includes(`${firstPath}.boiler.${fuelValue}`) &&
                        record.value === 'Light Fuel Oil',
                    );
                }
                if (fuel3LFOCHObj) {
                  const fuel3ObjCH = createdItems.flat().find((record) => {
                    return (
                      record.label.includes('Fuel3') &&
                      record.group_label === singleItem.group_label &&
                      record.path_v2.includes(pathV2Pattern)
                    );
                  });
                  if (fuel3ObjCH) {
                    singleItem.value = fuel3ObjCH.value;
                    singleItem.editValue = fuel3ObjCH.editValue;
                    fuel3ObjCH.value = '0.00';
                  }
                }
                if (fuel3LFOObj) {
                  const fuel3Obj = createdItems
                    .flat()
                    .find(
                      (record) =>
                        record.name.includes('CONSUMED') &&
                        record.group_label === singleItem.group_label &&
                        record.path_v2.includes(pathV2Pattern),
                    );

                  if (fuel3Obj) {
                    singleItem.value = fuel3Obj.value;
                    singleItem.editValue = fuel3Obj.editValue;
                    fuel3Obj.value = '0.00';
                  }
                }
              }

              if (singleItem.name.includes('APPLICABLE') && singleItem.itemVersion >= 2) {
                const applicableMapping = {
                  CEAPPLICABLE_RO: 'CEAPPLICABLE',
                  CEAPPLICABLE_IC: 'CEAPPLICABLE',
                  IGGAPPLICABLE_RO: 'IGGAPPLICABLE',
                  IGGAPPLICABLE_IC: 'IGGAPPLICABLE',
                };

                const baseName = applicableMapping[singleItem.name] || singleItem.name;
                const applicableObj = createdItems['consumption'].find(
                  (consumptionObj) => consumptionObj.name === baseName,
                );

                if (applicableObj) {
                  singleItem.value = applicableObj.value;
                }
              }
            }

            if (
              (singleItem.group_label === 'rob' || singleItem.group_label === 'bunkering') &&
              item.version < 3
            ) {
              if (
                singleItem.name.includes('HIGH') &&
                (singleItem.name.includes('LFO') || singleItem.name.includes('HFO'))
              ) {
                // Construct the corresponding TOTAL key by replacing "HIGH" with "TOTAL" and adjusting the name format
                const fuelType = singleItem.name.includes('LFO') ? 'LFO' : 'HFO';
                const totalKey = `TOTALROB${fuelType}`; // This constructs TOTALROBLFO or TOTALROBHFO

                // Find the corresponding TOTAL item in createdItems
                const totalItem = createdItems.find((item) => item.name === totalKey);

                if (totalItem && totalItem.value !== singleItem.value) {
                  // Set the HIGH value to match TOTAL if they are different
                  singleItem.value = totalItem.value;
                }
              }
            }
          });

          return createdItems;
        });

        groupedJson[eachItem] = records;
      });

      const filteredData = Object.fromEntries(
        Object.entries(groupedJson).filter(([key, val]) => {
          return !val.every((arr) => arr.length == 0);
        }),
      );

      setCompareReportData(filteredData);
      setData(filteredData);
      setLoading(false);
    }
  }, [reportDatMapper, technicalReports, vesselList]);

  const fetchTechnicalReports = async () => {
    setLoading(true);
    const pageSize = 4;
    let updatedPageIndex;
    let sortDesc = true;
    if (next === 0 && prev === 0) {
      updatedPageIndex = 0;
    } else if (prev > 0) {
      updatedPageIndex = prev - 1;
      sortDesc = false;
    } else {
      updatedPageIndex = next;
    }

    const dateParams = `${dateLabel}=${
      prev > 0
        ? `${getURLParams(dateLabel, history.location.search)},`
        : `,${getURLParams(dateLabel, history.location.search)}`
    }`;
    try {
      let response;
      switch (reportTab) {
        case 'marpol':
          response = await vesselService.getMonthlyMarpolList(
            ownershipId,
            {
              pageIndex: updatedPageIndex,
              pageSize,
              sortBy: [{ id: dateLabel, desc: sortDesc }],
              pageMode: 'detail',
            },
            dateParams,
          );
          break;
        case 'ninety-six-hours':
          response = await vesselService.get96HoursReportList(
            ownershipId,
            {
              pageIndex: updatedPageIndex,
              pageSize,
              sortBy: [{ id: dateLabel, desc: sortDesc }],
              pageMode: 'detail',
            },
            dateParams,
          );
          break;

        default:
          response = await vesselService.getTechnicalReports(
            reportTab,
            ownershipId,
            {
              pageIndex: updatedPageIndex,
              pageSize,
              sortBy: [{ id: dateLabel, desc: sortDesc }],
            },
            dateParams,
            false,
          );
          break;
      }
      const results = sortDesc ? response.data.results : _.reverse(response.data.results);
      setVesselName(results[0].name);
      setTechnicalReports(results);
      if (Number(results[0].row_number) === 1) {
        setPrevDisabled(true);
      } else {
        setPrevDisabled(false);
      }
      if (Number(results[results.length - 1].row_number) === Number(response.data.total)) {
        setNextDisabled(true);
      } else {
        setNextDisabled(false);
      }
    } catch (error) {
      if (error?.message.includes('403')) {
        setIsAccessDenied(true);
      }
      if (httpService.axios.isCancel(error)) {
        return;
      }
      setLoading(false);
      console.log('Get Reports failed', error);
    }
  };

  useEffect(() => {
    fetchTechnicalReports();
  }, [next, prev]);

  const handleSubmit = () => {
    fetchTechnicalReports();
  };

  const breadCrumbsItems = useMemo(() => {
    let title, label, link;
    switch (reportTab) {
      case 'marpol':
        title = `Environmental Reports • Monthly Marpol Reports / ${technicalReports[0]?.name}`;
        label = 'To marpol Reports List Page';
        link = `/vessel/report/environmental/${reportTab}`;
        break;
      case 'ninety-six-hours':
        title = `Environmental Reports • 96 Hours Reports / ${technicalReports[0]?.vessel_name}`;
        label = 'To 96hours Reports List Page';
        link = `/vessel/report/environmental/${reportTab}`;
        break;
      default:
        title = `Technical Reports • ${name} Reports / ${technicalReports[0]?.name}`;
        label = `To ${name} Reports List Page`;
        link = `/vessel/report/technical/${reportTab}`;
        break;
    }

    return [
      {
        title,
        label,
        link,
      },
      {
        title: 'Compare Reports',
        label: 'Compare Reports',
        link: '#',
      },
    ];
  }, [ownershipId, vesselOwnership, technicalReports]);

  const onBreadcrumbClick = (label) => {
    try {
      ga4EventTrigger(
        'Breadcrumb',
        `Vessel ${name} Report - Menu`,
        `${technicalReports[0]?.name} and ${formatDate(
          getURLParams(dateLabel, history.location.search),
          'DD MMM YYYY',
        )}`,
      );
    } catch (error) {
      console.log(error);
    }
  };

  const handleGeneratePDF = async (report_id) => {
    const doc = new jsPDF();
    let completeData = [];
    let yHeight = 0;
    const pageWidth = doc.internal.pageSize.width - 20;
    const pageHeight = doc.internal.pageSize.height;
    const pageWdithHalf = pageWidth / 2;
    const headerData = {};

    // combining all the report items including header
    Object.keys(data).forEach((item) => {
      const headerLabel = order?.find((labelItem) => labelItem.name === item)?.label;
      let reportItem = data[item][report_id];
      reportItem = _.orderBy(reportItem, ['order'], ['asc']);
      completeData = [
        ...completeData,
        {
          main: item,
          label: headerLabel ? headerLabel.toUpperCase() : '',
          value: '',
        },
        ...reportItem,
      ];
      if (item === 'general') {
        headerData.type = reportItem[0].value;
        headerData.date = reportItem[['position', 'voyage'].includes(reportTab) ? 1 : 0].value;
      }
    });

    const rejectedData = completeData.filter((item) => {
      return item?.validation_json?.hidden;
    });

    completeData = completeData.filter((item) => {
      return !item?.validation_json?.hidden;
    });

    doc.setFont('helvetica', 'bold');
    let intialStartXPosition = 10;
    let intialStartYPosition = 10;
    const yPositionhederHeight = await headerSection(
      doc,
      intialStartXPosition,
      intialStartYPosition,
      `${_.startCase(reportTab)} Report ${
        ['position', 'voyage'].includes(reportTab) ? '(' + headerData.type + ')' : ''
      }`,
      headerData?.date,
      pageWidth,
      `${technicalReports[0]?.name}`,
    );
    intialStartYPosition += yPositionhederHeight - 20;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(8);

    //function to get the max text height of a row
    const maxTextHeight = (textArrayList, cellWidth, lineHeight = 2) => {
      let maxCellHeight = 10;
      for (const item of textArrayList) {
        const cellHeight = (doc.getTextWidth(item) / cellWidth) * lineHeight;
        maxCellHeight = Math.max(maxCellHeight, cellHeight);
      }
      return maxCellHeight > 10 ? Math.round(maxCellHeight) : 10;
    };

    //function to map all the report items
    const mapData = (rejectedData) => {
      let mainCount = 0;
      let lengthDiff = 0;
      let itemHeight = 10;
      completeData?.forEach((eachItem, index) => {
        const getPathWithoutLastElement = (item) =>
          fetchPathAsPerReportVersion(item, item.version)?.split('.').slice(0, -1).join('.');

        const pathArr = getPathWithoutLastElement(eachItem);

        if (eachItem.label.includes('Fuel3 Type') && eachItem.value === 'Fuel not listed') {
          const matchingItem = rejectedData?.find((cd) => {
            const pathArr2 = getPathWithoutLastElement(cd);
            const isMatchingPathAndLabel =
              pathArr === pathArr2 && cd.label === 'Other Fuel Type (When Not Listed)';

            return isMatchingPathAndLabel;
          });

          if (matchingItem) eachItem.value = matchingItem.value;
        }

        let nextPageTrigger = false;
        if (eachItem.main) {
          let height = maxTextHeight([eachItem.label], pageWidth, 8);
          let balancePageHeight = pageHeight - yHeight + 40;
          if (
            balancePageHeight - height - 10 < 60 ||
            (!completeData[index + 1].validation_json.header && balancePageHeight < 100)
          ) {
            nextPageTrigger = true;
          }
        }
        if (((index + mainCount) % 2 === 0 && yHeight + 40 > pageHeight) || nextPageTrigger) {
          // condition to handle addition of next page
          yHeight = 0;
          intialStartYPosition = 10;
          intialStartXPosition = 10;
          lengthDiff = 0;
          doc.addPage();
          completeData.splice(0, index);
          mapData(rejectedData);
          return false;
        } else if (eachItem.main || eachItem.validation_json.header) {
          // condition to draw header and subheader
          let startPositionVariance = eachItem.main ? 20 : 10;
          doc.setFontSize(12);
          doc.setFont('helvetica', 'bold');
          doc.setFillColor(211, 211, 211);
          let itemHeadHeight = maxTextHeight([eachItem.label], pageWidth, 8);
          doc.rect(
            intialStartXPosition,
            startPositionVariance + (yHeight !== 0 ? yHeight : intialStartYPosition),
            pageWidth,
            itemHeadHeight,
            'F',
          );
          doc.cell(
            intialStartXPosition,
            startPositionVariance + (yHeight !== 0 ? yHeight : intialStartYPosition),
            pageWidth,
            itemHeadHeight,
            _.startCase(eachItem.label),
          );
          if ((index + mainCount) % 2 === 0) {
            mainCount += 1;
          } else {
            mainCount += 2;
          }
          if (eachItem.main) {
            mainCount += 2;
          }
          lengthDiff += itemHeadHeight - 10;
          yHeight =
            yHeight != 0
              ? yHeight + startPositionVariance + (itemHeadHeight - 10)
              : intialStartYPosition + startPositionVariance + (itemHeadHeight - 10);
        } else if ((index + mainCount) % 2 === 0) {
          // condition to draw sub-items on first half
          doc.setFontSize(8);
          doc.setFont('helvetica', 'normal');
          let startYPosition =
            intialStartYPosition + 10 * ((index + 2) / 2) + 10 * (mainCount / 2) + lengthDiff;
          let startXPosition = intialStartXPosition;
          let textArrayList = [eachItem.label, eachItem.value.toString()];
          if (!completeData[index + 1]?.main && !completeData[index + 1]?.validation_json?.header) {
            textArrayList = [
              ...textArrayList,
              completeData[index + 1]?.label ?? '',
              completeData[index + 1]?.value.toString() ?? '',
            ];
          }
          itemHeight = maxTextHeight(textArrayList, Math.round(pageWdithHalf / 2), 8);
          doc.cell(startXPosition, startYPosition, pageWdithHalf / 2, itemHeight, eachItem.label);
          startXPosition = startXPosition + pageWdithHalf / 2;
          doc.cell(
            startXPosition,
            startYPosition,
            pageWdithHalf / 2,
            itemHeight,
            eachItem.value.toString(),
          );
          yHeight = startYPosition;
          if (completeData.length === index + 1) {
            intialStartYPosition = startYPosition + itemHeight;
          }
          if (completeData[index + 1]?.main || completeData[index + 1]?.validation_json?.header) {
            lengthDiff += itemHeight - 10;
            yHeight += itemHeight - 10;
            itemHeight = 10;
          }
        } else {
          // condition to draw sub-items on second half
          let startYPosition =
            intialStartYPosition + 10 * ((index + 1) / 2) + 10 * (mainCount / 2) + lengthDiff;
          let startXPosition = intialStartXPosition + pageWdithHalf;
          doc.cell(startXPosition, startYPosition, pageWdithHalf / 2, itemHeight, eachItem.label);
          startXPosition = startXPosition + pageWdithHalf / 2;
          doc.cell(
            startXPosition,
            startYPosition,
            pageWdithHalf / 2,
            itemHeight,
            eachItem.value.toString(),
          );
          if (completeData.length === index + 1) {
            intialStartYPosition = yHeight + itemHeight;
          }
          yHeight = startYPosition;
          lengthDiff += itemHeight - 10;
          if (completeData[index + 1]?.main || completeData[index + 1]?.validation_json?.header) {
            yHeight += itemHeight - 10;
          }
          itemHeight = 10;
        }
      });
    };
    mapData(rejectedData);
    footerSection(doc, 10, intialStartYPosition, pageWdithHalf);
    doc.save(`${reportTab + headerData.date}.pdf`);
  };

  const getButtons = (report, index) => {
    if (['ninety-six-hours'].includes(reportTab)) return [];
    const sendButton = {
      id: `${report.id}-send`,
      text: 'Send',
      onClick: () => {
        const listItem = _.find(vesselList, { id: Number(ownershipId) });
        setEmailContent(
          getEmailMessage(
            userEmail,
            report,
            controlParameters,
            _.get(report, dateLabel),
            reportTab,
            _.get(report, 'name'),
            formatValue(listItem?.fleet_staff.tech_group),
          ),
        );
        setIsSendEmailModalVisible(true);
        ga4EventTrigger(
          'Send Email',
          `Vessel ${name} Report Compare - Menu`,
          `${technicalReports[0]?.name} and ${formatDate(
            getURLParams(dateLabel, history.location.search),
            'DD MMM YYYY',
          )}`,
        );
      },
    };
    const pdfButton = {
      id: `${report.id}-pdf`,
      text: 'PDF',
      onClick: () => {
        ga4EventTrigger(
          'Export to PDF',
          `Vessel ${name} Report - Menu`,
          `${technicalReports[0]?.name} and ${formatDate(
            getURLParams(dateLabel, history.location.search),
            'DD MMM YYYY',
          )}`,
        );
        handleGeneratePDF(index);
      },
    };
    return roleConfig.vessel.send ? [sendButton, pdfButton] : [pdfButton];
  };

  const reportCompareJsons = useCallback(
    (xl = false) => {
      const compareJson = {};
      order.forEach((item) => {
        let columns;
        if (xl) {
          columns = reportCols(item.label, technicalReports, false, false, true, xl);
        } else {
          columns =
            item.name === 'general'
              ? reportCols(item.label, technicalReports, true, getButtons)
              : reportCols(item.label, technicalReports, false, false, true, false, item?.tooltip);
        }
        compareJson[item.name] = {
          jsonData: transpose(data[item.name] ?? []),
          columns,
        };
      });
      return compareJson;
    },
    [data, technicalReports, controlParameters, userEmail],
  );

  const handleExportExcel = () => {
    ga4EventTrigger(
      'Export to Excel',
      `Vessel ${name} Report - Menu`,
      `${technicalReports[0]?.name} and ${formatDate(
        getURLParams(dateLabel, history.location.search),
        'DD MMM YYYY',
      )}`,
    );
    exportTableToExcel(
      reportCompareJsons(true),
      technicalReports[0]?.name
        ? `${name} Report for ${technicalReports[0]?.name}`
        : `${name} Report`,
    );
  };

  const handleVesselDetails = () => {
    ga4EventTrigger(
      'Vessel Details',
      `Vessel ${name} Report - Menu`,
      `${technicalReports[0]?.name} and ${formatDate(
        getURLParams(dateLabel, history.location.search),
        'DD MMM YYYY',
      )}`,
    );
    history.push(`/vessel/ownership/details/${ownershipId}`);
  };

  const BreadCrumbsRow = () => (
    <Row className="details-breadcrumb">
      <Col className="col-md-auto align-header">
        <BreadcrumbHeader
          items={breadCrumbsItems}
          activeItem="Compare Reports"
          onClick={onBreadcrumbClick}
        />
      </Col>
    </Row>
  );

  const eventTracker = (type) => {
    let action;
    if (type === 'prev') {
      action = 'Compare Previous';
    } else {
      action = type === 'next' ? 'Compare Next' : 'Print';
    }
    ga4EventTrigger(
      action,
      `Vessel ${name} Report - Menu`,
      `${technicalReports[0]?.name} and ${formatDate(
        getURLParams(dateLabel, history.location.search),
        'DD MMM YYYY',
      )}`,
    );
  };

  const renderCompareTableData = () => {
    const compareData = reportCompareJsons(false);
    return Object.keys(compareData).map((i, index) => (
      <CompareTable
        key={i + index}
        columns={compareData[i].columns}
        data={compareData[i].jsonData}
        loading={loading}
        tableRef={tableRef}
        id={i}
        isEditable={technicalReports.map((report) => report.isEditable)}
        handleShowAlert={handleShowAlert}
        reportIds={technicalReports.map((report) => report.id)}
        reportTypes={technicalReports.map((report) => report.report_type)}
        reportTab={reportTab}
        reportVersion={technicalReports.map((report) => report.version)}
      />
    ));
  };

  return (
    <Container>
      <div className="details_page">
        <CompareToolBar
          handleExportExcel={handleExportExcel}
          handleVesselDetails={handleVesselDetails}
          prev={prev}
          next={next}
          setPrev={setPrev}
          setNext={setNext}
          prevDisabled={prevDisabled}
          nextDisabled={nextDisabled}
          eventTracker={eventTracker}
          reportTab={reportTab}
        />
        <BreadCrumbsRow />
        {loading && <Spinner alignClass="spinner-table mt-5" />}
        {!loading && technicalReports.length > 0 ? (
          <>
            <SendEmailModal
              isVisible={isSendEmailModalVisible}
              onClose={() => setIsSendEmailModalVisible(false)}
              onSuccess={() => setIsSendEmailModalVisible(false)}
              emailContent={emailContent}
              onSubmitButtonClick={() =>
                ga4EventTrigger(
                  'Confirm Send Email',
                  `Vessel ${name} Report Compare - Menu`,
                  `${technicalReports[0]?.name} and ${formatDate(
                    getURLParams(dateLabel, history.location.search),
                    'DD MMM YYYY',
                  )}`,
                )
              }
            />
            {renderCompareTableData()}
          </>
        ) : (
          !loading && <NoResult />
        )}
      </div>
      <ScrollArrow />
      <CustomEditModal handleModalSubmit={handleSubmit} report={reportTab} />
      <AlertModal onClose={handleClose} show={showAlert} message={alertMessage} />
    </Container>
  );
};

export default TechnicalReportComparePage;
