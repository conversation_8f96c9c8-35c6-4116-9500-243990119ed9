import React, { useContext } from 'react';
import { useFlexLayout, useTable } from 'react-table';
import { useSticky } from 'react-table-sticky';
import NoResult from '../../NoResult';
import _ from 'lodash';
import { Icon } from '../../../styleGuide';
import { VesselContext } from '../../../context/VesselContext';
import { OverlayTrigger, Tooltip } from 'react-bootstrap';

const CompareTable = ({
  columns,
  data,
  loading,
  reportId = '',
  id,
  reportIds = [],
  isEditable = [],
  handleShowAlert = (message) => {},
  reportTypes = [],
  reportTab,
  reportVersion = [],
}) => {
  const { setEditData, setShowEditModal, ga4EventTrigger = () => {}, roleConfig, setReportVersion } = useContext(
    VesselContext,
  );
  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows } = useTable(
    {
      columns,
      data,
    },
    useSticky,
    useFlexLayout,
  );

  return (
    <div className="vessel-table">
      <div {...getTableProps()} className="table sticky">
        <div className="header">
          {headerGroups.map((headerGroup, i) => (
            <div key={i} {...headerGroup.getHeaderGroupProps()} className="tr">
              {headerGroup.headers.map((column, j) => (
                <div
                  key={j}
                  {...column.getHeaderProps()}
                  className={`th heading-border ${column.customClass} ${
                    reportId && reportId != j && j != 0 && 'no-print'
                  }`}
                  style={column.style}
                  data-testid={`fml-compare-${id}-table-header-${j}`}
                >
                  {column.render('Header')}
                </div>
              ))}
            </div>
          ))}
        </div>
        <div {...getTableBodyProps()} className="body">
          {rows.length > 0
            ? rows.map((row, i) => {
                prepareRow(row);
                return (
                  !row.original.field.hidden && (
                    <div key={i} {...row.getRowProps()} className="tr">
                      {row.cells.map((cell, j) => {
                        let isHeaderRow = false
                        if(j > 0 && cell.value === '- - -' && row?.original?.field?.header){
                          isHeaderRow = true
                        }
                        return(
                        <div
                          key={j}
                          {...cell.getCellProps()}
                          className={`td d-flex ${cell.column.customClass} ${
                            reportId && reportId != j && j != 0 && 'no-print'
                          }`}
                          style={cell.column.style}
                          onMouseOver={() => {
                            const ele = document.getElementById(`fml-compare-edit-${id}-${i}-${j}`);
                            if (ele) {
                              ele.hidden = false;
                            }
                          }}
                          onMouseOut={() => {
                            const ele = document.getElementById(`fml-compare-edit-${id}-${i}-${j}`);
                            if (ele) {
                              ele.hidden = true;
                            }
                          }}
                          onFocus={() => {
                            const ele = document.getElementById(`fml-compare-edit-${id}-${i}-${j}`);
                            if (ele) {
                              ele.hidden = false;
                            }
                          }}
                          onBlur={()=>{
                            const ele = document.getElementById(`fml-compare-edit-${id}-${i}-${j}`);
                            if (ele) {
                              ele.hidden = false;
                            }
                          }}
                          aria-hidden="true"
                          data-testid={`fml-compare-${id}-table-row-${i}-column-${j}`}
                        >
                          {j != 0 &&
                          (_.get(row, `original.values[${j - 1}].value_1`) ||
                            _.get(row, `original.values[${j - 1}].value_2`)) ? (
                            <div>
                              <span
                                className={`${
                                  j !== 0 && _.get(row, `original.values[${j - 1}].text_color_1`)
                                }`}
                              >
                                {_.get(row, `original.values[${j - 1}].value_1`)}
                              </span>
                              /
                              <span
                                className={`${
                                  j !== 0 && _.get(row, `original.values[${j - 1}].text_color_2`)
                                }`}
                              >
                                {_.get(row, `original.values[${j - 1}].value_2`)}
                              </span>
                            </div>
                          ) : (
                            <div
                              className={`${
                                j !== 0 && _.get(row, `original.values[${j - 1}].text_color`)
                              }`}
                              style={{"display":"inline-flex", alignItems: 'center'}}
                            >
                              {!isHeaderRow && cell.render('Cell')}
                              {j === 0 && row?.original?.values?.[0]?.validation_json.tooltip && (
                              <OverlayTrigger
                                placement="right"
                                overlay={
                                  <Tooltip id="desc_tooltip" className="tooltip">
                                    {row?.original?.values?.[0]?.validation_json.tooltip}
                                  </Tooltip>
                                }
                              >
                                <div className = "paris2-tooltip-icon" style={{ marginLeft: '10px', cursor: 'pointer' }}>
                                  <Icon icon="info"/>
                                </div>
                              </OverlayTrigger>)}
                            </div>
                          )}
                          {j !== 0 &&
                          roleConfig.vessel.editReport &&
                          row?.original?.values?.[`${j - 1}`]?.validation_json?.isEditable &&
                          (row?.original?.values?.[`${j - 1}`]?.validation_json?.report_type
                            ? row?.original?.values?.[`${j - 1}`]?.validation_json?.report_type ===
                              reportTypes[j - 1]
                            : true) && (
                            <div>
                              <Icon
                                icon="Edit"
                                size={20}
                                id={`fml-compare-edit-${id}-${i}-${j}`}
                                data-testid={`fml-compare-edit-${id}-${i}-${j}`}
                                hidden={true}
                                className="edit-icon compare-edit"
                                onClick={() => {
                                  if((['position', 'voyage'].includes(reportTab) ) && isEditable[j -1]){
                                    handleShowAlert(<p>This voyage has been validated. <br /> 
                                     Please remove validation certificate from ETS page and proceed to make change. <br />
                                     After change please ensure to receive new certificate of validity from verifier and upload the same in ETS page. <br /> <br />
                                     Changes will be visible in verifier’s portal after 24 hours
                                    </ p>);
                                    return;
                                  };
                                  ga4EventTrigger(
                                    `Edit ${reportTab} Report`,
                                    `Vessel ${reportTab} Report - Menu`,
                                    _.get(row, `original.values[${j - 1}]`).edit_label,
                                  );
                                  setShowEditModal(true);
                                  setEditData({
                                    ..._.get(row, `original.values[${j - 1}]`),
                                    id: reportIds[j - 1],
                                    reportNum: j,
                                  });
                                  setReportVersion(reportVersion[j -1]);
                                }}
                              />
                            </div>
                          )}
                        </div>
                      )})}
                    </div>
                  )
                );
              })
            : !loading && <NoResult />}
        </div>
      </div>
    </div>
  );
};

export default CompareTable;
