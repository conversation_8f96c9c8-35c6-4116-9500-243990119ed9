import React, { Fragment, useRef } from 'react';
import { ButtonToolbar } from 'react-bootstrap';
import { Typeahead } from 'react-bootstrap-typeahead';
import { Icon } from '../../styleGuide';
import { ChevronExpand } from 'react-bootstrap-icons';
import _ from 'lodash';

const VesselDropDown = (props) => {
  const ref = useRef(null);
  const newProps = {};
  if (props.selectedItem?.length > 0) newProps.selected = props.selectedItem;

  const handleChange = (event) => {
    props.onChange(event, ref.current);
    ref.current.blur();
  };

  const handleClear = (event) => {
    event.target.value = '';
    ref.current.clear();
    ref.current.toggleMenu();
    props.handleClear();
  };

  return (
    <Fragment>
      <Typeahead
        ref={ref}
        id="search-type-menu"
        {...newProps}
        labelKey={props.labelKey}
        onChange={handleChange}
        options={props.dropdownData}
        inputProps={{ 'data-testid': props.dataTestId }}
        placeholder={props.placeholder}
        onFocus={handleClear}
        className="vessel-dropdown-menu"
      />
      {!props.selectedItem || _.isEmpty(props.selectedItem) ? (
        <ButtonToolbar className="interval-angle-icon">
          <ChevronExpand onClick={handleClear} data-testid="fml-seal-management-dropdown-btn" />
        </ButtonToolbar>
      ) : (
        <ButtonToolbar className="interval-angle-icon vessel-cross-icon">
          <Icon
            icon="remove"
            size={20}
            className="remove"
            onClick={handleClear}
            data-testid="fml-seal-management-remove-btn"
          />
        </ButtonToolbar>
      )}
    </Fragment>
  );
};

export default VesselDropDown;
