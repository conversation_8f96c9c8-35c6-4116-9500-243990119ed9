import React, { useEffect, useState, useContext, useMemo } from 'react';
import { Col, Button, Form, Row } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Icon } from '../../styleGuide';
import _ from 'lodash';
import { getPortsByCountry } from '../../service/reference-service';
import { TechnicalReportContext } from '../../context/TechnicalReportContext';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';
import { MONTHS } from '../../model/constants';

const SELECTCATEGORY = 'select';

const VoyageHistoryFilter = ({ linkCountryPortTriggered, setLinkCountryPortTriggered }) => {
  const {
    filterVoyageOptions,
    filterVoyageData,
    setFilterVoyageData,
    filterData,
    setFilterData,
    elements,
    setElements,
  } = useContext(TechnicalReportContext);
  const [filterOptions, setFilterOptions] = useState({
    ...filterVoyageOptions,
    port: [],
    month: MONTHS,
  });
  const elementTypes = ['vessel', 'year', 'month', 'country', 'port'];

  const loadCategoryOptions = () => {
    const diffElements = _.difference(elementTypes, elements);
    if (_.includes(diffElements, 'country') && _.includes(diffElements, 'port')) {
      _.pull(diffElements, 'port');
    }
    if (_.includes(diffElements, 'year') && _.includes(diffElements, 'month')) {
      _.pull(diffElements, 'month');
    }
    const options = diffElements?.map((value, index) => {
      return { id: index, value: value, label: _.capitalize(value) };
    });
    return options;
  };

  const onCategorySelect = (value) => {
    const dataElements = [...elements];
    _.pull(dataElements, SELECTCATEGORY);
    if (value === 'country') {
      dataElements.push('country', 'port');
    } else if (value === 'year') {
      dataElements.push('year', 'month');
    } else {
      dataElements.push(value);
    }
    setElements(dataElements);
  };

  const dropDownElement = (name, dropDownOptions) => {
    return (
      <Col className="mb-4" key={name}>
        <Row>
          <Col sm="4">
            <CustomTypeAhead
              id="basic-typeahead-single"
              labelKey="label"
              inputProps={{ 'data-testid': `fml-voyage-history-${name}-category` }}
              onChange={(event) => {
                onCategorySelect(event[0]?.value);
              }}
              options={loadCategoryOptions()}
              placeholder="Select Category"
              selected={name !== SELECTCATEGORY && [{ value: name, label: _.capitalize(name) }]}
              showDropDownIcon={false}
              disabled={name !== SELECTCATEGORY}
            />
          </Col>

          {name === 'year' && (
            <Col sm="4">
              <DatePicker
                showYearPicker
                placeholderText="Select Year"
                dateFormat="yyyy"
                allowClear={true}
                selected={filterVoyageData?.year}
                onChange={(data) => setFieldValue(name, data)}
                customInput={<input data-testid={`fml-voyage-history-${name}`} type="text" />}
              />
            </Col>
          )}
          {name === 'month' && (
            <Col sm="4">
              <Form.Control
                as="select"
                name={name}
                data-testid={`fml-voyage-history-${name}`}
                className="form-select"
                value={filterVoyageData?.[name]}
                onChange={(event) => setFieldValue(name, event.target?.value)}
              >
                <option value="" className="text-capitalize" key={`all-${name}`}>
                  All {_.capitalize(name)}s
                </option>
                {dropDownOptions.map(({ id, value, index }) => (
                  <option className="font-size-large" value={id} key={index}>
                    {value}
                  </option>
                ))}
              </Form.Control>
            </Col>
          )}
          {_.includes(['vessel', 'port', 'country'], name) && (
            <Col sm="4">
              <CustomTypeAhead
                id="basic-typeahead-single"
                labelKey="value"
                inputProps={{ 'data-testid': `fml-voyage-history-${name}` }}
                onChange={(event) => setFieldValue(name, event)}
                options={dropDownOptions}
                placeholder={` All ${_.capitalize(name === 'country' ? 'countrie' : name)}s`}
                selected={filterVoyageData?.[name]}
                showDropDownIcon={true}
                handleClear={() => {
                  setFilterVoyageData({ ...filterVoyageData, [name]: [] });
                  if (name === 'vessel') {
                    setFilterData({ ...filterData, vessel: [] });
                  }
                }}
              />
            </Col>
          )}
          {name !== 'vessel' && (
            <Icon
              icon="remove"
              size={20}
              data-testId={`fml-voyage-history-${name}-remove`}
              onClick={() => removeElement(name)}
              className="remove my-auto"
            />
          )}
        </Row>
      </Col>
    );
  };

  const addElements = () => {
    if (!_.includes(elements, SELECTCATEGORY) && elements.length < 5) {
      setElements([...elements, SELECTCATEGORY]);
    }
  };

  const removeElement = (value) => {
    const filterValues = { ...filterVoyageData };
    const currentElements = _.remove(elements, function (n) {
      return n !== value;
    });

    if (value === 'country') {
      _.remove(currentElements, function (n) {
        return n === 'port';
      });

      delete filterValues['port'];
    }
    if (value === 'year') {
      _.remove(currentElements, function (n) {
        return n === 'month';
      });
      delete filterValues['month'];
    }

    delete filterValues[value];

    setFilterVoyageData(filterValues);
    setElements(currentElements);
  };

  const setFieldValue = (field, value) => {
    const filter = {
      ...filterVoyageData,
    };
    if (field === 'country') {
      fetchPorts(value);
      _.omit(filter, 'port');
    }

    if (field === 'vessel') {
      setFilterData({ ...filterData, vessel: value || [] });
    }
    setFilterVoyageData({
      ...filterVoyageData,
      [field]: value,
    });
  };

  const fetchPorts = async (value) => {
    const countryCode = _.find(filterVoyageOptions.country, ['value', value[0]?.value]);
    const getPortsResponse = await getPortsByCountry(`countryCode=${countryCode?.label}`);
    const portData = getPortsResponse.data.ports?.map((port) => {
      return { id: port.name, value: port.name };
    });
    setFilterOptions({ ...filterOptions, port: portData });
  };

  useEffect(() => {
    if (linkCountryPortTriggered && filterVoyageData?.country) {
      fetchPorts(filterVoyageData?.country);
      setElements(elementTypes);
      setLinkCountryPortTriggered(false);
    }
  }, [filterVoyageData]);

  const renderFilterFields = useMemo(() => {
    return elements?.map((item) => {
      if (item === 'port' || item === 'month') {
        return dropDownElement(item, filterOptions[item]);
      } else {
        return dropDownElement(item, filterVoyageOptions[item]);
      }
    });
  }, [filterVoyageData, elements, filterOptions, filterVoyageOptions]);

  return (
    <div className="filter-background my-2 p-2">
      <h6 className="filter-heading">Filter Voyage History</h6>
      <Row xs={1} md={2}>
        {renderFilterFields}
      </Row>
      <Button
        variant="outline-primary"
        data-testid="fml-voyage-history-add-filter"
        className="mb-3"
        onClick={() => addElements()}
      >
        Add Another Filter
      </Button>
    </div>
  );
};

export default VoyageHistoryFilter;
