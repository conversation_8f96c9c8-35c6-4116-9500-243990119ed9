import React, { useState, useCallback, useEffect, useContext, useMemo } from 'react';
import { Tab, Container, ButtonToolbar, Dropdown } from 'react-bootstrap';
import { useParams, useHistory, Link } from 'react-router-dom';
import TabWrapper from '../TabWrapper';
import PositionReports from './ListReports/PositionReports';
import QuarterlyReports from './ListReports/QuarterlyReports';
import TechnicalReportFilter from './TechnicalReportFilter';
import getURLParams from '../../util/getURLParams';
import MonthlyReports from './ListReports/MonthlyReports';
import VoyageReports from './ListReports/VoyageReports';
import { LOCAL_STORAGE_FIELDS } from '../../model/constants';
import PerformanceReports from './ListReports/PerformanceReports';
import _ from 'lodash';
import { ErrorPage } from '../../styleGuide';
import { TechnicalReportContext } from '../../context/TechnicalReportContext';
import VoyageHistory from './ListReports/VoyageHistory';
import { exportTableToExcel } from '../../util/excel-export';

const technicalReportsPageTabData = [
  {
    eventKey: 'position',
    tabName: 'Position Reports',
  },
  {
    eventKey: 'performance',
    tabName: 'ME Performance',
  },
  {
    eventKey: 'monthly',
    tabName: 'Monthly Reports',
  },
  {
    eventKey: 'quarterly',
    tabName: 'Quarterly Reports',
  },
  {
    eventKey: 'voyage',
    tabName: 'Voyage Reports',
  },
  {
    eventKey: 'voyage-history',
    tabName: 'Voyage History',
  },
];

const TechnicalReportsList = () => {
  const {
    filterData,
    roleConfig,
    ga4EventTrigger,
    vesselList,
    excelDownloadedData,
    excelColumns,
    filterVoyageData,
  } = useContext(TechnicalReportContext);
  const history = useHistory();
  const { tab = 'position' } = useParams();
  const [isAccessDenied, setIsAccessDenied] = useState(false);
  const [activeTab, setActiveTab] = useState(tab);
  const [loading, setLoading] = useState(false);

  const handleTabSelect = (key) => {
    localStorage.removeItem(LOCAL_STORAGE_FIELDS.tableSortKey);
    if (key !== activeTab) {
      ga4EventTrigger(
        'Switch Tab',
        `Vessel ${activeTab} Report - Menu`,
        technicalReportsPageTabData.find((item) => item.eventKey === key)?.tabName,
      );
      setActiveTab('');
      setActiveTab(key);
    }
    if (key !== 'voyage-history') {
      const ownershipId = getURLParams('vessel_ownership_id', history.location.search);
      if (ownershipId) {
        return history.push(
          `/vessel/report/technical/${key}?vessel_ownership_id=${ownershipId}&gmt=${filterData.startDate},${filterData.endDate}`,
        );
      }
      return history.push(
        `/vessel/report/technical/${key}?gmt=${filterData.startDate},${filterData.endDate}`,
      );
    } else {
      return history.push(`/vessel/report/technical/${key}`);
    }
  };

  const handleControlParameter = () => {
    ga4EventTrigger('Control Parameters', 'Technical Report - Menu', 'Control Parameters');
  };

  const handlePrint = () => {
    if (tab === 'voyage-history') {
      ga4EventTrigger(
        'Print',
        'Voyage History - Menu',
        filterVoyageData?.vessel ? filterVoyageData?.vessel[0]?.name : 'All Vessels',
      );
    }
    window.print();
  };

  const updateQuery = useCallback(() => {
    let queryParam = '';
    if (tab !== 'voyage-history') {
      if (!_.isEmpty(filterData)) {
        queryParam += `&gmt=${filterData.startDate},${filterData.endDate}`;
        if (filterData.vessel[0]?.id) {
          queryParam += `&vessel_ownership_id=${filterData.vessel[0]?.id}`;
        }
        history.push(`${history.location.pathname}?${queryParam}`);
      }
    } else {
      if (!_.isEmpty(filterData?.vessel)) {
        queryParam = `vessel_ownership_id=${filterData?.vessel[0]?.id}`;
        history.push(`${history.location.pathname}?${queryParam}`);
      } else {
        history.push(`${history.location.pathname}`);
      }
    }
  }, [filterData, tab, vesselList]);

  useEffect(() => {
    updateQuery();
  }, [filterData, vesselList]);

  const TechnicalReportButtons = useMemo(() => {
    return (
      <div className="no-print d-flex">
        <ButtonToolbar className="pb-4">
          {(roleConfig.params.view || tab === 'voyage-history') && (
            <Dropdown align={'end'}>
              <Dropdown.Toggle variant="outline-primary" id="dropdown-more">
                More
              </Dropdown.Toggle>
              <Dropdown.Menu>
                <Dropdown.Item
                  as={Link}
                  to="/vessel/report/technical/control-parameter"
                  onClick={handleControlParameter}
                  hidden={!roleConfig.params.view}
                >
                  Control Parameters
                </Dropdown.Item>
                {tab === 'voyage-history' && (
                  <>
                    <Dropdown.Item
                      data-testid="fml-voyage-history-export-excel"
                      onClick={() => {
                        ga4EventTrigger(
                          'Export to Excel',
                          'Voyage History - Menu',
                          (Array.isArray(filterVoyageData?.vessel) && filterVoyageData.vessel.length > 0
                            ? filterVoyageData.vessel[0].name
                            : 'All Vessels'),
                        );
                        exportTableToExcel(
                          {
                            voyage_history: {
                              jsonData: excelDownloadedData,
                              columns: excelColumns,
                            },
                          },
                          'Voyage-history',
                        );
                      }}
                    >
                      Export to Excel
                    </Dropdown.Item>
                    <Dropdown.Item data-testid="fml-voyage-history-print" onClick={handlePrint}>
                      Print
                    </Dropdown.Item>
                  </>
                )}
              </Dropdown.Menu>
            </Dropdown>
          )}
        </ButtonToolbar>
      </div>
    );
  }, [tab, excelDownloadedData, excelColumns]);

  return (
    <Container>
      {isAccessDenied ? (
        <ErrorPage errorCode={403} />
      ) : (
        <Tab.Container activeKey={activeTab} defaultActiveKey="all">
          <div className="d-flex justify-content-between">
            <div className="pb-4 font-weight-bold technical-reports">Technical Reports</div>
            {TechnicalReportButtons}
          </div>

          <div className="no-print tab-wrapper">
            <TabWrapper
              handleTabSelect={handleTabSelect}
              data={technicalReportsPageTabData}
              step={tab}
              setActiveTab={setActiveTab}
              activeTab={activeTab}
            />
          </div>

          {tab !== 'voyage-history' && (
            <TechnicalReportFilter loading={loading} tab={tab} ga4EventTrigger={ga4EventTrigger} />
          )}
          {tab === 'position' && (
            <PositionReports
              setIsAccessDenied={setIsAccessDenied}
              loading={loading}
              setLoading={setLoading}
              ga4EventTrigger={ga4EventTrigger}
            />
          )}
          {tab === 'quarterly' && (
            <QuarterlyReports
              filterData={filterData}
              setIsAccessDenied={setIsAccessDenied}
              loading={loading}
              setLoading={setLoading}
              ga4EventTrigger={ga4EventTrigger}
            />
          )}
          {tab === 'monthly' && (
            <MonthlyReports
              filterData={filterData}
              setIsAccessDenied={setIsAccessDenied}
              loading={loading}
              setLoading={setLoading}
              ga4EventTrigger={ga4EventTrigger}
            />
          )}
          {tab === 'voyage' && (
            <VoyageReports
              filterData={filterData}
              setIsAccessDenied={setIsAccessDenied}
              loading={loading}
              setLoading={setLoading}
              ga4EventTrigger={ga4EventTrigger}
            />
          )}
          {tab === 'performance' && (
            <PerformanceReports
              filterData={filterData}
              setIsAccessDenied={setIsAccessDenied}
              loading={loading}
              setLoading={setLoading}
              ga4EventTrigger={ga4EventTrigger}
            />
          )}
          {tab === 'voyage-history' && <VoyageHistory />}
        </Tab.Container>
      )}
    </Container>
  );
};

export default TechnicalReportsList;
