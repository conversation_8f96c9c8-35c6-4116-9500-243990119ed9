import React, { useEffect, useState, useContext, useCallback } from 'react';
import {
  GoogleMap, InfoWindow, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useJsApiLoader
} from '@react-google-maps/api';
import getURLParams from '../../util/getURLParams';
import { useHistory } from 'react-router-dom';
import vesselService from '../../service/vessel-service';
import { formatDate, parseDMS } from '../../util/view-utils';
import _ from 'lodash';
import moment from 'moment';
import { TechnicalReportContext } from '../../context/TechnicalReportContext';
import { Container, Row, Col, Card, Badge } from 'react-bootstrap';
import { Icon } from '../../styleGuide';
import markerIconPng from '../../../public/icons/marker.svg';
import stratumMarkerIconPng from '../../../public/icons/stratum-marker.svg';
import Spinner from '../Spinner';

// Convert longitude range from (-180, 180) to (0, 360)
const normalizeLongitude = (lon) => (lon < 0 ? 360 + Number(lon) : lon);

const { GOOGLE_API_KEY = '' } = process.env;

const VoyageMovement = () => {
  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: GOOGLE_API_KEY,
  });
  const { vesselList } = useContext(TechnicalReportContext);
  const history = useHistory();
  const dateRange = getURLParams('gmt', history.location.search);
  const showStartumFeed = getURLParams('stratum-feed', history.location.search) === 'true';
  const [startDate, endDate] = dateRange?.split(',') ?? '';
  const isVoyageReport = getURLParams('report-type', history.location.search) === 'voyage';
  const vesselOwnershipId = getURLParams('vessel_ownership_id', history.location.search);
  const [coordinates, setCoordinates] = useState([]);
  const [stratumCoordinates, setStratumCoordinates] = useState([]);
  const [currentLocation, setCurrentLocation] = useState({});
  const [markerPopUp, setMarkerPopUp] = useState(false);
  const [reportStats, setReportStats] = useState({
    distanceOverGround: 0,
    stratumDistance: 0,
  });
  const [loading, setLoading] = useState(true);

  const getVoyageReport = async (paginationParams) => {
    if (!isVoyageReport) return;
    const voyageReports = await vesselService.getTechnicalReports(
      'voyage',
      vesselOwnershipId,
      paginationParams,
      `gmt=${dateRange}`,
      false,
    );
    return voyageReports.data.results.filter((item) => moment(item.gmt).isSameOrBefore(endDate));
  };
  const getPositionReport = async (paginationParams) => {
    const positionReport = await vesselService.getTechnicalReports(
      'position',
      vesselOwnershipId,
      paginationParams,
      `gmt=${dateRange}`,
      false,
    );
    return startDate ? positionReport.data.results : positionReport.data.results.slice(0, 2);
  };
  const fetchData = async (paginationParams) => {
    try {
      setLoading(true);
      setCoordinates([]);
      const [positionReports, voyageReports] = await Promise.all([
        getPositionReport(paginationParams),
        getVoyageReport(paginationParams),
      ]);
      let points = positionReports?.map((item) => {
        return {
          latitude: item?.report_json?.general?.latitude,
          longitude: item?.report_json?.general?.longitude,
        };
      });

      if (isVoyageReport && voyageReports?.length > 0) {
        const voyageReportStats = {
          distanceOverGround: parseFloat(voyageReports[0]?.report_json?.dist?.ground ?? 0),
          stratumDistance: parseFloat(voyageReports[0]?.report_json?.stratumfive?.distance ?? 0),
        };
        setReportStats(voyageReportStats);
        const [arrivalPoint, departurePoint] = voyageReports.map((item) => {
          return {
            latitude: item?.report_json?.current?.berth_lat,
            longitude: item?.report_json?.current?.berth_lon,
          };
        });
        if (arrivalPoint && departurePoint) {
          points.push(departurePoint);
          points.unshift(arrivalPoint);
        }
      } else {
        const positionStats = positionReports?.slice(0, -1).reduce(
          (acc, item) => {
            acc.distanceOverGround =
              parseFloat((item?.report_json?.general.distance_over_ground ?? 0) + acc.distanceOverGround);
            acc.stratumDistance =
              parseFloat((item?.report_json?.stratumfive?.distance ?? 0) + acc.stratumDistance);
            return acc;
          },
          { distanceOverGround: 0, stratumDistance: 0 },
        );
        setReportStats(positionStats);
      }
      points = points?.map((item) => {
        const [lat, lon] = parseDMS(item).split(',').map(Number);
        return { lat: lat, lng: normalizeLongitude(lon) };
      });
      setCoordinates(points);
      setCurrentLocation({
        ...positionReports[0]?.report_json?.general,
        date: formatDate(positionReports[0]?.gmt, 'YYYY-MM-DD'),
      });
      if (showStartumFeed) {
        const reports = isVoyageReport ? voyageReports : positionReports;
        const startPositionReport = reports[reports.length - 1];
        const endPositionReport = reports[0];
        await fetchStratumData(
          moment(startPositionReport.gmt).unix() * 1000,
          moment(endPositionReport.gmt).unix() * 1000,
        );
      }
      setLoading(false);
    } catch (error) {
      console.log('get vessel failed', error);
      setLoading(false);
    }
  };
  const fetchStratumData = async (startDateEpoch, endDateEpoch) => {
    try {
      setStratumCoordinates([]);
      const vesselData = vesselList.find((i) => i.id === Number(vesselOwnershipId));
      const stratumVesselPositions = await vesselService.getStratumPositions(
        vesselData.vesselId,
        startDateEpoch,
        endDateEpoch,
      );
      const stratumPoints = stratumVesselPositions?.map((item) => {
        return { lat: Number(item.lat), lng: Number(normalizeLongitude(item.lon)) };
      });
      setStratumCoordinates(stratumPoints);
    } catch (error) {
      console.log('get stratum data failed', error);
    }
  };

  useEffect(() => {
    if (vesselList?.length > 0) {
      fetchData({ pageIndex: 0, sortBy: [{ id: 'gmt', desc: true }], pageSize: 1000 });
    }
  }, [vesselList]);

  const getReportTitle = useCallback(() => {
    const vesselData = vesselList.find((i) => i.id === Number(vesselOwnershipId));
    return `${vesselData?.name || ""} Compare Voyage: ${moment(startDate || endDate).format('DD MMM YYYY')}`
  }, [vesselList]);

  const getVarianceColor = (variancePercentage) => {
    if (variancePercentage >= 0 && variancePercentage <= 2) {
      return 'success';
    } else if (variancePercentage <= 5) {
      return 'warning';
    } else {
      return 'danger';
    }
  };

  const handleCrossButtonClick = () => {
    history.goBack();
  };

  const renderMap = () => {
    const variation = reportStats?.distanceOverGround - reportStats.stratumDistance;
    const variationPercentage = (
      (reportStats?.distanceOverGround ? variation / reportStats?.distanceOverGround : 1) * 100
    )
      .toFixed(2)
      .replace(/\.00$/, '');
    const varianceBgColor = getVarianceColor(Math.abs(variationPercentage));
   
    if (loading) {
      return <Spinner alignClass={`spinner-table`} />
    }
    if (!loading && _.isEmpty(coordinates || stratumCoordinates)) {
      return (
        <div className="d-flex" style={{ height: '50vh' }}>
          <h4 className="m-auto">No Position Reports for the vessel in the selected time period</h4>
        </div>
      )
    }
    const markerArray = [{
      name: "positionReportStart",
      position: _.first(coordinates),
      icon: {
        url: markerIconPng,
        scaledSize: new google.maps.Size(25, 41),
      },
      onClick: () => setMarkerPopUp(true),
      children: (
        markerPopUp && (
          <InfoWindow
            onCloseClick={() => setMarkerPopUp(true)}
            position={_.first(coordinates)}
            options={{ pixelOffset: new window.google.maps.Size(0, -10) }}
          >
            {currentLocation?.port ? (
              <>
                <h4>{currentLocation?.port}</h4>
                <p>
                  {currentLocation?.country}--{currentLocation?.date}
                </p>
              </>
            ) : (
              <>
                <h4>Sea</h4>
                <p>{currentLocation?.date}</p>
              </>
            )}
          </InfoWindow>
        )
      ),
    }, {
      name: "positionReportEnd",
      position: _.last(coordinates),
      icon: {
        url: markerIconPng,
        scaledSize: new google.maps.Size(25, 41),
      }
    }, {
      name: "stratumReportStart",
      position: _.first(stratumCoordinates),
      icon: {
        url: stratumMarkerIconPng,
        scaledSize: new google.maps.Size(25, 41),
      }
    }, {
      name: "stratumReportStart",
      position: _.last(stratumCoordinates),
      icon: {
        url: stratumMarkerIconPng,
        scaledSize: new google.maps.Size(25, 41),
      }
    },]
   
    return (
      <>
        {isLoaded && (
          <GoogleMap
            mapContainerStyle={{ height: '75vh', width: '95vw', maxWidth: '100%', margin: 'auto' }}
            zoom={3}
            center={_.first(coordinates)}
            options={{
              scaleControl: true,
              streetViewControl: false,
              zoomControl: true,
              zoomControlOptions: {
                position: google.maps.ControlPosition.LEFT_BOTTOM,
              },
              mapTypeId: google.maps.MapTypeId.HYBRID,
              mapTypeControl: true,
              mapTypeControlOptions: {
                style: google.maps.MapTypeControlStyle.DEFAULT,
                mapTypeIds: [
                  google.maps.MapTypeId.SATELLITE,
                  google.maps.MapTypeId.HYBRID,
                  google.maps.MapTypeId.ROADMAP,
                  google.maps.MapTypeId.TERRAIN,
                ],
              },
              fullscreenControl: true,
              rotateControl: false,
            }}
          >
            {markerArray.map((marker) => {
              return <Marker
                name={marker.name}
                position={marker.position}
                icon={marker.icon}
                onClick={marker.onClick}
              >{marker.children}</Marker>
            })}
            <Polyline
              path={coordinates}
              options={{
                geodesic: false,
                strokeColor: '#32FBFA',
                strokeOpacity: 1.0,
                strokeWeight: 2,
              }}
            />
            <Polyline
              path={stratumCoordinates}
              options={{
                geodesic: false,
                strokeColor: '#FF0000',
                strokeOpacity: 1.0,
                strokeWeight: 2,
              }}
            />
          </GoogleMap>
        )}
        {showStartumFeed && (
          <div
            className="voyage-compare-stats"
            style={{ display: 'flex', width: '95vw', maxWidth: '100%', margin: '20px auto' }}
          >
            <Card>
              <Card.Body>
                <Card.Title className="d-flex align-items-center">
                  <div className="box mr-2" style={{ backgroundColor: '#32FBFA' }} />
                  <Badge pill bg="light" text="dark">
                    Master Report
                  </Badge>
                </Card.Title>
                <Card.Text>Distance Travelled: {reportStats?.distanceOverGround}nm</Card.Text>
              </Card.Body>
            </Card>
            <Card>
              <Card.Body>
                <Card.Title className="d-flex align-items-center">
                  <div className="box mr-2" style={{ backgroundColor: '#FF0000' }} />
                  <Badge pill bg="light" text="dark">
                    StratumFive Feed
                  </Badge>
                </Card.Title>
                <Card.Text>Distance Travelled: {reportStats.stratumDistance}nm</Card.Text>
              </Card.Body>
            </Card>
            <Card>
              <Card.Body>
                <Card.Title>Distance travelled</Card.Title>
                <Card.Text>
                  <Badge
                    pill
                    bg={varianceBgColor}
                    text={varianceBgColor === 'warning' ? 'dark' : 'light'}
                  >
                    Variance: {variation}nm ({variationPercentage}%)
                  </Badge>
                </Card.Text>
              </Card.Body>
            </Card>
          </div>
        )}
      </>
    );
  };
  return (
    <Container>
      <Row>
        <Col>
          <div className="font-weight-bold technical-reports mb-4">
            {getReportTitle()}
          </div>
        </Col>
        <Col className="text-right">
          <Icon
            icon="close"
            className="cursor-pointer"
            size={30}
            onClick={() => handleCrossButtonClick()}
          />
        </Col>
      </Row>
      {renderMap()}
    </Container>
  );
};

export default VoyageMovement;