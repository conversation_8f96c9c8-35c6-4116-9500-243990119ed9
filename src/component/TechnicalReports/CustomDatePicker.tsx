import React, { useState } from 'react';
import { Form, Row } from 'react-bootstrap';
import DatePicker, { CalendarContainer } from 'react-datepicker';
import { stringAsDate } from '../../util/view-utils';
import Spinner from '../Spinner';

const CustomDatePicker = (props) => {
  const [highlightDates, setHighlightDates] = useState([]);
  const [loading, setLoading] = useState(false);
  const getHighlightedDates = async (date = props.selectedDate) => {
    setLoading(true);
    const highlightDatesResponse = await props.getHighlightedDates(date);
    setHighlightDates(highlightDatesResponse);
    setLoading(false);
  };

  const onChangeRaw = async (event) => {
    if (/^\d{2} [a-zA-Z]{3} \d{4}$/.test(event.target.value)) {
      const date = stringAsDate(event.target.value);
      if (date) {
        setLoading(true);
        const highlightDatesResponse = await props.onChangeRaw(date);
        setHighlightDates(highlightDatesResponse);
        setLoading(false);
      }
    }
  };

  const calendarContainer = (props) => {
    const children = [<Spinner />, ...props.children];
    return (
      <CalendarContainer className={props.className}>
        <div style={{ position: 'relative' }}>{children}</div>
      </CalendarContainer>
    );
  };
  return (
    <Form.Group className="custom-picker form-group">
      <Row className="m-auto">
        <DatePicker
          selectsStart
          className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
          disabled={props.disabled}
          onChangeRaw={onChangeRaw}
          dateFormat="dd MMM yyyy"
          selected={props.selectedDate}
          onMonthChange={getHighlightedDates}
          highlightDates={highlightDates}
          onCalendarOpen={getHighlightedDates}
          onChange={props.onDateChange}
          calendarContainer={loading ? calendarContainer : null}
          disabledKeyboardNavigation
          dayClassName={
            (date) => {
              if (highlightDates[0]) {
                const dateFound = highlightDates[0]['custom-picker__highlighted'].some(
                  (highlightedDate) => highlightedDate.toDateString() === date.toDateString(),
                );
                return dateFound ? '' : 'custom-picker__disabled';
              }
              return '';
            }
            // highlightDates[0] && !highlightDates[0]['custom-picker__highlighted'].includes(date)
            //   ? 'custom-picker__disabled'
            //   : ''
          }
          customInput={<input data-testid={`${props.dataTestId}`} type="text" />}
        />
      </Row>
    </Form.Group>
  );
};

export default CustomDatePicker;
