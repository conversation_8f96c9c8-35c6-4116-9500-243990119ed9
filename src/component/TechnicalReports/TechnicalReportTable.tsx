/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useContext, useEffect, useMemo } from 'react';
import { Form } from 'react-bootstrap';
import { useSticky } from 'react-table-sticky';
import {
  useFlexLayout,
  useSortBy,
  useTable,
  usePagination,
} from 'react-table/dist/react-table.development';
import styleGuide from '../../styleGuide';
import { fetchIcon, PageNum } from '../../util/view-utils';
import Spinner from '../Spinner';
import NoResult from '../NoResult';
import { TechnicalReportContext } from '../../context/TechnicalReportContext';

const { Icon } = styleGuide;

const TechnicalReportTable = ({
  column,
  reportData,
  fetchData,
  tableRef,
  isLoading,
  pageCount,
  totalCount,
  eventTracker,
}) => {
  const defaultColumns = useMemo(() => column, []);
  return (
    <div className="vessel-table">
      <Table
        columns={defaultColumns}
        data={reportData}
        tableRef={tableRef}
        isLoading={isLoading}
        fetchData={fetchData}
        totalPageCount={pageCount}
        totalCount={totalCount}
        eventTracker={eventTracker}
      />
    </div>
  );
};

const Table = ({
  columns,
  data,
  tableRef,
  fetchData,
  isLoading,
  totalPageCount,
  totalCount,
  eventTracker,
}) => {
  const { tableSort, filterData, setTableSort, loadingOptions } =
    useContext(TechnicalReportContext);
  const defaultColumn = useMemo(
    () => ({
      minWidth: 30,
      maxWidth: 120,
    }),
    [],
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    rows,
    gotoPage,
    setPageSize,
    canPreviousPage,
    canNextPage,
    pageCount,
    state: { pageIndex, pageSize, sortBy },
  } = useTable(
    {
      columns,
      defaultColumn,
      data,
      manualPagination: true,
      manualSortBy: true,
      autoResetPage: false,
      autoResetSortBy: false,
      fetchData,
      isLoading,
      initialState: { pageIndex: 0, sortBy: tableSort },
      pageCount: totalPageCount,
    },
    useSortBy,
    useFlexLayout,
    useSticky,
    usePagination,
  );

  const resetPage = async (pageNo = 0, pageSize = 10) => {
    await fetchData({ pageSize: pageSize, sortBy: sortBy, pageIndex: pageNo });
    setPageSize(pageSize);
    gotoPage(pageNo);
  };

  useEffect(() => {
    (async () => {
      if (!loadingOptions) {
        await resetPage();
      }
    })();
  }, [filterData, sortBy, loadingOptions]);
  useEffect(() => {
    if (!loadingOptions) {
      setTableSort(sortBy);
    }
  }, [sortBy]);
  const pageSwitch = async (page_no) => {
    await fetchData({ pageSize, sortBy: sortBy, pageIndex: page_no });
    gotoPage(page_no);
  };

  const pageSizeSwitch = async (page_size) => {
    //Internally pageIndex gets recalibrated as follows
    eventTracker('Pagination', page_size);
    const new_index = Math.floor((pageIndex * pageSize) / page_size);
    await fetchData({ pageIndex: new_index, sortBy: sortBy, pageSize: page_size });
    setPageSize(page_size);
  };

  const filterPages = (visiblePages, totalPages) =>
    visiblePages.filter((page) => page <= totalPages);
  const getVisiblePages = (page, total) => {
    if (total < 7) {
      return filterPages([1, 2, 3, 4, 5, 6], total);
    }
    if (page % 5 >= 0 && page > 4 && page + 2 < total) {
      return [1, page - 1, page, page + 1, total];
    }
    if (page % 5 >= 0 && page > 4 && page + 2 >= total) {
      return [1, total - 3, total - 2, total - 1, total];
    }
    return [1, 2, 3, 4, 5, total];
  };
  const visiblePages = getVisiblePages(pageIndex, pageCount);
  sessionStorage.removeItem("comparePage");
  return (
    <>
      <div className="d-flex pl-2 no-print">
        <div className="page-number-border">
          <PageNum
            onClick={() => (canPreviousPage ? pageSwitch(pageIndex - 1) : '')}
            disabled={!canPreviousPage}
            dataTestId="fml-pagination-previous"
          >
            {'<'}
          </PageNum>
          {visiblePages.map((page, index, array) => (
            <PageNum
              key={index}
              active={page - 1 === pageIndex}
              disabled={page - 1 === pageIndex}
              onClick={() => pageSwitch(page - 1)}
              dataTestId={`fml-pagination-${page}`}
            >
              {array[index - 1] + 2 < page ? `...${page}` : page}
            </PageNum>
          ))}
          <PageNum
            onClick={() => (canNextPage ? pageSwitch(pageIndex + 1) : '')}
            disabled={!canNextPage}
            dataTestId="fml-pagination-next"
          >
            {'>'}
          </PageNum>
        </div>
        <Form inline>
          <Form.Control
            as="select"
            value={pageSize}
            className="ml-3"
            onChange={(e) => {
              pageSizeSwitch(Number(e.target.value));
            }}
            data-testid="fml-select-pageSize"
          >
            {[10, 20, 50, 100].map((pageSize) => (
              <option key={pageSize} value={pageSize} data-testid={`fml-show-${pageSize}`}>
                Show {pageSize}
              </option>
            ))}
          </Form.Control>
        </Form>
        <div className="list-count">
          <b>{totalCount}</b> Results
        </div>
      </div>
      <div {...getTableProps()} className="table sticky" ref={tableRef}>
        <div className="header">
          {headerGroups.map((headerGroup, index) => (
            <div key={index} {...headerGroup.getHeaderGroupProps()} className="tr">
              {headerGroup.headers.map((column, index2) => {
                const thProps = column.getHeaderProps(column.getSortByToggleProps());

                return (
                  <div
                    key={index2}
                    {...thProps}
                    className="th d-flex justify-left-center align-items-center"
                  >
                    <div
                      className="text-left"
                      data-testid={`fml-technical-table-column-header-${column.render('Header')}`}
                    >
                      {column.render('Header')}
                    </div>
                    <div>
                      {column.canSort && (
                        <Icon
                          icon={fetchIcon(column)}
                          size={20}
                          className="default float-none"
                          data-testid={`fml-sort-icon-${column.render('Header')}`}
                          onClick={() =>{
                            eventTracker(
                              'Sorting',
                              `${column.render('Header')} - ${
                                column.isSorted && column.isSortedDesc
                                  ? 'sort-descending'
                                  : 'sort-ascending'
                              }`,
                            )
                          }}
                        />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        {isLoading && <Spinner alignClass={'spinner-table'} />}
        <div {...getTableBodyProps()} className="body">
          {rows.length > 0
            ? rows.map((row, index) => {
                prepareRow(row);

                return (
                  <div key={index} {...row.getRowProps()} className="tr">
                    {row.cells.map((cell, index2) => {
                      const tdProps = cell.getCellProps();

                      return (
                        <div
                          key={index2}
                          {...tdProps}
                          data-testid={`fml-technical-report-row-${index}-${cell.column.Header}`}
                          className={`td ${
                            row?.original?.vessel_ref_id ? 'vessel-company-type' : ''
                          }`}
                        >
                          {cell.render('Cell')}
                        </div>
                      );
                    })}
                  </div>
                );
              })
            : !isLoading && <NoResult />}
        </div>
      </div>

      <br />
      <div className="d-flex p-2 no-print">
        <div className="page-number-border">
          <PageNum
            onClick={() => (canPreviousPage ? pageSwitch(pageIndex - 1) : '')}
            disabled={!canPreviousPage}
            dataTestId="fml-pagination-previous"
          >
            {'<'}
          </PageNum>
          {visiblePages.map((page, index, array) => (
            <PageNum
              key={index}
              active={page - 1 === pageIndex}
              disabled={page - 1 === pageIndex}
              onClick={() => pageSwitch(page - 1)}
              dataTestId={`fml-pagination-${page}`}
            >
              {array[index - 1] + 2 < page ? `...${page}` : page}
            </PageNum>
          ))}
          <PageNum
            onClick={() => (canNextPage ? pageSwitch(pageIndex + 1) : '')}
            disabled={!canNextPage}
            dataTestId="fml-pagination-next"
          >
            {'>'}
          </PageNum>
        </div>
        <Form inline>
          <Form.Control
            as="select"
            value={pageSize}
            className="ml-3"
            onChange={(e) => {
              pageSizeSwitch(Number(e.target.value));
            }}
            data-testid="fml-select-pageSize"
          >
            {[10, 20, 50, 100].map((pageSize) => (
              <option key={pageSize} value={pageSize} data-testid={`fml-show-${pageSize}`}>
                Show {pageSize}
              </option>
            ))}
          </Form.Control>
        </Form>
      </div>
    </>
  );
};

export default TechnicalReportTable;
