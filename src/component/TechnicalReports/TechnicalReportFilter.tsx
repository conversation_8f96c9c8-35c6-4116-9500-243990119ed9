import React, { useContext } from 'react';
import { Tab, Form, Row } from 'react-bootstrap';
import CustomDatePicker from '../../component/customComponent/CustomDatePicker';
import moment from 'moment';
import VesselDropDown from './VesselDropDown';
import { TechnicalReportContext } from '../../context/TechnicalReportContext';
import { formatDate } from '../../util/view-utils';
const TechnicalReportFilter = ({ loading, tab, ga4EventTrigger }) => {
  const { vesselList, filterData, setFilterData, filterVoyageData, setFilterVoyageData } =
    useContext(TechnicalReportContext);
  const onInputChange = async (date, name) => {
    ga4EventTrigger(
      'Report Date (GMT)',
      `Vessel ${tab} Report - Filter`,
      `${name} - ${formatDate(date, 'DD MMM YYYY')}`,
    );
    setFilterData({
      ...filterData,
      [name]: date ? moment(date).format('YYYY-MM-DD') : '',
    });
  };

  const handleDropdownChange = (event) => {
    ga4EventTrigger('Vessel Name', `Vessel ${tab} Report - Filter`, `${event[0]?.value}`);
    setFilterData({ ...filterData, vessel: event });
    setFilterVoyageData({ ...filterVoyageData, vessel: event });
  };

  const handleVesselClear = () => {
    setFilterData({ ...filterData, vessel: [] });
  };

  return (
    <Tab.Container>
      <Row className="form-row">
        <Form.Label className="filter-reports">
          <b>Filter Reports</b>
        </Form.Label>
      </Row>

      <Row className="form-row">
        <Form.Group className="reports-filter-textField form-group">
          <Form.Control type="text" placeholder="Report Submit Date" disabled />
        </Form.Group>

        <Form.Group className="startDatePicker form-group">
          <CustomDatePicker
            value={filterData.startDate}
            onChange={(event) => onInputChange(event, 'startDate')}
            dataTestId="fml-technical-report-filter-start-date"
            disabled={loading}
          />
        </Form.Group>

        <Form.Group className=" datePickerRange form-group">To</Form.Group>

        <Form.Group className="endDatePicker form-group">
          <CustomDatePicker
            value={filterData.endDate}
            onChange={(event) => onInputChange(event, 'endDate')}
            dataTestId="fml-technical-report-filter-end-date"
            disabled={loading}
          />
        </Form.Group>

        <Form.Group className="reports-filter-textField form-group">
          <Form.Control type="text" placeholder="Vessel Name" disabled />
        </Form.Group>

        <Form.Group className={`vessel-dropdown ${loading && 'disabled'} form-group`}>
          <VesselDropDown
            onChange={handleDropdownChange}
            dropdownData={vesselList}
            labelKey="value"
            selectedItem={filterData.vessel}
            handleClear={handleVesselClear}
            placeholder="All Vessels"
            dataTestId="fml-technical-report-filter-vessel"
          />
        </Form.Group>
      </Row>
    </Tab.Container>
  );
};

export default TechnicalReportFilter;
