/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useCallback, useState } from 'react';
import { Form } from 'react-bootstrap';
import { Highlighter, Hint, Menu, Typeahead } from 'react-bootstrap-typeahead';
import { Icon } from '../../styleGuide';
import Spinner from '../Spinner';

const DropdownSearchControl = ({
  selectedVessel,
  onChange,
  vesselsDropdownData,
  labelKey,
  placeholder,
  dropdownLoading,
  dataTestId,
}) => {
  const [isVesselFoundInSearch, setVesselFoundInSearch] = useState(selectedVessel?.id !== null);
  const onSelect = useCallback(
    (selected) => {
      if (selected.length > 0) {
        onChange(selected[0]);
        if (!isVesselFoundInSearch) setVesselFoundInSearch(true);
      } else if (isVesselFoundInSearch) setVesselFoundInSearch(false);
    },
    [onChange, isVesselFoundInSearch],
  );

  const customVesselDropdownDesignProps = {};
  if (dropdownLoading) {
    customVesselDropdownDesignProps.renderMenu = (menuProps) => {
      return (
        <Menu {...menuProps}>
          <Spinner />
        </Menu>
      );
    };
  }
  customVesselDropdownDesignProps.filterBy = (option, props) => {
    const vesselName = option?.value ?? '';
    if (selectedVessel && isVesselFoundInSearch) {
      // Display all the options if there's a selection.
      return true;
    }
    // Otherwise filter on some criteria.
    return vesselName.toLowerCase().indexOf(props.text.toLowerCase()) !== -1;
  };
  customVesselDropdownDesignProps.renderInput = ({
    inputRef,
    referenceElementRef,
    ...inputProps
  }) => (
    <Hint>
      <Form.Control
        {...inputProps}
        ref={(node) => {
          inputRef(node);
          referenceElementRef(node);
        }}
        value={selectedVessel?.id && isVesselFoundInSearch ? '' : inputProps.value}
      />
    </Hint>
  );

  customVesselDropdownDesignProps.renderMenuItemChildren = (option, props) => {
    const DropdownIcon =
      option.id === selectedVessel?.id ? (
        <Icon
          icon="checked"
          size={16}
          color="green"
          className="float-left screening-page__moved-tick check-icon"
        />
      ) : (
        <div className="vessel_dropdown__dropdown-icon" />
      );
    return (
      // <MenuItem option={option} position={index} className="vessel-header-typeahead-item" key={`${option.vessel_id}-${option.ownership_id}`}>
      <div data-testid={`fml-vessel-dropdown-item-${option.value}`} key={`${option.id}`}>
        {DropdownIcon}
        <span>
          <Highlighter search={props.text}>{option.value ?? '- - -'}</Highlighter>
        </span>
      </div>
    );
  };

  return (
    <Typeahead
      placeholder={placeholder ?? ''}
      inputProps={{ 'data-testid': dataTestId }}
      className="vessel_dropdown__vessel-header-dropdown-switch"
      selected={isVesselFoundInSearch && selectedVessel ? [selectedVessel] : []}
      labelKey={labelKey}
      id="typeahead-wrapper"
      onChange={onSelect}
      options={vesselsDropdownData}
      {...customVesselDropdownDesignProps}
    />
  );
};
export default DropdownSearchControl;
