import React from 'react';
import { Col, Row } from 'react-bootstrap';
import _ from 'lodash';

export const RenderTableRow = (props) => {
  return props.data?.map((key, index) => {
    return (
      <>
        {key.header ? (
          <tr>
            <th className="details_page__sub-header" colSpan="2">
              {key?.header}
            </th>
          </tr>
        ) : (
          key.label && (
            <tr key={index}>
              <td className="details_page__row-name" data-testid={`fml-table-${key.label}-key`}>
                {key.label}
              </td>
              <td className="details_page__row-value" data-testid={`fml-table-${key.label}-value`}>
                {key.value}
                <span> {key.unit}</span>
              </td>
            </tr>
          )
        )}
      </>
    );
  });
};

export const TableSection = (props) => {
  const sortedData = _.sortBy(props.data, 'order');
  return (
    <Row>
      <Col>
        <table className="table table-hover">
          <thead className="details_page__table_head">
            <tr>
              <th id={props?.id} colSpan="2">
                {props?.title}
              </th>
            </tr>
          </thead>
          <tbody>
            <RenderTableRow data={sortedData} />
          </tbody>
        </table>
      </Col>
    </Row>
  );
};
export default TableSection;
