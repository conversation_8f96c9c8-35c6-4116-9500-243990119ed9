import _ from 'lodash';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import {
  ButtonToolbar,
  Col,
  Container,
  Dropdown,
  DropdownButton,
  Form,
  OverlayTrigger,
  Row,
  Tooltip,
} from 'react-bootstrap';
import { Link, useHistory, useParams, useLocation } from 'react-router-dom';
import vesselService from '../../service/vessel-service';
import { ErrorPage, Icon } from '../../styleGuide';
import {
  exportTableToExcel2,
  formatCertificateData,
} from '../../util/certificates-export/excel-export-v2';
import { vessel_list_column } from '../../util/certificates-export/certifictes-columns';
import getURLParams, { base64_encode } from '../../util/getURLParams';
import { formatDate, formatValue } from '../../util/view-utils';
import CustomTable from '../customComponent/CustomTable';
import ErrorAlert from '../ErrorAlert';
import { DetailContext } from '../../context/DetailContext';
import 'react-datepicker/dist/react-datepicker.css';
import moment from 'moment';
import AssignCertificateDialogV2 from './AssignCertificateDialog';
import keycloakService from '../../service/keycloak-service';
import { Dash } from '../../model/utils';
import { CustomSearchIcon } from '../customComponent/CustomIcons';
import { checkOperation } from '../../util/certificates-export/utils'

const { PARIS2_URL } = process.env;

const HeaderVessel = ({ row }) => (
  <div className="border-right-cell">
    <Link to={`/vessel/ownership/details/${row.vessel_ownership_id}`} className="button-link">
      {formatValue(_.get(row, 'vessel_name'))}
    </Link>
  </div>
);

const SurveyDepartment = ({ row }) => (
  <div className="border-right-cell">{checkOperation(formatValue(_.get(row, 'department')))}</div>
);

const RemarkCell = ({ row, index }) => {
  const remark = formatValue(_.get(row, 'remark'));
  const trimmedRemark = remark.length > 50 ? `${remark.substring(0, 50)}...` : remark;

  return (
    <div>
      <OverlayTrigger
        overlay={
          <Tooltip id="remark-tooltip" className="custom-tooltip-vessel-list">
            {remark}
          </Tooltip>
        }
        placement="bottom"
      >
        <div id={index} className="line-text-truncate" aria-hidden="true">
          {trimmedRemark}
        </div>
      </OverlayTrigger>
    </div>
  );
};

const AssignedCertificatesToVessel = ({ ga4EventTrigger }) => {
  const history = useHistory();
  const certificateName = getURLParams('name', history.location.search);
  const certificateType = getURLParams('type', history.location.search);
  const { ownershipId } = useParams();
  const certificateId = getURLParams('id', history.location.search);
  const [loading, setLoading] = useState(false);
  const [certificates, setCertificates] = useState();
  const [sortData, setSortData] = useState([{ id: 'vessel_name', asc: true }]);
  const [pageSize, setPageSize] = useState(200);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState(null);
  const [showCertificateModal, setShowCertificateModal] = useState(false);
  const [editData, setEditData] = useState({});

  const [searchKeyword, setSearchKeyword] = useState('');
  const [groupFilter, setGroupFilter] = useState({});
  const [techGroupFilter, setTechGroupFilter] = useState({});
  const [techGroups, setTechGroups] = useState([]);
  const location = useLocation();

  const fetchTechGroups = async () => {
    try {
      const response = await keycloakService.getTechgroup();
      setTechGroups(response?.data?.response?.tech_group || []);
    } catch (error) {
      console.error('Failed to fetch tech groups:', error);
    }
  };

  useEffect(() => {
    fetchTechGroups();
  }, []);

  const { roleConfig } = useContext(DetailContext);

  const dueDate = (row) => {
    const dateDiff = moment(row?.audit_due_date).diff(moment(new Date()), 'days');
    const fallback = dateDiff < 30 ? 'badge-warning' : 'badge-success';
    const badgeColor = dateDiff < 0 ? 'badge-danger' : fallback;
    if (!_.get(row, 'audit_due_date')) {
      return <div className="border-right-cell">{Dash}</div>;
    }

    return (
      <div className="border-right-cell">
        <OverlayTrigger
          overlay={
            <Tooltip id="due-date-tooltip" className="custom-tooltip-vessel-list">
              {`Due ${dateDiff > 0 ? 'in' : ''} ${Math.abs(dateDiff)} days ${
                dateDiff < 0 ? 'ago' : ''
              }`}
            </Tooltip>
          }
          placement="bottom"
        >
          <p style={{ display: 'flex', marginBottom: 0 }}>
            {formatDate(row?.audit_due_date)}
            {dateDiff < 60 && (
              <div className={`cert-due-date-badge badge badge-pill ml-2 ${badgeColor}`}>
                {`${dateDiff}D`}
              </div>
            )}
          </p>
        </OverlayTrigger>
      </div>
    );
  };

  const getDocument = (row) => {
    if (!row.url) {
      return <div className="border-left-cell ml-4"> --- </div>;
    }
    return (
      <Link
        className="button-link border-left-cell ml-3"
        to={{
          pathname: `${PARIS2_URL}/vessel/document?source=certificate&path=${base64_encode(
            row.url,
          )}&id=${row.id}&scrollable=true`,
        }}
        target="_blank"
      >
        View
      </Link>
    );
  };

  const columns = [
    {
      Header: '',
      id: 'no',
      sticky: 'left',
      type: 'text',
      disableSortBy: true,
      maxWidth: 80,
      columns: [
        {
          Header: 'No.',
          sticky: 'left',
          accessor: (row, index) => pageSize * pageIndex + index + 1,
          id: 'id',
          name: 'index',
          type: 'text',
          disableSortBy: true,
          maxWidth: 70,
          headerClassName: 'custom-table-height',
        },
      ],
    },
    {
      Header: '',
      id: 'vessel-t-t',
      sticky: 'left',
      headerClassName: 'border-right-cell',
      columns: [
        {
          Header: 'Vessel',
          sticky: 'left',
          headerClassName: 'border-right-cell custom-table-height',
          accessor: (row) => <HeaderVessel row={row} />, // NOSONAR
          id: 'vessel_name',
          name: 'vessel_name',
          type: 'text',
          minWidth: 300,
        },
      ],
    },
    {
      Header: 'Tech Group',
      accessor: (row) => formatValue(_.get(row, 'tech_group')),
      id: 'tech-group',
      name: 'tech_group',
      type: 'item',
      minWidth: 120,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Certificate Group',
      accessor: (row) => _.capitalize(formatValue(_.get(row, 'group'))),
      id: 'group',
      name: 'group',
      type: 'item',
      minWidth: 130,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Survey department',
      accessor: (row) => <SurveyDepartment row={row} />, // NOSONAR
      id: 'department',
      name: 'department',
      type: 'item',
      minWidth: 180,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Annual Audit/Inspection',
      type: 'item',
      id: 'Annual',
      headerClassName:
        'border-bottom border-left-cell border-right-cell annual-header-vessel-certs-table',
      columns: [
        {
          Header: 'Date of Survey',
          type: 'item',
          headerClassName: 'border-left-cell custom-table-height',
          accessor: (row) => formatDate(_.get(row, 'audit_date')),
          id: 'audit_date',
          name: 'audit_date',
          minWidth: 180,
        },
        {
          Header: 'Due Date',
          type: 'item',
          headerClassName: 'border-right-cell custom-table-height',
          accessor: (row) => dueDate(row),
          id: 'audit_due_date',
          name: 'audit_due_date',
          minWidth: 180,
        },
      ],
    },
    {
      Header: 'Underway Audit/Inspection',
      type: 'item',
      id: 'underway',
      headerClassName: 'border-bottom border-right-cell annual-header-vessel-certs-table',
      columns: [
        {
          Header: 'Date of Survey',
          type: 'item',
          accessor: (row) => formatDate(_.get(row, 'underway_audit_date')),
          id: 'underway_audit_date',
          name: 'underway_audit_date',
          minWidth: 180,
          headerClassName: 'custom-table-height',
        },
        {
          Header: 'Due Date',
          type: 'item',
          headerClassName: 'border-right-cell custom-table-height',
          accessor: (row) => <div className="border-right-cell">{formatDate(_.get(row, 'underway_audit_due_date'))}</div>,
          id: 'underway_audit_due_date',
          name: 'underway_audit_due_date',
          minWidth: 180,
        },
      ],
    },
    {
      Header: 'Place of Survey',
      accessor: (row) => formatValue(_.get(row, 'place')),
      id: 'place',
      name: 'place',
      type: 'item',
      minWidth: 180,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Grace Period',
      type: 'item',
      accessor: (row) =>
        formatValue(_.get(row, 'grace')) + formatValue(_.get(row, 'grace_unit')).charAt(0),
      id: 'grace_days',
      name: 'grace_period',
      minWidth: 150,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Status',
      type: 'item',
      accessor: (row) => _.capitalize(formatValue(_.get(row, 'status'))),
      id: 'status',
      name: 'status',
      headerClassName: 'custom-table-height',
      minWidth: 120,
    },
    {
      Header: 'Planned Date of Inspection',
      type: 'item',
      accessor: (row) => formatDate(_.get(row, 'planned_date')),
      id: 'planned_date',
      name: 'planned_date',
      minWidth: 180,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Port',
      type: 'item',
      accessor: (row) => formatValue(_.get(row, 'port')),
      id: 'port',
      name: 'port',
      minWidth: 120,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Remark',
      type: 'item',
      accessor: (row, index) => <RemarkCell row={row} index={index} />, // NOSONAR
      id: 'remark',
      name: 'remark',
      disableSortBy: true,
      minWidth: 300,
      headerClassName: 'custom-table-height',
    },
    {
      Header: '',
      id: 'view',
      sticky: 'right',
      disableSortBy: true,
      headerClassName: 'cert-right-sticky-header-adjustment border-left-cell',
      columns: [
        {
          Header: 'Document',
          //sticky: 'right',
          type: 'item',
          disableSortBy: true,
          headerClassName: 'no-print cert-right-sticky-header border-left-cell custom-table-height',
          accessor: (row) => getDocument(row),
          minWidth: 100,
        },
      ],
    },
  ];

  const ActionCell = ({ row }) => (
    <div hidden={isHidden(row, roleConfig)} className="ml-3">
      <Icon
        icon="Edit"
        size={20}
        data-testid="fml-assigned-certificates-edit-button"
        onClick={() => {
          setEditData(row);
          setShowCertificateModal(true);
        }}
      />
    </div>
  );

  if (roleConfig.certificates.assign) {
    columns.push({
      Header: '',
      id: 'Action',
      sticky: 'right',
      disableSortBy: true,
      hidden: !roleConfig.certificates.assign,
      headerClassName: 'actions-border-left-cell',
      columns: [
        {
          Header: 'Action',
          type: 'item',
          accessor: (row) => <ActionCell row={row} />, // NOSONAR
          disableSortBy: true,
          width: 100,
          headerClassName: 'custom-table-height',
        },
      ],
    });
  }

  const isHidden = (row, roleConfig) => {
    if (!roleConfig.certificates.assign) return true;
    if (roleConfig.certificates.all) return false;
    return !roleConfig.departments.includes(row.department) || !row.is_current_owner;
  };

  const fetchCertificates = async (
    sortData = [{ id: 'vessel_name', asc: true }],
    pageIndex = 0,
    pageSize = 200,
    search = {},
  ) => {
    setLoading(true);
    try {
      const response = await vesselService.getAssignedCertificates(
        certificateId,
        {
          sortBy: sortData,
          pageSize: pageSize,
          pageIndex: pageIndex,
        },
        search,
      );

      const certificates = response.data.results?.map((cet) => ({
        ...cet,
      }));

      setCertificates(certificates);
      const total = response.data.total;
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
      setError(null);
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
    }
    setLoading(false);
  };

  const debouncedFetchCertificates = useMemo(() => _.debounce(fetchCertificates, 700), []);

  const filterSearchObject = ({
    searchKeyword,
    groupFilter,
    techGroupFilter,
  }) => {
    const obj = {};
    if (searchKeyword) {
      obj.vesselName = searchKeyword;
    }

    if (Object.keys(groupFilter).length) {
      obj.certificateGroup = `${Object.keys(groupFilter).join(',')}`;
    }

    if (Object.keys(techGroupFilter).length) {
      obj.techGroup = `${Object.keys(techGroupFilter).join(',')}`;
    }

    return obj;
  };

  useEffect(() => {
    if (!roleConfig.vessel.view) {
      return;
    }

    debouncedFetchCertificates(
      sortData,
      pageIndex,
      pageSize,
      filterSearchObject({
        searchKeyword,
        groupFilter,
        techGroupFilter,
      }),
    );
  }, [
    searchKeyword,
    groupFilter,
    techGroupFilter,
  ]);

  useEffect(() => {
    return () => {
      debouncedFetchCertificates.cancel();
    };
  }, [debouncedFetchCertificates]);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
    fetchCertificates(sortData, value, pageSize);
  };

  const onPageSizeChange = (value) => {
    setPageSize(value);
    setPageIndex(0);
    fetchCertificates(sortData, 0, value);
  };

  const onSortChange = (value) => {
    setSortData(value);
    fetchCertificates(value, pageIndex, pageSize);
  };

  const handleExportExcel = () => {
    ga4EventTrigger('Export to Excel', 'Certificates & Surveys Vessel List', 'Export to Excel');
    exportTableToExcel2(
      {
        certificates: {
          jsonData: formatCertificateData(certificates),
          columns: vessel_list_column,
          tableinfo: {
            name: getURLParams('name', location?.search),
            type: getURLParams('type', location?.search),
            department: certificates ? certificates[0]?.department : '',
            crtGroup: '',
          },
        },
      },
      `Certificates & Surveys - ${certificateName}`,
    );
  };

  const handleSubmit = () => {
    fetchCertificates(sortData, pageIndex, pageSize);
    setEditData({});
  };

  const handleModalCancel = () => {
    setShowCertificateModal(false);
  };

  const handleClear = () => {
    setEditData({});
  };

  const certificatesButton = useMemo(() => {
    return (
      <div className="d-flex no-print">
        <ButtonToolbar className="pr-2">
          <Dropdown alignRight>
            <Dropdown.Toggle
              variant="outline-primary"
              id="dropdown-more"
              data-testid="fml-assigned-certificates-more-button"
            >
              More
            </Dropdown.Toggle>
            <Dropdown.Menu>
              <Dropdown.Item
                data-testid="fml-assigned-certificatest-export-excel"
                onClick={handleExportExcel}
              >
                Export to Excel
              </Dropdown.Item>
              <Dropdown.Item
                data-testid="fml-assigned-certificates-print"
                onClick={() => window.print()}
              >
                Print
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </ButtonToolbar>
      </div>
    );
  }, [certificates]);

  const CertificateAdvancedFilter = useMemo(() => {
    return (
      <Container>
        <Row id="surveys-filter" className="g-2">
          <Col className="d-flex">
            <div
              className="search-container"
              style={{
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                width: '100%',
              }}
            >
              <CustomSearchIcon
                style={{
                  position: 'absolute',
                  left: '10px',
                  zIndex: 1,
                  color: '#aaa',
                }}
              />
              <Form.Control
                id="search-bar"
                className="cert-search-bar flex-item"
                type="search"
                name="keyword"
                placeholder="Search Vessel"
                style={{ paddingLeft: '35px' }}
                onChange={(e) => {
                  setSearchKeyword(e.target.value);
                  debouncedFetchCertificates(
                    sortData,
                    pageIndex,
                    pageSize,
                    filterSearchObject({
                      searchKeyword: e.target.value,
                      groupFilter,
                      techGroupFilter,
                    }),
                  );
                }}
              />
            </div>
          </Col>

          <Col className="d-flex">
            <DropdownButton
              title={`${
                Object.entries(techGroupFilter)
                  .filter(([_, value]) => value)
                  .map(([key]) => key)
                  .join(', ')
                  .slice(0, 55) +
                  `${
                    Object.entries(techGroupFilter)
                      .filter(([_, value]) => value)
                      .map(([key]) => key)
                      .join(', ').length > 55
                      ? ' ...'
                      : ''
                  }` || 'Tech Group'
              }`}
              className="cert-group-dropdown flex-item"
            >
              {techGroups.map((group) => (
                <Dropdown.Item
                  eventKey={group}
                  key={group}
                  onClick={(e) => {
                    e.stopPropagation();
                    setTechGroupFilter((prev) => {
                      const isChecked = !prev[group];
                      if (isChecked) {
                        return { ...prev, [group]: true };
                      } else {
                        const updatedFilter = { ...prev };
                        delete updatedFilter[group];
                        return updatedFilter;
                      }
                    });
                  }}
                >
                  <Form.Check key={techGroupFilter[group]}>
                    <Form.Check.Input checked={!!techGroupFilter[group]} />
                    <Form.Check.Label>{group}</Form.Check.Label>
                  </Form.Check>
                </Dropdown.Item>
              ))}
              <Dropdown.Item
                eventKey="clear"
                className="text-primary text-underline"
                onClick={() => setTechGroupFilter({})}
              >
                Clear All
              </Dropdown.Item>
            </DropdownButton>
          </Col>

          <Col className="d-flex">
            <DropdownButton
              title={`${
                Object.entries(groupFilter)
                  .filter(([_, value]) => value)
                  .map(
                    ([key]) =>
                      ({
                        flag: 'Flag',
                        class: 'Class',
                        other: 'Other',
                        new_reg: 'New Regs',
                      }[key]),
                  )
                  .join(', ') || 'Certificate Groups'
              }`}
              className="cert-group-dropdown flex-item"
            >
              {[
                { name: 'Flag', key: 'flag' },
                { name: 'Class', key: 'class' },
                { name: 'Other', key: 'other' },
                { name: 'New Regs', key: 'new_reg' },
              ].map((group) => (
                <Dropdown.Item
                  eventKey={group.key}
                  key={group.key}
                  onClick={(e) => {
                    e.stopPropagation();
                    setGroupFilter((prev) => {
                      const isChecked = !prev[group.key];
                      if (isChecked) {
                        return { ...prev, [group.key]: true };
                      } else {
                        const updatedFilter = { ...prev };
                        delete updatedFilter[group.key];
                        return updatedFilter;
                      }
                    });
                  }}
                >
                  <Form.Check key={groupFilter[group.key]}>
                    <Form.Check.Input key={group.key} checked={!!groupFilter[group.key]} />
                    <Form.Check.Label>{group.name}</Form.Check.Label>
                  </Form.Check>
                </Dropdown.Item>
              ))}
              <Dropdown.Item
                eventKey="clear"
                className="text-primary text-underline"
                onClick={() => setGroupFilter({})}
              >
                Clear All
              </Dropdown.Item>
            </DropdownButton>
          </Col>
        </Row>
      </Container>
    );
  }, [
    groupFilter,
    techGroupFilter,
    techGroups,
  ]);

  const certTypePill = () => {
    let badgeClass = '';

    if (certificateType === 'Statutory') {
      badgeClass = 'badge-primary';
    } else if (certificateType === 'Important') {
      badgeClass = 'badge-info';
    } else {
      badgeClass = 'badge-light';
    }
    return (
      <div className="d-flex">
        <div className={'cert-title-badge-pill badge badge-pill ' + badgeClass}>
          {certificateType}
        </div>
      </div>
    );
  };

  return roleConfig.vessel.view ? (
    <Container id="assigned-cert-to-vessel" className="assigned-certificates-to-vessel-list">
      <div className="d-flex justify-content-between">
        <div className="technical-reports d-flex align-items-center">
          <div>
            <u>
              <Link
                className={`button-link`}
                to={`/vessel/ownership/details/${ownershipId}/certificates`}
              >
                Surveys and Certificates
              </Link>
            </u>{' '}
            / Assigned {certificateName}{' '}
          </div>{' '}
          {certTypePill()}
        </div>
        {certificatesButton}
      </div>
      <div className="certificate-filter-row">{CertificateAdvancedFilter}</div>
      <div className="pt-2">
        {error && <ErrorAlert message={error} />}
        <CustomTable
          column={columns}
          reportData={certificates}
          tableRef={null}
          isLoading={loading}
          pagination={true}
          pageCount={pageCount}
          totalCount={totalCount}
          setPageNo={onPageIndexChange}
          setPageListSize={onPageSizeChange}
          setSortData={onSortChange}
          pageNo={pageIndex}
          className="vessel-certificate-header vessel-certificate-header-vessel-list"
          pageSizeList={[10, 20, 50, 100, 200, 500]}
          pageSize={pageSize}
        />
        <AssignCertificateDialogV2
          showModal={showCertificateModal}
          edit={!_.isEmpty(editData)}
          data={editData}
          handleModalCancel={handleModalCancel}
          handleModalSubmit={handleSubmit}
          handleModalClear={handleClear}
          setListLoading={setLoading}
          setShowModal={setShowCertificateModal}
          setPageError={setError}
        />
      </div>
    </Container>
  ) : (
    <ErrorPage errorCode={403} />
  );
};

export default AssignedCertificatesToVessel;
