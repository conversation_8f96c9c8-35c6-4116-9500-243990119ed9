import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { Button, Col, Form, Modal, OverlayTrigger, Row, Tooltip } from 'react-bootstrap';
import { formatDate, getTimeDiffWithUTC } from '../../util/view-utils';
import CustomDatePicker from '../customComponent/CustomDatePicker';
import { STATUS } from '../../constants/certificates';
import vesselService from '../../service/vessel-service';
import { AsyncTypeahead } from 'react-bootstrap-typeahead';
import moment from 'moment';
import INTERVAL_UNIT from '../../constants/interval-unit';
import { DetailContext } from '../../context/DetailContext';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import CustomCountryPortDropDown from '../customComponent/CustomCountryPortDropDown';
import { BsUpload } from 'react-icons/bs';
import { IoClose } from 'react-icons/io5';
import FileSvg from '../customComponent/FileSvg';
import { toast } from 'react-toastify';
import { canUserEditCertificate } from '../../util/certificatesAndSurveys';
import { CustomCalendarIcon } from '../customComponent/CustomIcons';
import CustomDropDown from '../customComponent/CustomDropDown';

const bytesToKB = (bytes) => {
  return (bytes / 1024).toFixed(1);
};

const bytesToMB = (bytes) => {
  return (bytes / (1024 * 1024)).toFixed(1);
};

const displayBytes = (byte) => {
  if (byte < 1024) {
    return `${byte} B`;
  } else if (byte < 1024 * 1024) {
    return `${bytesToKB(byte)} KB`;
  }
  return `${bytesToMB(byte)} MB`;
};

const WrapCalendarIcon = () => {
  return (
    <CustomCalendarIcon
      style={{
        position: 'absolute',
        right: '10px',
        zIndex: 1,
        color: '#aaa',
        pointerEvents: 'none',
      }}
    />
  );
};

const UploadItem = ({ fileUpload, onRemove }) => {
  const { file } = fileUpload;
  const sizeLabel = displayBytes(file.size);

  return (
    <li key={file.name} className="upload-item-li">
      <Col className='crt-file-container' sm={1}>
        <FileSvg fill={'#0091B8'} />
      </Col>
      <Col className='crt-file-text-container' sm={10}>
        <h6 className="truncate w-full mb-0text-muted">{file.name}</h6>
        {file.size && <div className="uploader-label mt-6">{sizeLabel}</div>}
      </Col>
      <IoClose
        type="button"
        className="icon cursor-pointer"
        data-testid="uploadItemRemoveBtn"
        onClick={() => onRemove(file)}
        fontSize={16}
      />
    </li>
  );
};

const WrapCustomDatePicker = ({ value = '', onChange = (e) => {}, dataTestId = '', disabled=false }) => {
  const dateRef = useRef(null);

  useEffect(() => {
    if (!dateRef.current) return;
    dateRef.current.input.onfocus = () => {
      dateRef.current.input.parentNode.parentNode.parentNode.lastChild.style.display = 'none'
    }

    dateRef.current.input.onblur = () => {
      dateRef.current.input.parentNode.parentNode.parentNode.lastChild.style.display = 'block'
      if (!dateRef.current.input.value) {
        dateRef.current.input.parentNode.parentNode.parentNode.lastChild.style.display = 'block'
      } else {
        dateRef.current.input.parentNode.parentNode.parentNode.lastChild.style.display = 'none'
      }
    }

  }, []);

  return (
    <CustomDatePicker
      value={value}
      dataTestId={dataTestId}
      onChange={(e) => onChange(e)}
      wrapclassname="crt-wrapper-datepicker"
      icon={WrapCalendarIcon()}
      disabled={disabled}
      customProps={{ref:dateRef}}
    />
  );
};

const AssignCertificateDialog = ({
  showModal = true,
  setShowModal = () => {},
  edit = false,
  handleModalCancel = () => {},
  handleModalSubmit = () => {},
  handleModalClear = () => {},
  data,
  vesselId = '',
  setListLoading,
  setPageError,
  pageType,
}) => {
  const [newData, setNewData] = useState(data);
  const [certificateList, setCertificateList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [isDisabledSave, setIsDisabledSave] = useState(true);
  const [isAuditDueDate, setIsAuditDueDate] = useState(edit  ? !!data?.audit_due_date : true);
  const [isAuditDate, setIsAuditDate] = useState(edit ? !!data?.audit_date : true);
  const [form, setForm] = useState({});
  const [errors, setErrors] = useState({});
  const [modalError, setModalError] = useState('');
  const DROPDOWN_GRACE_PERIOD = Object.entries(INTERVAL_UNIT).map(([id, value]) => ({
    id: id,
    value: value,
  }));
  const { ga4EventTrigger, roleConfig, vesselName, setVesselName } = useContext(DetailContext);

  useEffect(() => {
    if (data?.vessel_name) {
      setVesselName(data?.vessel_name);
    }
  }, [data]);

  const validateInspectionField = (value, comparedValue, field, errorMessage, removeFieldError) => {
    const valid = !moment(value).isAfter(comparedValue, 'day');
    return {
      [field]: valid ? undefined : errorMessage,
      [removeFieldError]: undefined,
    };
  };

  const validateFile = (file) => {
    return {
      file: file?.size / 1000000 > 1000 ? 'Cannot upload a file larger than 1GB' : undefined,
    };
  };

  const validateGrace = (grace, grace_unit) => {
    const validateGraceValue = (grace, unit) => {
      const ranges = {
        DAY: { min: -30, max: 30, message: 'Value must be between -30 to 30' },
        MONTH: { min: -11, max: 11, message: 'Value must be between -11 to 11' },
        YEAR: { min: -5, max: 5, message: 'Value must be between -5 to 5' },
      };

      if (unit in ranges) {
        const { min, max, message } = ranges[unit];
        if (!_.inRange(grace, min, max + 1)) {
          return message;
        }
      }
      return undefined;
    };

    const validateInputs = () => {
      if (_.isEmpty(grace?.toString()) || !grace_unit) {
        return {
          grace_unit: !grace_unit ? 'Please enter the value' : undefined,
          grace: _.isEmpty(grace) ? 'Please enter the value' : undefined,
        };
      }
      return null;
    };

    const validationError = validateInputs();
    if (validationError) {
      return validationError;
    }

    const err_msg = validateGraceValue(grace, grace_unit);
    return {
      grace: err_msg,
      grace_unit: undefined,
    };
  };

  const validateName = (value) => ({
    name: _.isEmpty(value) ? 'Survey / Certificate is a required field' : undefined,
  });

  const setFieldValue = (field, value) => {
    const updateErrors = (field, value) => {
      let errObj = {
        ...errors,
        ...validateName(form.name),
        ...validateGrace(form.grace, form.grace_unit),
      };

      const validateAuditDates = (dateField, dueDateField, errorMessage) => {
        if (value && form[dueDateField]) {
          errObj = {
            ...errObj,
            ...validateInspectionField(
              value,
              form[dueDateField],
              field,
              errorMessage,
              dueDateField,
            ),
          };
        } else {
          errObj = {
            ...errObj,
            [dueDateField]: undefined,
            [dateField]: undefined,
          };
        }
      };

      switch (field) {
        case 'audit_date':
          validateAuditDates(
            'audit_date',
            'audit_due_date',
            'Annual audit/inspection date must be less than due date',
          );
          break;
        case 'audit_due_date':
          validateAuditDates(
            'audit_date',
            'audit_due_date',
            'Due date must be greater than audit/inspection date',
          );
          break;
        case 'underway_audit_date':
          validateAuditDates(
            'underway_audit_date',
            'underway_audit_due_date',
            'Underway audit/inspection date must be less than due date',
          );
          break;
        case 'underway_audit_due_date':
          validateAuditDates(
            'underway_audit_date',
            'underway_audit_due_date',
            'Underway due date must be greater than audit/inspection date',
          );
          break;
        case 'name':
          errObj = { ...errObj, ...validateName(value) };
          break;
        case 'file':
          errObj = { ...errObj, ...validateFile(value) };
          break;
        case 'grace':
          errObj = { ...errObj, ...validateGrace(value, form.grace_unit) };
          break;
        case 'grace_unit':
          errObj = { ...errObj, ...validateGrace(form.grace, value) };
          break;
      }

      errObj = _.pickBy(errObj, (i) => i !== undefined);
      setErrors(errObj);
      setIsDisabledSave(!_.isEmpty(errObj));
    };

    const updateFormData = (field, value) => {
      let newFormdata = { ...form };

      switch (field) {
        case 'country':
          newFormdata = {
            ...newFormdata,
            [field]: value,
            port_info: [],
            port: setPortData(value, []),
          };
          break;
        case 'port_info':
          newFormdata = {
            ...newFormdata,
            [field]: value,
            port: setPortData(form?.country, value),
          };
          break;
        default:
          /***** DO NOT CHANGE  ternary expression as Sonarqube suggestions to simpler alternatives,
          as we don't define the type of value for each field in frontend.
          For audit_due_date acceptable type is either date or null,
          if change to (value ?? null),  will convert the null value to empty string which will cause update certificate failure
          *****/
          newFormdata = { ...newFormdata, [field]: value ? value : null }; //NOSONAR
      }

      setForm(newFormdata);
    };

    updateErrors(field, value);
    updateFormData(field, value);
  };

  const validateDatesIsPressent = (state, field) => {
    let errObj = {
      ...errors,
    }
    if (state && !form[field]) {
      errObj = {
        ...errObj,
        [field] : 'Annual audit/inspection date must be filled'
      };
    } else {
      errObj = {
        ...errObj,
        [field]: undefined,
      };
    }

    setErrors(errObj)

    return (state && !form[field])
  };

  useEffect(() => {
    if (edit) {
      const formData = {
        status: _.get(newData, 'status', null),
        grace_unit: newData.grace_unit,
        audit_date: newData.audit_date,
        audit_due_date: newData.audit_due_date,
        underway_audit_date: newData.underway_audit_date,
        underway_audit_due_date: newData.underway_audit_due_date,
        planned_date: newData.planned_date,
        grace: newData.grace,
        port_info: newData.port_info,
        country: newData.country,
        port: newData.port,
        remark: newData.remark,
        place: newData.place,
        name: [{ id: newData.certificate_id, value: newData.name }],
        file: { name: newData.url },
      };
      setForm(formData);
    }
  }, [newData]);

  const validateDate = (date) => (date ? moment(date).format('YYYY-MM-DD') : null);

  const getEditData = (originalData, editFormData) => {
    const diff = _.fromPairs(
      _.differenceWith(
        _.toPairs(_.omit(editFormData, ['name', 'file', 'country', 'port_info'])),
        _.toPairs(originalData),
        _.isEqual,
      ),
    );
    let result = _.merge(
      diff,
      [
        'audit_date',
        'audit_due_date',
        'underway_audit_date',
        'underway_audit_due_date',
        'planned_date',
      ].reduce((res, item) => {
        if (diff[item]) res[item] = moment(diff[item]).format('YYYY-MM-DD');
        return res;
      }, {}),
    );
    if (editFormData.file?.name && editFormData.file?.type) {
      result.url = editFormData.file.name;
    } else if (editFormData.file === null) {
      result.url = null;
    }
    if (!editFormData.port) result.port = null;

    return result;
  };

  const getPostData = (form) => {
    return {
      status: form.status,
      grace_unit: form.grace_unit,
      audit_date: validateDate(form.audit_date),
      audit_due_date: validateDate(form.audit_due_date),
      underway_audit_date: validateDate(form.underway_audit_date),
      underway_audit_due_date: validateDate(form.underway_audit_due_date),
      planned_date: validateDate(form.planned_date),
      grace: form.grace,
      port: form.port ? form.port : null,
      remark: form.remark,
      place: form.place,
      url: form.file?.name,
    };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    edit
      ? ga4EventTrigger(
          'Confirm Edit Vessel Survey',
          'Vessel Surveys Certificates - Menu',
          form.name?.[0]?.value,
        )
      : ga4EventTrigger(
          'Save Assign Vessel Survey',
          'Vessel Surveys Certificates - Menu',
          form.name?.[0]?.value,
        );

    if(validateDatesIsPressent(isAuditDate, 'audit_date') || validateDatesIsPressent(isAuditDueDate, 'audit_due_date')) return;


    if (!_.isEmpty(errors)) return;

    let payload = edit ? getEditData(data, form) : getPostData(form);
    if (payload.grace) payload.grace = _.toNumber(payload.grace);
    setIsDisabledSave(true);
    setModalLoading(true);
    try {
      if (payload?.url) {
        payload = _.omit(payload, ['file']);
        payload.url = await uploadFile(
          edit,
          vesselId,
          form.name[0]?.id,
          { ...payload, id: data?.id },
          form.file,
        );
      }
      edit
        ? await vesselService.updateAssignCertificate(data?.id, payload)
        : await vesselService.createAssignCertificate(vesselId, form.name[0]?.id, payload);
      setListLoading(true);
      setModalLoading(false);
      setShowModal(false);
      handleModalSubmit();
      handleCancel();
      toast.success(edit ? 'Changes saved successfully' : 'Certificate assigned successfully');
    } catch (error) {
      const errorText = 'You are not able to save in the onboarding process.';
      if (error?.response?.data === errorText) {
        setModalError(errorText);
        setModalLoading(false);
        setIsDisabledSave(false);
      } else {
        setModalLoading(false);
        setShowModal(false);
        setIsDisabledSave(false);
        setPageError('Oops, something went wrong. Please try again.');
        setListLoading(false);
      }
      toast.error('Error saving the changes');
    }
  };

  const handleCancel = () => {
    setForm({});
    setErrors({});
    handleModalCancel();
    handleModalClear();
    setIsDisabledSave(true);
    setModalError('');
  };

  const uploadFile = async (isEdit, vesselId, certificateId, payload, file) => {
    let url = '';
    let preSignedUrl = '';
    try {
      const response = await vesselService.getPreSignedUploadLink({
        record_type: 'certificate',
        payload: isEdit
          ? payload
          : { ...payload, vessel_id: vesselId, certificate_id: certificateId },
        action: isEdit ? 'replace' : 'upload',
        file_name: file.name,
      });
      url = response.data.url;
      preSignedUrl = response.data.pre_signed_link;
    } catch (e) {
      setPageError(`${e?.response.data}. Please try again.`);
      setLoading(false);
    }

    if (url) {
      try {
        await vesselService.uploadPresignedDocument(preSignedUrl, file, file.type, 0);
      } catch (e) {
        url = '';
        setPageError(`${e?.response.data}. Please try again.`);
        setLoading(false);
      }
    }
    return url;
  };

  const handleSearch = async (searchedKeyword = '') => {
    try {
      setLoading(true);
      setCertificateList([]);
      const response = await vesselService.getAdminCertificatesList(
        {
          sortBy: [{ id: 'name', desc: false }],
        },
        '',
        searchedKeyword,
      );
      const listOptions = response.data.results
        .filter((item) => canUserEditCertificate(roleConfig, item.department))
        .map((item) => ({ id: item.id, value: item.name }));
      setCertificateList(listOptions);
      setLoading(false);
    } catch (error) {
      if (error?.code !== 'ERR_CANCELED') {
        setErrors('Oops, something went wrong. Please try again.');
        console.error(error);
        setLoading(false);
      }
    }
  };

  const filterBy = () => true;

  const setPortData = (country, port) => {
    const data = [];
    if (!_.isEmpty(country)) data.push(country[0]?.value);
    if (!_.isEmpty(port)) data.push(port[0]?.value);

    return data.join(', ');
  };

  const handleDrag = (event) => {
    event.preventDefault();
  };

  const handleDrop = async (event) => {
    event.preventDefault();
    setFieldValue('file', event.dataTransfer.files[0]);
  };

  const handleRemoveFile = () => {
    setFieldValue('file', null);
  };

  return (
    <Modal
      id="survey-certificate-modal"
      show={showModal}
      aria-labelledby="survey-certificate-modal"
      centered
      scrollable
      size="lg"
      backdrop="static"
      className="survey-certificate-modal-v2"
    >
      <CustomOverlayLoader active={modalLoading}>
        <Modal.Header className="sticky-header">
          <Modal.Title>
            {edit
              ? `Edit Assigned Survey/Certificate of ${vesselName}`
              : `Assign Survey/Certificate to ${vesselName}`}

            {/* <p className="required-field-text">* Required fields</p> */}
          </Modal.Title>
        </Modal.Header>
        {modalError && (
          <div className="alert-danger ml-3 alert-danger-with-border">{modalError}</div>
        )}

        <Form className="form-main-control">
          <Modal.Body>
            <Row>
              <Col>
                <Form.Group className="form-group" controlId="survey-certificate">
                  <Form.Label className="from-label">Survey / Certificate*</Form.Label>
                  <AsyncTypeahead
                    id="search-type-menu"
                    labelKey="value"
                    name="certificate Group"
                    inputProps={{ 'data-testid': 'fml-assign-survey-and-certificate-name' }}
                    placeholder="Search Survey/Certificate"
                    multiple={false}
                    disabled={edit}
                    filterBy={filterBy}
                    isLoading={loading}
                    options={certificateList}
                    selected={form.name ? form.name : []}
                    onChange={(e) => {
                      setFieldValue('name', e);
                      setCertificateList([]);
                    }}
                    onFocus={() => {
                      setFieldValue('name', []);
                    }}
                    onSearch={handleSearch}
                  />
                  <div className="validate-error">{errors.name}</div>
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Place of Survey/Certificate</Form.Label>
                  <Form.Control
                    value={form?.place}
                    data-testid="fml-assign-certificate-place"
                    placeholder="Please Type"
                    onChange={(e) => {
                      setFieldValue('place', e.target.value ? e.target.value : null);
                    }}
                  />
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Annual Audit/Inspection Date*</Form.Label>
                  <Row>
                    <span className="radiobox-container">
                      <Form.Check
                        type="radio"
                        label="Yes"
                        name="aunal-audit-date"
                        value="yes"
                        style={{color:'#343a40'}}
                        defaultChecked={isAuditDate}
                        onChange={() => {
                          setIsAuditDate(true)
                          setIsDisabledSave(true)
                        }}
                      />
                    </span>
                    <span className="radiobox-container">
                      <Form.Check
                        type="radio"
                        label="No"
                        name="aunal-audit-date"
                        value="no"
                        style={{color:'#343a40'}}
                        defaultChecked={!isAuditDate}
                        onChange={() => {
                          setIsAuditDate(false);
                          setFieldValue('audit_date', null);
                        }}
                      />
                    </span>
                  </Row>
                  {/* <div className="validate-error">{errors.audit_date}</div> */}
                </Form.Group>

                {/** repert */}
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Annual Audit/Inspection Due Date*</Form.Label>
                  <Row>
                    <Form.Check
                      type="radio"
                      label="Yes"
                      name="aunal-audit-due-date"
                      style={{color:'#343a40'}}
                      value="yes"
                      defaultChecked={isAuditDueDate}
                      //checked={selectedOption === "option1"}
                      onChange={() => {
                        setIsAuditDueDate(true)
                        setIsDisabledSave(true)
                      }}
                    />
                    <Form.Check
                      type="radio"
                      label="No"
                      name="aunal-audit-due-date"
                      value="no"
                      style={{color:'#343a40'}}
                      //checked={selectedOption === "option2"}
                      defaultChecked={!isAuditDueDate}
                      onChange={() => {
                        setIsAuditDueDate(false);
                        setFieldValue('audit_due_date', null);
                      }}
                    />
                  </Row>
                  {/* <div className="validate-error">{errors.audit_date}</div> */}
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Underway Audit/Inspection Date</Form.Label>
                  <WrapCustomDatePicker
                    value={form?.underway_audit_date}
                    dataTestId="fml-assign-certificate-underway-audit-date"
                    onChange={(e) => {
                      setFieldValue('underway_audit_date', e);
                    }}
                  />
                  <div className="validate-error">{errors.underway_audit_date}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Planned Date of Inspection</Form.Label>
                  <WrapCustomDatePicker
                    value={form?.planned_date}
                    dataTestId="fml-assign-certificate-planned-date"
                    onChange={(e) => setFieldValue('planned_date', e)}
                  />
                </Form.Group>

                <Form.Group className="form-group">
                  <CustomCountryPortDropDown
                    labelPort={<b>Port</ b>}
                    countryData={form.country ? form.country : []}
                    portData={form.port_info ? form.port_info : []}
                    rowData={data}
                    formUpdate={(e) => setNewData(e)}
                    onChangeCountry={(e) => {
                      setFieldValue('country', e);
                      setModalLoading(true);
                    }}
                    handleClearCountry={() => {
                      setFieldValue('country', []);
                    }}
                    onChangePort={(e) => {
                      setFieldValue('port_info', e);
                    }}
                    handleClearPort={() => {
                      setFieldValue('port_info', []);
                    }}
                    disablePort={false}
                    afterPortFetch={() => setModalLoading(false)}
                    required={false}
                    labelCountry = {<b>Country</ b>}
                    icontype='chevrondown'
                  />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Status</Form.Label>
                  <CustomDropDown
                      key={'crt-admin-department-dropdown'}
                      selectedItems={form?.status}
                      itemsList={STATUS}
                      clearText="Clear"
                      onchange={(value) => {
                        setFieldValue('status', value);
                      }}
                    />
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label"></Form.Label>
                  <WrapCustomDatePicker
                  value={form?.audit_date}
                  dataTestId="fml-assign-certificate-due-date"
                  disabled={!isAuditDate}
                  onChange={(e) => setFieldValue('audit_date', e)}
                  />
                  <div className="validate-error">{errors.audit_date}</div>
                </Form.Group>

                {/** repert */}
                <Form.Group className="form-group">
                  <Form.Label className="from-label"></Form.Label>
                  <WrapCustomDatePicker
                  value={form?.audit_due_date}
                  dataTestId="fml-assign-certificate-due-date"
                  disabled={!isAuditDueDate}
                  onChange={(e) => {
                    setFieldValue('audit_due_date', e);
                  }}
                  />
                  <div className="validate-error">{errors.audit_due_date}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Underway Audit/Inspection Due Date</Form.Label>
                  <WrapCustomDatePicker
                  value={form?.underway_audit_due_date}
                  dataTestId="fml-assign-certificate-underway-audit-due-date"
                  onChange={(e) => {
                    setFieldValue('underway_audit_due_date', e);
                  }}
                  />
                  <div className="validate-error">{errors.underway_audit_due_date}</div>
                </Form.Group>

                <Form.Group className="form-group">
                  <Form.Label className="from-label">Grace Period*</Form.Label>
                  <Row>
                    <Col style={{paddingRight:'0px', paddingLeft:'15px'}}>
                      <Form.Control
                        name="grace"
                        data-testid="fml-assign-certificate-grace"
                        value={form?.grace}
                        type="number"
                        onChange={(e) => {
                          setFieldValue('grace', e.target.value);
                        }}
                      />
                      <div className="validate-error">{errors.grace}</div>
                    </Col>
                    <Col style={{paddingLeft:'8px'}}>
                      <CustomDropDown
                      selectedItems={form?.grace_unit}
                      itemsList={DROPDOWN_GRACE_PERIOD}
                      clearText="Clear"
                      onchange={(value) => {
                        setFieldValue('grace_unit', value);
                      }}
                    />
                      <div className="validate-error">{errors.grace_unit}</div>
                    </Col>
                  </Row>
                </Form.Group>

              </Col>
            </Row>
            <Col className='crt-assign-certificate-remark'>
            <Form.Label className="from-label">Remark</Form.Label>
                  <Form.Control
                    as="textarea"
                    placeholder='Add Remarks'
                    maxLength="255"
                    value={form?.remark}
                    data-testid="fml-assign-certificate-remark"
                    onChange={(e) => {
                      setFieldValue('remark', e.target.value);
                    }}
                  />
                  <span>0/255</span>
            </Col>
            <Row>
              <Col md={12}>
                <Form.Group className="form-group">
                  <Form.Label className="from-label">Document</Form.Label>
                  {form.file?.name ? (
                    <div className="mt-1" >
                      <ul className="upload-item-ul">
                        <UploadItem
                          fileUpload={{ file: form.file, status: 'done' }}
                          onRemove={() => handleRemoveFile()}
                        />
                      </ul>
                    </div>
                  ) : (
                    <div
                      style={{borderStyle:'dashed'}}
                      className={`d-flex flex-column align-items-center py-5 mt-1 uploader-drag-drop-container`}
                      onDragEnter={handleDrag}
                      onDragLeave={handleDrag}
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onDrop={handleDrop}
                    >
                      <BsUpload fontSize={24} className="mb-3" fill={'#6C757D'} />
                      {/*  */}
                      <span className="uploader-label">Drag and drop here</span>
                      <span className="uploader-label">or</span>
                      <label className="uploader-browse-files" htmlFor="fileUploadInput">
                        Browse Files
                      </label>
                      <input
                        type="file"
                        value=""
                        id="fileUploadInput"
                        className="d-none"
                        onChange={(event) => setFieldValue('file', event.target.files?.[0])}
                      />
                    </div>
                  )}
                </Form.Group>
                {edit && (
                  <Form.Label className="edit-label pt-2">
                    Last Edited by {data?.updated_by ?? data?.created_by} on{' '}
                    {formatDate(data?.updated_at ?? data?.created_at, 'DD MMM YYYY HH:mm')}{' '}
                    {getTimeDiffWithUTC()}
                  </Form.Label>
                )}
              </Col>
            </Row>
          </Modal.Body>

          <Modal.Footer className="sticky-footer">
            <Button
              variant="primary"
              data-testid="fml-assign-certificate-close"
              onClick={handleCancel}
            >
              Close
            </Button>
            <OverlayTrigger
            overlay={
              isDisabledSave ? <Tooltip id="remark-tooltip" className="custom-tooltip-vessel-list">
                Add all mandatory fields
              </Tooltip> : <></>
            }
            placement="bottom"
            >
            <Button
              variant="secondary"
              data-testid="fml-assign-certificate-save"
              type="submit"
              disabled={isDisabledSave}
              onClick={handleSubmit}
            >
              Save
            </Button>
            </OverlayTrigger>
          </Modal.Footer>
        </Form>
      </CustomOverlayLoader>
    </Modal>
  );
};

export default AssignCertificateDialog;
