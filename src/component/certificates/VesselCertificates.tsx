import _, { capitalize, debounce } from 'lodash';
import moment from 'moment';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import {
  Button,
  Form,
  OverlayTrigger,
  Popover,
  Row,
  Tab,
  Tabs,
  DropdownButton,
  Dropdown,
  Container,
  Col,
} from 'react-bootstrap';
import { Loader } from 'react-bootstrap-typeahead';
import { Link, useLocation } from 'react-router-dom';
import ConfirmModal from '../customComponent/CustomConfirmationModal';
import { CERTIFICATE_GROUP, STATUS } from '../../constants/certificates';
import { DetailContext } from '../../context/DetailContext';
import { Dash } from '../../model/utils';
import vesselService from '../../service/vessel-service';
import { Icon } from '../../styleGuide';
import { CustomSearchIcon } from '../customComponent/CustomIcons';
import { formatDate, formatValue, isOnboardingSuptd } from '../../util/view-utils';
import { canUserEditCertificate } from '../../util/certificatesAndSurveys';
import CustomTable from '../customComponent/CustomTable';
import { SendEmailModal } from '../SendEmailModal';
import AssignCertificateDialog from './AssignCertificateDialog';
import { VesselContext } from '../../context/VesselContext';
import { base64_encode } from '../../util/getURLParams';
import { getTechGroupDetails } from '../../service/keycloak-service';
import { toast } from 'react-toastify';
import { checkOperation } from '../../util/certificates-export/utils'

const { PARIS2_URL } = process.env;

const InfoText = () => {
  return (
    <Row className="infotext">
      The <b>max size is 10 MB</b> for sending surveys docs.
    </Row>
  );
};

const WarningText = () => {
  return (
    <div className="model-body-content">
      <p>
        Are you sure you want to unassign this Survey and Certificate? This action will{' '}
        <b>permanently remove the document and all associated input data.</b>
      </p>
    </div>
  );
};

const FallBackContent = ({ type = '' }) => {
  const displaytype = type === 'all_types' ? '' : type;
  return (
    <div className="crt-fallback-main-div">
      <div className="crt-fallback-child-div">
        <span className="info-icon"></span>
        <div className="text-below">
          <span>Currently no {displaytype}</span> <br /> <span>Surveys and Certificates</span>
        </div>
      </div>
    </div>
  );
};

const CustomTooltip = ({ children, message = '', style = {}, show = true }) => {
  const [visible, setVisible] = useState(false);
  const tooltipRef = useRef(null);

  if (!show) {
    return <>{children}</>;
  }

  return (
    <div
      className="tooltip-container"
      onMouseEnter={() => setVisible(true)}
      onMouseLeave={() => setVisible(false)}
    >
      {children}
      {visible && (
        <div className="custom-tooltip" style={style} ref={tooltipRef}>
          <div className="tooltip-arrow"></div>
          {message}
        </div>
      )}
    </div>
  );
};

const VesselCertificates = ({ vesselId, techGroup }) => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const shouldSendEmail = queryParams.get('sendEmail') === 'true';
  const activeTabParam = queryParams.get('activeTab') ?? 'all_types';

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [certificates, setCertificates] = useState();
  const [filteredCertificates, setFilteredCertificates] = useState();
  const [dueFilteredCertificates, setDueFilteredCertificates] = useState();
  const [searchKeyword, setSearchKeyword] = useState(null);
  const [groupFilter, setGroupFilter] = useState({});
  const [deptFilter, setDeptFilter] = useState({});

  const [sortData, setSortData] = useState();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pageSize, setPageSize] = useState(200);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [isSendEmailModalVisible, setIsSendEmailModalVisible] = useState(shouldSendEmail);
  const [isSendSurveyDocModalVisible, setIsSendSurveyDocModalVisible] = useState(false);
  const [emailContent, setEmailContent] = useState({});
  const { vesselList } = useContext(VesselContext);

  const storeRowId = useRef({ id: null, name: null });
  const {
    vesselName,
    vesselEmail,
    ownershipId,
    ga4EventTrigger = () => {},
    roleConfig,
    isCurrentOwner,
    userEmail,
    handleError = () => {},
    setError = () => {},
  } = useContext(DetailContext);
  const [activeTab, setActiveTab] = useState(activeTabParam);
  const [activeDueTab, setActiveDueTab] = useState('all');
  const [certificatesTypeCount, setCertificatesTypeCount] = useState({
    all_types: 0,
    ancillary: 0,
    important: 0,
    statutory: 0,
  });
  const [showCertificateModal, setShowCertificateModal] = useState(false);
  const [rowData, setRowData] = useState({});
  const [selectedCertificates, setSelectedCertificates] = useState([]);
  const [showEmailSuccessModal, setShowEmailSuccessModal] = useState(false);
  const [emailContentLoading, setEmailContentLoading] = useState(false);
  const titleRef = useRef(null);
  const dataTableRef = useRef(null);
  const datePickerRef = useRef(null);

  const columns = [
    {
      Header: '',
      id: 'docs',
      sticky: 'left',
      headerClassName: 'ml5 border-right-cell',
      columns: [
        {
          Header: '',
          accessor: (row) => (
            <Form.Check
              type={'checkbox'}
              className="cert-checkbox checkbox-container"
              size={4}
              disabled={!row.url}
              onChange={(e) => handleCheckboxChange(e.target.checked, row)}
            />
          ), // NOSONAR
          id: 'trash',
          type: 'text',
          disableSortBy: true,
          minWidth: 30,
          maxWidth: 30,
        },
        {
          Header: 'No.',
          accessor: (row, index) => pageSize * pageIndex + index + 1,
          id: 'id',
          name: 'index',
          type: 'text',
          disableSortBy: true,
          maxWidth: 70,
          minWidth: 70,
          headerClassName: 'custom-table-height',
        },
        {
          Header: 'Surveys and Certificate',
          headerClassName: 'border-right-cell custom-table-height',
          accessor: (row) => {
            let certType = 'Ancillary';
            if (row.is_compulsory && activeTab !== 'important') {
              certType = 'Statutory';
            } else if (row.is_important && activeTab !== 'ancillary') {
              certType = 'Important';
            }
            return (
              <div className="border-right-cell">
                <Link
                  className={`button-link`}
                  onClick={() => {
                    ga4EventTrigger(
                      'Individual Vessel Survey',
                      'Vessel Surveys Certificates - Menu',
                      _.get(row, 'name'),
                    );
                  }}
                  to={`/vessel/${ownershipId}/assigned-certificates?id=${row.certificate_id}&name=${row.name}&type=${certType}`}
                >
                  {formatValue(_.get(row, 'name'))}
                </Link>
              </div>
            );
          },
          id: 'name',
          name: 'name',
          type: 'text',
          minWidth: 300,
          maxWidth: 300,
        },
      ],
    },
    {
      Header: 'Type',
      accessor: (row) => {
        let badgeText = '';
        let badgeClass = '';

        if (row.is_compulsory && activeTab !== 'important') {
          badgeText = 'Statutory';
          badgeClass = 'badge-primary';
        } else if (row.is_important && activeTab !== 'ancillary') {
          badgeText = 'Important';
          badgeClass = 'badge-info';
        } else {
          badgeText = 'Ancillary';
          badgeClass = 'badge-light';
        }
        return <div className={`badge badge-pill ${badgeClass}`}>{badgeText}</div>;
      },
      id: 'type',
      name: 'type',
      type: 'item',
      minWidth: 90,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Certificate Group',
      accessor: (row) =>
        formatValue(CERTIFICATE_GROUP.find((group) => group.id === row.group)?.value),
      id: 'group',
      name: 'group',
      type: 'item',
      minWidth: 130,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Survey Department',
      accessor: (row) => (
        <div className="border-right-cell"> {checkOperation(formatValue(_.get(row, 'department')))} </div>
      ), // NOSONAR
      id: 'department',
      name: 'department',
      type: 'item',
      minWidth: 180,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Annual Audit/Inspection',
      type: 'item',
      id: 'Annual',
      headerClassName: 'border-bottom border-left-cell border-right-cell crt-table-top-header',
      columns: [
        {
          Header: 'Date of Survey',
          headerClassName: 'border-left-cell custom-table-height',
          type: 'date',
          accessor: (row) => <div>{formatDate(_.get(row, 'audit_date'))}</div>, // NOSONAR
          id: 'audit_date',
          name: 'audit_date',
          minWidth: 180,
        },
        {
          Header: 'Due Date',
          type: 'date',
          headerClassName: 'border-right-cell custom-table-height',
          accessor: (row) => {
            const grace_days = row.grace_days ? (row.grace_days) : 0;
            const dateDiff = moment(row?.audit_due_date).diff(moment(new Date()), 'days') + grace_days;
            const fallback = dateDiff < 30 ? 'badge-warning' : 'badge-success';
            const badgeColor =
              dateDiff < 0 ? 'badge-danger' : fallback;
            if (!_.get(row, 'audit_due_date')) {
              return <div className="border-right-cell"> - - - </div>;
            }

            return (
              <div className="border-right-cell">
                <CustomTooltip
                  message={
                    <span>
                      {`Due ${dateDiff < 0 ? dateDiff * -1 : `in ` + dateDiff}`} <br />{' '}
                      {dateDiff < 0 ? 'days ago' : 'days'}
                    </span>
                  }
                >
                  <span>
                    {formatDate(row?.audit_due_date)}

                    {dateDiff < 60 && (
                      <div className={`badge badge-pill ml-2 ${badgeColor}`}>{`${dateDiff}D`}</div>
                    )}
                  </span>
                </CustomTooltip>
              </div>
            );
          },
          id: 'audit_due_date',
          name: 'audit_due_date',
          minWidth: 180,
        },
      ],
    },
    {
      Header: 'Underway Audit/Inspection',
      type: 'item',
      id: 'underway',
      headerClassName: 'border-bottom border-right-cell crt-table-top-header',
      columns: [
        {
          Header: 'Date of Survey',
          type: 'date',
          accessor: (row) => formatDate(_.get(row, 'underway_audit_date')),
          id: 'underway_audit_date',
          name: 'underway_audit_date',
          minWidth: 180,
          headerClassName: 'custom-table-height',
        },
        {
          Header: 'Due Date',
          type: 'date',
          headerClassName: 'border-right-cell custom-table-height',
          accessor: (row) => <div className="border-right-cell">{formatDate(_.get(row, 'underway_audit_due_date'))}</div>,
          id: 'underway_audit_due_date',
          name: 'underway_audit_due_date',
          minWidth: 180,
        },
      ],
    },
    {
      Header: 'Place of Survey',
      type: 'item',
      id: 'place',
      accessor: (row) => formatValue(_.get(row, 'place')),
      name: 'place',
      minWidth: 180,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Grace Period',
      type: 'item',
      accessor: (row) =>
        _.get(row, 'grace')
          ? formatValue(_.get(row, 'grace')) + _.get(row, 'grace_unit').charAt(0)
          : Dash,
      id: 'grace_unit',
      name: 'grace_period',
      minWidth: 150,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Status',
      type: 'item',
      accessor: (row) => formatValue(STATUS.find((status) => status.id === row.status)?.value),
      id: 'status',
      name: 'status',
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Planned Date of Inspection',
      type: 'date',
      accessor: (row) => formatDate(_.get(row, 'planned_date')),
      id: 'planned_date',
      name: 'planned_date',
      minWidth: 180,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Port',
      type: 'item',
      accessor: (row) => formatValue(_.get(row, 'port')),
      id: 'port',
      name: 'port',
      minWidth: 80,
      headerClassName: 'custom-table-height',
    },
    {
      Header: 'Remark',
      type: 'item',
      accessor: (row, index) => {
        const remark = formatValue(_.get(row, 'remark'));
        const remarktooltipstring = remark.split(' ').reduce((acc, word, index) => {
          if (index % 4 === 0 && index !== 0) {
            acc.push(<br key={index} />);
          }
          acc.push(word);
          acc.push(' ');
          return acc;
        }, []);
        return (
          <div>
            <CustomTooltip style={{ transform: 'translateX(-80%)' }} message={remarktooltipstring}>
              <div className="line-text-truncate">{remark}</div>
            </CustomTooltip>
          </div>
        );
      },
      id: 'remark',
      name: 'remark',
      disableSortBy: true,
      minWidth: 300,
      headerClassName: 'custom-table-height',
    },
  ];

  let rightStickyColumns = [
    {
      Header: 'Document',
      type: 'item',
      customClass: 'cert-right-sticky-header-adjustment border-left-cell',
      accessor: (row) => {
        if (!row.url) {
          return <div className="border-left-cell ml-3"> --- </div>;
        }

        return (
          <Link
            className="button-link border-left-cell ml-3"
            to={{
              pathname: `${PARIS2_URL}/vessel/document?source=certificate&path=${base64_encode(
                row.url,
              )}&id=${row.id}&scrollable=true`,
            }}
            target="_blank"
            onClick={() => {
              ga4EventTrigger(
                'View Survey Docs',
                'Vessel Surveys Certificates - Menu',
                formatValue(_.get(row, 'name')),
              );
            }}
          >
            View
          </Link>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
      minWidth: 100,
    },
  ];

  if (roleConfig.certificates.assign) {
    rightStickyColumns.push({
      Header: 'Action',
      type: 'item',
      accessor: (row) =>
        canUserEditCertificate(roleConfig, row?.department) && (
          <OverlayTrigger
            trigger={'click'}
            key="bottom"
            placement="bottom"
            rootClose
            overlay={
              <Popover>
                <Popover.Body>
                  <ul id="cert-action-popover" className="cert-action-popoverMenu">
                    <li
                      aria-hidden="true"
                      onClick={() => {
                        setRowData(row);
                        setShowCertificateModal(true);
                        ga4EventTrigger(
                          'Edit Vessel Survey',
                          'Vessel Surveys Certificates - Menu',
                          formatValue(_.get(row, 'name')),
                        );
                      }}
                    >
                      Edit
                    </li>
                    {(
                      <li
                        aria-hidden="true"
                        onClick={() => {
                          storeRowId.current = { id: row.id, name: row.name };
                          setShowConfirmModal(true);
                          ga4EventTrigger(
                            'Delete Vessel Survey',
                            'Vessel Surveys Certificates - Menu',
                            formatValue(_.get(row, 'name')),
                          );
                        }}
                      >
                        Unassign
                      </li>
                    )}
                  </ul>
                </Popover.Body>
              </Popover>
            }
          >
            <div className="ml-3">
              <Icon icon="more" size={20} className="default" style={{ cursor: 'pointer' }} />
            </div>
          </OverlayTrigger>
        ),
      disableSortBy: true,
      maxWidth: 100,
      minWidth: 80,
      customClass: '',
    });
  }

  columns.push({
    Header: '',
    id: 'Action',
    sticky: 'right',
    disableSortBy: true,
    headerClassName: 'no-print cert-right-sticky-header border-left-cell',
    columns: rightStickyColumns,
  });

  const CertificateButtons = useMemo(() => {
    return (
      <div className="no-print">
        <>
          <Button
            size="sm"
            variant="primary"
            className="mr12"
            data-testid="fml-vessel-certificates-assign-survey-Button"
            hidden={isCurrentOwner ? !roleConfig?.certificates.assign : !isCurrentOwner}
            onClick={() => {
              ga4EventTrigger(
                'Assign Vessel Survey',
                'Vessel Surveys Certificates - Menu',
                vesselName,
              );
              setShowCertificateModal(true);
            }}
          >
            Assign Survey/Certificate
          </Button>
          {roleConfig.vessel.send && (
            <>
              <Button
                id="send-email-btn"
                size="sm"
                variant="outline-primary"
                className="mr12"
                data-testid="fml-vessel-certificates-email-Button"
                onClick={() => {
                  ga4EventTrigger(
                    'Email Vessel Survey List',
                    'Vessel Surveys Certificates - Menu',
                    vesselName,
                  );
                  setIsSendEmailModalVisible(true);
                  getEmailMessage();
                }}
              >
                Email to Vessel
              </Button>
              <CustomTooltip
                style={{ transform: 'translateX(-80%)' }}
                show={selectedCertificates.length === 0}
                message={
                  <span>
                    {`Select Surveys and`} <br /> {`Certificates`}
                  </span>
                }
              >
                <Button
                  id="send-survey-button"
                  size="sm"
                  variant="outline-primary"
                  className=""
                  data-testid="fml-vessel-certificates-send-Survey-Button"
                  disabled={selectedCertificates.length === 0}
                  onClick={() => {
                    ga4EventTrigger(
                      'Send Vessel Survey Docs',
                      'Vessel Surveys Certificates - Menu',
                      vesselName,
                    );
                    getSendDocumentMessage();
                    setIsSendSurveyDocModalVisible(true);
                  }}
                >
                  Send Survey Docs
                </Button>
              </CustomTooltip>
            </>
          )}
        </>
      </div>
    );
  }, [certificates, selectedCertificates]);

  const filterCertificatesByType = (filter) => {
    let filteredCert;
    if (filter === 'all_types') filteredCert = certificates;
    else if (filter === 'statutory')
      filteredCert = certificates?.filter((cert) => cert.is_compulsory === true);
    else if (filter === 'important')
      filteredCert = certificates?.filter(
        (cert) => cert.is_important === true && cert.is_compulsory === false,
      );
    else
      filteredCert = certificates?.filter(
        (cert) => cert.is_compulsory === false && cert.is_important === false,
      );

    let advancedFilterResult = getAdvanceFilterResults(filteredCert ?? []);
    if (sortData && sortData?.length > 0) {
      const { id, desc } = sortData[0];
      advancedFilterResult = _.orderBy(advancedFilterResult, [id], [desc ? 'desc' : 'asc']);
    }
    setFilteredCertificates(advancedFilterResult);
  };

  const getAdvanceFilterResults = (dueFilteredCert) => {
    return dueFilteredCert.filter((cert) => {
      let shouldInclude = true;

      if (searchKeyword)
        shouldInclude = Object.entries(cert)
          .filter(([key, _]) =>
            ['name', 'place', 'remark', 'port', 'group', 'department'].includes(key),
          )
          .some(
            ([_, value]) =>
              value && String(value).toLowerCase().includes(searchKeyword.toLowerCase()),
          );

      const activeGroupFilters = Object.entries(groupFilter).filter(([_, value]) => value);
      if (activeGroupFilters.length > 0) {
        shouldInclude &&= activeGroupFilters.some(([group]) => cert.group == group);
      }

      const activeDeptFilters = Object.entries(deptFilter).filter(([_, value]) => value);
      if (activeDeptFilters.length > 0) {
        shouldInclude &&= activeDeptFilters.some(([dept]) => checkOperation(cert.department) == dept);
      }

      return shouldInclude;
    });
  };

  const filterCertificatesByDueDate = (
    filter = activeDueTab,
    filteredCert = filteredCertificates ?? [],
  ) => {
    let dueFilteredCert;
    if (filter === 'all') dueFilteredCert = filteredCert;
    else {
      dueFilteredCert = filteredCert.filter((cert) => {
        if (filter === 'missingDueDate') return !cert.audit_due_date;
        const graceDays = cert?.grace_days ? cert.grace_days : 0;
        const dueDate = moment(cert.audit_due_date).add(graceDays, 'days');
        console.log(dueDate.toLocaleString(), graceDays, cert.audit_due_date )
        const diff = dueDate.diff(moment(new Date()), 'days');
        if (filter === 'overdue') return diff < 0;
        else if (filter === 'due30') return diff < 30 && diff >= 0;
        else if (filter === 'due60') return diff < 60 && diff >= 30;
      });
    }

    const start = pageIndex * pageSize;
    setDueFilteredCertificates(dueFilteredCert.slice(start, start + pageSize));
    setTotalCount(dueFilteredCert.length);
  };

  useEffect(() => {
    filterCertificatesByDueDate();
  }, [filteredCertificates, activeDueTab, pageIndex, pageSize]);

  useEffect(() => {
    filterCertificatesByType(activeTab);
  }, [certificates, activeTab, searchKeyword, groupFilter, deptFilter]);

  const onPageIndexChange = (value) => {
    setPageIndex(value);
  };

  const onPageSizeChange = (value) => {
    setPageSize(value);
    setPageIndex(0);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Vessel Surveys Certificates - List', value[0].id);
    setSortData(value);
    fetchCertificates();
  };

  const CertitificateTypeTabs = useMemo(() => {
    const tabs = [
      {
        title: 'All',
        key: 'all_types',
        count: certificatesTypeCount.all_types,
      },
      {
        title: 'Statutory',
        key: 'statutory',
        count: certificatesTypeCount.statutory,
      },
      {
        title: 'Important',
        key: 'important',
        count: certificatesTypeCount.important,
      },
      {
        title: 'Ancillary',
        key: 'ancillary',
        count: certificatesTypeCount.ancillary,
      },
    ];
    return (
      <Tabs
        activeKey={activeTab}
        variant="pills"
        onSelect={(eventKey) => {
          setActiveTab(eventKey);
          setPageIndex(0);
          ga4EventTrigger(
            `Number of clicks to ${capitalize(
              eventKey === 'All_types' ? 'All' : eventKey,
            )} section`,
            'Vessel Surveys Certificates - Main Page',
            'Survey and Certificate - ' + capitalize(eventKey === 'All_types' ? 'All' : eventKey),
          );
        }}
      >
        {tabs.map((tab) => (
          <Tab
            title={
              <div>
                {tab.title}{' '}
                <span
                  className={`badge badge-pill ${
                    tab.key == activeTab ? 'badge-secondary' : 'badge-light'
                  }`}
                >
                  {tab.count}
                </span>
              </div>
            }
            eventKey={tab.key}
            key={tab.key}
            tabClassName="reference__tabNavigationItem"
          ></Tab>
        ))}
      </Tabs>
    );
  }, [certificatesTypeCount, activeTab]);

  const CertificateDueTabs = useMemo(() => {
    let overdue = 0,
      due30 = 0,
      due60 = 0,
      missingDueDate = 0;

    filteredCertificates?.forEach((cert) => {
      if (!cert.audit_due_date) {
        missingDueDate++;
        return;
      }
      const graceDays = cert?.grace_days ? cert.grace_days : 0;
      const dueDate = moment(cert.audit_due_date).add(graceDays, 'days');
      const diff = dueDate.diff(moment(new Date()), 'days');
      if (diff < 0) overdue++;
      else if (diff < 30 && diff >= 0) due30++;
      else if (diff < 60 && diff >= 0) due60++;
    });

    const tabs = [
      {
        title: 'All',
        key: 'all',
        count: filteredCertificates?.length ?? 0,
      },
      {
        title: 'Overdue',
        key: 'overdue',
        count: overdue,
      },
      {
        title: 'Due Within 30 Days',
        key: 'due30',
        count: due30,
      },
      {
        title: 'Due Within 60 Days',
        key: 'due60',
        count: due60,
      },
    ];
    return (
      <>
        {tabs.map((tab) => (
          <Button
            key={tab.key}
            variant="outline-primary"
            className={tab.key == activeDueTab ? 'active' : ''}
            onClick={() => {
              setActiveDueTab(tab.key);
              setPageIndex(0);
              let eventAction = 'Number of clicks to Overdue Filter';
              let eventLabel = 'Survey and Certificate - Overdue Filter';
              if (tab.key === 'due30') {
                eventAction = 'Number of clicks to Due Within 30 days Filter';
                eventLabel = 'Survey and Certificate - Due Within 30 days Filter';
              } else if (tab.key === 'due60') {
                eventAction = 'Number of clicks to Due Within 60 days Filter';
                eventLabel = 'Survey and Certificate - Due Within 60 days Filter';
              } else if (tab.key === 'all') {
                eventAction = 'Number of clicks to All Filter';
                eventLabel = 'Survey and Certificate - All Filter';
              }
              ga4EventTrigger(eventAction, 'Vessel Surveys Certificates - Main Page', eventLabel);
            }}
          >
            {
              <div>
                {tab.title}{' '}
                <span
                  className={`badge badge-pill ${
                    tab.key == activeDueTab ? 'badge-info' : 'badge-light'
                  }`}
                >
                  {tab.count}
                </span>
              </div>
            }
          </Button>
        ))}
      </>
    );
  }, [filteredCertificates, certificatesTypeCount, activeTab, activeDueTab]);

  const CertificatePagination = useMemo(() => {
    if (totalCount === 0) return null;

    const maxPageCount = Math.ceil(totalCount / pageSize);
    const pages = Array.from({ length: maxPageCount }, (_, i) => {
      return (
        <div
          key={'page' + i}
          className={`page-num ${pageIndex === i ? 'page-num-active' : 'page-num-enabled'}`}
          data-testid="fml-pagination-1"
          onClick={() => onPageIndexChange(i)}
          onKeyDown={() => {}}
        >
          {i + 1}
        </div>
      );
    });

    if (maxPageCount > 5) {
      if (pageIndex < 5) {
        pages.splice(5, maxPageCount - 6, <div key={'pagefirst'}>...</div>);
      } else if (pageIndex > maxPageCount - 5) {
        pages.splice(1, maxPageCount - 6, <div key={'pagelast'}>...</div>);
      } else {
        pages.splice(pageIndex + 2, maxPageCount - pageIndex - 3, <div key={'pagemid2'}>...</div>);
        pages.splice(1, pageIndex - 2, <div key={'pagemid1'}>...</div>);
      }
    }

    return (
      <>
        <div className="page-number-border">
          <div
            className={`page-num ${pageIndex === 0 ? 'page-num-disabled' : 'page-num-enabled'}`}
            data-testid="fml-pagination-previous"
            aria-hidden="true"
            onClick={() => onPageIndexChange(pageIndex - 1 > 0 ? pageIndex - 1 : 0)}
          >
            &lt;
          </div>
          {pages}
          <div
            className={`page-num ${
              pageIndex === maxPageCount - 1 ? 'page-num-disabled' : 'page-num-enabled'
            }`}
            data-testid="fml-pagination-next"
            aria-hidden="true"
            onClick={() =>
              onPageIndexChange(pageIndex + 1 < maxPageCount ? pageIndex + 1 : maxPageCount - 1)
            }
          >
            &gt;
          </div>
        </div>
        <form>
          <select
            data-testid="fml-select-pageSize"
            className="ml-3 form-control"
            onChange={(e) => onPageSizeChange(Number(e.target.value))}
            defaultValue={pageSize}
          >
            {[10, 20, 50, 100, 200, 500].map((size) => (
              <option value={size} data-testid={`fml-show-${size}`} selected={size == pageSize}>
                Show {size}
              </option>
            ))}
          </select>
        </form>
      </>
    );
  }, [dueFilteredCertificates, pageSize, pageIndex, pageCount, totalCount]);

  const CertificateAdvancedFilter = useMemo(() => {
    return (
      <Container className="p-0">
        <Row id="surveys-filter" className="">
          <Col md={4} className="d-flex">
            <div
              className="search-container"
              style={{
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                width: '100%',
              }}
            >
              <Form.Control
                id="search-bar"
                className="cert-search-bar flex-item"
                style={{ paddingLeft: '30px' }}
                type="search"
                name="keyword"
                placeholder="Search Surveys and Certificates"
                onClick={() =>
                  ga4EventTrigger(
                    'Click',
                    'Vessel Certificate Keyword Search',
                    'Vessel Certificate',
                  )
                }
                onChange={(e) => setSearchKeyword(e.target.value)}
              />
              <CustomSearchIcon
                style={{
                  position: 'absolute',

                  left: '8px',

                  zIndex: 1,

                  color: '#aaa',
                }}
              />
            </div>
          </Col>

          <Col className="d-flex">
            <DropdownButton
              title={`${
                Object.entries(groupFilter)
                  .filter(([_, value]) => value)
                  .map(
                    ([key]) =>
                      ({
                        flag: 'Flag',
                        class: 'Class',
                        other: 'Other',
                        new_reg: 'New Regs',
                      }[key]),
                  )
                  .join(', ') || 'Certificate Groups'
              }`}
              className="cert-group-dropdown flex-item"
            >
              {[
                { name: 'Flag', key: 'flag' },
                { name: 'Class', key: 'class' },
                { name: 'Other', key: 'other' },
                { name: 'New Regs', key: 'new_reg' },
              ].map((group) => (
                <Dropdown.Item
                  eventKey={group.key}
                  key={group.key}
                  style={{color:'#343a40'}}
                  onClick={(e) => {
                    e.stopPropagation();
                    setGroupFilter((prev) => ({
                      ...prev,
                      [group.key]: !prev[group.key],
                    }));
                    ga4EventTrigger(
                      'Number of clicks to Certificate Group filter',
                      'Vessel Surveys Certificates - Main Page',
                      'Survey and Certificate - Certificate Group Filter',
                    );
                  }}
                >
                  <Form.Check>
                    <Form.Check.Input
                      className="custom-colour-checkbox checkbox-container"
                      key={group.key}
                      onClick={(e) => {
                        e.stopPropagation();
                        setGroupFilter((prev) => ({
                          ...prev,
                          [group.key]: !prev[group.key],
                        }));
                        ga4EventTrigger(
                          'Number of clicks to Certificate Group filter',
                          'Vessel Surveys Certificates - Main Page',
                          'Survey and Certificate - Certificate Group Filter',
                        );
                      }}
                      checked={!!groupFilter[group.key]}
                    />
                    <Form.Check.Label>{group.name}</Form.Check.Label>
                  </Form.Check>
                </Dropdown.Item>
              ))}
              <Dropdown.Item
                eventKey="clear"
                className="text-primary text-underline"
                onClick={() => setGroupFilter({})}
              >
                Clear All
              </Dropdown.Item>
            </DropdownButton>
          </Col>

          <Col className="d-flex">
            <DropdownButton
              className="cert-group-dropdown flex-item"
              style={{marginRight:'15px'}}
              title={`${
                Object.entries(deptFilter)
                  .filter(([_, value]) => value)
                  .map(([key]) => key)
                  .join(', ').slice(0, 55) + 
                  `${
                    Object.entries(deptFilter)
                      .filter(([_, value]) => value)
                      .map(([key]) => key)
                      .join(', ').length > 55
                      ? ' ...'
                      : ''
                  }` || 'Survey Department'
              }`}
            >
              {['Tech Group', 'QMS Management', 'Fleet Personnel', 'Insurance', 'Marine Operations'].map((dept) => (
                <Dropdown.Item
                  eventKey={dept}
                  key={dept}
                  onClick={(e) => {
                    e.stopPropagation();
                    setDeptFilter((prev) => ({
                      ...prev,
                      [dept]: !prev[dept],
                    }));
                    ga4EventTrigger(
                      'Number of clicks to Survey department filter',
                      'Vessel Surveys Certificates - Main Page',
                      'Survey and Certificate - Survey Department Filter',
                    );
                  }}
                >
                  <Form.Check>
                    <Form.Check.Input
                      className="custom-colour-checkbox checkbox-container"
                      onClick={(e) => {
                        e.stopPropagation();
                        setDeptFilter((prev) => ({
                          ...prev,
                          [dept]: !prev[dept],
                        }));
                        ga4EventTrigger(
                          'Number of clicks to Survey department filter',
                          'Vessel Surveys Certificates - Main Page',
                          'Survey and Certificate - Survey Department Filter',
                        );
                      }}
                      checked={!!deptFilter[dept]}
                    />
                    <Form.Check.Label>{dept}</Form.Check.Label>
                  </Form.Check>
                </Dropdown.Item>
              ))}
              <Dropdown.Item
                eventKey="clear"
                className="text-primary text-underline"
                onClick={() => setDeptFilter({})}
              >
                Clear All
              </Dropdown.Item>
            </DropdownButton>
          </Col>
        </Row>
      </Container>
    );
  }, [groupFilter, deptFilter]);

  const paginationWraper = () => {
    return (
      <div className="certificate-pagination-row">
        <div className="certificate-pagination">{CertificatePagination}</div>
      </div>
    );
  };

  const handleRemove = async () => {
    const { id, name } = storeRowId.current;
    ga4EventTrigger('Confirm Delete Vessel Survey', 'Vessel Surveys Certificates - Menu', name);
    try {
      await vesselService.deleteCertificate(id);
      fetchCertificates();
      setPageIndex(0);
      setError(null);
      setShowConfirmModal(false);
      toast.success('Successfully unassigned');
    } catch (error) {
      console.log('Unable to remove certificates ', error);
      setShowConfirmModal(false);
      handleError(error.response);
      toast.error('Unable to remove certificates');
    }
    setLoading(false);
  };

  const fetchCertificates = async (pageSize = 200) => {
    setLoading(true);
    try {
      const response = await vesselService.getVesselCertificates(vesselId, {}, '');
      const result = response.data.results;

      const statutory = result.filter((cert) => cert.is_compulsory).length;
      const important = result.filter(
        (cert) => cert.is_important && cert.is_compulsory === false,
      ).length;
      const ancillary = result.filter(
        (cert) => cert.is_compulsory === false && cert.is_important === false,
      ).length;
      const total = response.data.total;

      const sortedResults = _.orderBy(result, ['audit_due_date'], ['asc']);
      setCertificates(sortedResults);
      setCertificatesTypeCount({
        all_types: total,
        statutory,
        important,
        ancillary,
      });
      setPageCount(Math.ceil(total / pageSize));
      setTotalCount(total);
      setError(null);
    } catch (error) {
      handleError(error.response);
    }
    setLoading(false);
    if (initialLoading) setInitialLoading(false);
  };

  useEffect(() => {
    fetchCertificates();
  }, []);

  useEffect(() => {
    if (initialLoading) return;
    const handleScroll = debounce(() => {
      if (titleRef.current) {
        titleRef.current?.click();
      }
    }, 100);
    dataTableRef.current?.addEventListener('scroll', handleScroll);
    if (datePickerRef?.current?.input) {
      datePickerRef.current.input.onfocus = (e) => {
        e.target.parentElement.parentElement.parentElement.lastElementChild.style.display = 'none';
      };
      datePickerRef.current.input.onblur = (e) => {
        e.target.parentElement.parentElement.parentElement.lastElementChild.style.display = 'block';
      };
    }

    return () => {
      dataTableRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, [initialLoading]);

  const handleCancel = () => {
    storeRowId.current = { id: null, name: null };
    setShowConfirmModal(false);
  };

  const handleSubmit = () => {
    fetchCertificates();
    setRowData({});
  };

  const handleModalCancel = () => {
    setShowCertificateModal(false);
  };

  const handleClear = () => {
    setRowData({});
  };

  const getEmailMessage = async () => {
    try {
      setEmailContentLoading(true);
      const listItem = _.find(vesselList, { id: Number(ownershipId) });
      const response = await vesselService.getVesselCertificates(vesselId, {}, '');
      const values = [];
      response.data.results?.forEach((cert) => {
        const graceDays = cert?.grace_days ? cert.grace_days : 0;
        const validDays = moment(cert?.audit_due_date).diff(moment(new Date()), 'days') + graceDays;
        if (cert?.audit_due_date && validDays <= 60) {
          values.push(cert);
        } else {
          return;
        }
      });
      values.sort((cert1, cert2) => (cert1.name > cert2.name ? 1 : -1));
      const techGroupEmail = await getTechGroupDetails(`tech_groups=${techGroup}&attributes=email`);
      setEmailContent({
        from: userEmail,
        subject: `Surveys and Certificates for ${vesselName}`,
        recipient: vesselEmail,
        cc: techGroupEmail?.data?.techGroupDetails?.[0]?.email?.[0] ?? '',
        message: `Master - ${vesselName}\n\nPlease update us on the status of the following surveys. \n${values
          .map((cert) => {
            const name = _.get(cert, 'name');
            const audit_date = _.get(cert, 'audit_date');
            const place = _.get(cert, 'place');
            const audit_due_date = _.get(cert, 'audit_due_date');

            return `\nSurvey\t: ${name}
                    \nLast Done: ${formatDate(audit_date)}
                    \nat\t: ${place ? place : 'Nil'}
                    \nDue Date: ${formatDate(audit_due_date)}\n`;
          })
          ?.join('')}\nBest Regards\nFML /  ${listItem?.fleet_staff.tech_group}`,
      });
    } catch (error) {
      setIsSendEmailModalVisible(false);
      handleError(
        error.response,
        'Oops, something went wrong with getting email content. Please try again.',
      );
    }
    setEmailContentLoading(false);
  };

  useEffect(() => {
    // If the URL has sendEmail=true, get the email content
    if (shouldSendEmail) {
      getEmailMessage();
    }
  }, [shouldSendEmail]);

  const getSendDocumentMessage = async () => {
    const values = [];
    selectedCertificates?.map((cert) => values.push(cert.url));
    const techGroupEmail = await getTechGroupDetails(`tech_groups=${techGroup}&attributes=email`);
    setEmailContent({
      from: userEmail,
      subject: `Surveys and Certificates Documents for ${vesselName}`,
      recipient: '',
      cc: techGroupEmail?.data?.techGroupDetails?.[0]?.email?.[0] ?? '',
      message: '',
      documents: values,
    });
  };

  const handleCheckboxChange = (isChecked, item) => {
    if (isChecked) {
      setSelectedCertificates([...selectedCertificates, item]);
    } else {
      setSelectedCertificates(selectedCertificates.filter((cert) => cert.id !== item.id));
    }
  };

  return (
    <div ref={dataTableRef} className="vessel-certificates-v2">
      {initialLoading ? (
        <div className="cert-loader">
          <Loader />
        </div>
      ) : (
        <>
          <div className="d-flex justify-content-between">
            <div ref={titleRef} className="font-weight-bold emergency-drills">
              Assigned Surveys And Certificates
            </div>
            {CertificateButtons}
          </div>

          <Tab.Pane eventKey={'all'} className="show">
            <div className="reference__tabNavigation">{CertitificateTypeTabs}</div>
          </Tab.Pane>

          <div className="certificate-filter-row">{CertificateAdvancedFilter}</div>

          <div className="certificate-pagination-row">
            <div className="certificate-pagination">{CertificatePagination}</div>
            <div className="certificate-due-tab">{CertificateDueTabs}</div>
          </div>

          <div className="certificate-table">
            {totalCount === 0 ? (
              <FallBackContent type={''} />
            ) : (
              <CustomTable
                column={columns}
                reportData={dueFilteredCertificates}
                tableRef={dataTableRef}
                isLoading={loading}
                pagination={false}
                setSortData={onSortChange}
                className="vessel-certificate-header print-scaling"
                pageSize={pageSize}
                paginationComponent={paginationWraper}
              />
            )}

            <ConfirmModal
              id={'survey-confirm-modal'}
              showConfirmModal={showConfirmModal}
              setShowConfirmModal={setShowConfirmModal}
              title={'Unassign Survey and Certificate'}
              children={WarningText()}
              error={
                isOnboardingSuptd() ? 'You are not able to delete in the onboarding process.' : ''
              }
              confirmText={'Unassign'}
              handleCancel={handleCancel}
              handleConfirm={handleRemove}
              isDisabledConfirm={isOnboardingSuptd()}
              headerClassName="sticky-header"
              footerClassName="sticky-footer"
            />

            <ConfirmModal
              showConfirmModal={showEmailSuccessModal}
              setShowConfirmModal={setShowEmailSuccessModal}
              content={'Email has been sent'}
              confirmText={'Close'}
              handleConfirm={() => setShowEmailSuccessModal(false)}
              hideCancel={true}
              headerClassName="sticky-header"
              footerClassName="sticky-footer"
            />

            <SendEmailModal
              title="Email List to Vessel"
              headerClassName="sticky-header"
              footerClassName="sticky-footer"
              isVisible={isSendEmailModalVisible}
              onClose={() => setIsSendEmailModalVisible(false)}
              onSuccess={() => {
                setIsSendEmailModalVisible(false);
                toast.success('Email sent to vessel');
              }}
              onError={() => {
                setIsSendEmailModalVisible(false);
                toast.error('Error sending the Email');
              }}
              modalid={`send-email-modal`}
              loading={emailContentLoading}
              emailContent={emailContent}
              showDocuments={emailContent.documents}
              onSubmitButtonClick={() =>
                ga4EventTrigger(
                  emailContent.documents
                    ? 'Confirm Send Vessel Survey Docs'
                    : 'Confirm Email Vessel Survey List',
                  'Vessel Surveys Certificates - Menu',
                  vesselName,
                )
              }
            />

            <SendEmailModal
              title="Send Surveys Docs"
              isVisible={isSendSurveyDocModalVisible}
              onClose={() => setIsSendSurveyDocModalVisible(false)}
              onSuccess={() => {
                setIsSendSurveyDocModalVisible(false);
                toast.success('Email sent successfully');
              }}
              onError={() => {
                setIsSendSurveyDocModalVisible(false);
                toast.error('Error sending the Email');
              }}
              headerClassName="sticky-header"
              footerClassName="sticky-footer"
              modalid={`send-email-modal`}
              infoText={InfoText}
              loading={emailContentLoading}
              emailContent={emailContent}
              showDocuments={emailContent.documents}
              onSubmitButtonClick={() =>
                ga4EventTrigger(
                  emailContent.documents
                    ? 'Confirm Send Vessel Survey Docs'
                    : 'Confirm Email Vessel Survey List',
                  'Vessel Surveys Certificates - Menu',
                  vesselName,
                )
              }
            />
            <AssignCertificateDialog
              showModal={showCertificateModal}
              setShowModal={setShowCertificateModal}
              edit={!_.isEmpty(rowData)}
              data={rowData}
              handleModalCancel={handleModalCancel}
              handleModalSubmit={handleSubmit}
              handleModalClear={handleClear}
              vesselId={vesselId}
              setListLoading={setLoading}
              setPageError={setError}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default VesselCertificates;
