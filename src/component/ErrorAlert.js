/* eslint-disable react/jsx-key */
import React from 'react';
import { Alert } from 'react-bootstrap';
import PropTypes from 'prop-types';

export const ErrorAlert = (props) => {
  if (props.message) {
    return (
      <Alert variant="danger">
        <p>{props.message}</p>
      </Alert>
    );
  } else if (props.errors && Object.keys(props.errors > 0)) {
    props.setShowModal(false);
    return (
      <Alert variant="danger">
        {Object.keys(props.errors).map((key, index) => {
          return <p key={index}>{props.errors[key]}</p>;
        })}
      </Alert>
    );
  } else {
    return null;
  }
};

ErrorAlert.propTypes = {
  message: PropTypes.string,
  errors: PropTypes.func,
  setShowModal: PropTypes.bool,
};

export default ErrorAlert;
