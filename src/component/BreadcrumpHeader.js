import React from 'react';
import { Breadcrumb } from 'react-bootstrap';
import PropTypes from 'prop-types';

const BreadcrumbHeader = ({ items = [], activeItem, onClick }) => {
  return (
    <Breadcrumb className="bread-crump-wrapper">
      {items.map((item) => {
        const isItemActive = item?.title === activeItem;
        const className = isItemActive ? 'breadcrumb-text' : 'inactive-breadcrumb-text';
        return (
          item && (
            <Breadcrumb.Item
              key={item.title}
              href={item.link}
              className={className}
              active={isItemActive}
              onClick={() => onClick(item.label)}
              data-testid={`fml-breadcrumb-${item.label}`}
            >
              {item.title}
            </Breadcrumb.Item>
          )
        );
      })}
    </Breadcrumb>
  );
};

BreadcrumbHeader.propTypes = {
  items: PropTypes.array,
  activeItem: PropTypes.string,
  onClick: PropTypes.func,
};

export { BreadcrumbHeader };
