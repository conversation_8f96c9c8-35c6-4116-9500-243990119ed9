import React, { useContext, useMemo } from 'react';
import { formatDate, formatValue } from '../../util/view-utils';
import CustomTable from '../customComponent/CustomTable';
import _ from 'lodash';
import { DetailContext } from '../../context/DetailContext';

const HistoryTable = ({ entity, data, onSortChange }) => {
  const { vesselName } = useContext(DetailContext);

  const columns = useMemo(() => {
    const commonColumn = [
      {
        Header: 'Date',
        accessor: (row) => formatDate(row?.start_date),
        id: 'start_date',
        name: 'start_date',
        type: 'text',
        disableSortBy: false,
      },
      {
        Header: 'Updated By',
        accessor: (row) => formatValue(row?.updated_by),
        id: 'updated_by',
        name: 'updated_by',
        type: 'text',
        disableSortBy: false,
      },
    ];
    switch (entity.type) {
      case 'owner':
      case 'register_owner':
        return [
          ...commonColumn,
          {
            Header: `${entity.type === 'owner' ? 'Owner' : 'Registered Owner'}`,
            accessor: (row) => formatValue(row?.value.owner_name),
            id: `value->'owner_name'`,
            name: 'owner_name',
            type: 'text',
            disableSortBy: false,
          },
          {
            Header: 'Vessel Name',
            accessor: (row) => formatValue(row?.value.vessel_name),
            id: `value->'vessel_name'`,
            name: 'vessel_name',
            type: 'text',
            disableSortBy: false,
          },
          {
            Header: 'Vessel Account Code',
            accessor: (row) => formatValue(row?.value.vessel_account_code),
            id: `value->'vessel_account_code'`,
            name: 'vessel_account_code',
            type: 'text',
            disableSortBy: false,
          },
        ];
      case 'flag':
        return [
          ...commonColumn,
          {
            Header: 'Country',
            accessor: (row) => formatValue(row?.country_desc),
            id: 'country',
            name: 'country',
            type: 'text',
            disableSortBy: true,
          },
          {
            Header: 'Port of Registry',
            accessor: (row) => formatValue(row?.port_desc),
            id: 'port_of_registry',
            name: 'port_of_registry',
            type: 'text',
            disableSortBy: true,
          },
          {
            Header: 'Flag Office',
            accessor: (row) => formatValue(row?.value?.office_value),
            id: 'flag_office',
            name: 'flag_office',
            type: 'text',
            disableSortBy: true,
          },
          {
            Header: 'Call sign',
            id: 'call_sign',
            type: 'text',
            accessor: (row) => formatValue(row?.value.call_sign),
            disableSortBy: true,
          },
        ];
      case 'accountant':
      case 'payroll':
        return [
          ...commonColumn,
          {
            Header: 'Vessel Name',
            accessor: () => vesselName,
            id: 'name',
            name: 'name',
            type: 'text',
            disableSortBy: true,
          },
          {
            Header: 'Primary',
            accessor: (row) => formatValue(row?.value[`${entity.type}1`]),
            id: `value->'${entity.type}1'`,
            name: 'primary',
            type: 'text',
            disableSortBy: false,
          },
          {
            Header: 'Secondary',
            accessor: (row) => formatValue(row?.value[`${entity.type}2`]),
            id: `value->'${entity.type}2'`,
            name: 'secondary',
            type: 'text',
            disableSortBy: false,
          },
        ];
      case 'buyer':
        return [
          ...commonColumn,
          {
            Header: 'Buyer',
            accessor: (row) => formatValue(row?.value?.buyer),
            id: `value->'buyer'`,
            name: 'buyer',
            type: 'text',
            disableSortBy: false,
          },
          {
            Header: 'Lead Buyer',
            accessor: (row) => formatValue(row?.value?.buyer_lead),
            id: `value->'buyer_lead'`,
            name: 'buyer_lead',
            type: 'text',
            disableSortBy: false,
          },
          {
            Header: 'Senior Lead Buyer',
            accessor: (row) => formatValue(row?.value?.buyer_senior_lead),
            id: `value->'buyer_senior_lead'`,
            name: 'buyer_senior_lead',
            type: 'text',
            disableSortBy: false,
          },
        ];
      case 'operation':
        return [
          ...commonColumn,
          {
            Header: 'Operation Manager',
            accessor: (row) => formatValue(row?.value?.operation),
            id: `value->'operation'`,
            name: 'manager_name',
            type: 'text',
            disableSortBy: false,
          },
        ];
      case 'qhse':
        return [
          ...commonColumn,
          {
            Header: 'QHSE Manager',
            accessor: (row) => formatValue(row?.value?.qhse),
            id: `value->'qhse'`,
            name: 'qhse_name',
            type: 'text',
            disableSortBy: false,
          },
        ];
      case 'tech_group':
        return [
          ...commonColumn,
          {
            Header: `Tech Group`,
            accessor: (row) => formatValue(row?.value?.tech_group),
            id: `value->'tech_group'`,
            name: 'tech_group',
            type: 'text',
            disableSortBy: false,
          },
          {
            Header: `Group Head`,
            accessor: (row) => formatValue(row?.value?.tech_group_group_head),
            id: `value->'tech_group_group_head'`,
            name: 'tech_group_group_head',
            type: 'text',
            disableSortBy: false,
          },
          {
            Header: `Superintendent`,
            accessor: (row) => formatValue(row?.value?.superintendent),
            id: `value->'superintendent'`,
            name: 'superintendent',
            type: 'text',
            disableSortBy: false,
          },
        ];
      default:
        return [];
    }
  }, [data, entity]);

  return (
    <>
      {entity && entity.type !== '' ? (
        <CustomTable
          column={columns}
          className={`flag-change-list custom-table-scroll`}
          reportData={!_.isEmpty(data) ? data : []}
          tableRef={null}
          setSortData={onSortChange}
          name="change-history"
        />
      ) : null}
    </>
  );
};

export default HistoryTable;
