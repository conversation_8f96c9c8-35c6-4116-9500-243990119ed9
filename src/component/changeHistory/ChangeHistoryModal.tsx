import React, { useState, useContext } from 'react';
import { Button, Modal } from 'react-bootstrap';
import { DetailContext } from '../../context/DetailContext';
import vesselService from '../../service/vessel-service';
import HistoryTable from './HistoryTable';
import Spinner from '../Spinner';
import CustomTypeAhead from '../customComponent/CustomTypeAhead';
import _ from 'lodash';

export const HISTORY_MENU_SEARCH_TYPES = [
  {
    type: 'flag',
    name: 'Flag',
  },
  {
    type: 'tech_group',
    name: 'Tech Group',
  },
  {
    type: 'buyer',
    name: 'Procurement',
  },
  {
    type: 'accountant',
    name: 'Vessel Accountant',
  },
  {
    type: 'payroll',
    name: 'Payroll Accountant',
  },
  {
    type: 'operation',
    name: 'Operation Manager',
  },
  {
    type: 'qhse',
    name: 'QHSE Manager',
  },
];

const ChangeLogModal = ({ roleConfig, showModal = false, setShowModal = (value) => {} }) => {
  const { ga4EventTrigger = () => {}, vesselName, ownershipId } = useContext(DetailContext);
  const [selected, setSelected] = useState('');
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState();
  const [, setSortData] = useState([]);
  const HISTORY_MENU_SEARCH_TYPES_CONDITION = [
    ...HISTORY_MENU_SEARCH_TYPES,
    ...(roleConfig?.vessel?.viewHistory
      ? [
          {
            type: 'owner',
            name: 'Ownership',
          },
          {
            type: 'register_owner',
            name: 'Registered Ownership',
          },
        ]
      : []),
  ];

  const getData = async (type, sortBy = []) => {
    try {
      let query = '';
      if (sortBy[0]?.id) {
        query = `&orderBy=${sortBy[0].id}+${sortBy[0].desc ? 'desc' : 'asc'}`;
      }
      const { data } = await vesselService.getChangeLogs(ownershipId, type, query);
      setData(data);
    } catch (e) {
      setError('Something went wrong. Please try again.');
    }
    setLoading(false);
  };

  const onFilterTypeChange = async (event) => {
    let element = event[0];
    setData([]);
    if (element === undefined) {
      setSelected('');
      return;
    }
    setLoading(true);
    setSelected(element);
    await getData(element.type);
  };

  const onSortChange = (value) => {
    ga4EventTrigger('Sorting', 'Cash Call Report Recipients - List', value[0]?.id);
    setSortData(value);
    if(value[0]?.id === 'updated_by'){
      setData(_.orderBy(data, ['updated_by'],[value[0].desc ? 'desc': 'asc']));
    }else{
      getData(selected.type, value);
    }
  };

  return (
    <Modal
      id="change-history-modal"
      show={showModal}
      centered
      size="lg"
      backdrop="static"
      aria-labelledby="change-history-modal"
      data-testid="fml-change-history-modal"
    >
      <Modal.Header>
        <Modal.Title>{`${vesselName} Activity Log`}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && <p className="validate-error">{error}</p>}
        <CustomTypeAhead
          id="basic-typeahead-change-history"
          labelKey="name"
          name="staff_category"
          inputProps={{ 'data-testid': 'fml-change-history-dialog-dropdown' }}
          multiple={false}
          placeholder="Select Category"
          isLoading={loading}
          options={HISTORY_MENU_SEARCH_TYPES_CONDITION}
          showDropDownIcon={true}
          clearOnFocus={true}
          onChange={(e) => {
            onFilterTypeChange(e);
          }}
          handleClear={() => {}}
        />

        {loading ? (
          <Spinner alignClass={`spinner-table`} />
        ) : (
          <HistoryTable entity={selected} data={data} onSortChange={onSortChange} />
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          data-testid="fml-change-history-modal-close"
          onClick={() => {
            setSelected('');
            setShowModal(false);
          }}
        >
          Ok
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ChangeLogModal;
