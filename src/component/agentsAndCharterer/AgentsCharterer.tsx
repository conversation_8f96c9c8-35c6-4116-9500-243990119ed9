import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Button } from 'react-bootstrap';
import { DetailContext } from '../../context/DetailContext';
import vesselService from '../../service/vessel-service';
import { Icon } from '../../styleGuide';
import { formatDate, formatValue } from '../../util/view-utils';
import CustomTable from '../customComponent/CustomTable';
import ErrorAlert from '../ErrorAlert';
import AgentsCharterDialog from './AgentsChartererDialog';

const AgentsCharterer = ({ type }) => {
  const [loading, setLoading] = useState(false);
  const [rowData, setRowData] = useState({});
  const [sortData, setSortData] = useState([{ id: 'last_modified', desc: true }]);
  const [agentsCharterer, setAgentsCharterer] = useState([]);
  const [showAgentChartererModal, setShowAgentChartererModal] = useState(false);
  const {
    setExcelColumns,
    ownershipId,
    ga4EventTrigger = () => {},
    vesselName,
    handleError = () => {},
    error,
    setError = () => {},
    roleConfig,
  } = useContext(DetailContext);

  const columns = useMemo(
    () => [
      {
        Header: 'No.',
        accessor: (row, index) => index + 1,
        sticky: 'left',
        id: 'id',
        name: 'id',
        type: 'text',
        maxWidth: 30,
        disableSortBy: true,
      },
      ...(type === 'Agent'
        ? [
          {
            Header: 'Agent',
            accessor: (row) => <div className="text-spacing">{formatValue(row.agent)}</div>,
            id: 'agent',
            name: 'agent',
            type: 'text',
            disableSortBy: true,
          },
          {
            Header: 'Port',
            accessor: (row) => formatValue(row.port),
            id: 'port',
            name: 'port',
            type: 'text',
            maxWidth: 100,
          },
        ]
        : [
          {
            Header: 'Charterer',
            accessor: (row) => <div className="text-spacing">{formatValue(row.charterer)}</div>,
            id: 'charterer',
            name: 'charterer',
            type: 'text',
            disableSortBy: true,
          },
        ]),
      {
        Header: 'Last Modified',
        accessor: (row) => formatDate(row.last_modified, 'DD MMM YYYY HH:mm'),
        id: 'last_modified',
        name: 'last_modified',
        type: 'date',
        maxWidth: 80,
      },
      ...(roleConfig.vessel.edit ? [{
        Header: 'Action',
        headerClassName: 'justify-content-center ',
        accessor: (row, index) => (
          <div className="text-center">
            <Icon
              icon="Edit"
              data-testid={`fml-agents-charterer-list-edit-${index}`}
              size={20}
              className="edit-icon"
              onClick={() => {
                ga4EventTrigger(
                  'Edit Vessel Agent',
                  type === 'Agent' ? 'Vessel Agent - menu' : ' Vessel Charterer - menu',
                  vesselName,
                );
                setRowData(row);
                setShowAgentChartererModal(true);
              }}
            />
          </div>
        ),
        type: 'item',
        maxWidth: 20,
        disableSortBy: true,
      }] : []),
    ],
    [type, roleConfig],
  );

  const fetchAgentList = async () => {
    setLoading(true);
    try {
      const response =
        type === 'Agent'
          ? await vesselService.getAgentsList(sortData, ownershipId)
          : await vesselService.getChartererList(sortData, ownershipId);
      setAgentsCharterer(response.data.results);
      setExcelColumns(columns);
      setError(null);
    } catch (error) {
      handleError(error.response);
    }
    setLoading(false);
  };

  useEffect(() => {
    setLoading(true);
    fetchAgentList();
  }, [sortData, type]);

  const handleSubmit = () => {
    fetchAgentList();
    setRowData({});
  };

  const handleModalCancel = () => {
    setShowAgentChartererModal(false);
    setRowData({});
  };

  const onSortChange = (value) => {
    ga4EventTrigger(
      'Sorting',
      type === 'Agent' ? 'Vessel Agent - list' : ' Vessel Charterer - list',
      value[0]?.id,
    );
    setSortData(value);
  };

  return (
    <div>
      {roleConfig.vessel.edit && <div className="d-flex justify-content-end no-print">
        <Button
          size="md"
          variant="outline-primary"
          className="mr-2"
          data-testid="fml-agents-charterer-list-add-Button"
          onClick={() => {
            ga4EventTrigger(
              'Add Vessel Agent',
              type === 'Agent' ? 'Vessel Agent - menu' : ' Vessel Charterer - menu',
              vesselName,
            );
            setShowAgentChartererModal(true);
          }}
        >
          Add
        </Button>
      </div>}

      {error && <ErrorAlert message={error} />}
      <CustomTable
        column={columns}
        reportData={agentsCharterer}
        tableRef={null}
        isLoading={loading}
        setSortData={onSortChange}
        name="fml-agents-list"
      />

      <AgentsCharterDialog
        showAgentChartererModal={showAgentChartererModal}
        editData={rowData}
        handleModalCancel={handleModalCancel}
        handleModalSubmit={handleSubmit}
        setPageError={setError}
        type={type}
      />
    </div>
  );
};

export default AgentsCharterer;
