import _ from 'lodash';
import React, { useState, useContext, useEffect } from 'react';
import { Button, Form, Modal } from 'react-bootstrap';
import { DetailContext } from '../../context/DetailContext';
import vesselService from '../../service/vessel-service';
import { getTimeDiffWithUTC } from '../../util/view-utils';
import CustomCountryPortDropDown from '../customComponent/CustomCountryPortDropDown';
import CustomOverlayLoader from '../customComponent/CustomOverlayLoader';
import ErrorAlert from '../ErrorAlert';
import { Icon } from '../../styleGuide';
import moment from 'moment';

const AgentsCharterDialog = ({
  showAgentChartererModal,
  editData,
  setPageError,
  handleModalCancel,
  handleModalSubmit,
  type = '',
}) => {
  const { vesselName, ownershipId } = useContext(DetailContext);
  const edit = !_.isEmpty(editData);
  const [newEditData, setNewEditData] = useState();
  const [errors, setErrors] = useState({});
  const [validationError, setValidationError] = useState();
  const [loading, setLoading] = useState(false);
  const [initialEditData, setInitialEditData] = useState();
  const [form, setForm] = useState({});

  const validatePort = (value) => ({
    port: _.isEmpty(value) ? 'Country and Port is a required field' : undefined,
  });

  const validateAgent = (value) => ({
    agent: _.isEmpty(value) ? `${type} is a required field` : undefined,
  });

  const validateAgentCountryPort = (form) => {
    const errList = {
      ...validatePort(form?.country),
      ...validatePort(form?.port_info),
    };
    if (edit) {
      if (!form?.country && !form?.port_info && form?.port) {
        return {};
      } else return errList;
    } else {
      return errList;
    }
  };

  const intialValidateError = () => ({
    ..._.omitBy(errors, (v) => v == null),
    ...(type === 'Agent' && validateAgentCountryPort(form)),
    ...validateAgent(form?.agent),
  });

  const validateForm = (field, value) => {
    let newErrors = {
      ...errors,
    };
    switch (field) {
      case 'country':
        newErrors = { ...newErrors, ...validatePort(value) };
        break;
      case 'port_info':
        newErrors = { ...newErrors, ...validatePort(value) };
        break;
      case 'agent':
        newErrors = { ...newErrors, ...validateAgent(value) };
        break;
    }
    newErrors = _.pickBy(newErrors, (i) => i !== undefined);
    setErrors(newErrors);
  };

  const setFieldValue = (field, value) => {
    validateForm(field, value);
    let newFormdata = { ...form };

    switch (field) {
      case 'country':
        newFormdata = {
          ...newFormdata,
          [field]: value,
          port_info: [],
          port: setPortData(value, []),
        };
        break;
      case 'port_info':
        newFormdata = {
          ...newFormdata,
          [field]: value,
          port: setPortData(form?.country, value),
        };
        break;
      default:
        newFormdata = { ...newFormdata, [field]: value };
    }

    setForm(newFormdata);
  };

  useEffect(() => {
    if (edit && newEditData) {
      const formData =
        type === 'Agent'
          ? {
              port: newEditData.port,
              agent: newEditData.agent,
              country: newEditData.country,
              port_info: newEditData.port_info,
            }
          : {
              agent: newEditData.charterer,
            };
      setForm(formData);
      setInitialEditData(formData);
    }
  }, [newEditData]);

  useEffect(() => {
    setNewEditData(editData);
  }, [editData]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    let errorList = { ...intialValidateError() };
    errorList = _.pickBy(errorList, (i) => i !== undefined);

    if (_.isEmpty(errorList)) {
      try {
        setLoading(true);
        if (edit) {
          const submitFormData = _.fromPairs(
            _.differenceWith(_.toPairs(form), _.toPairs(initialEditData), _.isEqual),
          );
          {
            type === 'Agent'
              ? await vesselService.editAgent({
                  id: editData.id,
                  ..._.omit(submitFormData, ['country', 'port_info']),
                })
              : await vesselService.editCharterer({ id: editData.id, ...form });
          }
        } else {
          type === 'Agent'
            ? await vesselService.addAgent({
                ownership_id: Number(ownershipId),
                ..._.omit(form, ['country', 'port_info']),
              })
            : await vesselService.addCharterer({ ownership_id: Number(ownershipId), ...form });
        }
        handleModalSubmit();
        handleCancel();
      } catch (error) {
        if (error?.response?.status === 400) {
          setValidationError(`${error?.response.data}. Please try again.`);
          setLoading(false);
        } else {
          handleCancel();
          setPageError(
            'Something went wrong with updating finanacial report list. Please try later',
          );
        }
      }
    } else {
      setErrors(errorList);
    }
  };

  const handleCancel = () => {
    setForm({});
    setErrors({});
    handleModalCancel();
    setLoading(false);
  };

  const setPortData = (country, port) => {
    const data = [];
    if (!_.isEmpty(country)) data.push(country[0]?.value);
    if (!_.isEmpty(port)) data.push(port[0]?.value);

    return data.join(', ');
  };

  return (
    <Modal
      id="agent-popup"
      show={showAgentChartererModal}
      aria-labelledby="agent-modal"
      centered
      size="lg"
      backdrop="static"
      data-testid="fml-agents-charterer-dialog"
    >
      <Modal.Header>
        <Modal.Title style={{ borderBottom: '0' }}>
          {!edit ? `New ${type} for ${vesselName}` : `Edit ${type} for ${vesselName}`}
          <div className="required-field-text">* Required fields</div>
        </Modal.Title>
      </Modal.Header>
      {validationError && <ErrorAlert message={validationError} />}

      <CustomOverlayLoader active={loading}>
        <Form className="form-main-control">
          <Modal.Body>
            {type === 'Agent' && (
              <Form.Group className="form-group">
                <CustomCountryPortDropDown
                  required={true}
                  labelPort={'Port'}
                  rowData={editData}
                  formUpdate={(e) => setNewEditData(e)}
                  countryData={form.country ? form.country : []}
                  portData={form.port_info ? form.port_info : []}
                  onChangeCountry={(e) => {
                    setFieldValue('country', e);
                    setLoading(true);
                  }}
                  handleClearCountry={() => {
                    setFieldValue('country', []);
                  }}
                  onChangePort={(e) => {
                    setFieldValue('port_info', e);
                  }}
                  handleClearPort={() => {
                    setFieldValue('port_info', []);
                  }}
                  disablePort={false}
                  afterPortFetch={() => setLoading(false)}
                />
                <div className="d-flex justify-content-between">
                  <Form.Control
                    value={form?.port}
                    data-testid="fml-agents-charterer-dialog-port"
                    disabled={true}
                  />
                  <Icon
                    icon="remove"
                    size={20}
                    className="remove my-auto mx-2"
                    onClick={() => {
                      setFieldValue('country', []);
                    }}
                    dataTestId={`fml-gents-charterer-dialog-remove-port`}
                  />
                </div>
                <div className="validate-error">{errors.port}</div>
              </Form.Group>
            )}

            <Form.Group className="form-group">
              <Form.Label className="from-label">{type}*</Form.Label>
              <Form.Control
                as="textarea"
                maxLength="500"
                data-testid="fml-agents-charterer-dialog-type"
                rows="10"
                name="agent"
                className="form-main-control"
                value={form.agent}
                onChange={(e) => {
                  setFieldValue('agent', e.target.value);
                }}
              />
              <div className="validate-error">{errors.agent}</div>
            </Form.Group>

            {edit && (
              <Form.Label className="edit-label pt-2">
                Last Edited by {editData.updated_by} on{' '}
                {moment(editData.last_modified).utcOffset(-8).format('DD MMM YYYY HH:mm')}{' '}
                {getTimeDiffWithUTC()}
              </Form.Label>
            )}
          </Modal.Body>
          <Modal.Footer style={{ borderTop: '0', width: '100%' }}>
            <div className="ml-auto">
              <Button
                variant="primary"
                data-testid="fml-agents-charterer-dialog-cancel"
                className="m-2"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                variant="secondary"
                data-testid="fml-agents-charterer-dialog-save"
                type="submit"
                onClick={handleSubmit}
              >
                Save
              </Button>
            </div>
          </Modal.Footer>
        </Form>
      </CustomOverlayLoader>
    </Modal>
  );
};

export default AgentsCharterDialog;
