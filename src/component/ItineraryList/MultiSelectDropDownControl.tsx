import _ from 'lodash';
import React, { Fragment, useState } from 'react';
import { <PERSON>u, <PERSON>uItem, Typeah<PERSON>, Highlighter } from 'react-bootstrap-typeahead';
import { Icon } from '../../styleGuide';

const OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE = 'OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE';
const PRIMITIVE_DROPDOWN_VALUE_TYPE = 'PRIMITIVE_DROPDOWN_VALUE_TYPE';

const MultiSelectDropDownControl = ({
  dropDownValues,
  name,
  onInputChange,
  selectedValue,
  disabled,
  isInvalid,
  multipleDropdownRef,
  dataTestId,
  multiple = false,
}) => {
  let dropDownValueType = undefined;
  const [paginate, setPaginate] = useState(true);

  if (dropDownValues?.length) {
    const dropDownValue = dropDownValues[0];

    if (typeof dropDownValue === 'object' && dropDownValue.constructor === Object) {
      //plain object
      if (Object.hasOwn(dropDownValue, 'id') && Object.hasOwn(dropDownValue, 'value')) {
        dropDownValueType = OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE;
      }
    } else if (typeof dropDownValue !== 'object') {
      //primitive type
      dropDownValueType = PRIMITIVE_DROPDOWN_VALUE_TYPE;
    }
  } else {
    //for empty array
    dropDownValueType = PRIMITIVE_DROPDOWN_VALUE_TYPE;
  }

  if (dropDownValueType === undefined) {
    throw Error('format of dropDownValues is not supported, dropDownValues: ', dropDownValues);
  }

  let options, onChange, selected;

  if (dropDownValueType === OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE) {
    options = (dropDownValues ?? []).map((item) => {
      return item.value;
    });

    onChange = (value) => {
      if (multiple) {
        let ids = value.map((item) => getIdOfSelectedOption(item));
        const isExists =
          selectedValue.length < ids.length &&
          selectedValue?.find((val) => val === ids[ids.length - 1]);
        if (isExists) {
          // Remove unselected value from dropdown
          ids = ids.filter((id) => id !== isExists);
        }
        const results = { target: { name: name, value: ids.length > 0 ? ids : undefined } };
        onInputChange(results);
      } else {
        const id = getIdOfSelectedOption(value[0]);
        const result = { target: { name: name, value: id } };
        onInputChange(result);
      }
    };

    const getIdOfSelectedOption = (value) => {
      const result = (dropDownValues ?? []).filter((item) => {
        return item.value === value;
      });
      return result.length > 0 ? result[0].id : undefined;
    };

    const getOptionById = (id) => {
      const result = (dropDownValues ?? []).filter((item) => {
        return typeof item.id === 'object' ? _.isEqual(item.id, id) : item.id === id;
      });
      return result.length > 0 ? result[0].value : undefined;
    };

    if (multiple) {
      selected = selectedValue?.map((item) => getOptionById(item));
    } else {
      const selectedOption = getOptionById(selectedValue);
      selected = selectedOption ? [selectedOption] : [];
    }
  } else if (dropDownValueType === PRIMITIVE_DROPDOWN_VALUE_TYPE) {
    options = dropDownValues;

    onChange = (value) => {
      if (multiple) {
        throw Error('Not supporting primitve dropdown values for multiple select for now');
      } else {
        const result = { target: { name: name, value: value[0] } };
        onInputChange(result);
      }
    };

    selected = selectedValue ? [selectedValue] : [];
  }

  const validateArgument = () => {
    if (dropDownValues?.length && options === undefined) {
      throw Error('options cannot be undefined');
    }

    if (onChange === undefined) {
      throw Error('onChange cannot be undefined');
    }

    if (selectedValue !== undefined && selected === undefined) {
      throw Error('selected cannot be undefined');
    }
  };

  validateArgument();

  const isMatch = (input, option) => {
    let searchStr = input.toLowerCase();
    let str = option.toLowerCase();
    return str.indexOf(searchStr) !== -1;
  };
  const handleOnRemove = (option) => {
    console.log('remove ' + option);
  };
  /**
   * Custom Dropdown Input/Menu design
   */
  const customDropdownDesignProps = {};
  if (multiple) {
    customDropdownDesignProps.renderMenu = (results, menuProps, state) => {
      let filteredResults = results;
      if (state.text !== '') {
        filteredResults = options.filter((option) => {
          if (typeof option === 'string') return isMatch(state.text, option);
          return true;
        });
        setPaginate(false);
      }
      const items = filteredResults.map((option, index) => {
        const menuItemProps = {
          option,
        };
        if (option.paginationOption) {
          return (
            <Fragment key="pagination-item">
              <Menu.Divider />
              <MenuItem
                {...menuItemProps}
                className="rbt-menu-pagination-option"
                label={'Display additional results...'}
              >
                {'Display additional results...'}
              </MenuItem>
            </Fragment>
          );
        }
        return (
          <MenuItem
            key={`${option.toString()} ${index}`}
            checked={true}
            {...menuItemProps}
            position={index}
          >
            <>
              {state.selected.includes(option) ? (
                <Icon
                  icon="checked"
                  size={20}
                  className="float-left screening-page__moved-tick check-icon"
                />
              ) : (
                <span className="empty-icon-padding"></span>
              )}
              <Highlighter search={state.text}>{option.toString()}</Highlighter>
            </>
          </MenuItem>
        );
      });
      return <Menu {...menuProps}>{items}</Menu>;
    };
    // Show all the options including selected in dropdown menu
    customDropdownDesignProps.filterBy = (option, props) => {
      return true;
    };
    customDropdownDesignProps.className = 'multi-dropdown';
  }

  return (
    <Typeahead
      ref={multipleDropdownRef}
      id={name}
      key={name}
      inputProps={{ 'data-testid': dataTestId, onRemove: handleOnRemove }}
      onChange={onChange}
      options={options}
      placeholder="Please select"
      selected={selected}
      disabled={disabled}
      isInvalid={isInvalid}
      multiple={multiple}
      paginate={paginate}
      {...customDropdownDesignProps}
    />
  );
};

export default MultiSelectDropDownControl;
