import React from 'react';
import { Form, Row, Col } from 'react-bootstrap';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import TakeoverDropDownSearchControl from '../takeover/TakeoverDropDownSearchControl.js';
import MultiSelectDropDownControl from './MultiSelectDropDownControl';

const ItinerarySubtypeField = (props) => {
  let type = props.type.inputType;
  let defaultField = (
    <SubtypeFieldText
      isFilterRow={true}
      subtype={''}
      disabled={props.disabled ?? true}
      title={props.title !== null ? props.title : true}
    />
  );

  switch (type) {
    case 'multiselect':
      return <SubtypeFieldMultiSelectDropDown {...props} />;

    case 'dropdown':
      return <SubtypeFieldDropDown {...props} />;

    case 'text':
      return <SubtypeFieldText {...props} />;

    case 'date':
      return <SubtypeFieldDate {...props} />;

    case 'year':
      return <SubtypeFieldYear {...props} />;

    case 'number_range':
      return <SubtypeFieldNumberRange {...props} />;

    case 'number':
      return <SubtypeFieldNumber {...props} />;

    default:
      return defaultField;
  }
};

const SubtypeFieldText = (props) => (
  <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
    {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
    <Form.Control
      className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
      disabled={props.disabled}
      onChange={props.onSubtypeChange}
      value={props.subtype}
    />
  </Form.Group>
);

const SubtypeFieldNumber = (props) => {
  let limit = 12;
  if (props.type.type === 'imo_number') {
    limit = 7;
  }
  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <Form.Control
        className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
        type="number"
        onInput={(e) => {
          e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, limit);
        }}
        disabled={props.disabled}
        onChange={props.onSubtypeChange}
        value={props.subtype}
      />
    </Form.Group>
  );
};

const SubtypeFieldNumberRange = (props) => {
  let labelClass = props.disabled ? 'label-to-in-row' : 'label-to-on-top';

  let minValue = props.min;
  let maxValue = props.max;

  if (props.isFilterRow) {
    minValue = props.subtype.min;
    maxValue = props.subtype.max;
  }

  const onMinNumberChange = (event) => {
    let value = event.target.value;
    props.onSubtypeChange(event, { min: value, max: maxValue });
  };

  const onMaxNumberChange = (event) => {
    let value = event.target.value;
    props.onSubtypeChange(event, { min: minValue, max: value });
  };

  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      <Row>
        <Form.Group as={Col} className="mb-0 form-group">
          {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
          <Form.Control
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            type="number"
            onInput={(e) => {
              e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, limit);
            }}
            disabled={props.disabled}
            onChange={onMinNumberChange}
            value={minValue}
          />
        </Form.Group>{' '}
        <div className={labelClass}>to</div>
        <Form.Group as={Col} className="mb-0 form-group">
          {props.title ? <Form.Label className="hidden-label">.</Form.Label> : null}
          <Form.Control
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            type="number"
            onInput={(e) => {
              e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, limit);
            }}
            disabled={props.disabled}
            onChange={onMaxNumberChange}
            value={maxValue}
          />
        </Form.Group>
      </Row>
    </Form.Group>
  );
};

const SubtypeFieldYear = (props) => {
  const date = getDate(props.subtype);

  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <DatePicker
        className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
        disabled={props.disabled}
        selected={date}
        onChange={props.onYearFieldChange}
        showYearPicker
        placeholderText="Please select"
        dateFormat="yyyy"
      />
    </Form.Group>
  );
};

const getDate = (value) => {
  if ((typeof value === 'string' || value instanceof String) && value != '') {
    return moment(value).toDate();
  } else {
    return value;
  }
};

const SubtypeFieldDate = (props) => {
  const { subtype = {} } = props;
  const { startDate = null, endDate = null } = subtype;

  const onDatesChange = (type, date) => {
    let value = { startDate: startDate, endDate: endDate };

    if (type === 'startDate') {
      value = { startDate: date, endDate: endDate };
    }

    if (type === 'endDate') {
      value = { startDate: startDate, endDate: date };
    }

    props.onDateFieldChange(value);
  };

  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <Row className="m-auto">
        <span className="align-self-center pr-1">From</span>
        <Form.Group as={Col} className="pl-0 mb-0 form-group">
          <DatePicker
            selectsStart
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            disabled={props.disabled}
            selected={startDate}
            startDate={startDate}
            endDate={endDate}
            onChange={onDatesChange.bind(this, 'startDate')}
            minDate={props.disablePreviousDates ? moment().toDate() : undefined}
            dateFormat="d MMM yyyy"
            showMonthDropdown
            showYearDropdown
            dropdownMode="select"
            dateFormatCalendar=" "
            customInput={<input data-testid={`${props.dataTestId}-start`} type="text" />}
          />
        </Form.Group>{' '}
        <span className="align-self-center pr-1">To</span>
        <Form.Group as={Col} className="px-0 mb-0 form-group">
          <DatePicker
            selectsEnd
            disabled={props.disabled}
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            selected={endDate}
            onChange={onDatesChange.bind(this, 'endDate')}
            startDate={startDate}
            endDate={endDate}
            minDate={startDate}
            dateFormat="d MMM yyyy"
            showMonthDropdown
            showYearDropdown
            dropdownMode="select"
            dateFormatCalendar=" "
            customInput={<input data-testid={`${props.dataTestId}-end`} type="text" />}
          />
        </Form.Group>
      </Row>
    </Form.Group>
  );
};

const SubtypeFieldDropDown = (props) => {
  const type = props.type.type;
  const data = props.dropDownData[type] ?? [];
  let selectedValue = props.subtype ? props.subtype.id : '';
  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <TakeoverDropDownSearchControl
        name={type}
        selectedValue={selectedValue}
        dropDownValues={data}
        onInputChange={props.onSubtypeChange}
        disabled={props.disabled}
      />
    </Form.Group>
  );
};

const SubtypeFieldMultiSelectDropDown = (props) => {
  const type = props.type.type;
  const data = props.dropDownData[type] ?? [];
  let selectedValue = props.subtype ? props.subtype.map((item) => item.id) : [];
  return (
    <Form.Group as={Col} md={{ span: 5, offset: 1 }} className="subtype-field form-group">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <MultiSelectDropDownControl
        name={type}
        selectedValue={selectedValue}
        dropDownValues={data}
        onInputChange={props.onSubtypeChange}
        disabled={props.disabled}
        multiple={true}
        isInvalid={false}
        dataTestId={props.dataTestId}
      />
    </Form.Group>
  );
};

export default ItinerarySubtypeField;
