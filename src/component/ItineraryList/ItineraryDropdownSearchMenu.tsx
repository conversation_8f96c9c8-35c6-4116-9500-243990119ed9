import React from 'react';
import { Typeahead } from 'react-bootstrap-typeahead';

const ItineraryDropDownSearchMenu = (props) => {
  const ref = React.createRef();
  const handleChange = (event) => {
    props.onChange(event, ref.current);
  };

  return (
    <Typeahead
      ref={ref}
      {...props}
      selected={props.defaultSelected}
      inputProps={{ 'data-testid': props.dataTestId }}
      labelKey="name"
      id="search-type-menu"
      onChange={handleChange}
      options={props.searchMenuOptions}
      placeholder={props.placeholder || 'Please Select'}
    />
  );
};

export default ItineraryDropDownSearchMenu;
