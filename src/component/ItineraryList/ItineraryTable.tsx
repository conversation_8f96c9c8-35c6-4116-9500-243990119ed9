/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo } from 'react';
import { Form } from 'react-bootstrap';
import { useSticky } from 'react-table-sticky';
import { useFlexLayout, useSortBy, useTable, usePagination } from 'react-table';
import styleGuide from '../../styleGuide';
const { Icon } = styleGuide;
import { storePageSort } from '../../util/local-storage-helper';
import Spinner from '../Spinner';
import { fetchIcon, formatDate, formatValue, PageNum } from '../../util/view-utils';
import { LOCAL_STORAGE_FIELDS } from '../../model/constants';
import NoResult from '../NoResult';

export const columns = [
  {
    Header: 'Port',
    accessor: (row) => formatValue(row.port),
    sticky: 'left',
    id: 'itinerary.port',
    name: 'port',
    type: 'text',
  },
  {
    Header: 'Berth',
    accessor: (row) => formatValue(row.berth),
    id: 'itinerary.berth',
    name: 'berth',
    type: 'text',
    disableSortBy: true,
  },
  {
    Header: 'Country',
    accessor: (row) => formatValue(row.country),
    id: 'itinerary.country',
    name: 'country',
    type: 'text',
  },
  {
    Header: 'ETA',
    accessor: (row) => formatDate(row.estimated_arrival, 'DD MMM YYYY HH:mm'),
    id: 'itinerary.estimated_arrival',
    name: 'estimated_arrival',
    type: 'date',
    width: 120,
    maxWidth: 120,
  },
  {
    Header: 'ETB',
    accessor: (row) => formatDate(row.estimated_berth, 'DD MMM YYYY HH:mm'),
    id: 'itinerary.estimated_berth',
    name: 'estimated_berth',
    type: 'date',
    width: 120,
    maxWidth: 120,
    disableSortBy: true,
  },
  {
    Header: 'ETD',
    accessor: (row) => formatDate(row.estimated_departure, 'DD MMM YYYY HH:mm'),
    id: 'itinerary.estimated_departure',
    name: 'estimated_departure',
    type: 'date',
    width: 120,
    maxWidth: 120,
  },
  {
    Header: 'Reason for Portcall',
    accessor: (row) => formatValue(row.vessel_itinerary_reasons),
    id: 'itinerary.vessel_itinerary_reasons',
    name: 'vessel_itinerary_reasons',
    type: 'text',
    width: 200,
    maxWidth: 200,
    disableSortBy: true,
  },
];

const ItineraryTable = ({
  itinerary,
  fetchData,
  tableRef,
  isLoading,
  pageCount,
  initSort,
  query,
  keyword,
  showFutureItinerary,
  totalItineraryCount,
  ga4EventTrigger,
}) => {
  const defaultColumns = useMemo(() => columns, []);
  return (
    <div className="vessel-table">
      <Table
        columns={defaultColumns}
        data={itinerary}
        tableRef={tableRef}
        isLoading={isLoading}
        fetchData={fetchData}
        totalPageCount={pageCount}
        keyword={keyword}
        query={query}
        initSort={initSort}
        showFutureItinerary={showFutureItinerary}
        totalItineraryCount={totalItineraryCount}
        ga4EventTrigger={ga4EventTrigger}
      />
    </div>
  );
};

const Table = ({
  columns,
  data,
  tableRef,
  isLoading,
  fetchData,
  keyword,
  query,
  initSort,
  totalPageCount,
  showFutureItinerary,
  totalItineraryCount,
  ga4EventTrigger,
}) => {
  const defaultColumn = useMemo(
    () => ({
      minWidth: 30,
      maxWidth: 90,
    }),
    [],
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    rows,
    gotoPage,
    setPageSize,
    canPreviousPage,
    canNextPage,
    pageCount,
    state: { pageIndex, pageSize, sortBy },
  } = useTable(
    {
      columns,
      defaultColumn,
      data,
      initialState: { pageIndex: 0, sortBy: initSort },
      manualPagination: true,
      manualSortBy: true,
      autoResetPage: false,
      autoResetSortBy: false,
      pageCount: totalPageCount,
    },
    useSortBy,
    usePagination,
    useFlexLayout,
    useSticky,
  );

  const resetPage = async (page_no = 0, page_size = 10) => {
    await fetchData({ pageSize: page_size, sortBy, pageIndex: page_no });
    setPageSize(page_size);
    gotoPage(page_no);
  };

  useEffect(() => {
    storePageSort(sortBy, LOCAL_STORAGE_FIELDS.itineraryKey);
  }, [sortBy]);

  useEffect(() => {
    (async function reset_page() {
      await resetPage();
    })();
  }, [sortBy, query, keyword, showFutureItinerary]);

  const pageSwitch = async (page_no) => {
    await fetchData({ pageSize, sortBy, pageIndex: page_no });
    gotoPage(page_no);
  };

  const pageSizeSwitch = async (page_size) => {
    //Internally pageIndex gets recalibrated as follows
    ga4EventTrigger('Number of rows', 'Vessel Itinerary Pagination', page_size);
    const new_index = Math.floor((pageIndex * pageSize) / page_size);
    await fetchData({ pageIndex: new_index, sortBy, pageSize: page_size });
    setPageSize(page_size);
  };

  const filterPages = (visiblePages, totalPages) =>
    visiblePages.filter((page) => page <= totalPages);
  const getVisiblePages = (page, total) => {
    if (total < 7) {
      return filterPages([1, 2, 3, 4, 5, 6], total);
    }
    if (page % 5 >= 0 && page > 4 && page + 2 < total) {
      return [1, page - 1, page, page + 1, total];
    }
    if (page % 5 >= 0 && page > 4 && page + 2 >= total) {
      return [1, total - 3, total - 2, total - 1, total];
    }
    return [1, 2, 3, 4, 5, total];
  };
  const visiblePages = getVisiblePages(pageIndex, pageCount);

  return (
    <>
      <div className="d-flex no-print">
        <div className="page-number-border">
          <PageNum
            onClick={() => (canPreviousPage ? pageSwitch(pageIndex - 1) : '')}
            disabled={!canPreviousPage}
            dataTestId="fml-pagination-previous"
          >
            {'<'}
          </PageNum>
          {visiblePages.map((page, index, array) => (
            <PageNum
              key={index}
              active={page - 1 === pageIndex}
              disabled={page - 1 === pageIndex}
              onClick={() => pageSwitch(page - 1)}
              dataTestId={`fml-pagination-${page}`}
            >
              {array[index - 1] + 2 < page ? `...${page}` : page}
            </PageNum>
          ))}
          <PageNum
            onClick={() => (canNextPage ? pageSwitch(pageIndex + 1) : '')}
            disabled={!canNextPage}
            dataTestId="fml-pagination-next"
          >
            {'>'}
          </PageNum>
        </div>
        <Form inline>
          <Form.Control
            as="select"
            value={pageSize}
            className="ml-3"
            onChange={(e) => {
              pageSizeSwitch(Number(e.target.value));
            }}
            data-testid="fml-select-pageSize"
          >
            {[10, 20, 50, 100].map((pageSize) => (
              <option key={pageSize} value={pageSize} data-testid={`fml-show-${pageSize}`}>
                Show {pageSize}
              </option>
            ))}
          </Form.Control>
        </Form>
        <div className="list-count">
          <b>{totalItineraryCount}</b> Results
        </div>
      </div>
      <div {...getTableProps()} className="table sticky" ref={tableRef}>
        <div className="header">
          {headerGroups.map((headerGroup, index) => (
            <div key={index} {...headerGroup.getHeaderGroupProps()} className="tr">
              {headerGroup.headers.map((column, index2) => {
                const thProps = column.getHeaderProps(column.getSortByToggleProps());
                return (
                  <div
                    key={index2}
                    data-testid={`fml-itinerary-table-column-header-${column.render('Header')}`}
                    {...thProps}
                    className="itinerary-table-th"
                  >
                    {column.render('Header')}
                    <span>
                      {column.canSort && (
                        <Icon
                          icon={fetchIcon(column)}
                          size={20}
                          className="default float-none"
                          data-testid={`fml-sort-icon-${column.render('Header')}`}
                          onClick={() =>
                            ga4EventTrigger(
                              'Sorting',
                              'Vessel Itinerary',
                              `${column.render('Header')} - ${
                                column.isSorted && column.isSortedDesc 
                                  ? 'sort-descending'
                                  : 'sort-ascending'
                              }`,
                            )
                          }
                        />
                      )}
                    </span>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        {isLoading && <Spinner alignClass={'spinner-table'} />}
        <div {...getTableBodyProps()} className="body">
          {rows.length > 0
            ? rows.map((row, index) => {
                prepareRow(row);
                return (
                  <div key={index} {...row.getRowProps()} className="tr">
                    {row.cells.map((cell, index2) => {
                      const tdProps = cell.getCellProps();
                      return (
                        <div
                          key={index2}
                          {...tdProps}
                          data-testid={`fml-itinerary-row-${index}-${cell.column.Header}`}
                          className={`td ${
                            row?.original?.vessel_ref_id ? 'vessel-company-type' : ''
                          }`}
                        >
                          {cell.render('Cell')}
                        </div>
                      );
                    })}
                  </div>
                );
              })
            : !isLoading && <NoResult />}
        </div>
      </div>

      <br />
      <div className="d-flex p-4 no-print">
        <div className="page-number-border">
          <PageNum
            onClick={() => (canPreviousPage ? pageSwitch(pageIndex - 1) : '')}
            disabled={!canPreviousPage}
            dataTestId="fml-pagination-previous"
          >
            {'<'}
          </PageNum>
          {visiblePages.map((page, index, array) => (
            <PageNum
              key={index}
              active={page - 1 === pageIndex}
              disabled={page - 1 === pageIndex}
              onClick={() => pageSwitch(page - 1)}
              dataTestId={`fml-pagination-${page}`}
            >
              {array[index - 1] + 2 < page ? `...${page}` : page}
            </PageNum>
          ))}
          <PageNum
            onClick={() => (canNextPage ? pageSwitch(pageIndex + 1) : '')}
            disabled={!canNextPage}
            dataTestId="fml-pagination-next"
          >
            {'>'}
          </PageNum>
        </div>
        <Form inline>
          <Form.Control
            as="select"
            value={pageSize}
            className="ml-3"
            onChange={(e) => {
              pageSizeSwitch(Number(e.target.value));
            }}
            data-testid="fml-select-pageSize"
          >
            {[10, 20, 50, 100].map((pageSize) => (
              <option key={pageSize} value={pageSize} data-testid={`fml-show-${pageSize}`}>
                Show {pageSize}
              </option>
            ))}
          </Form.Control>
        </Form>
      </div>
    </>
  );
};

export default ItineraryTable;
