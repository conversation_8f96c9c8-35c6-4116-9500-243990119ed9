import React, { useState, useContext } from 'react';
import { Container, Form, Col, Button, Row } from 'react-bootstrap';
import { toString, parseInt } from 'lodash';
import ItineraryDropDownSearchMenu from './ItineraryDropdownSearchMenu';
import ItinerarySubtypeField from './ItinerarySubtypeField';
import styleGuide from '../../styleGuide';
import { resetAllTabs } from '../../util/local-storage-helper';
import { useHistory } from 'react-router-dom';
import { getType } from '../../controller/itinerary-controller';
import { formatDate } from '../../util/view-utils';
const { Icon } = styleGuide;
import * as searchTypes from '../../constants/itinerary-search-types';
import { DetailContext } from '../../context/DetailContext';
import { VesselContext } from '../../context/VesselContext';

const ItineraryAdvancedSearch = ({ setFilters, filters, loadPortDropdown, ga4EventTrigger }) => {
  const history = useHistory();
  const { countries } = useContext(VesselContext);
  const { setShowFutureItinerary, showFutureItinerary } = useContext(DetailContext);
  const [dropDownData, setDropDownData] = useState({ countries: countries });

  const getsearchMenu = () => {
    return searchTypes.ITINERARY_SEARCH_TYPES.filter((types) => {
      const selectedFilter = filters.find(
        (filteredValue) => filteredValue.type.name === types.name,
      );
      if (types.name === 'Port') {
        const selectedCountryFilter = filters.find(
          (filteredValue) => filteredValue.type.name === 'Country',
        );
        return (
          selectedCountryFilter &&
          selectedCountryFilter.type !== '' &&
          selectedCountryFilter.subtype !== '' &&
          typeof selectedFilter === 'undefined'
        );
      } else {
        return (
          searchTypes.FILTER_MENU_ITEMS.includes(types.name) &&
          typeof selectedFilter === 'undefined'
        );
      }
    });
  };

  let searchMenuOptions = getsearchMenu();

  const addFilter = () => {
    if (filters.length === 0 || filters[filters.length - 1].type === '') return;
    setFilters((oldArray) => [...oldArray, { type: '' }]);
  };

  const onRemoveItem = (index) => {
    const array = [...filters];
    if (index > -1) {
      array.splice(index, 1);
    }
    setFilters(array);
    if (array.length === 0) {
      resetAllTabs();
      window.history.pushState({}, null, `${history.location.pathname}`);
    }
  };

  const onFilterDateFieldChange = (value, index) => {
    let filtersState = [...filters];
    ga4EventTrigger(
      'Value',
      'Vessel Itinerary Advance Search',
      `${toString(filtersState[index]?.type?.name)} - startDate: ${toString(
        formatDate(value?.startDate, 'DD MMM yyyy', ''),
      )} endDate: ${toString(formatDate(value?.endDate, 'DD MMM yyyy', ''))}`,
    );
    filtersState[index] = { type: filtersState[index].type, subtype: value };
    setFilters([...filtersState]);
  };

  const onFilterTypeChange = (event, index) => {
    let element = event[0];
    let filtersState = [...filters];
    if (element === undefined) {
      filtersState[index] = { type: '', subtype: '' };
      setFilters([...filtersState]);
      return;
    }
    let type = getType(element.type);
    ga4EventTrigger('Category', 'Vessel Itinerary Advance Search', type.name);
    if (!index) {
      setFilters([{ type, subtype: '' }]);
      return;
    }
    if (type) {
      filtersState[index] = { type, subtype: '' };
      setFilters([...filtersState]);
    }
  };

  const loadPortList = async (value) => {
    const portData = await loadPortDropdown(value);
    const existingDropDownData = dropDownData;
    existingDropDownData.ports = portData;
    setDropDownData(existingDropDownData);
  };

  const onFilterSubtypeChange = async (value, index, customValue) => {
    let filtersState = [...filters];
    let subtype = value;
    let newSelectedItem = value;
    if (!value) {
      filtersState[index] = { type: filtersState[index].type, subtype: '' };
      setFilters([...filtersState]);
      return;
    }
    switch (filtersState[index].type.inputType) {
      case 'multiselect':
        if (filtersState[index].type.type === 'countries') {
          loadPortList(value);
        }
        subtype = findSubtype(dropDownData[filtersState[index].type.type], value);
        if (value.length > 1) {
          const existingIds = filtersState[index].subtype.map((item) => item.id);
          newSelectedItem = subtype.filter((item) => !existingIds.includes(item.id));
        } else {
          newSelectedItem = subtype;
        }
        break;
      case 'dropdown':
        subtype = findSubtype(dropDownData[filtersState[index].type.type], value);
        break;
      case 'number_range':
        subtype = customValue;
        break;
      default:
        break;
    }
    ga4EventTrigger(
      'Value',
      'Vessel Itinerary Advance Search',
      `${toString(filtersState[index]?.type?.name)} - ${newSelectedItem[0].value}`,
    );
    filtersState[index] = { type: filtersState[index].type, subtype };
    setFilters([...filtersState]);
  };

  const findSubtype = (array, value) =>
    array.filter((element) => {
      if (typeof value === 'object') {
        return value.includes(element.id);
      }
      if (typeof value === 'string') {
        return element.id === value;
      }
      return element.value === parseInt(value);
    });

  const handleCheckboxChange = (event) => {
    ga4EventTrigger(
      'Display Future Itinerary Only',
      'Vessel Itinerary Advance Search',
      'Display Future Itinerary Only',
    );
    setShowFutureItinerary(event.currentTarget.checked);
  };

  const FilterRow = (props) => {
    searchMenuOptions = getsearchMenu();
    return props.filters.map((filter, index) => (
      <Row key={index} id={index} className="advanced_search__filter-row-borderless">
        <Form.Group className="form-group" as={Col} md="5">
          <ItineraryDropDownSearchMenu
            defaultSelected={[filter.type]}
            placeholder="Select Category"
            onChange={(e) => onFilterTypeChange(e, index)}
            searchMenuOptions={searchMenuOptions}
            dataTestId="fml-itinerary-list-filter-menu"
          />
        </Form.Group>

        {filter.type && (
          <>
            {ItinerarySubtypeField({
              type: filter.type,
              subtype: filter.subtype,
              disabled: false,
              disablePreviousDates:
                filter.type.type === 'estimated_departure' ? showFutureItinerary : false,
              onSubtypeChange: (e, customValue) =>
                onFilterSubtypeChange(e.target.value, index, customValue),
              onDateFieldChange: (value) => onFilterDateFieldChange(value, index),
              dropDownData: filter.type === 'countries' ? countries : dropDownData,
              title: false,
              onYearFieldChange: (value) => onFilterDateFieldChange(value, index),
              min: filter.subtype?.min ?? '',
              max: filter.subtype?.max ?? '',
              dataTestId: `fml-itinerary-list-subtype-${filter.type.name}`,
            })}

            <Form.Group className="form-group" as={Col} md={1}>
              <Icon
                icon="remove"
                size={30}
                className="remove"
                onClick={onRemoveItem.bind(this, index)}
                dataTestId={`fml-itinerary-list-remove-${filter.type.name}-filter`}
              />
            </Form.Group>
          </>
        )}
      </Row>
    ));
  };

  return (
    <div className="advanced_search">
      <Container>
        <Form>
          <Row>
            <Form.Group as={Col} md="6" className="m-0 form-group">
              <Form.Label className="filterHeading">
                <b>Filter Itinerary</b>
              </Form.Label>
            </Form.Group>
          </Row>

          <Row>
            <Form.Group as={Col} md="5" className="form-label form-group">
              <Form.Check
                type="checkbox"
                label="Display Future Itineraries Only"
                data-testid="fml-itinerary-list-future-only"
                checked={showFutureItinerary}
                onChange={(e) => handleCheckboxChange(e)}
              />
            </Form.Group>
          </Row>

          {FilterRow({ filters: filters })}

          {filters.length === 0 && (
            <Row>
              <Form.Group className="form-group" as={Col} md="5">
                <ItineraryDropDownSearchMenu
                  onChange={(e) => onFilterTypeChange(e)}
                  placeholder="Select Category"
                  searchMenuOptions={searchMenuOptions}
                  dataTestId="fml-itinerary-list-filter-menu"
                />
              </Form.Group>
            </Row>
          )}

          <Button
            data-testid="fml-itinerary-list-add-another-filter"
            variant="outline-primary"
            className="mb-3"
            size="sm"
            onClick={addFilter}
          >
            Add Another Filter
          </Button>
        </Form>
      </Container>
    </div>
  );
};

export default ItineraryAdvancedSearch;
