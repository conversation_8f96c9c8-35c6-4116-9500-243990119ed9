import httpService from './http-service';
import UserService from './user-service';

const { VESSEL_HOST } = process.env;

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const getRecentOwnershipRequestData = async (vesselId) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/${vesselId}/ownership/recent`, {
    headers: await createHeaders(),
  });
};

export const requestOwnershipChange = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/ownership-change/request`, data, {
    headers: await createHeaders(),
  });
};

export const updateRequestOwnershipChange = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/ownership-change/patch`, data, {
    headers: await createHeaders(),
  });
};

export const confirmOwnershipChange = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/ownership-change/confirm`, data, {
    headers: await createHeaders(),
  });
};

export const updateOwnershipApprovalStatus = async (id, approvalStatus, remarks) => {
  const REMARKS_LIMIT = 1000;
  const stripedRemarks =
    remarks && remarks.length > REMARKS_LIMIT
      ? remarks.substring(0, REMARKS_LIMIT).trim()
      : remarks;

  const requestJson = {
    id,
    approval_status: approvalStatus,
    remarks: stripedRemarks,
  };

  return httpService
    .getAxiosClient()
    .post(`${VESSEL_HOST}/ownership-change/update-approval-status`, requestJson, {
      headers: await createHeaders(),
    });
};

export const isVesselOwnershipPending = async (vesselId) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/${vesselId}/is-ownership-pending`, {
    headers: await createHeaders(),
  });
};

export const getDistinctFieldData = async (fields) => {
  const fieldsParam = fields.map((f) => `field=${f}`).join('&');
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/field-data?${fieldsParam}`, {
    headers: await createHeaders(),
  });
};
