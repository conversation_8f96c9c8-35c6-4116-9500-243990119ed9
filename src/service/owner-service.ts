import moment from 'moment';
import httpService from './http-service';
import UserService from './user-service';

const { OWNER_REPORTING_HOST } = process.env;
const createHeaders = async (ownershipId, ship_party_id) => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
    'x-active-vessel': ownershipId,
    'x-ownership-id': ownershipId,
    'x-ship-party-id': ship_party_id,
  };
};
const sortReportsByPeriodAndVersion = (data) => {
  // Sort the data by the "period" and "version" field in descending order
  return data.sort((a, b) => {
    // Sort by "period" in descending order based on month and year
    const periodComparison = moment(b.period, 'MMM-YY').diff(moment(a.period, 'MMM-YY'), 'months');
    // If "period" is the same, sort by "version"
    if (periodComparison === 0) {
      // Sort null versions to the end
      if (a.version === null && b.version !== null) return 1;
      if (a.version !== null && b.version === null) return -1;
      return b.version - a.version;
    }

    return periodComparison;
  });
};

const getLatestTwoReportsIds = (data) => {
  const lastTwoMonthsReports = data.filter((report) => report.status !== 'FROZEN').slice(0, 2);

  const isCurrentMonthReportSubmitted = lastTwoMonthsReports.some(
    (report) =>
      report?.period.slice(0, 3) === moment().format('MMM') && report?.status === 'SUBMITTED',
  );

  if (isCurrentMonthReportSubmitted) {
    return [lastTwoMonthsReports[0]?.id];
  }

  return lastTwoMonthsReports.map((report) => report?.id);
};

const updateReportProperties = async (
  report,
  index,
  latestTwoReportsIds,
  endDate,
  disableReopen,
  ship_party_id,
) => {
  const currentDate = moment();

  // Subtract 2 months from the current date and set it to the start of that month
  const twoMonthsAgo = currentDate.clone().subtract(2, 'months').startOf('month');
  // Parse the button's month as a moment object (assuming buttonMonth is a string like "June")

  const startOfCurrentMonth = currentDate.clone().startOf('month');
  const buttonDate = moment(`01 ${report?.name}`, 'DD MMMM YYYY');
  // Check if the button's date is before the start of the current month and after or equal to two months ago
  if (index === 0 && !report.period.includes('Dec')) {
    report.canGenrateNewReport =
      buttonDate.isSameOrAfter(twoMonthsAgo) && buttonDate.isBefore(startOfCurrentMonth);
  }

  if (latestTwoReportsIds.includes(report.id)) {
    report.canReopen =
      buttonDate.isAfter(twoMonthsAgo) && buttonDate.isSameOrBefore(startOfCurrentMonth);
  }

  if (endDate && disableReopen) {
    const startMonth = moment(endDate).format('MMMM YYYY');
    report.canReopen = report?.name === startMonth ? false : report.canReopen;
  }
  if (report.period.includes('Dec')) {
    const nextYearDate = moment(report.startDate).add(1, 'year');
    const nextYearReports = await getReports(
      nextYearDate,
      report.vesselCode,
      report.vesselId,
      null,
      ship_party_id,
      null,
      true,
    );
    if (nextYearReports.length) {
      report.canGenrateNewReport = false;
    } else {
      report.canGenrateNewReport =
        buttonDate.isSameOrAfter(twoMonthsAgo) && buttonDate.isBefore(startOfCurrentMonth);
    }
  }

  return report;
};

const reportHydration = (data, endDate, disableReopen, ship_party_id) => {
  const sortedData = sortReportsByPeriodAndVersion(data);
  const latestTwoReportsIds = getLatestTwoReportsIds(sortedData);

  if (latestTwoReportsIds.length) {
    sortedData.forEach((report, index) => {
      updateReportProperties(
        report,
        index,
        latestTwoReportsIds,
        endDate,
        disableReopen,
        ship_party_id,
      );
    });
  }

  return sortedData;
};
const getReports = async (
  date,
  vesselCode,
  vesselId,
  endDate,
  ship_party_id,
  disableReopen,
  skipHydration = false,
) => {
  const year = moment(date).year();
  const { data } = await httpService.getAxiosClient().get(`${OWNER_REPORTING_HOST}/reports`, {
    params: {
      year,
      vesselCode,
    },
    headers: await createHeaders(vesselId, ship_party_id),
  });
  return skipHydration ? data : reportHydration(data, endDate, disableReopen, ship_party_id);
};
const reopenReports = async (reportId, vesselId, ship_party_id) => {
  const resp = await httpService
    .getAxiosClient()
    .patch(`${OWNER_REPORTING_HOST}/reports/re-open/${reportId}`, null, {
      headers: await createHeaders(vesselId, ship_party_id),
    });
  return resp.status === 204;
};
const getBifercationData = async (reportIds, vesselId, ship_party_id) =>
  await httpService
    .getAxiosClient()
    .get(
      `${OWNER_REPORTING_HOST}/comments/bifurcation?${reportIds
        .map((i) => `reportIds=${i}`)
        .join('&')}`,
      {
        headers: await createHeaders(vesselId, ship_party_id),
      },
    );
export const generateFinancialReportForNextMonth = async (vesselCode, vesselId, ship_party_id) => {
  const resp = await httpService
    .getAxiosClient()
    .post(`${OWNER_REPORTING_HOST}/reports/generate-new-report/${vesselCode}`, null, {
      headers: await createHeaders(vesselId, ship_party_id),
    });
  return resp;
};
export const deleteGeneratedReport = async (reportId, vesselId, ship_party_id) => {
  const resp = await httpService
    .getAxiosClient()
    .delete(`${OWNER_REPORTING_HOST}/reports/delete-manually-added-report/${reportId}`, {
      headers: await createHeaders(vesselId, ship_party_id),
    });
  return resp;
};
const getOwnerVesselPref = async (vesselCode, vesselId, shipPartyId) => {
  const response = await httpService
    .getAxiosClient()
    .get(`${OWNER_REPORTING_HOST}/owner-vessel-preference`, {
      params: {
        shipPartyId,
        vesselCode,
      },
      headers: await createHeaders(vesselId, shipPartyId),
    });
  return response;
};
export const getVesselAccountDetails = async (vesselId, ownershipId, ship_party_id) => {
  const response = await httpService
    .getAxiosClient()
    .post(`${OWNER_REPORTING_HOST}/owner-vessel-preference/account-splitting/${vesselId}`, null, {
      headers: await createHeaders(ownershipId, ship_party_id),
    });
  return response;
};
export default {
  getReports,
  reopenReports,
  getBifercationData,
  generateFinancialReportForNextMonth,
  deleteGeneratedReport,
  getOwnerVesselPref,
  getVesselAccountDetails,
};
