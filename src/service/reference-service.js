// NOTE: move this to either paris2-web-base or standalone web module for api-reference
import httpService from './http-service';
import UserService from './user-service';

const { REFERENCE_HOST } = process.env;

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const getPortsByCountry = async (query) => {
  return httpService.getAxiosClient().get(`${REFERENCE_HOST}/ports?${query}`, {
    headers: await createHeaders(),
  });
};

export const getCountries = async () => {
  return httpService.getAxiosClient().get(`${REFERENCE_HOST}/countries`, {
    headers: await createHeaders(),
  });
};

export default {
  getCountries,
};
