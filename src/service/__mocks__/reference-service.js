const mockGetCountries = jest.fn().mockImplementation(async () => {
  return {
    data: {
      countries: [
        {
          alpha2_code: 'VN',
          alpha3_code: 'VNM',
          value: 'Viet Nam',
          numeric_code: '704',
        },
        {
          alpha2_code: 'CH',
          alpha3_code: 'CHE',
          value: 'Switzerland',
          numeric_code: '756',
        },
        {
          alpha2_code: 'US',
          alpha3_code: 'USA',
          value: 'United States',
          numeric_code: '840',
        },
      ],
    },
  };
});

const mockGetPortsByCountry = jest.fn().mockImplementation(async () => {
  return {
    data: {
      ports: [
        {
          name: 'Ballwil',
          city_code: 'BA8',
        },
        {
          name: 'Biberist',
          city_code: 'BIB',
        },
        {
          name: 'Birrwil',
          city_code: 'BIR',
        },
      ],
      count: 3,
    },
  };
});

export const getCountries = mockGetCountries;
export const getPortsByCountry = mockGetPortsByCountry;
