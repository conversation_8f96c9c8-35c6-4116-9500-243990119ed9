export const getRecentOwnershipRequestData = {
  id: 52,
  vessel_id: 1234,
  owner_id: 354,
  registered_owner_id: 244,
  ownership_status: 'pending',
  expected_date_of_takeover: null,
  owner_start_date: null,
  owner_end_date: null,
  registered_owner_start_date: null,
  registered_owner_end_date: null,
  vessel_short_code: 'VSC',
  vessel_account_code_new: '1212',
  vessel_tel_fac_code: '2121',
  name: 'Ultra Bosque',
  tech_group: 'Tech D7',
  ownership_change_request_id: 42,
  created_at: '2021-06-10T17:29:42.355Z',
  updated_at: '2021-06-10T17:29:42.355Z',
  ownership_change_request: {
    id: 42,
    request_status: 'pending',
    request_for: 'owner',
    requested_by_user: '<EMAIL>',
    is_split_of_accounting_books_require: false,
    created_at: '2021-06-10T17:29:42.326Z',
    updated_at: '2021-06-10T17:29:42.326Z',
    ownership_change_approval: [
      {
        id: 199,
        ownership_change_request_id: 42,
        department: 'Fleet Personnel',
        changed_by_user: '<PERSON>',
        approval_status: 'approved',
        remarks: null,
        final_approver: false,
        created_at: '2021-06-10T17:29:42.375Z',
        updated_at: '2021-06-10T17:29:42.375Z',
      },
      {
        id: 200,
        ownership_change_request_id: 42,
        department: 'Accounts',
        changed_by_user: null,
        approval_status: 'rejected',
        remarks: null,
        final_approver: false,
        created_at: '2021-06-10T17:29:42.376Z',
        updated_at: '2021-06-10T17:29:42.376Z',
      },
      {
        id: 201,
        ownership_change_request_id: 42,
        department: 'Tech Group',
        changed_by_user: null,
        approval_status: 'pending',
        remarks: null,
        final_approver: false,
        created_at: '2021-06-10T17:29:42.377Z',
        updated_at: '2021-06-10T17:29:42.377Z',
      },
      {
        id: 202,
        ownership_change_request_id: 42,
        department: 'Insurance',
        changed_by_user: 'Engerraund Serac',
        approval_status: 'rejected',
        remarks:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec sapien neque, suscipit.',
        final_approver: false,
        created_at: '2021-06-10T17:29:42.385Z',
        updated_at: '2021-06-10T17:29:42.385Z',
      },
      {
        id: 203,
        ownership_change_request_id: 42,
        department: 'Business',
        changed_by_user: null,
        approval_status: 'pending',
        remarks: null,
        final_approver: false,
        created_at: '2021-06-10T17:29:42.386Z',
        updated_at: '2021-06-10T17:29:42.386Z',
      },
      {
        id: 204,
        ownership_change_request_id: 42,
        department: 'Executive Director',
        changed_by_user: null,
        approval_status: 'pending',
        remarks: null,
        final_approver: true,
        created_at: '2021-06-10T17:29:42.387Z',
        updated_at: '2021-06-10T17:29:42.387Z',
      },
    ],
  },
  fleet_staff: [
    {
      tech_group: 'Tech D7',
    },
  ],
};

export const requestOwnershipChange = async () => {
  return {
    data: {
      created_at: '2021-06-10T17:29:42.326Z',
      id: 42,
      is_split_of_accounting_books_require: false,
      request_for: 'owner',
      request_status: 'pending',
      requested_by_user: '<EMAIL>',
      updated_at: '2021-06-10T17:29:42.326Z',
    },
  };
};

export const ownershipApprovedData = {
  id: 52,
  vessel_id: 1234,
  owner_id: 354,
  ownership_change_request: {
    id: 42,
    request_status: 'pending',
    request_for: 'owner',
    requested_by_user: '<EMAIL>',
    is_split_of_accounting_books_require: false,
    ownership_change_approval: [
      {
        id: 199,
        ownership_change_request_id: 42,
        department: 'Fleet Personnel',
        changed_by_user: 'Bernard Lowe',
        approval_status: 'approved',
        remarks: null,
        final_approver: false,
        created_at: '2021-06-10T17:29:42.375Z',
        updated_at: '2021-06-10T17:29:42.375Z',
      },
      {
        id: 200,
        ownership_change_request_id: 42,
        department: 'Business',
        changed_by_user: 'Bernard Lowe',
        approval_status: 'approved',
        remarks: null,
        final_approver: false,
        created_at: '2021-06-10T17:29:42.375Z',
        updated_at: '2021-06-10T17:29:42.375Z',
      },
      {
        id: 201,
        ownership_change_request_id: 42,
        department: 'Accounts',
        changed_by_user: 'Bernard Lowe',
        approval_status: 'approved',
        remarks: null,
        final_approver: false,
        created_at: '2021-06-10T17:29:42.375Z',
        updated_at: '2021-06-10T17:29:42.375Z',
      },
      {
        id: 202,
        ownership_change_request_id: 42,
        department: 'Insurance',
        changed_by_user: 'Bernard Lowe',
        approval_status: 'approved',
        remarks: null,
        final_approver: false,
        created_at: '2021-06-10T17:29:42.375Z',
        updated_at: '2021-06-10T17:29:42.375Z',
      },
      {
        id: 203,
        ownership_change_request_id: 42,
        department: 'TechD6',
        changed_by_user: 'Bernard Lowe',
        approval_status: 'approved',
        remarks: null,
        final_approver: false,
        created_at: '2021-06-10T17:29:42.375Z',
        updated_at: '2021-06-10T17:29:42.375Z',
      },
      {
        id: 204,
        ownership_change_request_id: 42,
        department: 'Executive Director',
        changed_by_user: null,
        approval_status: 'approved',
        remarks: null,
        final_approver: true,
      },
    ],
  },
  fleet_staff: [
    {
      tech_group: 'Tech D7',
    },
  ],
};

export const ownershipChangedData = {
  id: 52,
  vessel_id: 1234,
  owner_id: 354,
  registered_owner_id: 244,
  ownership_status: 'pending',
  expected_date_of_takeover: null,
  owner_start_date: '2021-06-10T17:29:42.355Z',
  owner_end_date: null,
  registered_owner_start_date: null,
  registered_owner_end_date: null,
  vessel_short_code: 'VSC',
  vessel_account_code_new: '1212',
  vessel_tel_fac_code: '2121',
  name: 'Ultra Bosque',
  tech_group: 'Tech D7',
  ownership_change_request_id: 42,
  created_at: '2021-06-10T17:29:42.355Z',
  updated_at: '2021-06-10T17:29:42.355Z',
  ownership_change_request: {
    id: 42,
    request_status: 'completed',
    request_for: 'owner',
    requested_by_user: '<EMAIL>',
    is_split_of_accounting_books_require: false,
    created_at: '2021-06-10T17:29:42.326Z',
    updated_at: '2021-06-10T17:29:42.326Z',
    ownership_change_approval: [
      {
        id: 204,
        ownership_change_request_id: 42,
        department: 'Executive Director',
        changed_by_user: null,
        approval_status: 'approved',
        remarks: null,
        final_approver: true,
        created_at: '2021-06-10T17:29:42.387Z',
        updated_at: '2021-06-10T17:29:42.387Z',
      },
    ],
  },
  fleet_staff: [
    {
      tech_group: 'Tech D7',
    },
  ],
};

export const getDistinctFieldData = async () => {
  return {
    data: {
      vessel_short_code: ['ABC', 'PQR'],
      vessel_account_code_new: ['1111', '2222'],
    },
  };
};

export const isVesselOwnershipPending = async () => {
  return {
    data: true,
  };
};
