export const getOwnersData = jest.fn().mockImplementation(async () => {
  return {
    data: {
      miscRegisteredOwners: [
        {
          id: 3,
          value: 'Heroic Hermes Inc.',
        },
        {
          id: 5,
          value: 'New Golden Shipping Limited',
        },
        {
          id: 6,
          value: 'New Fortune Shipping Limited',
        },
        {
          id: 7,
          value: 'East Blue Marlin SA',
        },
      ],
      owners: [
        {
          id: 3,
          value: 'Blue Forest Shipping Co.',
        },
        {
          id: 4,
          value: 'Kowa Kaiun Co. Ltd.',
        },
        {
          id: 5,
          value: 'Saito Shipping Co. Ltd.',
        },
        {
          id: 6,
          value: 'Scottish Ship Owners & Managers Pty Ltd.',
        },
      ],
    },
  };
});

export const getVesselFieldsData = jest.fn().mockImplementation(async (id, name) => {
  return {
    data: {
      vessels: [
        {
          id: 1234,
          name: 'Ultra Bosque',
        },
      ],
    },
  };
});

export const isAnyInputPending = jest
  .fn()
  .mockImplementation(() => Promise.resolve({ data: false }));

export const getVessel = jest.fn().mockImplementation(() =>
  Promise.resolve({
    data: {
      id: 1234,
      status: 'draft',
      pending_status: 'active',
      name: 'Ultra Bosque',
      ownerships: [
        {
          id: 100,
          name: 'Ultra Bosque',
          tech_group: 'Tech D7',
          fleet_staff: [
            {
              tech_group: 'Tech D7',
            },
          ],
        },
      ],
      phones: [],
      emails: [],
    },
  }),
);

export default {
  getVesselFieldsData,
  getOwnersData,
  isAnyInputPending,
  getVessel,
};
