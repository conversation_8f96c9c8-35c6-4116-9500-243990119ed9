import httpService from './http-service';
import { createHeaders } from './vessel-service';

const { VESSEL_HOST } = process.env;

export const getVesselApprovalData = async (vessel) => {
  const targetStatus = vessel.pending_status ? vessel.pending_status : vessel.status;
  console.log('get vessel approval data for id ', vessel.id);
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/approvals/${vessel.id}/?targetVesselStatus=${targetStatus}`, {
      headers: await createHeaders(),
    });
};

export const isVesselAllApproved = async (vesselId, targetStatus) => {
  return httpService
    .getAxiosClient()
    .get(
      `${VESSEL_HOST}/approvals/${vesselId}/is-all-approved?targetVesselStatus=${targetStatus}`,
      {
        headers: await createHeaders(),
      },
    );
};

export const updateVesselApprovalStatus = async (id, approvalStatus, remarks) => {
  console.log(`updating vessel approval status for approval record with id: ${id}`);

  const REMARKS_LIMIT = 1000;
  const stripedRemarks =
    remarks && remarks.length > REMARKS_LIMIT
      ? remarks.substring(0, REMARKS_LIMIT).trim()
      : remarks;

  const requestJson = {
    id,
    approval_status: approvalStatus,
    remarks: stripedRemarks,
  };

  return httpService.getAxiosClient().post(`${VESSEL_HOST}/update-approval-status`, requestJson, {
    headers: await createHeaders(),
  });
};

export const requestApprovalFor = async (vesselId, status) => {
  const requestJson = {
    vessel_id: parseInt(vesselId),
    status: status,
  };

  return httpService.getAxiosClient().post(`${VESSEL_HOST}/request-status-change`, requestJson, {
    headers: await createHeaders(),
  });
};

export default {
  getVesselApprovalData,
  updateVesselApprovalStatus,
  requestApprovalFor,
  isVesselAllApproved,
};
