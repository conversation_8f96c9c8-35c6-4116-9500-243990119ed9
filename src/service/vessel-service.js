import { vesselListTabKeys, vesselStatuses } from '../model/constants';
import { checkSortBy } from '../util/view-utils';
import httpService from './http-service';
import UserService from './user-service';

const {
  VESSEL_HOST,
  FILE_HOST,
  SHIP_PARTY_HOST,
  SEAFARER_HOST,
  NOTIFICATION_HOST,
  CREW_ASSIGNMENT_HOST,
} = process.env;

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

const queryGraphql = async (query) => {
  const { data } = await httpService.getAxiosClient().post(`${VESSEL_HOST}/query-graphql`, query, {
    headers: await createHeaders(),
  });
  return data;
};

export const getVessel = async (vesselId) => {
  console.log('get vessel for id ', vesselId);
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/${vesselId}`, {
    headers: await createHeaders(),
  });
};

export const downloadContingencyFile = async (id) => {
  const headers = await createHeaders();
  headers['Accept'] = 'image/jpeg, image/png, application/pdf';
  return httpService.getAxiosClient().get(`${SHIP_PARTY_HOST}/download-contingency-file/${id}`, {
    headers,
    responseType: 'arraybuffer',
  });
};

export const getOwnershipVessel = async (ownershipId) => {
  console.log('get ownership data for vessel with ownership id ', ownershipId);
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/ownership/${ownershipId}`, {
    headers: await createHeaders(),
  });
};

const vesselStatusToQueryParamMap = new Map([
  [vesselListTabKeys.ACTIVE_VESSEL, vesselStatuses.ACTIVE],
  [vesselListTabKeys.NEW_TAKEOVERS, vesselStatuses.DRAFT],
  [vesselListTabKeys.HANDED_OVER, vesselStatuses.HANDED_OVER],
  [vesselListTabKeys.ARCHIVED, vesselStatuses.ARCHIVED],
]);

export const getVessels = async (activeTab, paginationParams, query = '', name = '') => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const withRelations = 'withRelations=true';
  const statusParam = `status=${vesselStatusToQueryParamMap.get(activeTab)}`;
  const sortParam = sortBy?.length
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=vessel.created_at+desc';
  const limitParam = `limit=${pageSize || 10}`;
  const offset = `offset=${pageIndex || 0}`;
  query += name.length > 0 ? `&name=${name}` : '';
  const fixedQuery = query
    .split('&')
    .filter(
      (q) =>
        !q.startsWith('state=') &&
        !q.startsWith('code=') &&
        !q.startsWith('session_state=') &&
        !q.startsWith('redirect'),
    )
    .join('&');
  const queryParams = [limitParam, offset, sortParam, statusParam, withRelations, fixedQuery].join(
    '&',
  );
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/query?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const getVesselFieldsData = async (vesselId, fields) => {
  const fieldsParam = fields.map((field) => `f=${field}`).join('&');
  const queryParam = `vessel.id=${vesselId}&${fieldsParam}`;
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/query?${queryParam}`, {
    headers: await createHeaders(),
  });
};

export const getDropDownData = async () => {
  return queryGraphql({
    query: `{
      phoneTypes { id, value }
      emailTypes { id, value }
      flags { id, value }
      hmUnderwriters { id, value }
      piClubs { id, value }
      ihmProviders { id, value }
      vesselClasss { id, value , is_eu_verifier}
      vesselServiceStatuss { id, value }
      vesselTypes { id, value }     
      owners { id, value }
      miscEngines  { id, value }
      miscRegisteredOwners  { id, value }
      miscOperators  { id, value }
      miscManagers  { id, value }
      miscCurrencys  { id, value }
      miscFlagIspss  { id, value }
      miscClassifications  { id, value }
      miscClassificationSocietys  { id, value }
      miscQis  { id, value }
      miscOsros  { id, value }
      miscSalvages  { id, value }
      miscMediaResponses  { id, value }
      miscManagementTypes  { id, value }
      miscOtherContactss { id, value }
      emissionTypes { id, emission_type, specification }
    }
    `,
  });
};

export const getOwnersData = async () => {
  return queryGraphql({
    query: `{
      owners { id, value }
      miscRegisteredOwners  { id, value }
    }
    `,
  });
};

export const createVessel = async (vessel) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/create`, vessel, {
    headers: await createHeaders(),
  });
};

export const patchVessel = async (id, vessel) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/${id}/patch`, vessel, {
    headers: await createHeaders(),
  });
};

export const requestVesselUploadUrls = async (fileNames) => {
  return httpService.getAxiosClient().post(
    `${VESSEL_HOST}/request-upload-urls`,
    {
      fileNames,
    },
    {
      headers: await createHeaders(),
    },
  );
};

export const requestVesselDownloadUrls = async (imagePaths) => {
  console.log('requestVesselDownloadUrls', JSON.stringify(imagePaths));
  const response = await httpService.getAxiosClient().post(
    `${FILE_HOST}/request-download-urls`,
    {
      paths: imagePaths,
    },
    {
      headers: await createHeaders(),
    },
  );
  if (response?.data?.results) {
    const data = response.data.results.reduce((map, { url, path }) => {
      return {
        ...map,
        [path]: url,
      };
    }, {});
    console.log('download urls', data);
    return { data };
  }
  return {
    data: {},
  };
};

export const isAnyInputPending = async (id) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/is-input-pending/${id}`, {
    headers: await createHeaders(),
  });
};

export const getLastPosition = async (imo) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/get-last-position/${imo}`, {
    headers: await createHeaders(),
  });
};

export const applyPendingStatus = async (payload) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/apply-pending-status/`, payload, {
    headers: await createHeaders(),
  });
};

export const getCrewAssignment = async (id) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/crew-assignment/${id}`, {
    headers: await createHeaders(),
  });
};

export const getCrewList = async (params) => {
  const queryParams = `${params}&withRelations=true`;
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/crew-list?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const canDoHandover = async (id) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/${id}/can-do-handover`, {
    headers: await createHeaders(),
  });
};

export const getSignedOnSeafarer = async (query) => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/query-signed-on?${query}`, {
    headers: await createHeaders(),
  });
};

let cancelToken;
export const getOwnerships = async (activeTab, paginationParams, query = '', name = '') => {
  if (typeof cancelToken != typeof undefined) {
    cancelToken.cancel('Operation canceled due to new request.');
  }
  cancelToken = httpService.axios.CancelToken.source();

  const { pageIndex, pageSize, sortBy } = paginationParams;
  const statusParam = activeTab ? `status=${vesselStatusToQueryParamMap.get(activeTab)}` : '';
  const sortParam = sortBy?.length
    ? `order=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'order=created_at+desc';
  const limitParam = `limit=${pageSize}`;
  const offset = `offset=${pageIndex * pageSize}`;
  query += name.length > 0 ? `&keyword=${name}` : '';
  const fixedQuery = query
    .split('&')
    .filter(
      (q) =>
        !q.startsWith('state=') &&
        !q.startsWith('code=') &&
        !q.startsWith('session_state=') &&
        !q.startsWith('redirect'),
    )
    .join('&');
  const queryParams = [limitParam, offset, sortParam, statusParam, fixedQuery, 'flatten=true'].join(
    '&',
  );
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/ownerships?${queryParams}`, {
    headers: await createHeaders(),
    cancelToken: cancelToken.token,
  });
};

const getVesselShipParty = async (vesselId) => {
  console.log('get ship party for vessel with ownership id ', vesselId);
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/ship-party/${vesselId}`, {
    headers: await createHeaders(),
  });
};

export const getItinerary = async (ownershipId, paginationParams, query = '', name = '') => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy?.length
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=estimated_departure+desc';
  const limitParam = `limit=${pageSize || 10}`;
  const offset = `offset=${pageIndex || 0}`;
  query += name.length > 0 ? `&keyword=${name}` : '';
  const queryParams = [limitParam, offset, sortParam, query].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/itinerary/${ownershipId}?${queryParams}`, {
      headers: await createHeaders(),
    });
};

export const getAllVessels = async (
  fields = 'f=name&f=id&f=fleet_staff.tech_group&f=vessel.id',
  active = false,
) => {
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/ownerships?${fields}${active ? '&status=active' : ''}`, {
      headers: await createHeaders(),
    });
};

export const getTechnicalReports = async (
  reportType,
  ownershipId,
  paginationParams,
  query = '',
  isListing = true,
) => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}` : '';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const pageMode = `pageMode=${isListing ? 'list' : 'detail'}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, sortParam, pageMode].join('&');
  return httpService
    .getAxiosClient()
    .get(
      `${VESSEL_HOST}/report/technical/${reportType}${
        ownershipId ? '/' + ownershipId : ''
      }?${queryParams}`,
      {
        headers: await createHeaders(),
      },
    );
};

export const getDrillHistory = async (ownershipId, paginationParams, query = '') => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=name+asc';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, sortParam].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/${ownershipId}/emergency-drill/history?${queryParams}`, {
      headers: await createHeaders(),
    });
};

export const getEmergencyDrillList = async (sortBy, params = '') => {
  const sortParam = sortBy[0]?.id ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}` : '';
  const queryParams = [sortParam, params].join('&');
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/emergency-drill?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const createDrill = async (drill) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/emergency-drill`, drill, {
    headers: await createHeaders(),
  });
};

export const patchDrill = async (id, drill) => {
  return httpService.getAxiosClient().patch(`${VESSEL_HOST}/emergency-drill/${id}`, drill, {
    headers: await createHeaders(),
  });
};

export const getAssignedEmergencyDrillList = async (sortBy, ownershipId) => {
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=name+asc';
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/${ownershipId}/emergency-drill?${sortParam}`, {
      headers: await createHeaders(),
    });
};

export const unAssignDrills = async (ownershipId, drillList) => {
  return httpService.getAxiosClient().delete(`${VESSEL_HOST}/${ownershipId}/emergency-drill`, {
    headers: await createHeaders(),
    data: drillList,
  });
};

export const assignDrills = async (ownershipId, drillList) => {
  return httpService
    .getAxiosClient()
    .post(`${VESSEL_HOST}/${ownershipId}/emergency-drill`, drillList, {
      headers: await createHeaders(),
    });
};

export const getContolParameters = async (ownershipId) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/${ownershipId}/control-parameter`, {
    headers: await createHeaders(),
  });
};

export const patchControlParameters = async (ownershipId, controlParams) => {
  return httpService
    .getAxiosClient()
    .patch(`${VESSEL_HOST}/${ownershipId}/control-parameter`, controlParams, {
      headers: await createHeaders(),
    });
};

let cancelAdminCertificateToken = null;
export const getAdminCertificatesList = async (paginationParams, query = '', name = '') => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=name+asc';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const field = `name=${name}`;
  const queryParams = [pageIndexParam, pageSizeParam, sortParam, field, query].join('&');

  if (cancelAdminCertificateToken != null) {
    cancelAdminCertificateToken.cancel();
  }
  cancelAdminCertificateToken = httpService.axios.CancelToken.source();
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/certificate?${queryParams}`, {
    headers: await createHeaders(),
    cancelToken: cancelAdminCertificateToken.token,
  });
};

export const getVesselCertificates = async (vesselId, paginationParams, name) => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy?.[0]?.id
    ? `orderBy=${sortBy[0]?.id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=group+asc';
  const pageSizeParam = pageSize ? `pageSize=${pageSize}` : '';
  const pageIndexParam = pageIndex ? `pageIndex=${pageIndex}` : '';
  const field = name ? `group=${name}` : null;
  const queryParams = [pageIndexParam, pageSizeParam, sortParam, field].join('&');
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/${vesselId}/certificate?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const deleteCertificate = async (certificateId) => {
  return httpService.getAxiosClient().delete(`${VESSEL_HOST}/certificate/${certificateId}`, {
    headers: await createHeaders(),
  });
};

export const createAdminCertificate = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/certificate`, data, {
    headers: await createHeaders(),
  });
};

export const updateAdminCertificate = async (data, id) => {
  return httpService.getAxiosClient().patch(`${VESSEL_HOST}/certificate/${id}`, data, {
    headers: await createHeaders(),
  });
};

export const sendEmail = async (payload, files = []) => {
  let headers = await createHeaders();
  const formData = new FormData();
  formData.append('emailContent', JSON.stringify(payload));
  files?.forEach((file) =>
    formData.append('attachments', new Blob([file.data], { type: file.type }), file.name),
  );
  headers['Content-Type'] = 'multipart/form-data';
  return httpService.getAxiosClient().post(`${NOTIFICATION_HOST}/api/send-email`, formData, {
    headers: headers,
  });
};

export const getAssignedCertificates = async (certificateId, paginationParams, search) => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=place+desc';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [pageIndexParam, pageSizeParam, sortParam].join('&');
  let searchString = '';
  Object.entries(search).forEach((a) => {
    searchString += '&' + a.join('=');
  });
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/certificates/${certificateId}?${queryParams}${searchString}`, {
      headers: await createHeaders(),
    });
};

export const getDocument = async (paths) => {
  const headers = await createHeaders();
  const { data: response } = await httpService
    .getAxiosClient()
    .post(`${FILE_HOST}/request-download-urls`, paths, {
      headers,
    });
  const promises = response?.results?.map(async (result) =>
    httpService.getAxiosClient().get(`${result.url}`, {
      headers,
      responseType: 'arraybuffer',
    }),
  );
  return Promise.all(promises);
};

export const createAssignCertificate = async (vesselId, certificateId, data) => {
  let headers = await createHeaders();
  return httpService
    .getAxiosClient()
    .post(`${VESSEL_HOST}/${vesselId}/certificate/${certificateId}`, data, {
      headers: headers,
    });
};

export const updateAssignCertificate = async (certificateId, data) => {
  let headers = await createHeaders();
  return httpService
    .getAxiosClient()
    .patch(`${VESSEL_HOST}/certificate/assigned/${certificateId}`, data, {
      headers: headers,
    });
};

export const getVoyageHistory = async (paginationParams, params) => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const filterParams = Object.keys(params).map((key) => {
    if (params[key]) {
      return `${key}=${params[key]}`;
    }
  });

  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=group+asc';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = filterParams.concat([pageIndexParam, pageSizeParam, sortParam]).join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/technical/voyage-history?${queryParams}`, {
      headers: await createHeaders(),
    });
};

export const getMonthlyMarpolList = async (vesselId, paginationParams, query = '') => {
  const { pageIndex, pageSize, sortBy, pageMode } = paginationParams;
  const sortParam = sortBy[0]?.id ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}` : '';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const pageModeParam = `pageMode=${pageMode}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, pageModeParam, sortParam].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/environmental/marpol/${vesselId ?? ''}?${queryParams}`, {
      headers: await createHeaders(),
    });
};

export const getSealList = async (vesselId, paginationParams, query = '') => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=vessel_name+asc';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, sortParam].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/environmental/seal-number/${vesselId ?? ''}?${queryParams}`, {
      headers: await createHeaders(),
    });
};

export const getSealGroup = async (vesselId, paginationParams, query = '') => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=batch+asc';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, sortParam].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/environmental/seal-group/${vesselId ?? ''}?${queryParams}`, {
      headers: await createHeaders(),
    });
};

export const getSealLog = async (vesselId, paginationParams, query = '') => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}` : '';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, sortParam].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/environmental/seal-log/${vesselId ?? ''}?${queryParams}`, {
      headers: await createHeaders(),
    });
};

export const addSealGroup = async (vesselId, data) => {
  return httpService
    .getAxiosClient()
    .post(`${VESSEL_HOST}/report/environmental/seal-number/${vesselId ?? ''}`, data, {
      headers: await createHeaders(),
    });
};

export const deleteSealGroup = async (vesselId, data) => {
  return httpService
    .getAxiosClient()
    .patch(`${VESSEL_HOST}/report/environmental/seal-number/${vesselId ?? ''}`, data, {
      headers: await createHeaders(),
    });
};

export const getWasteStreamReport = async (vesselId = null, query = '') => {
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/environmental/waste-stream/${vesselId ?? ''}?${query}`, {
      headers: await createHeaders(),
    });
};

export const getImoDataReport = async (vesselId = null, query = '') => {
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/environmental/imo/${vesselId ?? ''}?${query}`, {
      headers: await createHeaders(),
    });
};

export const getMrvReport = async (vesselId = null, query = '') => {
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/environmental/mrv/${vesselId ?? ''}?${query}`, {
      headers: await createHeaders(),
    });
};

export const getETSReport = async (vesselId, query = '') => {
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/environmental/ets/${vesselId}?${query}`, {
      headers: await createHeaders(),
    });
};
export const getReportEnabledDatasourceConfigs = async (vessel_ownership_id) => {
  const VESSEL_SERVICE =
    process.env.VESSEL_SERVICE || 'https://paris2-vessel-service-dev2.fleetship.com';
  return httpService
    .getAxiosClient()
    .get(
      `${VESSEL_SERVICE}/api/vendor-datasource-config/vessel-ownership/${vessel_ownership_id}/enabled`,
      {
        headers: await createHeaders(),
      },
    );
};

export const updateVerificationStatus = async (data, etsId = '') => {
  return httpService
    .getAxiosClient()
    .patch(`${VESSEL_HOST}/report/environmental/ets/${etsId}`, data, {
      headers: await createHeaders(),
    });
};

export const deletePendingVoyage = async (data, etsId = '') => {
  return httpService
    .getAxiosClient()
    .delete(`${VESSEL_HOST}/report/environmental/eu-ets/pending-voyage/${etsId}`, {
      headers: await createHeaders(),
      data: data,
    });
};

export const uploadVoyageVerificationDocument = async (attachmentKey, etsId = '') => {
  return httpService.getAxiosClient().patch(
    `${VESSEL_HOST}/ets/voyages/${etsId}/attachment`,
    { attachment_key: attachmentKey },
    {
      headers: await createHeaders(),
    },
  );
};

export const getFinancialReportTypes = async () => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/report/financial/type`, {
    headers: await createHeaders(),
  });
};
let cancelFinancialToken = null;
export const getFinancialReports = async (vesselId, reportType, paginationParams, query = '') => {
  if (cancelFinancialToken != null) {
    cancelFinancialToken.cancel();
  }
  cancelFinancialToken = httpService.axios.CancelToken.source();
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}` : '';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, sortParam].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/financial/${reportType}/${vesselId ?? ''}?${queryParams}`, {
      headers: await createHeaders(),
      cancelToken: cancelFinancialToken.token,
    });
};

export const addFinancialReport = async (type, data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/report/financial/${type}`, data, {
    headers: await createHeaders(),
  });
};

export const editFinancialReport = async (type, data) => {
  return httpService.getAxiosClient().patch(`${VESSEL_HOST}/report/financial/${type}`, data, {
    headers: await createHeaders(),
  });
};

let cancelCashCallToken = null;
export const getCashCallReports = async (vessel_ownership_id, paginationParams, query = '') => {
  if (cancelCashCallToken != null) {
    cancelCashCallToken.cancel();
  }
  cancelCashCallToken = httpService.axios.CancelToken.source();
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}` : '';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, sortParam].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/financial/cash-call/${vessel_ownership_id ?? ''}?${queryParams}`, {
      headers: await createHeaders(),
      cancelToken: cancelCashCallToken.token,
    });
};

export const getRecipientList = async (vessel_ownership_id, paginationParams, query = '') => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy?.[0]?.id ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}` : '';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, sortParam].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/financial/recipient/${vessel_ownership_id ?? ''}?${queryParams}`, {
      headers: await createHeaders(),
    });
};

export const addRecipientList = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/report/financial/recipient`, data, {
    headers: await createHeaders(),
  });
};

export const editRecipientList = async (data) => {
  return httpService.getAxiosClient().patch(`${VESSEL_HOST}/report/financial/recipient`, data, {
    headers: await createHeaders(),
  });
};
export const getCashCallDropDownData = async () => {
  return queryGraphql({
    query: `{
      miscCurrencys  { id, value }
    }
    `,
  });
};

export const addCashCallReport = async (data) => {
  let headers = await createHeaders();
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/report/financial/cash-call`, data, {
    headers: headers,
  });
};

export const editCashCallReport = async (data) => {
  let headers = await createHeaders();
  return httpService.getAxiosClient().patch(`${VESSEL_HOST}/report/financial/cash-call`, data, {
    headers: headers,
  });
};

export const getPreSignedUploadLink = async (data) => {
  let headers = await createHeaders();
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/pre-signed-link`, data, {
    headers: headers,
  });
};

export const uploadPresignedDocument = async (url, file, type, timeout = undefined) => {
  let client = httpService.getAxiosClient();
  if (timeout !== undefined) client.defaults.timeout = timeout;
  return client.put(url, file, {
    headers: {
      'Content-Type': type,
    },
  });
};

export const getPresignedDocument = async (url) => {
  return httpService.getAxiosClient().get(url, {
    responseType: 'arraybuffer',
  });
};

export const getAgentsList = async (sortBy, ownershipId) => {
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=last_modified+asc';
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/agent-charterer/agent/${ownershipId}?${sortParam}`, {
      headers: await createHeaders(),
    });
};

export const addAgent = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/agent-charterer/agent`, data, {
    headers: await createHeaders(),
  });
};

export const editAgent = async (data) => {
  return httpService.getAxiosClient().patch(`${VESSEL_HOST}/agent-charterer/agent`, data, {
    headers: await createHeaders(),
  });
};

export const getChartererList = async (sortBy, ownershipId) => {
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=last_modified+asc';
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/agent-charterer/charterer/${ownershipId}?${sortParam}`, {
      headers: await createHeaders(),
    });
};

export const addCharterer = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/agent-charterer/charterer`, data, {
    headers: await createHeaders(),
  });
};

export const editCharterer = async (data) => {
  return httpService.getAxiosClient().patch(`${VESSEL_HOST}/agent-charterer/charterer`, data, {
    headers: await createHeaders(),
  });
};

export const getManualsList = async (vesselId, paginationParams) => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=group+asc';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [pageIndexParam, pageSizeParam, sortParam].join('&');
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/${vesselId}/drawing?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const getManualTypes = async () => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/drawing/type`, {
    headers: await createHeaders(),
  });
};

export const addManualsReport = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/drawing`, data, {
    headers: await createHeaders(),
  });
};

export const editManualsReport = async (data) => {
  return httpService.getAxiosClient().patch(`${VESSEL_HOST}/drawing`, data, {
    headers: await createHeaders(),
  });
};

export const getAssignedVesselsListToManuals = async (reportId, paginationParams) => {
  const { pageIndex, pageSize, sortBy } = paginationParams;
  const sortParam = sortBy[0]?.id
    ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}`
    : 'orderBy=group+asc';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const queryParams = [pageIndexParam, pageSizeParam, sortParam].join('&');
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/drawing/${reportId}?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const getFlagOffice = async () => {
  return queryGraphql({
    query: `{      
      flags { id, value }      
    }
    `,
  });
};

export const flagOfficeChange = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/flag-log`, data, {
    headers: await createHeaders(),
  });
};

export const getReportDataMapper = async (reportType, ownershipId) => {
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/${reportType}/attributes?vessel_ownership_id=${ownershipId}`, {
      headers: await createHeaders(),
    });
};

export const patchCompareReportField = async (report_type, reportId, data) => {
  return httpService
    .getAxiosClient()
    .patch(`${VESSEL_HOST}/report/${report_type}/attributes/${reportId}`, data, {
      headers: await createHeaders(),
    });
};

export const assignControlParameters = async (ownershipId, data) => {
  return httpService
    .getAxiosClient()
    .post(`${VESSEL_HOST}/${ownershipId}/control-parameter`, data, {
      headers: await createHeaders(),
    });
};

export const get96HoursReportList = async (vesselId, paginationParams, query = '') => {
  const { pageIndex, pageSize, sortBy, pageMode } = paginationParams;
  const sortParam = sortBy[0]?.id ? `orderBy=${sortBy[0].id}+${checkSortBy(sortBy[0].desc)}` : '';
  const pageSizeParam = `pageSize=${pageSize}`;
  const pageIndexParam = `pageIndex=${pageIndex}`;
  const pageModeParam = `pageMode=${pageMode}`;
  const queryParams = [query, pageIndexParam, pageSizeParam, pageModeParam, sortParam].join('&');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/report/environmental/ninety-six-hours/${vesselId ?? ''}?${queryParams}`, {
      headers: await createHeaders(),
    });
};
export const getAssignedStaffVessel = async (params) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/${params.id}/staff/${params.role}`, {
    headers: await createHeaders(),
  });
};

export const assignStaffToVessel = async (data) => {
  return httpService.getAxiosClient().post(`${VESSEL_HOST}/staff`, data, {
    headers: await createHeaders(),
  });
};

export const getChangeLogs = async (ownershipId, type, query) => {
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/${ownershipId}/change_log/${type}?${query}`, {
      headers: await createHeaders(),
    });
};

export const syncVendorData = async (vesselOwnershipId, year, vendor) => {
  return httpService.getAxiosClient().post(
    `${process.env.VESSEL_SERVICE}/api/sync-vendor-reports/${vesselOwnershipId}`,
    {
      year,
      vendor,
    },
    {
      headers: await createHeaders(),
    },
  );
};

export const getStratumPositions = async (vesselOwnershipId, startDateTime, endDateTime) => {
  const response = await httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/${vesselOwnershipId}/stratum/${startDateTime}/${endDateTime}`, {
      headers: await createHeaders(),
    });
  return response.data[vesselOwnershipId] ?? [];
};

export default {
  getVessel,
  getOwnershipVessel,
  getVessels,
  getAllVessels,
  createVessel,
  patchVessel,
  getDropDownData,
  getVesselFieldsData,
  createHeaders,
  isAnyInputPending,
  getLastPosition,
  applyPendingStatus,
  getOwnersData,
  getItinerary,
  getCrewAssignment,
  getCrewList,
  getSignedOnSeafarer,
  getOwnerships,
  requestVesselUploadUrls,
  requestVesselDownloadUrls,
  canDoHandover,
  getVesselShipParty,
  downloadContingencyFile,
  getTechnicalReports,
  getDrillHistory,
  getEmergencyDrillList,
  createDrill,
  patchDrill,
  getAssignedEmergencyDrillList,
  unAssignDrills,
  assignDrills,
  getContolParameters,
  patchControlParameters,
  getAdminCertificatesList,
  createAdminCertificate,
  updateAdminCertificate,
  getVesselCertificates,
  deleteCertificate,
  getAssignedCertificates,
  getDocument,
  createAssignCertificate,
  updateAssignCertificate,
  getVoyageHistory,
  getMonthlyMarpolList,
  getSealList,
  getSealGroup,
  getSealLog,
  addSealGroup,
  deleteSealGroup,
  getImoDataReport,
  getWasteStreamReport,
  getMrvReport,
  getETSReport,
  updateVerificationStatus,
  getFlagOffice,
  flagOfficeChange,
  getFinancialReports,
  getFinancialReportTypes,
  addFinancialReport,
  editFinancialReport,
  getCashCallReports,
  getRecipientList,
  addRecipientList,
  editRecipientList,
  getCashCallDropDownData,
  addCashCallReport,
  getPreSignedUploadLink,
  uploadPresignedDocument,
  editCashCallReport,
  getPresignedDocument,
  assignControlParameters,
  getAgentsList,
  addAgent,
  editAgent,
  getChartererList,
  addCharterer,
  editCharterer,
  getManualsList,
  getManualTypes,
  addManualsReport,
  editManualsReport,
  getAssignedVesselsListToManuals,
  getReportDataMapper,
  patchCompareReportField,
  get96HoursReportList,
  getAssignedStaffVessel,
  assignStaffToVessel,
  getChangeLogs,
  getStratumPositions,
  uploadVoyageVerificationDocument,
  getReportEnabledDatasourceConfigs,
  syncVendorData,
};
