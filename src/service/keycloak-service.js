import httpService from './http-service';
import UserService from './user-service';

const { KEYCLOAK_HOST } = process.env;
console.log(KEYCLOAK_HOST, 'asdsf');

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const getTechgroup = async () => {
  return httpService.getAxiosClient().get(`${KEYCLOAK_HOST}/vessel/get-tech-group`, {
    headers: await createHeaders(),
  });
};

export const findMembership = async (username) => {
  const requestJson = { username: username };
  return httpService
    .getAxiosClient()
    .post(`${KEYCLOAK_HOST}/vessel/memberships/find`, requestJson, {
      headers: await createHeaders(),
    });
};

export const getSuperintendentsInTechGroup = async () => {
  return httpService.getAxiosClient().get(`${KEYCLOAK_HOST}/vessel/get-tech-supdt`, {
    headers: await createHeaders(),
  });
};

export const getStaffList = async (type) => {
  return httpService.getAxiosClient().get(`${KEYCLOAK_HOST}/vessel/get-vessel-fml-staffs/${type}`, {
    headers: await createHeaders(),
  });
};

export const getUserList = async (params) => {
  return httpService.getAxiosClient().get(`${KEYCLOAK_HOST}/users?${params}`, {
    headers: await createHeaders(),
  });
};

export const getTechGroupDetails = async (params) => {
  return httpService.getAxiosClient().get(`${KEYCLOAK_HOST}/techgroup-details?${params}`, {
    headers: await createHeaders(),
  });
};

export default {
  getTechgroup,
  findMembership,
  getSuperintendentsInTechGroup,
  getStaffList,
  getUserList,
  getTechGroupDetails
};
