import { configure } from 'enzyme';
import Adapter from '@wojtekmaj/enzyme-adapter-react-17';
import { act } from 'react-dom/test-utils';
import lodash from 'lodash';

// mocked neccesary dependency
jest.mock('./service/user-service');
jest.mock('./styleGuide');
jest.mock('./service/owner-service');

configure({ adapter: new Adapter() });
global._ = lodash;

export const wait = (amount = 0) => new Promise((resolve) => setTimeout(resolve, amount));

// Use this in your test after mounting if you want the query to finish and update the wrapper
export async function updateWrapper(wrapper, amount = 0) {
  await act(async () => {
    await wait(amount);
    wrapper.update();
  });
}

global.document.createRange();
