import vesselService from '../service/vessel-service';
import * as searchQuery from '../util/search-query';
import keycloakService from '../service/keycloak-service';
import { fleetStaffTypes } from '../util/search-query';
import _ from 'lodash';

const SEARCH_TYPES = searchQuery.getSearchTypes();

const fetchStaffListByType = async (staffType, setError) => {
  try {
    const staffList = await keycloakService.getStaffList(staffType);
    return staffList.data.map((staff, index) => ({
      id: staff.id ?? index + 1,
      value: staff.full_name,
    }));
  } catch (error) {
    setError('Failed to fetch Staff Type data.');
    return [];
  }
};

const loadDropDownData = async (id) => {
  const { data } = await vesselService.getDropDownData(id);
  const vesselClasss = data?.vesselClasss || [];
  const euVerifiers = vesselClasss?.filter((vc) => vc.is_eu_verifier);

  return [
    'emailTypes',
    'flags',
    'hmUnderwriters',
    'owners',
    'phoneTypes',
    'piClubs',
    'ihmProviders',
    'portOfRegistrys',
    'vesselClasss',
    'vesselServiceStatuss',
    'vesselTypes',
  ].reduce(
    (map, key) => ({
      ...map,
      [key]: data?.[key] || [],
    }),
    { euVerifiers },
  );
};

class SearchController {
  async onLoadPage(criteria) {
    let dropDownData = await loadDropDownData();
    const eorbStatuses = [
      { id: 1, value: 'Open' },
      { id: 2, value: 'Pending Installation' },
      { id: 3, value: 'Not Installed' },
    ];
    const staffFilters = [];
    criteria.forEach((item) => {
      if ([...Object.keys(fleetStaffTypes), 'tech_group'].includes(item.type.type)) {
        staffFilters.push(item);
      }
    });
    if (!_.isEmpty(staffFilters)) {
      // Load staff data for selected staffType
      const response = await Promise.all(
        staffFilters.map((item) => this.loadStaffTypesData(item.type.type)),
      );
      staffFilters.forEach((item, index) => (dropDownData[item.type.type] = response[index]));
    }
    dropDownData = { ...dropDownData, eorbStatuses };
    return {
      dropDownData,
    };
  }

  async loadStaffTypesData(fleetStaff, setError = () => {}) {
    if (_.isArray(fleetStaffTypes[fleetStaff])) {
      const response = await Promise.all(
        fleetStaffTypes[fleetStaff].map((staffType) => fetchStaffListByType(staffType, setError)),
      );
      return [
        ...response.flat(),
        {
          id: 'not-assigned',
          value: '(Not assigned)',
        },
      ];
    } else
      return fetchStaffListByType(fleetStaff, setError).then((data) => [
        ...data,
        { id: 'not-assigned', value: '(Not assigned)' },
      ]);
  }

  getAllFilters() {
    return SEARCH_TYPES;
  }

  getType(type) {
    const found = SEARCH_TYPES.find(function (element) {
      return element.type === type;
    });

    return found;
  }

  getVesselQuery(filters) {
    return searchQuery.mapSearchCriteriaToVesselQuery(filters);
  }
}

export default SearchController;
