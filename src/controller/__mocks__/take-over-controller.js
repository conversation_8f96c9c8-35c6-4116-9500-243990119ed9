export const mockOnLoadPage = jest.fn().mockImplementation(() =>
  Promise.resolve({
    vessel: { isOwnershipChangePending: false },
    dropDownData: {},
    isAllApproved: false,
    userMemberships: [],
    distinctFieldData: { vessel_short_code: [], vessel_account_code_new: [] },
  }),
);
const mock = jest.fn().mockImplementation(() => {
  return { onLoadPage: mockOnLoadPage };
});

export default mock;
