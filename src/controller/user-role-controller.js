import { FIRST_APPROVERS_GROUPS } from '../model/constants';
import _ from 'lodash';
import ROLES from '../constants/roles';

class UserRoleController {
  async getConfig(kc) {
    const hasRole = (role) => kc.realmAccess.roles.includes(role);
    const departmentList = kc.tokenParsed.group.map((group) => group.split('/')[2]);
    const techGroups = kc.tokenParsed.group
      .filter((group) => group.startsWith('/Tech Group/'))
      .map((group) => group.split('/').pop());
    const groups = kc?.tokenParsed?.group

    return {
      departments: departmentList,
      groups: groups,
      vessel: {
        create: hasRole(ROLES.VESSEL.CREATE),
        edit: hasRole(ROLES.VESSEL.EDIT),
        view: hasRole(ROLES.VESSEL.VIEW),
        exportToExcel: hasRole(ROLES.VESSEL.EXPORT_TO_EXCEL),
        approve: hasRole(ROLES.VESSEL.APPROVE),
        moveToActive: hasRole(ROLES.VESSEL.MOVE_TO_ACTIVE),
        moveToHandover: hasRole(ROLES.VESSEL.MOVE_TO_HANDOVER),
        moveToArchival: hasRole(ROLES.VESSEL.MOVE_TO_ARCHIVAL),
        viewHistory: hasRole(ROLES.VESSEL.VIEW_HISTORY),
        viewApproval: hasRole(ROLES.VESSEL.VIEW_APPROVAL),
        viewAssigned: hasRole(ROLES.VESSEL.VIEW_ASSIGNED),
        requestHandOver: hasRole(ROLES.VESSEL.REQUEST_HANDOVER),
        requestArchival: hasRole(ROLES.VESSEL.REQUEST_ARCHIVAL),
        changeFlag: hasRole(ROLES.VESSEL.CHANGE_FLAG),
        editReport: hasRole(ROLES.VESSEL.REPORT_EDIT),
        manualSyncReport: hasRole(ROLES.VESSEL.MANUAL_SYNC_REPORT),
        staff: {
          buyer: hasRole(ROLES.VESSEL.BUYER_MANAGE),
          accountant: hasRole(ROLES.VESSEL.ACCOUNTANT_MANAGE),
          supdt: hasRole(ROLES.VESSEL.SUPDT_MANAGE),
          qhse: hasRole(ROLES.VESSEL.QHSE_MANAGE),
          operation: hasRole(ROLES.VESSEL.OPERATION_MANAGE),
          payroll: hasRole(ROLES.VESSEL.PAYROLL_MANAGE),
          pod_manager: hasRole(ROLES.VESSEL.POD_MANAGER),
        },
        send: hasRole(ROLES.VESSEL.SEND),
        environmental: {
          editEuEtsReport: hasRole(ROLES.VESSEL.EUETS_REPORT_EDIT),
        },
      },
      admin: {
        view: hasRole(ROLES.VESSEL.ADMIN),
        drills: {
          view: hasRole(ROLES.VESSEL.ADMIN_DRILLS_VIEW),
          create: hasRole(ROLES.VESSEL.ADMIN_DRILLS_CREATE),
          edit: hasRole(ROLES.VESSEL.ADMIN_DRILLS_EDIT),
        },
        certificates: {
          manage: hasRole(ROLES.VESSEL.CERTIFICATE_MANAGE),
          assign: hasRole(ROLES.VESSEL.CERTIFICATE_ASSIGN),
          create: hasRole(ROLES.VESSEL.CERTIFICATE_CREATE)
        },
      },
      drills: {
        view: hasRole(ROLES.VESSEL.DRILLS_VIEW),
        assign: hasRole(ROLES.VESSEL.DRILLS_ASSIGN),
      },
      params: {
        view: hasRole(ROLES.VESSEL.CONTROL_PARAMS_VIEW),
        edit: hasRole(ROLES.VESSEL.CONTROL_PARAMS_EDIT),
      },
      approvalGroups: _.intersection(FIRST_APPROVERS_GROUPS, departmentList),
      techGroups: {
        techGroups,
        manage: hasRole(ROLES.VESSEL.TECH_GROUP_MANAGE),
      },
      finalApprover: hasRole(ROLES.VESSEL.FINAL_APPROVE),
      viewInspections: !_.isEmpty(
        _.intersection(Object.values(ROLES.VIR_ROLES), kc.realmAccess.roles),
      ),
      crewAssignment: hasRole(ROLES.VESSEL.VIEW_CREW_ASSIGNMENT),
      certificates: {
        all: hasRole(ROLES.VESSEL.CERTIFICATE_VIEW_ALL),
        manage: hasRole(ROLES.VESSEL.CERTIFICATE_MANAGE),
        assign: hasRole(ROLES.VESSEL.CERTIFICATE_ASSIGN),
      },
      financial: {
        view: hasRole(ROLES.VESSEL.FINANCIAL_ACCOUNTS_VIEW),
        edit: hasRole(ROLES.VESSEL.FINANCIAL_EDIT),
        manage: hasRole(ROLES.VESSEL.FINANCIAL_ACCOUNTS_MANAGE),
        payCalculationEdit: hasRole(ROLES.VESSEL.FINANCIAL_PAY_CALCULATION),
      },
      cashCall: {
        view: hasRole(ROLES.VESSEL.FINANCIAL_CASH_CALL_VIEW),
        manage: hasRole(ROLES.VESSEL.FINANCIAL_CASH_CALL_MANAGE),
      },
      owner: {
        view: hasRole(ROLES.VESSEL.FINANCIAL_TECHNICAL_VIEW),
        manage: hasRole(ROLES.VESSEL.FINANCIAL_TECHNICAL_MANAGE),
      },
      eorb: {
        view: hasRole(ROLES.VESSEL.VESSEL_PRODUCT_EORB),
      },
      ownerReporting: {
        view: hasRole(ROLES.OWNER_REPORTING_ROLES.VIEW),
        manage: hasRole(ROLES.OWNER_REPORTING_ROLES.MANAGE),
        hasAllVesselAccess: hasRole(ROLES.OWNER_REPORTING_ROLES.HAS_ALL_VESSEL_ACCESS),
        canViewOFRTab: kc.tokenParsed?.ship_party_id ? kc.tokenParsed?.financialOwnerReportingAccess === "allow" : true,
        candeleteReport: hasRole(ROLES.OWNER_REPORTING_ROLES.CAN_DELETE_REPORT),
      },
      seafarerReportModellerView: hasRole(ROLES.SEAFARER_ROLES.SEAFARER_REPORT_MODELLER_VIEW),
      seafarerGeneralView: hasRole(ROLES.SEAFARER_ROLES.SEAFARER_VIEW_GENERAL),
      seafarerViewCrewList: hasRole(ROLES.SEAFARER_ROLES.SEAFARER_VIEW_CREW_LIST),
      paris1View: hasRole(ROLES.VESSEL.PARIS1_VIEW),
      shipPartyViewGeneral: hasRole(ROLES.SHIP_PARTY_ROLES.SHIP_PARTY_VIEW_GENERAL),
    };
  }
}

export default UserRoleController;
