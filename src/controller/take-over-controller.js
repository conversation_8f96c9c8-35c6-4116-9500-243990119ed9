import vesselService from '../service/vessel-service';
import keycloakService from '../service/keycloak-service';
import { isVesselAllApproved } from '../service/vessel-approval-service';
import _ from 'lodash';
import { CUSTOM_USER_GROUPS, vesselStatuses } from '../model/constants';
import { getDistinctFieldData, isVesselOwnershipPending } from '../service/ownership-service';

const EMAIL_TYPE_EMAIL = 1;
const EMAIL_TYPE_SATC_EMAIL = 2;

const mapTypedRows = (rows, typeId, textId) =>
  rows
    .map((row) => ({
      [textId]: row[textId],
      [typeId]: parseInt(row[typeId]),
    }))
    .filter((item) => {
      const type = item[typeId];
      const text = item[textId];
      return type && text;
    });

const mapValue = (key, value) => {
  switch (key) {
    case 'shipyard_id':
    case 'life_boat_capacity':
    case 'length_oa':
    case 'length_bp':
    case 'depth':
    case 'breadth_extreme':
    case 'summer_draft':
    case 'summer_dwt':
    case 'international_grt':
    case 'international_nrt':
    case 'service_speed':
    case 'dwt':
    case 'bhp':
    case 'vessel_account_code':
    case 'emission_type_id':
    case 'misc_engine_id':
    case 'maximum_continuous_rating_kw':
    case 'maximum_continuous_rating_rpm':
      return { [key]: Number(value) };
    case 'shipyard_text':
    case 'vessel_hull_number':
    case 'year_of_delivery':
    case 'date_of_delivery':
    case 'imo_number':
    case 'shop_trials':
    case 'sea_trials':
    case 'is_adding_new_sea_trials':
    case 'lng_engine_category_main_engine':
    case 'lng_engine_category_diesel_generator':
      return { [key]: value };
    case 'is_engine_consume_lng':
      return { [key]: value === 'true' };
    default:
      return {};
  }
};

const mapOwnershipValue = (key, value) => {
  switch (key) {
    case 'name':
    case 'owner_id':
    case 'vessel_short_code':
    case 'vessel_account_code_new':
    case 'is_manning_manager':
    case 'us_visa_required':
    case 'has_portage_bill':
    case 'pay_calculation_method':
    case 'wages_treatment':
    case 'wages_accumulation':
      return { [key]: value };
    case 'vessel_type_id':
    case 'p_i_club_id':
    case 'ihm_provider_id':
    case 'h_m_underwriter_id':
      return { [key]: parseFloat(value) };
    case 'vessel_service_status_id':
      return {
        service_status_id: parseFloat(value),
      };
    case 'misc_registered_owner_id':
      return {
        registered_owner_id: value,
      };
    case 'expected_date_of_takeover':
      return {
        expected_owner_start_date: value,
        expected_registered_owner_start_date: value,
      };
    case 'ihm_inspection_date':
      return {
        ihm_inspection_date: value,
      };
    case 'date_of_takeover':
      return {
        owner_start_date: value,
        registered_owner_start_date: value,
      };
    case 'date_of_handover':
      return {
        owner_end_date: value,
        registered_owner_end_date: value,
      };
    case 'vessel_tec_fac_code':
      return {
        vessel_tel_fac_code: value,
      };
    case 'techgroup':
      return {
        fleet_staff: [
          {
            tech_group: value,
          },
        ],
      };
    case 'misc_manager_id':
      return {
        manager_id: parseFloat(value),
      };
    case 'flag_isps_id':
      return {
        flag_isps_id: parseFloat(value),
      };
    case 'misc_classification_id':
      return {
        classification_id: parseFloat(value),
      };
    case 'misc_classification_euets_verifier_id':
      return {
        vessel_class_regulation: {
          class_id: parseFloat(value),
          regulation: 'EU', // since its EU regulation only
        },
      };
    case 'misc_operator_id':
      return {
        operator_id: parseFloat(value),
      };
    case 'misc_currency_id':
      return {
        currency_id: parseFloat(value),
      };
    case 'misc_classification_society_id':
      return {
        classification_society_id: parseFloat(value),
      };
    case 'misc_qi_id':
      return {
        qi_id: parseFloat(value),
      };
    case 'misc_salvage_id':
      return {
        salvage_id: parseFloat(value),
      };
    case 'misc_osro_id':
      return {
        osro_id: parseFloat(value),
      };
    case 'misc_media_response_id':
      return {
        media_response_id: parseFloat(value),
      };
    case 'misc_management_type_id':
      return {
        management_type_id: value,
      };
    case 'misc_other_contacts_id':
      return {
        other_contacts_id: parseFloat(value),
      };
    case 'class_notations':
      return {
        class_notations: mapTypedRows(value, 'vessel_class_id', 'notation'),
      };
    case 'phones':
      return {
        phones: mapTypedRows(value, 'phone_type_id', 'phone_number'),
      };
    case 'emails':
      return {
        emails: mapTypedRows(value, 'email_type_id', 'email'),
      };
    case 'images':
      return {
        images: value,
      };
    default:
      return {};
  }
};

const mapChangesToRequestVessel = (vessel, changes, ownershipId = null) => {
  const fixedChanges = {
    ...changes,
  };

  const hasEmailChanges = !!(
    changes.primary_email ||
    changes.primary_satc_email ||
    changes.other_emails
  );
  const finalEmailChanges = [];
  if (hasEmailChanges) {
    if (!changes.primary_email) {
      if (vessel?.primary_email) {
        finalEmailChanges.push({
          email_type_id: EMAIL_TYPE_EMAIL,
          email: vessel.primary_email,
        });
      }
    } else {
      finalEmailChanges.push({
        email_type_id: EMAIL_TYPE_EMAIL,
        email: changes.primary_email,
      });
    }
    if (!changes.primary_satc_email) {
      if (vessel?.primary_satc_email) {
        finalEmailChanges.push({
          email_type_id: EMAIL_TYPE_SATC_EMAIL,
          email: vessel.primary_satc_email,
        });
      }
    } else {
      finalEmailChanges.push({
        email_type_id: EMAIL_TYPE_SATC_EMAIL,
        email: changes.primary_satc_email,
      });
    }
    if (!changes.other_emails) {
      if (vessel?.other_emails) {
        vessel.other_emails.forEach((email) => finalEmailChanges.push(email));
      }
    } else {
      changes.other_emails
        .filter(({ email }) => email)
        .forEach((email) => finalEmailChanges.push(email));
    }
    fixedChanges.emails = finalEmailChanges;
  }
  delete fixedChanges.primary_email;
  delete fixedChanges.primary_satc_email;
  delete fixedChanges.other_emails;

  const vesselChanges = Object.keys(fixedChanges).reduce(
    (map, key) => ({
      ...map,
      ...mapValue(key, fixedChanges[key]),
      ownerships: [
        {
          ...mapOwnershipValue(key, fixedChanges[key]),
        },
      ],
    }),
    {},
  );

  if (vesselChanges.shop_trials) {
    vesselChanges.shop_trials
      .filter((shopTrial) => Object.keys(shopTrial).length > 1)
      .forEach((shopTrial) => {
        if (typeof shopTrial.id === 'string') delete shopTrial.id;
        for (const [key, value] of Object.entries(shopTrial)) {
          shopTrial[key] = parseFloat(value);
        }
      });
  }

  if (vesselChanges.sea_trials) {
    vesselChanges.sea_trials
      .filter((seaTrial) => Object.keys(seaTrial).length > 1)
      .forEach((seaTrial) => {
        if (typeof seaTrial.id === 'string') delete seaTrial.id;
        delete seaTrial.created_at;
        delete seaTrial.updated_at;
        for (const [key, value] of Object.entries(seaTrial)) {
          seaTrial[key] = parseFloat(value);
        }
      });
  }

  const ownershipIdJson = ownershipId ? { id: ownershipId } : {};
  const ownershipData = Object.keys(fixedChanges).reduce(
    (map, key) => ({
      ...map,
      ...ownershipIdJson,
      ...mapOwnershipValue(key, fixedChanges[key]),
    }),
    {},
  );

  return {
    ...vesselChanges,
    ownerships: [
      {
        ...ownershipData,
      },
    ],
  };
};

const createVessel = async (changes) => {
  console.log('takeover: create vessel:', changes);
  const vessel = mapChangesToRequestVessel(null, changes);
  delete vessel.isOwnershipChangePending;
  const { data = {} } = await vesselService.createVessel(vessel);
  console.log('takeover: create vessel: response:' + JSON.stringify(data));
  const { id } = data;
  const ownershipId = data.ownerships[0].id;
  return { vesselId: id, ownershipId };
};

const patchVessel = async (vessel, vesselId, ownershipId, changes) => {
  try {
    const cleanedChanges = removeEmptyStrings(changes);
    const mappedVessel = mapChangesToRequestVessel(vessel, cleanedChanges, ownershipId);
    console.log('## takeover: patch vessel', mappedVessel);
    const { data } = await vesselService.patchVessel(vesselId, mappedVessel);
    console.log('## takeover: patch vessel: response:', JSON.stringify(data));
    return vesselId;
  } catch (error) {
    console.log('something went wrong on patching vessel:', error);
  }
};

const removeEmptyStrings = (dictionary) => {
  Object.keys(dictionary).forEach(function (key) {
    const value = dictionary[key];
    if (_.isString(value)) {
      const trimedValue = value.trim();
      if (trimedValue === '') {
        dictionary[key] = null;
      }
    }
    console.log(key, dictionary[key]);
  });
  return dictionary;
};

const formatNotation = (class_notations) => {
  if (!class_notations) return [];
  return class_notations.map(({ vessel_class_id, notation }) => ({
    vessel_class_id,
    notation,
  }));
};

const formatEmail = (emails) => {
  if (!emails) return [];
  return emails.map(({ email_type_id, email }) => ({
    email_type_id,
    email,
  }));
};

const formatPhone = (phones) => {
  if (!phones) return [];
  return phones.map(({ phone_type_id, phone_number }) => ({
    phone_type_id,
    phone_number,
  }));
};

const formatLoadedVessel = (ownershipData) => {
  const vessel = ownershipData.vessel;
  const { class_notations, emails, phones, images } = ownershipData;
  delete vessel.created_by_user_info;
  delete vessel.updated_by_user_info;
  const vesselEmails = formatEmail(emails);
  const primaryEMail =
    vesselEmails.find(({ email_type_id }) => email_type_id === EMAIL_TYPE_EMAIL) ?? null;
  const primarySatcEMail =
    vesselEmails.find(({ email_type_id }) => email_type_id === EMAIL_TYPE_SATC_EMAIL) ?? null;
  const otherEmails = vesselEmails.filter((email) => {
    if (primaryEMail && primaryEMail.email === email.email) {
      return false;
    }
    return primarySatcEMail && primarySatcEMail.email !== email.email;
  });
  return {
    ...vessel,
    name: ownershipData.name,
    owner_id: ownershipData.owner_id,
    misc_registered_owner_id: ownershipData.registered_owner_id,
    expected_date_of_takeover: ownershipData.expected_owner_start_date,
    date_of_takeover: ownershipData.owner_start_date,
    date_of_handover: ownershipData.owner_end_date || ownershipData.registered_owner_end_date,
    vessel_short_code: ownershipData.vessel_short_code,
    vessel_account_code_new: ownershipData.vessel_account_code_new,
    vessel_tec_fac_code: ownershipData.vessel_tel_fac_code,
    class_notations: formatNotation(class_notations),
    emails: vesselEmails,
    primary_email: primaryEMail ? primaryEMail.email : null,
    primary_satc_email: primarySatcEMail ? primarySatcEMail.email : null,
    other_emails: otherEmails,
    phones: formatPhone(phones),
    wages_treatment: ownershipData.wages_treatment,
    wages_accumulation: ownershipData?.wages_accumulation,
    flag_isps_id: ownershipData.flag_isps_id,
    has_portage_bill: ownershipData.has_portage_bill,
    pay_calculation_method: ownershipData.pay_calculation_method,
    misc_classification_euets_verifier_id: ownershipData.vessel_class_regulation[0]?.class_id,
    misc_classification_euets_verifier:
      ownershipData.vessel_class_regulation[0]?.vessel_class?.value,
    images,
    techgroup: ownershipData.fleet_staff.tech_group,
  };
};

const loadVessel = async (ownershipId) => {
  try {
    const { data: ownershipData = {} } = await vesselService.getOwnershipVessel(ownershipId);
    return formatLoadedVessel(ownershipData);
  } catch (error) {
    console.error('error', error);
    if (error.message.includes('404')) return { status: 404 };
    return { status: 500 };
  }
};

const loadDropDownData = async () => {
  const [{ data: dropDownsData }, { data: techGroup }] = await Promise.all([
    vesselService.getDropDownData(),
    keycloakService.getTechgroup(),
  ]);

  const techgroups = techGroup.response.tech_group
    .filter((group) => !CUSTOM_USER_GROUPS.includes(group))
    .map((name) => ({ id: name, value: name }));

  const euVerifiers = dropDownsData.vesselClasss.filter((vc) => vc.is_eu_verifier);

  return [
    'emailTypes',
    'flags',
    'hmUnderwriters',
    'owners',
    'phoneTypes',
    'piClubs',
    'ihmProviders',
    'portOfRegistrys',
    'shipyards',
    'vesselClasss',
    'vesselServiceStatuss',
    'vesselTypes',
    'miscEngines',
    'miscRegisteredOwners',
    'miscManagers',
    'miscFlagIspss',
    'miscQis',
    'miscSalvages',
    'miscOperators',
    'miscCurrencys',
    'miscClassifications',
    'miscClassificationSocietys',
    'miscOsros',
    'miscMediaResponses',
    'miscManagementTypes',
    'miscOtherContactss',
    'emissionTypes',
  ].reduce(
    (map, key) => ({
      ...map,
      [key]: dropDownsData[key] || [],
    }),
    { techgroups, euVerifiers },
  );
};

class TakeOverController {
  vesselId = null;
  ownershipId = null;
  vesselChanges = {};

  async onLoadPage(step, ownershipId, username) {
    // reset
    this.vesselChanges = {};
    // set initial values
    this.step = step || 'basic';
    this.ownershipId = ownershipId ? parseInt(ownershipId) : null;
    let vessel = null;
    let isAllApproved = false;
    let dropDownData;
    let distinctFieldData;
    const uniqueFields = ['vessel_short_code', 'vessel_account_code_new'];

    if (this.ownershipId) {
      vessel = await loadVessel(this.ownershipId);
      this.vesselId = vessel.id;
      this.vessel = vessel;
      isAllApproved =
        vessel && vessel.pending_status === vesselStatuses.ACTIVE
          ? await isVesselAllApproved(vessel.id, vessel.pending_status)
          : false;
      const response = await Promise.all([
        loadDropDownData(),
        isVesselOwnershipPending(vessel.id),
        getDistinctFieldData(uniqueFields),
      ]);

      dropDownData = response[0];
      const { data: isOwnershipPending } = response[1];
      vessel.isOwnershipChangePending = isOwnershipPending;
      distinctFieldData = response[2].data;
    } else {
      this.vesselId = null;
      const response = await Promise.all([loadDropDownData(), getDistinctFieldData(uniqueFields)]);
      dropDownData = response[0];
      distinctFieldData = response[1].data;
    }

    const { data: userMemberships } = await keycloakService.findMembership(username);

    return {
      vessel,
      dropDownData,
      isAllApproved,
      userMemberships,
      distinctFieldData,
    };
  }

  async onSubmitVessel(values) {
    if (this.vesselId === null) {
      const payload = values.created_by_hash
        ? { ...values, ...this.vesselChanges }
        : { ...this.vesselChanges };

      const { vesselId, ownershipId } = await createVessel(payload);
      this.vesselId = vesselId;
      this.ownershipId = ownershipId;
      return { vesselId: this.vesselId, ownershipId: this.ownershipId };
    } else {
      const id = await patchVessel(
        this.vessel,
        this.vesselId,
        this.ownershipId,
        this.vesselChanges,
      );
      return { vesselId: id, ownershipId: this.ownershipId };
    }
  }

  onVesselChange(key, value) {
    if (typeof value === 'string' && value.length === 0) {
      value = null;
    }

    let modifiedJson = { ...this.vesselChanges, [key]: value };
    this.vesselChanges = {
      ...modifiedJson,
    };
    return this.vesselChanges;
  }

  removeIdOfTrial(trials) {
    return trials.map((trial) => {
      delete trial.id;
      return trial;
    });
  }

  onVesselCopy(vessel) {
    delete vessel.flag;
    delete vessel.h_m_underwriter;
    delete vessel.p_i_club;
    delete vessel.ihm_provider;
    delete vessel.ihm_inspection_date;
    delete vessel.shipyard;
    delete vessel.service_status;
    delete vessel.pending_status;
    delete vessel.vessel_type;
    delete vessel.port_of_registry;
    delete vessel.owner;
    delete vessel.vesselId;
    delete vessel.id;
    delete vessel.photos;
    delete vessel.images;
    delete vessel.misc_engine;
    delete vessel.misc_registered_owner;
    delete vessel.misc_operator;
    delete vessel.misc_manager;
    delete vessel.misc_currency;
    delete vessel.misc_flag_isps;
    delete vessel.misc_classification;
    delete vessel.misc_classification_society;
    delete vessel.misc_qi;
    delete vessel.misc_osro;
    delete vessel.misc_salvage;
    delete vessel.misc_media_response;
    delete vessel.misc_management_type;
    delete vessel.misc_other_contacts;
    delete vessel.ref_id;
    delete vessel.created_at;
    delete vessel.created_by;
    delete vessel.updated_at;
    delete vessel.vessel_account_code;
    delete vessel.vessel_account_code_new;
    delete vessel.date_of_takeover;
    delete vessel.vessel_short_code;

    for (const prop in vessel) {
      if (vessel[prop] === null) delete vessel[prop];
    }

    vessel.status = 'draft';

    vessel.shop_trials = this.removeIdOfTrial(vessel.shop_trials);
    vessel.sea_trials = this.removeIdOfTrial(vessel.sea_trials);

    this.vesselChanges = {
      ...vessel,
    };
  }
}

export default TakeOverController;
