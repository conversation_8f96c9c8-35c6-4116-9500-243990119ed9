import { getCountries } from '../service/reference-service';
import {
  mapSearchCriteriaToVesselQuery,
  modifyJSONData,
} from '../constants/itinerary-search-query';
import itinerarySearchTypes from '../constants/itinerary-search-types';

const loadDropDownData = async () => {
  const result = await Promise.all([getCountries()]);
  const dropDownsData = result[0];
  dropDownsData['countries'] = result[0].data.countries;
  return ['countries'].reduce(
    (map, key) => ({
      ...map,
      [key]: dropDownsData[key] || [],
    }),
    {},
  );
};

export const onLoadPage = async () => {
  return modifyJSONData(await loadDropDownData());
};

export const getType = (type) => {
  return itinerarySearchTypes.find((element) => {
    return element.type === type;
  });
};

export const getVesselQuery = (filters) => mapSearchCriteriaToVesselQuery(filters);
