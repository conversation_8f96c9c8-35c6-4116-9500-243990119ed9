import React, { useState, useEffect } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import ErrorAlert from '../component/ErrorAlert';
import vesselService from '../service/vessel-service';
import Spinner from '../component/Spinner';
import getURLParams from '../util/getURLParams';

const VesselDetails = () => {
  const { vesselId } = useParams();
  const history = useHistory();
  const [error, setError] = useState(null);
  const vesselName = getURLParams('name', history.location.search);

  const goToOwnershipDetails = async (activeKey, query) => {
    const paginationParams = {
      pageIndex: 0,
      pageSize: 0,
      sortBy: [],
    };

    const searchedKeyword = '';
    const { data } = await vesselService.getOwnerships(
      activeKey,
      paginationParams,
      query,
      searchedKeyword,
    );

    if (data.results.length > 1) {
      data.results = data.results.filter((ele) => {
        return ele.name === vesselName;
      });
    }
    if (data.results.length == 0) {
      throw new Error('No active ownership data present');
    }
    const ownershipId = data.results[0].id;
    history.replace(`/vessel/ownership/details/${ownershipId}`);
  };

  useEffect(() => {
    (async () => {
      try {
        const query = `vessel_id=${vesselId}`;
        await goToOwnershipDetails('active-vessels', query);
      } catch (error) {
        console.error(`Get vessel by ID: ${vesselId} failed. Error: ${error}`);
        // Using name as fallback
        try {
          const query = `name=${vesselName}`;
          await goToOwnershipDetails('', query);
        } catch (error) {
          console.error(`Get vessel by name: ${vesselName} failed. Error: ${error}`);
          setError('Oops, something went wrong. Please try again.');
        }
      }
    })();
  }, []);

  return error ? <ErrorAlert message={error} /> : <Spinner />;
};

export default VesselDetails;
