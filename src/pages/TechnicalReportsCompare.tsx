import { TechnicalReportContext } from '../context/TechnicalReportContext';
import React, { useContext, useEffect, useState } from 'react';
import { Container } from 'react-bootstrap';
import { useParams } from 'react-router-dom';
import vesselService from '../service/vessel-service';
import { ErrorPage } from '../styleGuide';
import TechnicalReportComparePage from '../component/TechnicalReports/CompareReports/TechnicalReportComparePage';
import { TechnicalReportCompareMapperData } from '../model/TechnicalReportCompareMapperData';

const TechnicalReportsCompare = () => {
  const { report, ownershipId } = useParams();
  const [isAccessDenied, setIsAccessDenied] = useState(false);
  const [controlParamters, setContolParameters] = useState();
  const { userEmail } = useContext(TechnicalReportContext);

  useEffect(() => {
    (async () => {
      const { data } = await vesselService.getContolParameters(ownershipId);
      setContolParameters({
        performance: data?.result.filter((item) => {
          return item.report_type === 'performance';
        }),
        monthly: data?.result.filter((item) => {
          return item.report_type === 'monthly';
        }),
        quarterly: data?.result.filter((item) => {
          return item.report_type === 'quarterly';
        }),
        voyage: data?.result.filter((item) => {
          return item.report_type === 'voyage';
        }),
        position: data?.result.filter((item) => {
          return item.report_type === 'position';
        }),
      });
    })();
  }, [ownershipId]);

  return (
    <>
      {isAccessDenied ? (
        <Container>
          <ErrorPage errorCode={403} />
        </Container>
      ) : (
        <TechnicalReportComparePage
          name={report.charAt(0).toUpperCase() + report.slice(1)}
          setIsAccessDenied={setIsAccessDenied}
          controlParameters={controlParamters?.[report]}
          reportTab={report}
          userEmail={userEmail}
          order={TechnicalReportCompareMapperData[report]}
          dateLabel={
            ['performance', 'monthly', 'quarterly'].includes(report) ? 'report_date' : 'gmt'
          }
        />
      )}
    </>
  );
};

export default TechnicalReportsCompare;
