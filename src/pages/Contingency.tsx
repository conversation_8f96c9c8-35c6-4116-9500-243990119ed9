import React, { useContext, useEffect, useRef, useState } from 'react';
import ShipPartyTable from '../component/contingency/shipPartyTable';
import vesselService from '../service/vessel-service';
import PositionTable from '../component/contingency/positionTable';
import { DetailContext } from '../context/DetailContext';
import moment from 'moment';

const Contingency = ({ ownershipId, vesselId }) => {
  const { roleConfig, ga4EventTrigger = () => {}, handleError = () => {} } = useContext(DetailContext);
  const [parties, setParties] = useState([]);
  const [position, setPosition] = useState([]);
  const tableRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [crewTotalCount, setcrewTotalCount] = useState(0);

  const getVesselShipParty = async () => {
    setLoading(true);
    try {
      const queryParams = `vessel_id=${vesselId}&crew_list_status_date=${moment().format(
        'YYYY-MM-DD',
      )}`;
      const [partiesResp, positionResp, crewList] = await Promise.all([
        vesselService.getVesselShipParty(ownershipId),
        vesselService.getTechnicalReports(
          'position',
          ownershipId,
          {
            pageIndex: 0,
            pageSize: 1,
            sortBy: [{ id: 'gmt', desc: true }],
          },
          '',
        ),
        vesselService.getCrewList(queryParams),
      ]);

      setParties(partiesResp.data.result.results);
      setPosition(positionResp.data.results);
      setcrewTotalCount(crewList?.data?.pagination.totalCount);
    } catch (error) {
      handleError(error.response);
      console.error(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    getVesselShipParty();
  }, []);

  return (
    <div>
      {roleConfig.seafarerGeneralView && <div className="details_page__table_head">
        <div className="row">
          <div className="col">
            <div className="font-weight-bold p-2" data-testid="fml-contingency-header">
              {crewTotalCount} CREWS ABOARD
            </div>
          </div>
        </div>
      </div>}
      <div className="details_page__table_head">
        <div className="row">
          <div className="col">
            <div className="font-weight-bold p-2" data-testid="fml-contingency-header">
              POSITION REPORT
            </div>
          </div>
        </div>
        <PositionTable
          data={position}
          tableRef={tableRef}
          isLoading={loading}
          ownershipId={ownershipId}
          ga4EventTrigger={ga4EventTrigger}
        />
      </div>
      <div className="details_page__table_head">
        <div className="row">
          <div className="col">
            <div className="font-weight-bold p-2" data-testid="fml-contingency-header">
              CONTACT
            </div>
          </div>
        </div>
        <ShipPartyTable
          data={parties}
          tableRef={tableRef}
          isLoading={loading}
          ga4EventTrigger={ga4EventTrigger}
        />
      </div>
    </div>
  );
};

export default Contingency;
