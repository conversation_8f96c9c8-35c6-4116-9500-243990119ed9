import React, { useEffect, useState } from 'react';
import { Container, <PERSON>, Modal, Button, Col, ButtonGroup } from 'react-bootstrap';
import { useHistory, useParams } from 'react-router-dom';
import styleGuide from '../styleGuide';
const { ErrorPage } = styleGuide;
import Spinner from '../component/Spinner';
import { getRecentOwnershipRequestData } from '../service/ownership-service';
import OwnershipRequest from './OwnershipRequest';
import ErrorAlert from '../component/ErrorAlert';
import { checkReviewStatus, getShortDate, sortOnGivenOrder } from '../util/view-utils';
import { Approve } from '../component/approval/Approve';
import { Reject } from '../component/approval/Reject';
import { Rework } from '../component/approval/Rework';
import {
  BUSINESS,
  ACCOUNTS,
  FLEET_PERSONNEL,
  INSURANCE,
  FIRST_APPROVERS_GROUPS,
  FINAL_APPROVER,
  TECH_GROUP,
  approvalStatuses,
} from '../model/constants';
import OwnerShipConfirmation from '../component/ownership/OwnerShipConfirmation';
import PropTypes from 'prop-types';

const OwnershipApproval = (props) => {
  const history = useHistory();
  let { vesselId } = useParams();
  const [requestData, setRequestData] = useState(null);
  const [approvalData, setApprovalData] = useState(null);
  const [error, setError] = useState(null);
  const [actionLoadingFor, setActionLoadingFor] = useState({ rowId: null });
  const [loading, setLoading] = useState(false);
  const [isPageViewInvoked, setPageViewInvoked] = useState(false);
  const [staffData, setStaffData] = useState(null);

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        props.ga4react?.pageview(history.location.pathname, '', 'Change Ownership');
        setPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  const ga4EventTrigger = (action, category, label) => {
    try {
      props.ga4react?.event(action, toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type, value) => {
    if (type === 'buttonClick') {
      ga4EventTrigger(value, 'Vessel Approval', 'Vessel Approval');
    } else {
      ga4EventTrigger('Click', 'Vessel Approval', value);
    }
  };

  useEffect(() => {
    fetchOwnerShipRequestData();
  }, []);

  const fetchOwnerShipRequestData = async () => {
    try {
      if (hasApprovalViewRole()) {
        setLoading(true);
        let { data } = await getRecentOwnershipRequestData(vesselId);
        setRequestData(data);
        if (data?.ownership_change_request?.ownership_change_approval)
          setApprovalData(data.ownership_change_request.ownership_change_approval);
        else setError('Ownership Approval record does not exist for this vessel.');
      }
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
      console.error(`vessel service error for ${vesselId}. Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const ButtonsOnStatus = (props) => {
    switch (props.rowData.approval_status) {
      case approvalStatuses.PENDING:
        return (
          <span>
            <ButtonGroup className="mr-2">
              <Approve
                dataTestId="fml-approval-approve"
                vessel={requestData}
                approvalId={props.rowData.id}
                renderTable={() => fetchOwnerShipRequestData()}
                setLoadingFor={setActionLoadingFor}
                isFinalApprover={props.rowData.final_approver}
                isOwnershipApprovalPending={true}
                eventTracker={eventTracker}
              />
            </ButtonGroup>
            {props.rowData.final_approver ? (
              <ButtonGroup className="mr-2">
                <Reject
                  dataTestId="fml-approval-Reject"
                  vessel={requestData}
                  approvalId={props.rowData.id}
                  renderTable={() => fetchOwnerShipRequestData()}
                  setLoadingFor={setActionLoadingFor}
                  isOwnershipApprovalPending={true}
                  eventTracker={eventTracker}
                />
              </ButtonGroup>
            ) : null}
          </span>
        );
      case approvalStatuses.REJECTED:
        return (
          <span>
            <ButtonGroup className="mr-1">
              <Rework
                vessel={requestData}
                group={props.rowData.department}
                approvalId={props.rowData.id}
                renderTable={() => fetchOwnerShipRequestData()}
                setLoadingFor={setActionLoadingFor}
                isOwnershipApprovalPending={true}
                eventTracker={eventTracker}
              />
            </ButtonGroup>
          </span>
        );
      default:
        return null;
    }
  };

  ButtonsOnStatus.propTypes = {
    rowData: PropTypes.object,
  };

  const TableHeaderRow = (props) => {
    return (
      <tr key="header">
        <th scope="col">{props.approverType}</th>
        <th scope="col">Name</th>
        <th scope="col">Last Update</th>
        <th scope="col">Status</th>
        <th scope="col">Remarks</th>
      </tr>
    );
  };
  TableHeaderRow.propTypes = {
    approverType: PropTypes.string,
  };

  const isFinalApproverApproved = () => {
    if (approvalData && approvalData.length === 0) return true;
    else {
      const finalApprover = approvalData?.find((data) => data?.final_approver);
      return finalApprover && finalApprover.approval_status === approvalStatuses.APPROVED;
    }
  };

  const hasApproverRoleInGroup = (rowData, roleConfig, vesselTechGroup) => {
    const isFinalApproverInFinalRow =
      rowData.department === FINAL_APPROVER && roleConfig.finalApprover;
    const rowGroupInApprovalGroups = roleConfig.approvalGroups.includes(rowData.department);

    if (isFinalApproverInFinalRow) {
      return roleConfig.departments?.includes(FINAL_APPROVER);
    } else
      return rowData.department === TECH_GROUP
        ? rowGroupInApprovalGroups &&
            roleConfig.vessel.approve &&
            roleConfig.techGroups.techGroups.includes(vesselTechGroup)
        : rowGroupInApprovalGroups;
  };

  const getDepartmentForRow = (dataForRole, techgroup) => {
    if (dataForRole.final_approver) return FINAL_APPROVER;
    else {
      return dataForRole.department === TECH_GROUP && techgroup
        ? techgroup
        : dataForRole.department;
    }
  };

  const TableDataRows = ({ techgroup, data, roleConfig, dataTestId }) => {
    return sortOnGivenOrder(data, FIRST_APPROVERS_GROUPS, 'department').map((dataForRole) => {
      const isRowStatusNotPending = dataForRole.approval_status !== approvalStatuses.PENDING;
      const department = getDepartmentForRow(dataForRole, techgroup);
      return (
        <tr key={dataForRole.id} className={dataForRole.approval_status}>
          <th>
            <span>{department}</span>
            {hasApproverRoleInGroup(dataForRole, roleConfig, techgroup) ? (
              <ButtonsOnStatus rowData={dataForRole} />
            ) : null}
          </th>
          <td>{isRowStatusNotPending ? dataForRole.changed_by_user : ''}</td>
          <td>
            {isRowStatusNotPending && dataForRole.updated_at
              ? getShortDate(dataForRole.updated_at)
              : ''}
          </td>
          <td data-testid={`${dataTestId}-status`}>
            {actionLoadingFor.rowId === dataForRole.id ? (
              <Spinner alignClass="align-left" />
            ) : (
              checkReviewStatus(dataForRole)
            )}
          </td>
          <td>
            {isRowStatusNotPending && dataForRole.remarks ? (
              <Remarks remarksData={dataForRole.remarks} />
            ) : (
              ''
            )}
          </td>
        </tr>
      );
    });
  };

  const OwnershipApprovalView = () => {
    return (
      <>
        {approvalData ? (
          <>
            <Row>
              <Col>
                <hr></hr>
                <h6 className="vessel_approval__section-header">2. REVIEW</h6>
                <table className="table" id="test__firstApproval">
                  <thead>
                    <TableHeaderRow approverType="Reviewer" />
                  </thead>
                  <tbody>
                    <TableDataRows
                      techgroup={staffData?.tech_group}
                      data={approvalData.filter((data) => data.final_approver === false)}
                      roleConfig={props.roleConfig}
                      dataTestId="fml-ownership-approval-reviewer"
                    />
                    <tr></tr>
                  </tbody>
                </table>
              </Col>
            </Row>
            <Row>
              <Col>
                <hr></hr>
                <h6 className="vessel_approval__section-header">3. FINAL APPROVAL</h6>
                <table className="table" id="test__finalApproval">
                  <thead>
                    <TableHeaderRow approverType="Approver" />
                  </thead>
                  <tbody>
                    <TableDataRows
                      techgroup={staffData?.tech_group}
                      data={approvalData.filter((data) => data.final_approver === true)}
                      roleConfig={props.roleConfig}
                      dataTestId="fml-ownership-approval-final-approver"
                    />
                    <tr></tr>
                  </tbody>
                </table>
              </Col>
            </Row>

            <OwnerShipConfirmation
              approvalData={approvalData}
              vessel={requestData}
              renderTable={() => fetchOwnerShipRequestData()}
              isFinalApproverApproved={isFinalApproverApproved}
              roleConfig={props.roleConfig}
              eventTracker={eventTracker}
            />
          </>
        ) : null}
      </>
    );
  };

  OwnershipApprovalView.propTypes = {
    roleConfig: PropTypes.string,
  };

  const Remarks = (props) => {
    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    return (
      <>
        <button type="button" className="btn btn-link p-0 text-left" onClick={handleShow}>
          <u>
            {props.remarksData.length > 40
              ? props.remarksData.substring(0, 40).trim().concat('...')
              : props.remarksData}
          </u>
        </button>

        <Modal className="action-modal" show={show} onHide={handleClose} centered>
          <Modal.Header>
            <Modal.Title className="h5">Remarks</Modal.Title>
          </Modal.Header>
          <Modal.Body>{props.remarksData}</Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={handleClose}>
              Close
            </Button>
          </Modal.Footer>
        </Modal>
      </>
    );
  };

  Remarks.propTypes = {
    remarksData: PropTypes.string,
  };

  const renderApprovalView = () => {
    return (
      <div className="ownership_change">
        <Container>
          <Container>
            <div className="vessel_approval">
              <Container>
                {error ? <ErrorAlert message={error} /> : null}
                <OwnershipApprovalView />
              </Container>
            </div>
          </Container>
        </Container>
      </div>
    );
  };

  const hasApprovalViewRole = () => {
    return (
      props.roleConfig.finalApprover ||
      props.roleConfig.approvalGroups.includes(BUSINESS) ||
      props.roleConfig.approvalGroups.includes(ACCOUNTS) ||
      props.roleConfig.approvalGroups.includes(FLEET_PERSONNEL) ||
      props.roleConfig.approvalGroups.includes(INSURANCE) ||
      props.roleConfig.approvalGroups.includes(TECH_GROUP)
    );
  };

  const renderOwnershipRequest = () => {
    if (loading) {
      return <Spinner />;
    } else {
      return (
        <>
          {requestData && (
            <OwnershipRequest
              {...props}
              isFinalApproverApproved={isFinalApproverApproved}
              request={requestData}
              setStaffData={setStaffData}
            />
          )}
          {renderApprovalView()}
        </>
      );
    }
  };

  const renderView = () => {
    return hasApprovalViewRole() ? (
      <>{error ? <ErrorAlert message={error} /> : renderOwnershipRequest()}</>
    ) : (
      <ErrorPage errorCode={403} />
    );
  };

  return renderView();
};

export default React.memo(OwnershipApproval);
