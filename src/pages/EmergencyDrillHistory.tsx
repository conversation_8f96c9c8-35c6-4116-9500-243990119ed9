import moment from 'moment';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Button, Container } from 'react-bootstrap';
import { useParams, useHistory } from 'react-router-dom';
import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import ErrorAlert from '../component/ErrorAlert';
import EmergencyDrillHistoryFilter from '../component/emergencyDrills/EmergencyDrillHistoryFilter';
import vesselService from '../service/vessel-service';
import { ErrorPage, Icon } from '../styleGuide';
import { exportTableToExcel } from '../util/excel-export';
import { QUERY_TYPE_LIKE, QUERY_TYPE_RANGE } from '../util/search-types';
import { formatDate, formatValue } from '../util/view-utils';
import getURLParams from '../util/getURLParams';
import { DetailContext } from '../context/DetailContext';
import { useIsMount } from '../util/useIsMount';
import CustomTable from '../component/customComponent/CustomTable';

const { PARIS2_URL } = process.env;

const SearchTypes = [
  {
    type: 'last_done_date',
    name: 'Last Done Date',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'drill_date',
  },
  {
    type: 'name',
    name: 'Drills',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'name',
  },
  {
    type: 'last_due_date',
    name: 'Last Due Date',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'orig_due_date',
  },
  {
    type: 'next_due_date',
    name: 'Next Due Date',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'next_due_date',
  },
];

const EmergencyDrillHistoryPage = () => {
  const { ownershipId } = useParams();
  const [isAccessDenied, setIsAccessDenied] = useState(false);
  const history = useHistory();
  const drillId = getURLParams('drill_id', history.location.search);
  const [filterData, setFilterData] = useState({
    startDate: moment().add(-1, 'years').format('YYYY-MM-DD'),
    endDate: formatDate(new Date(), 'YYYY-MM-DD', ''),
  });
  const [error, setError] = useState(null);
  const [emergencyDrillHistory, setEmergencyDrillHistory] = useState([]);
  const [emergencyDrills, setEmergencyDrills] = useState([]);
  const [pageCount, setPageCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [getDrillsLoading, setGetDrillsLoading] = useState(true);
  const [totalCount, settotalCount] = useState(0);
  const [sortState, setSortState] = useState([{ id: 'name', desc: false }]);
  const [page_size, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const { vesselName, ga4EventTrigger = () => {} } = useContext(DetailContext);
  const isMount = useIsMount();

  const breadCrumbsItems = useMemo(
    () => [
      { title: 'Vessel', label: 'To List Page', link: `${PARIS2_URL}/vessel` },
      {
        title: (vesselName || emergencyDrillHistory[0]?.vessel_name) ?? '- - -',
        label: 'Details',
        link: `${PARIS2_URL}/vessel/ownership/details/${ownershipId}/emergency-drills`,
      },
      { title: 'Emergency Drills History', label: 'Emergency Drills History', link: '#' },
    ],
    [ownershipId, emergencyDrillHistory],
  );

  const columns = useMemo(
    () => [
      {
        Header: 'No.',
        accessor: (row) => formatValue(row.id),
        id: 'id',
        name: 'id',
        type: 'text',
        maxWidth: 30,
        disableSortBy: true,
      },
      {
        Header: 'Last Done Date',
        accessor: (row) => formatDate(row.drill_date),
        id: 'last_done_date',
        name: 'drill_date',
        type: 'date',
        maxWidth: 40,
      },
      {
        Header: 'Drills',
        id: 'drills',
        type: 'name',
        name: 'name',
        headerClassName: 'text-center justify-content-center ',
        maxWidth: 40,
        accessor: (row) => (
          <div className="text-center">
            <Button
              variant="link"
              className="button-link"
              onClick={(e) => {
                ga4EventTrigger(
                  'Assigned Drill Link',
                  'Drill History - Menu',
                  e.target.textContent,
                );
                const drill = emergencyDrills.find((item) => item.value === e.target.textContent);
                setFilterData({ ...filterData, drill: [drill] });
                setPageIndex(0);
              }}
            >
              {formatValue(row.name)}
            </Button>
          </div>
        ),
      },
      {
        Header: 'Description',
        accessor: (row) => row.description,
        id: 'description',
        name: 'description',
        type: 'text',
        maxWidth: 80,
        disableSortBy: true,
      },
      {
        Header: 'Last Due Date',
        accessor: (row) => formatDate(row.orig_due_date),
        id: 'last_due_date',
        name: 'orig_due_date',
        type: 'date',
        maxWidth: 40,
      },
      {
        Header: 'Next Due Date',
        accessor: (row) => formatDate(row.next_due_date),
        id: 'next_due_date',
        name: 'next_due_date',
        type: 'date',
        maxWidth: 30,
      },
      {
        Header: 'Remarks',
        accessor: (row) => row.remark,
        id: 'remarks',
        name: 'remark',
        type: 'text',
        maxWidth: 80,
        disableSortBy: true,
      },
    ],
    [emergencyDrills, setFilterData],
  );

  const DrillButtons = useMemo(() => {
    return (
      <div className="no-print">
        <Button
          size="sm"
          variant="outline-primary"
          data-testid="fml-emergencyDrillHistory-exportToExcel"
          onClick={() => {
            ga4EventTrigger(
              'Export to Excel',
              'Drill History - Menu',
              emergencyDrillHistory[0]?.vessel_name || vesselName,
            );
            exportTableToExcel(
              {
                drill_history: { jsonData: emergencyDrillHistory, columns: columns },
              },
              emergencyDrillHistory[0]?.vessel_name &&
                `Vessel Name-${emergencyDrillHistory[0]?.vessel_name}`,
            );
          }}
          className="mr-2"
        >
          Export to Excel
        </Button>
        <Button
          size="sm"
          onClick={() => {
            ga4EventTrigger(
              'Print',
              'Drill History - Menu',
              emergencyDrillHistory[0]?.vessel_name || vesselName,
            );
            window.print();
          }}
          data-testid="fml-emergencyDrillHistory-print"
          variant="outline-primary"
          className="mr-2"
        >
          Print
        </Button>
        <Icon
          icon="close"
          size={30}
          onClick={() => history.push(`/vessel/ownership/details/${ownershipId}/emergency-drills`)}
        />
      </div>
    );
  }, [emergencyDrillHistory]);

  const getSortByParams = () => {
    const searchId = SearchTypes.filter(
      (type) => columns.find((item) => item.id === sortState[0].id)?.Header == type.name,
    )
      .map((type) => type.queryKey)
      .toString();
    const formObj = [
      {
        id: searchId,
        desc: sortState[0].desc,
      },
    ];
    return formObj;
  };

  const formatData = (drillHistory) => {
    return drillHistory.map((data) => ({
      ...data,
      period: `${data.interval} ${data.interval_unit}`,
    }));
  };

  useEffect(() => {
    (async () => {
      try {
        const response = await vesselService.getEmergencyDrillList([]);
        const data = response.data.map((drill) => {
          return { id: drill.id, value: formatValue(drill.name) };
        });
        if (drillId) {
          const drill = data.find((item) => item.id === Number(drillId));
          setFilterData({ ...filterData, drill: [drill] });
        }
        setEmergencyDrills(data);
      } catch (error) {
        if (error.message.includes('403')) {
          setIsAccessDenied(true);
        } else setError('Oops, something went wrong. Please try again.');
        console.error(`Get drills failed. Error: ${error}`);
      }
      setGetDrillsLoading(false);
    })();
  }, [drillId]);

  useEffect(() => {
    (async (paginationParams) => {
      try {
        setLoading(true);
        setEmergencyDrillHistory([]);
        if (!getDrillsLoading) {
          let sortByParam = [];
          if (sortState.length) {
            sortByParam = getSortByParams(paginationParams);
          }
          let pageFilterData = '';
          if (filterData.startDate || filterData.endDate) {
            pageFilterData = `drill_date=${filterData.startDate || ''},${filterData.endDate || ''}`;
          }
          if (filterData.drill?.length > 0) {
            pageFilterData += `&drill_id=${filterData.drill[0].id}`;
            history.replace({
              pathname: history.location.pathname,
              search: `drill_id=${filterData.drill[0].id}`,
            });
          } else {
            history.replace({
              pathname: history.location.pathname,
              search: '',
            });
          }
          const response = await vesselService.getDrillHistory(
            ownershipId,
            { pageSize: page_size, sortBy: sortByParam, pageIndex: pageIndex },
            pageFilterData,
          );
          setEmergencyDrillHistory(formatData(response.data.results));
          const total = response.data.total;
          setPageCount(Math.ceil(total / page_size));
          settotalCount(total);
        }
      } catch (error) {
        if (error.message.includes('403')) {
          setIsAccessDenied(true);
        } else setError('Oops, something went wrong. Please try again.');
        console.error(`Get drill history by ownership ID: ${ownershipId} failed. Error: ${error}`);
      }
      if (!getDrillsLoading) setLoading(false);
    })();
  }, [getDrillsLoading, filterData, page_size, pageIndex, sortState]);

  useEffect(() => {
    !isMount && ga4EventTrigger('Sorting', 'Drill History - Menu', sortState[0]?.id);
  }, [sortState]);

  return (
    <Container>
      <div className="d-flex  justify-content-between">
        <BreadcrumbHeader
          items={breadCrumbsItems}
          activeItem="Emergency Drills History"
          onClick={() =>
            ga4EventTrigger(
              'Breadcrumb',
              'Drill History - Menu',
              vesselName || emergencyDrillHistory[0]?.vessel_name,
            )
          }
        />
        {DrillButtons}
      </div>
      <EmergencyDrillHistoryFilter
        drillDropdownData={emergencyDrills}
        filterData={filterData}
        setFilterData={setFilterData}
        setPageIndex={setPageIndex}
        loading={loading}
        ga4EventTrigger={ga4EventTrigger}
      />
      <>
        {error && <ErrorAlert message={error} />}
        {isAccessDenied ? (
          <ErrorPage errorCode={403} />
        ) : (
          <CustomTable
            column={columns}
            reportData={emergencyDrillHistory}
            tableRef={null}
            isLoading={loading}
            pageCount={pageCount}
            totalCount={totalCount}
            setPageNo={setPageIndex}
            setPageListSize={setPageSize}
            pagination={true}
            setSortData={setSortState}
            pageNo={pageIndex}
            pageSize={page_size}
          />
        )}
      </>
    </Container>
  );
};

export default EmergencyDrillHistoryPage;
