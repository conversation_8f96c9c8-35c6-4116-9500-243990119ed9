import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import ItineraryTable, { columns } from '../component/ItineraryList/ItineraryTable';
import vesselService from '../service/vessel-service';
import { getPageSort, resetAllTabs, setLocalStorage } from '../util/local-storage-helper';
import { Button, ButtonGroup, Col, Container, Dropdown, Form, Row, Tab } from 'react-bootstrap';
import ItineraryAdvancedSearch from '../component/ItineraryList/ItineraryAdvancedSearch';
import { useDebounce, useDebouncedCallback } from 'use-debounce';
import { useHistory } from 'react-router-dom';
import itinerarySearchTypes from '../constants/itinerary-search-types';
import { getPortsByCountry } from '../service/reference-service';
import { modifyJSONData } from '../constants/itinerary-search-query';
import { formatDate } from '../util/view-utils';
import { LOCAL_STORAGE_FIELDS } from '../model/constants';
import { getVesselQuery } from '../controller/itinerary-controller';
import { DetailContext } from '../context/DetailContext';

const VesselItineraryList = ({ ownershipId }) => {
  const {
    showFutureItinerary,
    ga4EventTrigger = () => {},
    handleError = () => {},
  } = useContext(DetailContext);
  const [itinerary, setItinerary] = useState([]);
  const tableRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [pageCount, setPageCount] = useState(0);
  const [totalItineraryCount, settotalItineraryCount] = useState(0);
  const [sortState] = useState(getPageSort(LOCAL_STORAGE_FIELDS.itineraryKey));
  const history = useHistory();
  const [searchedKeyword, setSearchedKeyword] = useState('');
  const [filters, setFilters] = useState([]);
  const [query, setQuery] = useState('');
  const [debouncedFilters] = useDebounce(filters, 700);

  useEffect(() => {
    if (debouncedFilters.length) {
      resetAllTabs();
      let filteredList = removeEmptyFilters(debouncedFilters);
      if (filteredList.length === 0 && query.length > 0) {
        setQuery('');
      } else {
        const filterQuery = getVesselQuery(filteredList);
        setQuery(filterQuery);
      }
    }
  }, [debouncedFilters]);

  const removeEmptyFilters = (filter) => {
    return filter.filter(
      (item) => item.subtype !== null && item.subtype !== undefined && item.subtype !== '',
    );
  };

  const clearAdvancedFilters = () => {
    setFilters([]);
    resetAllTabs();
    window.history.replaceState({}, null, `${history.location.pathname}`);
    setLoading(false);
  };

  const visitSearch = () => {
    history.push(`/vessel/search?${query}`);
  };

  const getSortByParams = (paginationParams) => {
    let sortByObj = [...paginationParams.sortBy];
    const item = columns
      .filter((item) => item.id === sortByObj[0].id)
      .map((item) => item.Header)
      .toString();
    let searchId = itinerarySearchTypes
      .filter((type) => type.name === item)
      .map((type) => type.queryKey)
      .toString();
    const formObj = [
      {
        id: searchId,
        desc: sortByObj[0].desc,
      },
    ];
    paginationParams = { ...paginationParams, sortBy: formObj };
    return paginationParams;
  };

  const handleKeywordChange = useDebouncedCallback((value) => {
    //Set page number as 0 for all tabs in local store
    ga4EventTrigger('Keyword', 'Vessel Itinerary Keyword Search', value);
    resetAllTabs();
    setSearchedKeyword(value.trim().toLowerCase());
  }, 700);

  const fetchData = useCallback(
    async (paginationParams) => {
      setLoading(true);
      let queryParam = query;
      if (showFutureItinerary) {
        queryParam += `&estimated_departure=${formatDate(new Date(), 'YYYY-MM-DD')},`;
      }
      try {
        setItinerary([]);
        if (paginationParams.sortBy?.length) {
          paginationParams = getSortByParams(paginationParams);
        }
        const response = await vesselService.getItinerary(
          ownershipId,
          paginationParams,
          queryParam,
          searchedKeyword,
        );
        setItinerary(response.data.results);
        setLocalStorage(LOCAL_STORAGE_FIELDS.itineraryDataKey, response.data.results);
        const totalCount = response.data.total;
        settotalItineraryCount(totalCount);
        const pageSize = paginationParams.pageSize;
        setPageCount(Math.ceil(totalCount / pageSize));
        setLoading(false);
      } catch (error) {
        handleError(error.response);
        setLoading(false);
        console.log('get vessel failed', error);
      }
    },
    [query, searchedKeyword, showFutureItinerary],
  );

  const loadPortDropdown = async (id) => {
    const { data } = await getPortsByCountry(
      id.map((countryCode) => `countryCode=${countryCode}`).join('&'),
    );
    return modifyJSONData(data).ports;
  };

  // To Do- Create smaller subcomponents
  return (
    <Container>
      <Tab.Container>
        <Row className="no-print itinerary-filter">
          <Col xs={6} md={3}>
            <div className="mt-4 quick-filters-button">
              <Form.Control
                id="search-bar"
                type="text"
                name="keyword"
                placeholder="Type keywords to filter"
                data-testid="fml-itinerary-list-keyword-search"
                onClick={() =>
                  ga4EventTrigger('Click', 'Vessel Itinerary Keyword Search', 'Vessel Itinerary')
                }
                onChange={(e) => handleKeywordChange(e.target.value)}
              />
            </div>
          </Col>
          <Col xs={6} md="auto">
            <div className="mobile">
              <ButtonGroup className="advanced-search mt-4 w-100">
                <Button variant="outline-primary" onClick={visitSearch}>
                  Advanced Search
                </Button>
              </ButtonGroup>
            </div>
            <div className="desktop">
              <ButtonGroup className="advanced-search mt-4">
                <Dropdown
                  alignRight={false}
                  onToggle={(isOpen) =>
                    isOpen &&
                    ga4EventTrigger('Click', 'Vessel Itinerary Advance Search', 'Vessel Itinerary')
                  }
                >
                  <Dropdown.Toggle
                    variant="outline-primary"
                    data-testid="fml-itinerary-list-advance-search"
                    id="dropdown-advanced-search"
                  >
                    Advanced Search{' '}
                    {removeEmptyFilters(debouncedFilters).length
                      ? `(${removeEmptyFilters(debouncedFilters).length})`
                      : ''}
                  </Dropdown.Toggle>
                  <Dropdown.Menu>
                    <div className="advanced-search-menu">
                      {ItineraryAdvancedSearch({
                        filters: filters,
                        setFilters: setFilters,
                        loadPortDropdown: loadPortDropdown,
                        ga4EventTrigger: ga4EventTrigger,
                      })}
                    </div>
                  </Dropdown.Menu>
                </Dropdown>
              </ButtonGroup>
            </div>
          </Col>
          <Col xs="auto">
            {removeEmptyFilters(debouncedFilters).length > 0 && (
              <Button variant="outline-primary" className="mt-4" onClick={clearAdvancedFilters}>
                Clear All
              </Button>
            )}
          </Col>
        </Row>
        <Row>
          <Col>
            <ItineraryTable
              itinerary={itinerary}
              fetchData={fetchData}
              tableRef={tableRef}
              isLoading={loading}
              pageCount={pageCount}
              query={query}
              initSort={sortState}
              keyword={searchedKeyword}
              showFutureItinerary={showFutureItinerary}
              totalItineraryCount={totalItineraryCount}
              ga4EventTrigger={ga4EventTrigger}
            />
          </Col>
        </Row>
      </Tab.Container>
    </Container>
  );
};

export default VesselItineraryList;
