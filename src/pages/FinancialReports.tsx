import React from 'react';
import { Route, Switch, Redirect, useRouteMatch } from 'react-router-dom';
import FinancialList from '../component/FinancialReports/FinancialList';
import Recipients from '../component/FinancialReports/ListReports/Recipients';

const FinancialReports = () => {
  let match = useRouteMatch();
  return (
    <Switch>
      <Route exact path={match.path}>
        <Redirect to={`${match.path}/accounts`} />
      </Route>
      <Route path={`${match.path}/recipients`}>
        <Recipients />
      </Route>
      <Route path={`${match.path}/:tab`}>
        <FinancialList />
      </Route>
    </Switch>
  );
};

export default FinancialReports;
