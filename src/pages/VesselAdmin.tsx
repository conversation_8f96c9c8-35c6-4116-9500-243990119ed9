import React, { useContext, useState } from 'react';
import { Col, Row, Tab, Form, Container } from 'react-bootstrap';
import { useParams, useHistory } from 'react-router-dom';
import TabWrapper from '../component/TabWrapper';
import EmergencyDrillList from '../component/admin/EmergencyDrillList';
import CertificatesList from '../component/admin/certificates/CertificatesList';
import { AdminContext } from '../context/AdminContext';
import { ErrorPage } from '../styleGuide';

const adminPageTabData = (roleConfig) => [
  {
    eventKey: 'emergency-drills',
    tabName: 'Emergency Drills',
    hideTab: !roleConfig.admin.drills.view,
  },
  {
    eventKey: 'certificates',
    tabName: 'Surveys and Certificates',
  },
];

const VesselAdmin = () => {
  const history = useHistory();
  const { tab = 'certificates' } = useParams();
  const [activeTab, setActiveTab] = useState(tab);
  const { roleConfig, ga4EventTrigger = () => {} } = useContext(AdminContext);

  const handleTabSelect = (key) => {
    if (key !== activeTab) {
      ga4EventTrigger('Switch Tab', 'Vessel Admin - Menu', key);
      setActiveTab('');
      setActiveTab(key);
    }
    return history.push(`/vessel/admin/${key}`);
  };

  return roleConfig.admin.view ? (
    <Container>
      <Tab.Container activeKey={activeTab} defaultActiveKey="all">
        <Form.Label className="technical-reports pt-2 pb-2">
          <b>Admin</b>
        </Form.Label>

        <Row className="no-print tab-wrapper">
          <Col>
            <TabWrapper
              handleTabSelect={handleTabSelect}
              data={adminPageTabData(roleConfig)}
              step={tab}
              setActiveTab={setActiveTab}
              activeTab={activeTab}
            />
          </Col>
        </Row>

        {roleConfig.admin.drills.view && tab === 'emergency-drills' && <EmergencyDrillList />}
        {roleConfig.admin.certificates.manage && tab === 'certificates' && <CertificatesList />}
      </Tab.Container>
    </Container>
  ) : (
    <ErrorPage errorCode={404} />
  );
};

export default VesselAdmin;
