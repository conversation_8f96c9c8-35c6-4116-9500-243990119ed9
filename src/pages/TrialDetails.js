import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Container, Row, Col } from 'react-bootstrap';
import { useHistory, useParams } from 'react-router-dom';
import Spinner from '../component/Spinner';
import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import { MaximumContinuousRatingEngineData } from '../model/TrialData';
import { getOwnershipVessel } from '../service/vessel-service';
import { Icon } from '../styleGuide';
import './styles/trial-details.scss';
import { ResponsiveTable, SeaTrialContainer, TrialContainer } from '../component/TrialTable';
import { buildSeaTrialDataByDate } from '../util/trial-data-utils';
import PropTypes from 'prop-types';

const { PARIS2_URL } = process.env;

const RenderTableRow = (props) => {
  return props.data.map((key, index) => {
    return (
      <tr key={index}>
        <td className="details_page__row-name">{key.label}</td>
        <td className="details_page__row-value">
          {key.value}
          <span> {key.unit}</span>
        </td>
      </tr>
    );
  });
};

const EditedLabel = ({ action, name, date }) => {
  let formattedDate = '';

  if (date) {
    formattedDate = moment(date).format('DD MMM YYYY');
  }

  let labelStr;
  if (name) {
    labelStr = `${action} by ${name} on ${formattedDate}`;
  } else {
    labelStr = `${action} on ${formattedDate}`;
  }

  return <p className="details_page__edited_label">{labelStr}</p>;
};

EditedLabel.propTypes = {
  action: PropTypes.string,
  name: PropTypes.string,
  date: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
};

const TableSection = (props) => {
  return (
    <Row>
      <Col>
        <table className="table table-hover">
          <thead className="details_page__table_head">
            <tr>
              <th id={props.id} colSpan="2">
                {props.title}
              </th>
            </tr>
          </thead>
          <tbody>
            <RenderTableRow data={props.data} />
          </tbody>
        </table>
      </Col>
    </Row>
  );
};

TableSection.propTypes = {
  id: PropTypes.string,
  title: PropTypes.string,
  data: PropTypes.object,
};

const renderFloat = (num) => {
  return isNaN(parseFloat(num)) ? '- - -' : parseFloat(num).toString();
};

const TrialDetails = ({ roleConfig }) => {
  const history = useHistory();
  const { ownershipId } = useParams();
  const [isLoading, setLoading] = useState(false);
  const [trialData, setTrialData] = useState({
    vessel_name: null,
    maximum_continuous_rating_kw: null,
    maximum_continuous_rating_rpm: null,
    shop_trials: [],
    sea_trials: [],
    updated_by: null,
    updated_at: null,
    vessel_id: null,
  });

  const getTrialDetails = useCallback(async () => {
    try {
      setLoading(true);
      const { data } = await getOwnershipVessel(ownershipId);
      const { vessel, updated_at } = data;
      const {
        name: vessel_name,
        maximum_continuous_rating_kw,
        maximum_continuous_rating_rpm,
        shop_trials,
        sea_trials,
        updated_by_user_info,
        id: vessel_id,
      } = vessel;
      const { full_name: updated_by } = updated_by_user_info;
      setTrialData({
        vessel_name,
        maximum_continuous_rating_kw,
        maximum_continuous_rating_rpm,
        shop_trials,
        sea_trials,
        updated_at,
        updated_by,
        vessel_id,
      });
      setLoading(false);
    } catch (error) {
      console.log('error:', error);
      setLoading(false);
    }
  }, [ownershipId]);

  useEffect(() => {
    getTrialDetails();
  }, [getTrialDetails]);

  const breadCrumbsItems = useMemo(
    () => [
      { title: 'Vessel', link: `${PARIS2_URL}/vessel` },
      {
        title: trialData.vessel_name ?? '- - -',
        link: `${PARIS2_URL}/vessel/ownership/details/${ownershipId}`,
      },
      { title: 'Trial Data', link: `#` },
    ],
    [ownershipId, trialData],
  );

  const { maximum_continuous_rating_kw, maximum_continuous_rating_rpm, shop_trials, sea_trials } =
    trialData;

  const handleEditTrial = useCallback(
    () => history.push(`/vessel/${trialData.vessel_id}/${ownershipId}/takeover/trial-data`),
    [trialData, ownershipId],
  );

  const goBack = () => history.goBack();

  const mcrData = useMemo(
    () =>
      MaximumContinuousRatingEngineData({
        maximum_continuous_rating_kw,
        maximum_continuous_rating_rpm,
      }),
    [maximum_continuous_rating_kw, maximum_continuous_rating_rpm],
  );

  const seaTrialDataFormatted = useMemo(() => buildSeaTrialDataByDate(sea_trials), [sea_trials]);

  return (
    <Container>
      <div className="flex-between">
        <BreadcrumbHeader items={breadCrumbsItems} activeItem="Trial Data" />
        <div className="flex-between trial-detail-right-header">
          {roleConfig.vessel.edit && (
            <Button variant="outline-primary" onClick={handleEditTrial}>
              Edit
            </Button>
          )}
          <div style={{ cursor: 'pointer', position: 'absolute', right: 15 }}>
            <Icon icon="close" size={25} onClick={goBack} />
          </div>
        </div>
      </div>
      {isLoading ? (
        <Spinner />
      ) : (
        <Row>
          <Col md={4}>
            <TableSection
              id="mcr"
              title="Maximum Continuous Rating (MCR) of Engine"
              data={mcrData}
            />
            <Row>
              <EditedLabel
                action="Updated"
                name={trialData.updated_by}
                date={trialData.updated_at}
              />
            </Row>
          </Col>
          <Col md={4}>
            <Row>
              <Col>
                <TrialContainer
                  title="Shop Trial Data"
                  body={
                    <ResponsiveTable
                      columns={['#', 'Load', 'Corrected SFOC']}
                      tableBody={shop_trials.map(({ id, load, corrected_sfoc }, index) => (
                        <tr key={id}>
                          <td className="table-row">{index + 1}</td>
                          <td className="table-row">{renderFloat(load)}</td>
                          <td className="table-row">{renderFloat(corrected_sfoc)}</td>
                        </tr>
                      ))}
                    />
                  }
                />
              </Col>
            </Row>
          </Col>
          <Col md={4}>
            <Row>
              <Col>
                <TrialContainer title="Sea Trial Data" />
                {Object.entries(seaTrialDataFormatted).map(([key, value]) => {
                  return (
                    <SeaTrialContainer
                      key={key}
                      title={`Created on ${key}`}
                      body={
                        <ResponsiveTable
                          columns={['#', 'Load', 'Corrected SFOC', 'KW', 'RPM', 'Speed', 'Tons/Hr']}
                          tableBody={value.map(
                            (
                              { id, load, corrected_sfoc, kw, rpm, speed, tons_per_hour },
                              index,
                            ) => (
                              <tr key={id}>
                                <td className="table-row">{index + 1}</td>
                                <td className="table-row">{renderFloat(load)}</td>
                                <td className="table-row">{renderFloat(corrected_sfoc)}</td>
                                <td className="table-row">{renderFloat(kw)}</td>
                                <td className="table-row">{renderFloat(rpm)}</td>
                                <td className="table-row">{renderFloat(speed)}</td>
                                <td className="table-row">{renderFloat(tons_per_hour)}</td>
                              </tr>
                            ),
                          )}
                        />
                      }
                    />
                  );
                })}
              </Col>
            </Row>
          </Col>
        </Row>
      )}
    </Container>
  );
};

TrialDetails.propTypes = {
  roleConfig: PropTypes.object,
};

export { TrialDetails };
