import React, { useEffect, useState, useContext } from 'react';
import { Container } from 'react-bootstrap';
import { useParams } from 'react-router-dom';
import { ErrorPage } from '../styleGuide';
import vesselService from '../service/vessel-service';
import TechnicalReportComparePage from '../component/TechnicalReports/CompareReports/TechnicalReportComparePage';
import { TechnicalReportCompareMapperData } from '../model/TechnicalReportCompareMapperData';
import { EnvironmentalReportContext } from '../context/EnvironmentalReportContext';

const EnvironmentalReportsCompare = () => {
  const { report, ownershipId } = useParams();
  const [controlParamters, setContolParameters] = useState();
  const [isAccessDenied, setIsAccessDenied] = useState(false);
  const { userEmail } = useContext(EnvironmentalReportContext);

  useEffect(() => {
    (async () => {
      if(['ninety-six-hours'].includes(report))return
      const { data } = await vesselService.getContolParameters(ownershipId);
      setContolParameters({
        marpol: data?.result.filter((item) => {
          return item.report_type === 'marpol';
        }),
      });
    })();
  }, [ownershipId]);
  return (
    <>
      {isAccessDenied ? (
        <Container>
          <ErrorPage errorCode={403} />
        </Container>
      ) : (
        <TechnicalReportComparePage
          name={report.charAt(0).toUpperCase() + report.slice(1)}
          setIsAccessDenied={setIsAccessDenied}
          controlParameters={controlParamters?.[report]}
          reportTab={report}
          userEmail={userEmail}
          order={TechnicalReportCompareMapperData[report]}
          dateLabel={'report_date'}
        />
      )}
    </>
  );
};

export default EnvironmentalReportsCompare;
