import SealManagement from '../component/EnvironmentalReports/SealManagement';
import React, { useContext } from 'react';
import { Route, Switch, Redirect, useRouteMatch } from 'react-router-dom';
import MonthlyMarpolControlParameters from '../component/EnvironmentalReports/ControlParameters/MonthlyMarpolControlParameters';
import EnvironmentalReportsList from '../component/EnvironmentalReports/EnvironmentalReportsList';
import EnvironmentalReportsCompare from './EnvironmentalReportsCompare';
import { EnvironmentalReportContext } from '../context/EnvironmentalReportContext';
import { ErrorPage } from '../styleGuide';

const EnvironmentalReports = () => {
  let match = useRouteMatch();
  const { roleConfig } = useContext(EnvironmentalReportContext);
  return roleConfig.vessel.view ? (
    <Switch>
      <Route exact path={match.path}>
        <Redirect to={`${match.path}/marpol`} />
      </Route>
      <Route exact path={`${match.path}/:report/:ownershipId/compare`}>
        <EnvironmentalReportsCompare />
      </Route>
      <Route path={`${match.path}/seals/:ownershipId/:tab`}>
        <SealManagement />
      </Route>
      <Route exact path={`${match.path}/control-parameter`}>
        {roleConfig.params.view ? (
          <MonthlyMarpolControlParameters />
        ) : (
          <ErrorPage errorCode={403} />
        )}
      </Route>
      <Route path={`${match.path}/:tab`}>
        <EnvironmentalReportsList />
      </Route>
    </Switch>
  ) : (
    <ErrorPage errorCode={403} />
  );
};

export default EnvironmentalReports;
