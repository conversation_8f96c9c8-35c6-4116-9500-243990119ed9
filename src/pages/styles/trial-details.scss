.bread-crump-wrapper ol {
  background-color: #fff !important;
}

.flex-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.trial-detail-right-header {
  min-width: 6.5rem;
  flex-direction: row !important;
}

.breadcrumb-text {
  font: normal normal normal 24px/29px Inter,sans-serif;
  letter-spacing: 0px;
  color: #1F4A70 !important;
}

.inactive-breadcrumb-text a {
  @extend .breadcrumb-text;
  text-decoration: underline !important;
}

.breadcrumb-item+.breadcrumb-item::before {
  display: inline-block;
  padding-right: .5rem;
  color: #1F4A70 !important;
  content: "/";
}

.trial-row-container {
  border-top: 4px solid #1f4a70;
  padding-top: 0.8rem;

  .title {
    color: #1f4a70;
    font-size: 16px;
    font-weight: 600;
  }
}

.table-header {
  font: normal normal bold 14px/17px Inter,sans-serif;
  letter-spacing: 0px;
  color: #343A40;
  opacity: 1;
}

.table-row {
  font: normal normal normal 14px/40px Inter,sans-serif;
  letter-spacing: 0px;
  color: #343A40;
  opacity: 1;
}

.sea-trial-date {
  color: #1f4a70;
  font-size: 16px;
  font-weight: 600;
}

@media only screen and (max-width: 768px) {
  .flex-between {
    flex-direction: column-reverse;
  }
}