.filter-container {
  background-color: #f8f9fa;
  height: 100px;
  padding: 20px;
}

.filter-header {
  color: #1e4a70;
  font-size: 17px;
}

.filter-text {
  color: rgb(78, 74, 74);
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-left: 10px;
}

.comments-text {
  fill: #1e4a70;
  color: #1e4a70;

  &__disable {
    cursor: text;
    color: #cbced1;
    fill: #cbced1;
  }
}

.comments-text-gap1 {
  gap: 4px;
}

.report-status-tag {
  border-radius: 3px;
  padding: 2px 10px;
  width: max-content;

  &__blue {
    background-color: #d2e6fc;
    color: #2d8cff;
  }

  &__grey {
    background-color: #efefef;
    color: #6c757d;
  }

  &__yellow {
    background-color: #fff9e8;
    color: #c99a07;
  }

  &__green {
    background-color: #e9f6ec;
    color: #57bb70;
  }

  &__peach {
    background-color: #f5ced1;
    color: #964b49;
  }

  &__orange {
    background-color: #f3f6f8;
    color: #1f4a70;
  }
  &__red {
    background-color: #faf2f5;
    color: #c82333;
  }
}

.ofr-disabled-pointer {
  cursor: not-allowed !important;
  color: gray;
}

.ofr-actions-icon {
  text-align: left;
  max-width: 80px;
  min-width: 80px;
}

.ofr-actions-list {
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;

  li {
    cursor: pointer;
    margin: 10px;
  }
}

.actions-column-test {
  width: 100px;
  max-width: 100px;
  min-width: 100px;
}

.calander-year-header {
  display: flex;
  font-size: 14px;
  color: #1e4a70;
  margin-left: 15px;
}

.react-date-picker {
  &__wrapper {
    background-color: white;
    color: rgb(78, 74, 74);
    margin-left: 10px;

    .react-datepicker {
      padding: 8px 10px;
      border: none;
      box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;

      &__triangle,
      &__navigation {
        display: none;
      }

      &__header {
        background-color: inherit;
        border: none;
      }

      &__year-wrapper {
        display: flex;
        flex-wrap: wrap;
        max-width: 210px;
        font-size: 14px;
      }

      &__year-text {
        padding: 5px;
      }

      &__input-container {
        input {
          width: 70px;
          border-width: 2px;
          border-style: solid;
          border-color: #1e4a70;
        }
      }
    }
  }
}
