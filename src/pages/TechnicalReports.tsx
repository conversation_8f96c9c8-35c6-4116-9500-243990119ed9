import React, { useContext } from 'react';
import { Route, Switch, Redirect, useRouteMatch } from 'react-router-dom';
import ControlParameters from '../component/controlParameters/ControlParameter';
import TechnicalReportsList from '../component/TechnicalReports/TechnicalReportsList';
import VoyageMovement from '../component/TechnicalReports/VoyageMovement';
import TechnicalReportsCompare from './TechnicalReportsCompare';
import { TechnicalReportContext } from '../context/TechnicalReportContext';
import { ErrorPage } from '../styleGuide';

const TechnicalReports = (props) => {
  let match = useRouteMatch();
  const { roleConfig } = useContext(TechnicalReportContext);
  return roleConfig.vessel.view ? (
    <Switch>
      <Route exact path={match.path}>
        <Redirect to={`${match.path}/position`} />
      </Route>
      <Route exact path={`${match.path}/:report/:ownershipId/compare`}>
        <TechnicalReportsCompare />
      </Route>
      <Route exact path={`${match.path}/voyage-movement`}>
        <VoyageMovement />
      </Route>
      <Route exact path={`${match.path}/control-parameter`}>
        <ControlParameters />
      </Route>
      <Route path={`${match.path}/:tab`}>
        <TechnicalReportsList />
      </Route>
    </Switch>
  ) : (
    <ErrorPage errorCode={403} />
  );
};

export default TechnicalReports;
