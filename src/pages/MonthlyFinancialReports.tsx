import React, { useContext, useEffect, useState } from 'react';
import moment from 'moment';
import ownerService from '../service/owner-service';
import MonthlyFinancialReportTable from '../component/MonthlyFinancialReportTable/MonthlyFinancialReportTable';
import ReopenReportModal from '../component/ReopenReportModal';
import './styles/monthly-financial.scss';
import { DetailContext } from '../context/DetailContext';
import CustomDatePicker from '../component/customComponent/CustomDatePicker';
import { default as GenerateReportModal } from '../component/GenerateReportModal.tsx';
import {
  GENERATE_REPORT_BODY_TEXT,
  DELETE_REPORT_BODY_TEXT,
} from '../constants/montlyFinancialReprtStatus';
import CustomOverlayLoader from '../component/customComponent/CustomOverlayLoader';

const MonthlyFinancialReports = ({ vesselOwnership, vesselId, disableReopen = false }) => {
  const {
    vessel_account_code_new: vesselCode = '',
    fleet_staff: { primary_accountant, secondary_accountant } = {
      primary_accountant: '',
      secondary_accountant: '',
    },
    owner: { ship_party_id = null },
    owner_end_date,
    name: vesselName,
  } = vesselOwnership;
  const {
    handleError = () => {},
    roleConfig,
    userEmail,
    isOwner,
    ga4EventTrigger = () => {},
  } = useContext(DetailContext);
  const [reports, setReports] = useState([]);
  const [selectedYear, setSelectedYear] = useState(`${moment().year()}`);
  const [loading, setLoading] = useState(false);
  const [bifercationData, setBifercationData] = useState(null);
  const [reopenId, setReopenId] = useState(null);
  const [isDisable, setIsDisable] = useState(false);
  const [reportActionLoading, setReportActionLoading] = useState(false);
  const [openGenerateModal, setOpenGenerateModal] = useState(null);
  const [openDeleteReportModal, setOpenDeleteReportModal] = useState(false);
  const [selectedReportId, setSelectedReportId] = useState('');

  useEffect(() => {
    ga4EventTrigger('custom_page_view', 'OFR - Page View', 'Financial Reports');
  }, []);
  const isCurrentVa =
    userEmail &&
    (primary_accountant?.email === userEmail ||
      secondary_accountant?.email === userEmail ||
      roleConfig?.ownerReporting?.hasAllVesselAccess);

  const getBifercationDataHandler = (reportIds) => {
    ownerService.getBifercationData(reportIds, vesselId, ship_party_id).then((res) => {
      setBifercationData(res?.data || []);
    });
  };
  const getReports = async () => {
    setLoading(true);
    try {
      const result = await ownerService.getReports(
        selectedYear,
        vesselCode,
        vesselId,
        owner_end_date,
        ship_party_id,
        disableReopen,
      );
      const reportIds = await result.map((i) => i?.id);
      if (reportIds.length > 0) {
        getBifercationDataHandler(reportIds);
      }
      setReports(result);
    } catch (err) {
      const error = err?.response?.status ? err.response : err;
      handleError(error);
    } finally {
      setLoading(false);
    }
  };
  const reopenHandler = async () => {
    try {
      ga4EventTrigger('Reopen Report', 'OFR - Financial Report', 'Click');
      setIsDisable(true);
      const response = await ownerService.reopenReports(reopenId, vesselId, ship_party_id);
      if (response) {
        setIsDisable(false);
        setLoading(true);
        setReopenId(null);
        const result = await ownerService.getReports(
          selectedYear,
          vesselCode,
          vesselId,
          owner_end_date,
          ship_party_id,
          disableReopen,
        );
        setReports(result);
      }
    } catch (err) {
      setIsDisable(false);
      handleError(err);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (selectedYear) {
      getReports();
    }
  }, [selectedYear]);

  const handleReportSubmit = async () => {
    try {
      setReportActionLoading(true);
      await ownerService.generateFinancialReportForNextMonth(vesselCode, vesselId, ship_party_id);
      setReportActionLoading(false);
      setOpenGenerateModal(null);
      await getReports();
    } catch (err) {
      setReportActionLoading(false);
      setOpenGenerateModal(null);
      handleError(err);
    }
  };
  const handleDeleteModal = (id) => {
    if (id) {
      setSelectedReportId(id);
      setOpenDeleteReportModal(true);
    }
  };
  const handleDelete = async () => {
    try {
      if (selectedReportId) {
        setReportActionLoading(true);
        await ownerService.deleteGeneratedReport(selectedReportId, vesselId, ship_party_id);
        setReportActionLoading(false);
        setOpenDeleteReportModal(false);
        await getReports();
      }
    } catch (err) {
      setReportActionLoading(false);
      setOpenDeleteReportModal(false);
      handleError(err);
    }
  };

  return (
    <CustomOverlayLoader active={reportActionLoading}>
      <div className="filter-container">
        <div className="filter-header">Filter Financial Reports</div>
        <div className="filter-text">
          <div>Year</div>
          <div className="react-date-picker__wrapper">
            <CustomDatePicker
              value={selectedYear}
              onChange={(date) => setSelectedYear(date)}
              dataTestId="fml-monthly-financial-report-filter-year"
              customProps={{
                className: 'btn--primary',
                dateFormat: 'yyyy',
                showYearPicker: true,
                yearDropdownItemNumber: 5,
                maxDate: Date.now(),
                renderCustomHeader: () => <div className="calander-year-header">Years</div>,
                isClearable: false,
              }}
            />
          </div>
        </div>
      </div>
      <MonthlyFinancialReportTable
        vesselId={vesselId}
        data={reports}
        setData={setReports}
        isLoading={loading}
        setReopenId={setReopenId}
        isCurrentVa={isCurrentVa}
        isOwner={isOwner}
        bifercationData={bifercationData}
        setOpenGenerateModal={setOpenGenerateModal}
        handleDeleteModal={handleDeleteModal}
      />
      <ReopenReportModal
        onConfirm={() => reopenHandler()}
        reopenId={reopenId}
        onClose={() => setReopenId(null)}
        isDisable={isDisable}
      />
      <GenerateReportModal
        onConfirm={handleReportSubmit}
        open={!!openGenerateModal}
        onClose={() => setOpenGenerateModal(null)}
        headingText={'Generate Report'}
        headingDescription={`${GENERATE_REPORT_BODY_TEXT} ${moment(
          openGenerateModal,
          'DD MMMM YYYY',
        )
          .add(1, 'months')
          .format('MMMM YYYY')} for ${vesselName ?? ''}`}
        loading={reportActionLoading}
      />
      <GenerateReportModal
        onConfirm={handleDelete}
        open={openDeleteReportModal}
        onClose={() => setOpenDeleteReportModal(false)}
        headingText={'Delete Report'}
        headingDescription={DELETE_REPORT_BODY_TEXT}
        loading={reportActionLoading}
      />
    </CustomOverlayLoader>
  );
};
export default MonthlyFinancialReports;
