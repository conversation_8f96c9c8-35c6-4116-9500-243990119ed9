import React, { useEffect, useState } from 'react';
import { Container, Row, Form, Modal, Button } from 'react-bootstrap';
import { useHistory, useParams } from 'react-router-dom';
import styleGuide from '../styleGuide';
import BottomButton from '../component/advanced_search/BottomButton';
import OwnerShipDetails from '../component/ownership/OwnerShipDetails';
import Spinner from '../component/Spinner';
import { Formik } from 'formik';
import * as yup from 'yup';
import { not_xss } from '../util/validation';
import vesselService from '../service/vessel-service';
import * as ownershipService from '../service/ownership-service';
import {
  BUSINESS,
  ACCOUNTS,
  FLEET_PERSONNEL,
  INSURANCE,
  TECH_GROUP,
  CUSTOM_USER_GROUPS,
  OWNERSHIP_CHANGE_REQUEST_STATUS,
} from '../model/constants';
import { requestOwnershipChange, updateRequestOwnershipChange } from '../service/ownership-service';
import keycloakService from '../service/keycloak-service';
import ErrorAlert from '../component/ErrorAlert';
import PropTypes from 'prop-types';

const { Icon, ErrorPage } = styleGuide;

const OwnershipRequest = (props) => {
  const defaultDropDownValue = {
    owners: [],
    miscRegisteredOwners: [],
  };
  const history = useHistory();
  let { vesselId } = useParams();
  const [loading, setLoading] = useState(false);
  const [vessel, setVessel] = useState({ split_accounting_books: 'no' });
  const [vesselName, setVesselName] = useState(null);
  const [triedValidate, setTriedValidate] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [dropDownData, setDropDownData] = useState(defaultDropDownValue);
  const [error, setError] = useState(null);
  const [isOwnershipRequestCompleted, setIsOwnershipRequestCompleted] = useState(false);
  const [isFinalApproverApproved, setIsFinalApproverApproved] = useState(false);
  const [requestId, setRequestId] = useState(null);
  const [fieldsDistinctValues, setFieldsDistinctValues] = useState(null);
  const [ownershipId, setOwnershipId] = useState(null);
  const [isOwnershipPending, setIsOwnershipPending] = useState(false);

  const getVesselOwners = () => {
    switch (props.request.ownership_change_request.request_for) {
      case 'owner':
        return {
          owner_id: props.request.owner_id,
          reg_owner_id: null,
          expected_takeover_date: props.request.expected_owner_start_date,
        };
      case 'registered_owner':
        return {
          owner_id: null,
          reg_owner_id: props.request.registered_owner_id,
          expected_takeover_date: props.request.expected_registered_owner_start_date,
        };
      default:
        return {
          owner_id: props.request.owner_id,
          reg_owner_id: props.request.registered_owner_id,
          expected_takeover_date: props.request.expected_owner_start_date,
        };
    }
  };

  useEffect(() => {
    (async () => {
      try {
        if (props.request && !_.isEmpty(props.request)) {
          setIsFinalApproverApproved(props.isFinalApproverApproved());
        }
        setLoading(true);
        const [
          { data: dropDownsData },
          { data: techGroup },
          { data: vessel },
          { data: code },
          { data: pending },
        ] = await Promise.all([
          vesselService.getOwnersData(),
          keycloakService.getTechgroup(),
          vesselService.getVessel(vesselId),
          ownershipService.getDistinctFieldData(['vessel_short_code', 'vessel_account_code_new']),
          ownershipService.isVesselOwnershipPending(vesselId),
        ]);
        const techgroups = techGroup.response.tech_group
          .filter((group) => !CUSTOM_USER_GROUPS.includes(group))
          .map((name) => ({ id: name, value: name }));
        setDropDownData({ ...dropDownsData, techgroups });
        setVesselName(vessel.name);
        setOwnershipId(vessel.ownerships[0].id);
        setFieldsDistinctValues(code);
        setIsOwnershipPending(pending);
        props.setStaffData?.(vessel.ownerships[0]?.fleet_staff?.[0]);

        if (props.request) {
          setRequestId(props.request.ownership_change_request_id);
          const vesselOwners = getVesselOwners();
          if (props.request.ownership_change_request.is_split_of_accounting_books_require) {
            setVessel({
              ...vesselOwners,
              vessel_short_code: props.request.vessel_short_code,
              vessel_account_code: props.request.vessel_account_code_new,
              vessel_tel_fac_code: props.request.vessel_tel_fac_code,
              vessel_name:
                vessel.ownerships[0].name === props.request.name ? null : props.request.name,
              tech_group:
                vessel.ownerships[0].tech_group === props.request.tech_group
                  ? null
                  : props.request.tech_group,
              split_accounting_books: 'yes',
            });
          } else {
            setVessel({
              ...vesselOwners,
              expected_takeover_date: props.request.expected_date_of_takeover,
              split_accounting_books: 'no',
            });
          }
          if (props.request.request_status === OWNERSHIP_CHANGE_REQUEST_STATUS.COMPLETED) {
            setIsOwnershipRequestCompleted(true);
          }
        }
      } catch (error) {
        setLoading(false);
        console.log('error', error);
        setError('Oops, something went wrong. Please try again.');
      }
      setLoading(false);
    })();
  }, []);

  const getPayload = (values) => {
    // filter null values?
    return {
      owner_id: values['owner_id'],
      registered_owner_id: values['reg_owner_id'],
      vessel_id: parseInt(vesselId, 10),
      expected_date_of_takeover: values['expected_takeover_date'],
      vessel_short_code: values['vessel_short_code'],
      vessel_account_code_new: values['vessel_account_code'],
      vessel_tel_fac_code: values['vessel_tel_fac_code'],
      name: values['vessel_name'],
      tech_group: values['tech_group'],
      is_split_of_accounting_books_require: values['split_accounting_books'] == 'yes',
    };
  };

  const onSubmitVessel = async (values) => {
    let payload = getPayload(values);
    const result = await requestOwnershipChange(payload);
    return result;
  };

  const onUpdateVessel = async (values) => {
    const payload = getPayload(values);
    payload['ownership_change_request_id'] = requestId;
    const result = await updateRequestOwnershipChange(payload);
    return result;
  };

  const onSubmit = async (values) => {
    setTriedValidate(true);
    setShowModal(false);
    setLoading(true);
    try {
      if (!requestId) {
        await onSubmitVessel(values);
      } else {
        await onUpdateVessel(values);
      }
      history.push(`/vessel/ownership/details/${ownershipId}`);
    } catch (error) {
      setError('error occured while saving/updating the request');
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const getSchema = () => {
    let schema = {
      owner_id: yup.string().nullable(),
      reg_owner_id: yup.string().when('owner_id', {
        is: (value) => !value,
        then: () =>
          not_xss(
            yup
              .string()
              .required('At lease one input is required from Owner Name or Registered Owner Name'),
          ),
        otherwise: () => yup.string().nullable(),
      }),
      split_accounting_books: yup.string().required(),
      vessel_name: not_xss(yup.string().nullable()),
      vessel_tel_fac_code: yup.string().when('split_accounting_books', {
        is: (value) => value === 'yes',
        then: () => not_xss(yup.string().max(5).required('Vessel Tel FAC Code is required')),
        otherwise: () => yup.string().nullable(),
      }),
      vessel_account_code: yup.string().when('split_accounting_books', {
        is: (value) => value === 'yes',
        then: () =>
          not_xss(yup.string().length(4).required('Vessel Account Code New is required')).test(
            'vessel_account_code',
            'Duplicate input for Vessel Account Code New',
            (value) => !fieldsDistinctValues?.vessel_account_code_new?.includes(value),
          ),
        otherwise: () => not_xss(yup.string().length(4).nullable()),
      }),
      vessel_short_code: yup.string().when('split_accounting_books', {
        is: (value) => value === 'yes',
        then: () =>
          not_xss(yup.string().min(0).max(3).required('Vessel Short Code is required')).test(
            'vessel_short_code',
            'Duplicate input for Vessel Short Code',
            (value) => !fieldsDistinctValues?.vessel_short_code?.includes(value),
          ),
        otherwise: () => not_xss(yup.string().min(0).max(3).nullable()),
      }),
    };

    return yup.object().shape(schema);
  };

  const onVesselChange = (key, value) => {
    let splitAccountingFields = {};
    if (key === 'split_accounting_books' && value === 'no') {
      splitAccountingFields = {
        vessel_short_code: '',
        vessel_account_code: '',
        vessel_tel_fac_code: '',
        vessel_name: '',
        tech_group: '',
      };
    }
    setVessel({
      ...vessel,
      ...splitAccountingFields,
      [key]: value,
    });
  };

  const handleClose = () => {
    history.push(`/vessel/ownership/details/${ownershipId}`);
  };

  const handleModalClose = () => setShowModal(false);
  const handleModalOpen = () => setShowModal(true);

  const canUpdateRequest = () => {
    return (
      props.roleConfig.approvalGroups.includes(BUSINESS) &&
      !isOwnershipRequestCompleted &&
      !isFinalApproverApproved
    );
  };

  const canViewRequest = () => {
    return (
      props.roleConfig.finalApprover ||
      props.roleConfig.approvalGroups.includes(BUSINESS) ||
      props.roleConfig.approvalGroups.includes(ACCOUNTS) ||
      props.roleConfig.approvalGroups.includes(FLEET_PERSONNEL) ||
      props.roleConfig.approvalGroups.includes(INSURANCE) ||
      props.roleConfig.approvalGroups.includes(TECH_GROUP)
    );
  };

  const renderOwnernshipChange = () => {
    if (error) {
      return <ErrorAlert message={error} />;
    } else {
      return (
        <div className="ownership_change">
          <Container>
            <Formik
              validationSchema={getSchema()}
              onSubmit={onSubmit}
              enableReinitialize={true}
              initialValues={vessel}
              validateOnChange={triedValidate}
              validateOnBlur={triedValidate}
            >
              {({ handleSubmit, handleChange, handleBlur, values, touched, isValid, errors }) => {
                const onInputChange = (event) => {
                  onVesselChange(event.target.name, event.target.value);
                  handleChange(event);
                };

                const onDateChange = (key, value) => {
                  onVesselChange(key, value);
                };

                const getFormPageProps = () => {
                  const baseProps = {
                    vessel,
                    errors,
                    dropDownData,
                    isOwnershipRequestCompleted,
                    isFinalApproverApproved,
                    onInputChange,
                    handleBlur,
                    onDateChange,
                    roleConfig: props.roleConfig,
                  };
                  if (props.request) {
                    return { ...baseProps, request: props.request.ownership_change_request };
                  } else {
                    return { ...baseProps, request: {} };
                  }
                };

                return (
                  <Container>
                    <Row className="closeIcon">
                      <h5 id="ownership-title">Change Ownership</h5>
                      <Icon
                        icon="close"
                        data-testid="test-id-close-icon"
                        size={25}
                        onClick={handleClose}
                      />
                    </Row>
                    <Row>
                      <h6>{vesselName ?? <br></br>} </h6>
                    </Row>
                    <hr />
                    <Form>
                      {Object.keys(errors).length ? (
                        <ErrorAlert errors={errors} setShowModal={setShowModal} />
                      ) : null}
                      <OwnerShipDetails {...getFormPageProps()} />
                      {canUpdateRequest() && (
                        <BottomButton
                          disabled={isOwnershipPending}
                          title={'Save'}
                          onClick={handleModalOpen}
                        />
                      )}
                    </Form>
                    <Modal className="action-modal" id="requesting-modal" show={showModal} centered>
                      <Modal.Header className="mb-5">
                        <Modal.Title className="h5">
                          Confirm requesting for ownership change?
                        </Modal.Title>
                      </Modal.Header>
                      <Modal.Footer className="mt-5">
                        <Button variant="primary" onClick={handleModalClose}>
                          Cancel
                        </Button>
                        <Button variant="secondary" onClick={handleSubmit}>
                          Confirm
                        </Button>
                      </Modal.Footer>
                    </Modal>
                  </Container>
                );
              }}
            </Formik>
          </Container>
        </div>
      );
    }
  };

  const renderView = () => {
    return canViewRequest() ? renderOwnernshipChange() : <ErrorPage errorCode={403} />;
  };

  return loading ? (
    <Container>
      <Spinner />
    </Container>
  ) : (
    renderView()
  );
};

OwnershipRequest.propTypes = {
  request: PropTypes.object,
  isFinalApproverApproved: PropTypes.bool,
  setStaffData: PropTypes.func,
  roleConfig: PropTypes.object,
};

export default React.memo(OwnershipRequest);
