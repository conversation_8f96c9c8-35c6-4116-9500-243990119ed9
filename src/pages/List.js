import React, { useState, useEffect, useCallback, useContext } from 'react';
import { mapVesselQueryToSearchCriteria } from '../util/search-query';
import {
  Container,
  Col,
  Button,
  Row,
  ButtonToolbar,
  ButtonGroup,
  Dropdown,
  Nav,
  Tab,
  Form,
} from 'react-bootstrap';
import { useHistory, useParams, useLocation } from 'react-router-dom';
import vesselService from '../service/vessel-service';
import { vesselListTabKeys, LOCAL_STORAGE_FIELDS } from '../model/constants';
import ScrollArrow from '../component/BackToTopButton';
import SearchParametersView from '../component/SearchParametersView';
import styleGuide from '../styleGuide';
const { Icon, ErrorPage } = styleGuide;
import TableColumnsButton from '../component/vesselList/TableColumnsButton';
import VesselTable from '../component/vesselList/VesselTable';
import items from '../component/vesselList/MenuList';
import {
  retrieveColumns,
  storeColumns,
  isKeyStored,
  resetAllTabs,
  getPageSort,
  getPageTableState,
} from '../util/local-storage-helper';
import { useDebounce, useDebouncedCallback } from 'use-debounce';
import SearchController from '../controller/search-controller';
import AdvancedSearch from '../component/advanced_search/AdvancedSearch';
import httpService from '../service/http-service';
import searchTypes from '../util/search-types';
import { exportTableToExcel } from '../util/excel-export';
import { VesselContext } from '../context/VesselContext';
import AssignReplaceUser from '../component/assignOfficeData/AssignReplaceUser';
import { ASSIGNUSERSTATUS } from '../constants/assign-user';
import PropTypes from 'prop-types';

const List = ({ ga4react }) => {
  const history = useHistory();
  const [vessels, setVessels] = useState([]);
  let { tab } = useParams();
  const [activeKey, setActiveKey] = useState(tab);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const query = useLocation().search.substring(1);
  const criteria = mapVesselQueryToSearchCriteria(query);
  const [loading, setLoading] = useState(false);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const sortState = getPageSort(LOCAL_STORAGE_FIELDS.masterKey);
  const tabOptions = ['active-vessels', 'new-takeovers', 'handed-over', 'archived'];
  const [filters, setFilters] = useState([]);
  const [debouncedFilters] = useDebounce(filters, 700);
  const [searchedKeyword, setSearchedKeyword] = useState('');
  const [isPageViewInvoked, setPageViewInvoked] = useState(false);
  const { assignUserActionStatus, setAssignUserActionStatus, roleConfig, ga4EventTrigger } =
    useContext(VesselContext);
  const menuitems = items(setAssignUserActionStatus, roleConfig);
  const { UPDATED_STATUS } = ASSIGNUSERSTATUS;

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview('/vessel', '', 'Vessel');
        setPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  const eventTracker = (type, value) => {
    switch (type) {
      case 'tabs':
        ga4EventTrigger('Tab', 'Nav', `Vessel List Page - ${value}`);
        break;
      case 'pageSwitch':
        ga4EventTrigger('Go To Page', 'Pagination', `Vessel List Page - ${value}`);
        break;
      case 'pageSizeSwitch':
        ga4EventTrigger('Number of Rows', 'Pagination', `Vessel List Page - ${value}`);
        break;
      case 'scroll':
        ga4EventTrigger('Back to Top', 'Nav', 'Vessel List Page');
        break;
      case 'columnDisplay':
        ga4EventTrigger('Column', 'List', `Vessel List Page - ${value}`);
        break;
      case 'KeyworkSearchClick':
        ga4EventTrigger('Click', 'Keywork Search', 'Vessel List Page');
        break;
      case 'KeyworkSearch':
        ga4EventTrigger('Keyword', 'Keywork Search', `Vessel List Page - ${value}`);
        break;
      case 'advancedSearch':
        ga4EventTrigger('Click', 'Advance Search', `Vessel List Page - ${value}`);
        break;
      case 'filterTypeChange':
        ga4EventTrigger('Category', 'Advance Search', `Vessel List Page - ${value}`);
        break;
      case 'filterSubTypeChange':
        ga4EventTrigger('Value', 'Advance Search', `Vessel List Page - ${value}`);
        break;
      case 'sortBy':
        ga4EventTrigger('Sorting', 'List', `Vessel List Page - ${value}`);
        break;
      case 'moreDropdown':
        ga4EventTrigger('More', 'Menu', 'Vessel List Page');
        break;
      case 'actions':
        ga4EventTrigger('More at Listing', 'Menu', 'Vessel List Page');
        break;
      case 'edit':
        ga4EventTrigger('Open', 'Edit Vessel', `Vessel List Page - ${value}`);
        break;
      case 'add':
        ga4EventTrigger('Add Vessel', 'Add Vessel', 'Vessel List Page');
        break;
      case 'visit':
        ga4EventTrigger('Open', 'View Vessel Details', `Vessel List Page - ${value}`);
        break;
      default:
        ga4EventTrigger('Click', 'Vessel List Page', value);
        break;
    }
  };
  const handleKeywordChange = useDebouncedCallback((value) => {
    //Set page number as 0 for all tabs in local store
    eventTracker('KeyworkSearch', value);
    resetAllTabs();
    setSearchedKeyword(value.trim().toLowerCase());
  }, 700);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const controller = new SearchController();
        const { dropDownData } = await controller.onLoadPage(criteria);
        setFilters(mapVesselQueryToSearchCriteria(query, dropDownData));
      } catch (error) {
        console.error(error);
      }
      setLoading(false);
    })();
  }, [setFilters]);

  useEffect(() => {
    if (debouncedFilters.length) {
      resetAllTabs();
      let filteredList = removeEmptyFilters(debouncedFilters);
      if (filteredList.length === 0) {
        window.history.replaceState({}, null, `${history.location.pathname}`);
        return;
      }
      const controller = new SearchController();
      const filterQuery = controller.getVesselQuery(filteredList);
      window.history.replaceState({}, null, `${history.location.pathname}?${filterQuery}`);
    }
  }, [debouncedFilters]);

  const removeEmptyFilters = (filter) => {
    return filter.filter(
      (item) => item.subtype !== null && item.subtype !== undefined && item.subtype !== '',
    );
  };

  const getSortByParams = (paginationParams) => {
    let sortByObj = [...paginationParams.sortBy];
    let item = menuitems
      .filter((item) => item.id === sortByObj[0].id)
      .map((item) => item.Header)
      .toString();
    let searchId = searchTypes()
      .filter((type) => type.name === item)
      .map((type) => type.queryKey)
      .toString();
    let formObj = [
      {
        id: searchId,
        desc: sortByObj[0].desc,
      },
    ];
    paginationParams = { ...paginationParams, sortBy: formObj };
    return paginationParams;
  };

  const fetchData = useCallback(
    async (paginationParams) => {
      if (roleConfig.vessel.view) {
        try {
          setLoading(true);
          // reset state
          setVessels([]);
          /*Use default columns(based on tab)
            only if no selection/deselection
            is made till now*/
          if (
            !isKeyStored(
              'vessel-table-details',
              activeKey,
              LOCAL_STORAGE_FIELDS.tableSelectedColumns,
            )
          ) {
            if (activeKey === 'new-takeovers') {
              const default_heads = [
                menuitems[1],
                menuitems[2],
                menuitems[3],
                menuitems[4],
                menuitems[9],
                menuitems[13],
                menuitems[41],
                menuitems[44],
                menuitems[45],
              ];
              setSelectedColumns(default_heads);
            } else if (activeKey === 'handed-over') {
              const default_heads = [
                menuitems[1],
                menuitems[2],
                menuitems[3],
                menuitems[4],
                menuitems[11],
                menuitems[13],
                menuitems[41],
                menuitems[42],
                menuitems[43],
                menuitems[44],
                menuitems[45],
              ];
              setSelectedColumns(default_heads);
            } else {
              const default_heads = [
                menuitems[1],
                menuitems[2],
                menuitems[3],
                menuitems[4],
                menuitems[13],
                menuitems[41],
                menuitems[42],
                menuitems[43],
                menuitems[44],
                menuitems[45],
              ];
              setSelectedColumns(default_heads);
            }
          } else
            setSelectedColumns(retrieveColumns(activeKey, roleConfig, setAssignUserActionStatus));

          if (paginationParams.sortBy.length) paginationParams = getSortByParams(paginationParams);

          const response = await vesselService.getOwnerships(
            activeKey,
            paginationParams,
            query,
            searchedKeyword,
          );
          const data = response ? response.data : {};
          setVessels(
            data?.results?.map((item) => {
              return { ...item, keyword: searchedKeyword };
            }) || [],
          );
          const total = data.totalCount || 0;
          const pageSize = paginationParams.pageSize || 10;
          setPageCount(Math.ceil(total / pageSize));
          setTotalCount(total);
          setLoading(false);
        } catch (error) {
          if (httpService.axios.isCancel(error)) {
            return;
          }
          setLoading(false);
          console.log('get vessel failed', error);
        }
      }
    },
    [activeKey, query, searchedKeyword],
  );

  useEffect(() => {
    if (assignUserActionStatus?.status === UPDATED_STATUS) {
      const { pageIndex, pageSize } = getPageTableState(activeKey);
      fetchData({
        pageIndex: Math.floor((pageIndex * pageSize) / pageSize),
        sortBy: getPageSort(activeKey),
        pageSize,
      });
    }
  }, [assignUserActionStatus]);

  const clearAdvancedFilters = () => {
    setFilters([]);
    resetAllTabs();
    window.history.replaceState({}, null, `${history.location.pathname}`);
    setLoading(false);
  };

  const onSelectColumn = (item) => {
    const newSelection = selectedColumns.slice();
    const idx = selectedColumns.findIndex(({ id }) => id === item.id);
    if (idx !== -1) {
      newSelection.splice(idx, 1);
      eventTracker('columnDisplay', `${item.Header} remove`);
    } else {
      newSelection.push(item);
      newSelection.sort((a, b) => a.order - b.order);
      eventTracker('columnDisplay', `${item.Header} add`);
    }
    //Record selection/deselection in Local Storage
    storeColumns(activeKey, newSelection);
    setSelectedColumns(newSelection);
  };

  const takeoverVessel = () => {
    eventTracker('add', 'Add Vessel');
    history.push('/vessel/takeover/basic/');
  };

  const visitUpdateVessel = (vesselId, ownershipId, name) => {
    eventTracker('edit', `Edit Vessel - ${name}`);
    history.push(`/vessel/${vesselId}/${ownershipId}/takeover/basic`);
  };

  const visitVessel = (data, name) => {
    eventTracker('visit', `Visit Seafarer - ${name}`);
    history.push(`/vessel/ownership/details/${data}`);
  };

  const visitSearch = () => {
    history.push(`/vessel/search?${query}`);
  };

  const printAll = () => {
    window.print();
  };

  const handleTabSelect = (key) => {
    if (key === activeKey) {
      if (query) {
        // clear the current query and refresh page
        setFilters([]);
        resetAllTabs();
        setVessels([]);
        setActiveKey(key);
        history.push(`/vessel/${key}`);
      }
      return;
    }
    eventTracker('tabs', key);
    setVessels([]);
    setActiveKey(key);
    history.push(`/vessel/${key}?${query}`);
  };

  const ontoggle = (isOpen) => {
    eventTracker('advancedSearch', `${isOpen ? 'open' : 'close'}`);
  };

  const handleExportExcel = async () => {
    let alldata =[]
    let pageIndex = 0;
    let pageSize = 1000;
    const itr = Math.ceil(totalCount/pageSize);

    for (let i = 0; i < itr; i++) {
      const response = await vesselService.getOwnerships(activeKey, {pageIndex, pageSize}, query, searchedKeyword);
      alldata = [...alldata, ...response.data.results];
      pageIndex++;
    }

    exportTableToExcel(
      {
        vesselList: {
          jsonData: alldata,
          columns: [
            {
              Header: 'No.',
              accessor: 'id',
              name: 'id',
              maxWidth: 90,
              sticky: 'left',
              id: 'vessel.id',
            },
            ...selectedColumns,
          ],
          title: '',
        },
      },
      'Vessel List',
    );
  };

  const renderTabOptions = () => {
    if (tabOptions.includes(activeKey)) {
      return (
        <Container>
          <Tab.Container activeKey={activeKey} defaultActiveKey={vesselListTabKeys.ACTIVE_VESSEL}>
            <Row className="no-print">
              <Col>
                <Nav variant="tabs" onSelect={(k) => handleTabSelect(k)} className="nav-justified">
                  <Nav.Item>
                    <Nav.Link eventKey={vesselListTabKeys.ACTIVE_VESSEL} href="#">
                      <Icon icon="tab-vsl-active" size={30} />
                      Active Vessels
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey={vesselListTabKeys.NEW_TAKEOVERS} href="#">
                      <Icon icon="tab-vsl-newtakeovers" size={30} />
                      New Takeovers
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey={vesselListTabKeys.HANDED_OVER} href="#">
                      <Icon icon="tab-vsl-handedover" size={30} />
                      Vessels Handed Over
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey={vesselListTabKeys.ARCHIVED} href="#">
                      <Icon icon="tab-vsl-archived" size={30} />
                      Archived
                    </Nav.Link>
                  </Nav.Item>
                </Nav>
              </Col>
            </Row>
            <Row className="no-print">
              <Col xs={6} md={3}>
                <div className="mt-4 quick-filters-button">
                  <Form.Control
                    data-testid="data-testid-seach-vessel"
                    id="search-bar"
                    type="text"
                    name="keyword"
                    placeholder="Type keywords to filter"
                    onClick={() => eventTracker('KeyworkSearchClick', 'Keywork Search Click')}
                    onChange={(e) => handleKeywordChange(e.target.value)}
                  />
                </div>
              </Col>
              <Col xs={6} md="auto">
                <div className="mobile">
                  <ButtonGroup className="advanced-search mt-4 w-100">
                    <Button variant="outline-primary" onClick={visitSearch}>
                      Advanced Search
                    </Button>
                  </ButtonGroup>
                </div>
                <div className="desktop">
                  <ButtonGroup className="advanced-search mt-4">
                    <Dropdown alignRight={false} onToggle={(isOpen) => ontoggle(isOpen)}>
                      <Dropdown.Toggle variant="outline-primary" id="dropdown-advanced-search">
                        Advanced Search{' '}
                        {removeEmptyFilters(debouncedFilters).length
                          ? `(${removeEmptyFilters(debouncedFilters).length})`
                          : ''}
                      </Dropdown.Toggle>
                      <Dropdown.Menu>
                        <div className="advanced-search-menu">
                          {AdvancedSearch({
                            filters: filters,
                            setFilters: setFilters,
                            eventTracker: eventTracker,
                            roleConfig: roleConfig,
                          })}
                        </div>
                      </Dropdown.Menu>
                    </Dropdown>
                  </ButtonGroup>
                </div>
              </Col>
              <Col xs="auto">
                {removeEmptyFilters(debouncedFilters).length > 0 && (
                  <Button variant="outline-primary" className="mt-4" onClick={clearAdvancedFilters}>
                    Clear All
                  </Button>
                )}
              </Col>
              <Col>
                <ButtonToolbar className="toolbar-allignment">
                  <TableColumnsButton
                    selectedColumns={selectedColumns}
                    onSelectColumn={onSelectColumn}
                    menuItem={menuitems}
                  />
                  <Dropdown
                    alignRight
                    onToggle={(isOpen) => {
                      if (isOpen) eventTracker('moreDropdown', 'More');
                    }}
                  >
                    <Dropdown.Toggle
                      variant="outline-primary"
                      id="dropdown-more"
                      data-testid="data-testid-dropdown-more-btn"
                    >
                      {' '}
                      More
                    </Dropdown.Toggle>
                    <Dropdown.Menu>
                      {roleConfig.vessel.create ? (
                        <Dropdown.Item
                          onClick={takeoverVessel.bind(this)}
                          data-testid="test-id-add-a-vessel"
                        >
                          Add a Vessel
                        </Dropdown.Item>
                      ) : null}
                      {roleConfig.vessel.exportToExcel ? (
                        <Dropdown.Item
                          data-testid="vessel-list-export-to-excel"
                          onClick={handleExportExcel}
                        >
                          Export to Excel
                        </Dropdown.Item>
                      ) : null}
                      <Dropdown.Item onClick={printAll}>Print All</Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown>
                </ButtonToolbar>
              </Col>
            </Row>

            {criteria.length > 0 && (
              <Row className="mobile">
                <SearchParametersView criteria={criteria} query={query} />
              </Row>
            )}
            <Row>
              <Col className="pt-4 text-right">
                <span data-testid="vessel-list-result-count">
                  <b>{totalCount}</b> Results
                </span>
              </Col>
            </Row>
            <Row>
              <Col>
                <Tab.Content>
                  <Tab.Pane eventKey={activeKey}>
                    <VesselTable
                      tabName={activeKey}
                      vessels={vessels}
                      visitVessel={visitVessel}
                      visitUpdateVessel={visitUpdateVessel}
                      selectedColumns={selectedColumns}
                      isLoading={loading}
                      fetchData={fetchData}
                      pageCount={pageCount}
                      query={query}
                      keyword={searchedKeyword}
                      initSort={sortState}
                      roleConfig={roleConfig}
                      eventTracker={eventTracker}
                    />
                  </Tab.Pane>
                </Tab.Content>
              </Col>
            </Row>
          </Tab.Container>
          <ScrollArrow onClick={() => eventTracker('scroll', 'Scroll to top')} />
          <AssignReplaceUser />
        </Container>
      );
    } else {
      return <ErrorPage errorCode={404} />;
    }
  };

  return roleConfig.vessel.view ? renderTabOptions() : <ErrorPage errorCode={403} />;
};

List.propTypes = {
  ga4react: PropTypes.shape({
    pageview: PropTypes.func,
    ...PropTypes.objectOf(PropTypes.func),
  }),
};

export default List;
