import download from 'downloadjs';
import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Col, Container } from 'react-bootstrap';
import { Document, Page } from 'react-pdf';
// it is required for pdfjs: https://github.com/mozilla/pdf.js/issues/10478
// eslint-disable-next-line no-unused-vars
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry'; //NOSONAR
import { useHistory } from 'react-router-dom';
import vesselService, { downloadContingencyFile } from '../service/vessel-service';
import styleGuide from '../styleGuide';
import DocumentDownloadFailedModal from '../component/contingency/DocumentDownloadFailedModal';
import Spinner from '../component/Spinner';
import getURLParams from '../util/getURLParams';

const { Icon } = styleGuide;

const ViewDocument = () => {
  const history = useHistory();
  const [loading, setLoading] = useState(true);
  const id = getURLParams('id', history.location.search);
  const source = getURLParams('source', history.location.search);
  const filePath = getURLParams('path', history.location.search);
  const scrollable = getURLParams('scrollable', history.location.search) === 'true';
  const [type, setType] = useState();
  const [fileUrl, setFileUrl] = useState();
  const [file, setFile] = useState();
  const [fileName, setFileName] = useState('Document');
  const [showDownloadFailModal, setShowDownloadFailModal] = useState(false);
  const [, setDownloadFailStatus] = useState(undefined);
  const [downloadFailMessage] = useState(undefined);
  const [nextPageDisabled, setNextPageDisabled] = useState(false);
  const [prevPageDisabled, setPrevPageDisabled] = useState(true);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);

  const downloadDoc = () => {
    download(file, fileName, type);
  };

  const getFileName = () => {
    const extension = filePath?.split('/');
    setFileName(extension[extension.length - 1]);
  };

  useEffect(() => {
    document.getElementById('navbar').style.display = 'none';
    const bodyTag = document.getElementsByTagName('body')[0];
    bodyTag.style.marginTop = '0';
    bodyTag.style.background = '#000';
  }, []);

  useEffect(() => {
    (async () => {
      try {
        let fileData, type;
        if (source === 'contingency') {
          const response = await downloadContingencyFile(id);
          fileData = response.data;
          type = response.headers['content-type'];
        }
        if (['financial', 'cash-call', 'drawing', 'certificate', 'eu-ets'].includes(source)) {
          ({ fileData, type } = await downloadFile(source));
        }
        if (fileData) {
          displayFile(fileData, type);
        }
      } catch (error) {
        if (error?.response?.status) {
          setDownloadFailStatus(error?.response?.status);
        }
        setShowDownloadFailModal(true);
      }
      setLoading(false);
    })();
  }, []);

  const downloadFile = async (source) => {
    let preSignedUrl = '';
    const id = getURLParams('id', history.location.search);
    const responseUrl = await vesselService.getPreSignedUploadLink({
      record_type: source,
      payload: {
        id: id,
      },
      action: 'download',
    });
    preSignedUrl = responseUrl.data.pre_signed_link;
    const responseData = await vesselService.getPresignedDocument(preSignedUrl);
    let fileData = responseData.data;
    let type = responseData.headers['content-type'];
    return { fileData, type };
  };

  const displayFile = (fileData, type) => {
    const blob = new Blob([fileData], { type: type });
    getFileName();
    setFileUrl(window.URL.createObjectURL(blob));
    setType(type);
    setFile(blob);
  };

  const onDocumentLoadSuccess = ({ numPages }) => {
    if (numPages === 1) {
      setNextPageDisabled(true);
      setPrevPageDisabled(true);
    }
    setNumPages(numPages);
  };

  const nextPage = () => {
    if (pageNumber + 1 === numPages) {
      setNextPageDisabled(true);
    }
    setPrevPageDisabled(false);
    setPageNumber(pageNumber + 1);
  };

  const prevPage = () => {
    if (pageNumber === 2) {
      setPrevPageDisabled(true);
    }
    setNextPageDisabled(false);
    setPageNumber(pageNumber - 1);
  };

  const closeWindow = () => {
    window.close();
  };

  const documentType = useMemo(() => {
    if (type === 'application/pdf') {
      if (scrollable) {
        return (
          <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
            <div style={{ overflowY: 'scroll', height: '95vh' }}>
              {Array.from(new Array(numPages), (el, index) => (
                <div style={{marginBottom: '15px'}}> 
                <div className='crt-pdf-page-number'>
                    {`${index +1} of ${numPages}`}
                </div>
                <Page key={`page_${index + 1}`} pageNumber={index + 1} scale={1.5} />
                </div>
              ))}
            </div>
          </Document>
        );
      } else {
        return (
          <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
            <Page scale={1.5} key={`page_${pageNumber}`} pageNumber={pageNumber} />
          </Document>
        );
      }
    } else if (type === 'image/jpeg' || type === 'image/png') {
      return <img src={fileUrl} />;
    }
    return (
      <div className="d-flex justify-content-center documentDownloadBtn">
        <p>Unable to view the document. Please Download</p>
        <Button onClick={() => downloadDoc()}>Download</Button>
      </div>
    );
  }, [type, file, fileUrl, pageNumber, numPages, scrollable]);

  return (
    <Container>
      <div className="document-continer">
        <DocumentDownloadFailedModal
          show={showDownloadFailModal}
          onClose={closeWindow}
          title={'Download Failed'}
        >
          {<p>{downloadFailMessage}</p>}
        </DocumentDownloadFailedModal>
        {loading ? (
          <div className="document-continer__spinner-document">
            <Spinner alignClass="" />
          </div>
        ) : (
          <div>
            <div className="document-continer__header-container">
              <Col>
                <span>{fileName}</span>
              </Col>
              {numPages && !scrollable && (
                <Col className="document-continer__page-number-container">
                  {!prevPageDisabled && <Icon icon="arrow-left" onClick={prevPage} size={21} />}
                  <span>
                    {pageNumber} of {numPages}
                  </span>
                  {!nextPageDisabled && <Icon icon="arrow-right" onClick={nextPage} size={21} />}
                </Col>
              )}
              <Col>
                <div className="document-continer__icon-container">
                  <Icon icon="download" onClick={downloadDoc} size={21} />
                  <Icon icon="close" size={21} onClick={closeWindow} />
                </div>
              </Col>
            </div>
            <div className="document-continer__pdf-style">{documentType}</div>
          </div>
        )}
      </div>
    </Container>
  );
};

export default ViewDocument;
