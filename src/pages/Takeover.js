import React, { useState, useEffect, useMemo } from 'react';
import { Route, Switch, useParams, useHistory, useLocation } from 'react-router-dom';
import { Formik } from 'formik';
import * as yup from 'yup';
import { Container, Form, Col, Button, Row, Modal } from 'react-bootstrap';
import NavigationBarItem from '../component/takeover/NavigationBarItem';
import TakeoverBasicForm from '../component/takeover/TakeoverBasicForm';
import TakeoverFinancialForm from '../component/takeover/TakeoverFinancialForm';
import TakeoverContactForm from '../component/takeover/TakeoverContactForm';
import TakeoverParticularsForm from '../component/takeover/TakeoverParticularsForm';
import TakeoverOfficeDataForm from '../component/takeover/TakeoverOfficeDataForm';
import TakeoverMiscellaneousForm from '../component/takeover/TakeoverMiscellaneousForm';
import TakeoverPhotoForm from '../component/takeover/TakeoverPhotoForm';
import TakeoverFormErrorList from '../component/takeover/TakeoverFormErrorList';
import TakeOverController from '../controller/take-over-controller';
import styleGuide from '../styleGuide';
const { ErrorPage } = styleGuide;
import BottomButton from '../component/advanced_search/BottomButton';
import {
  DependencyValidationPairing,
  getBasicFormErrors,
  not_xss,
  TrialFormModel,
} from '../util/validation';
import Spinner from '../component/Spinner';
import { getPortsByCountry } from '../service/reference-service';
import TakeoverTrialForm from '../component/takeover/TakeoverTrialForm';
import { v4 } from 'uuid';
import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import _ from 'lodash';
import { EMAIL_REGEXP } from '../constants/regexp';
import moment from 'moment';
import PropTypes from 'prop-types';

const { PARIS2_URL } = process.env;

const DefaultRowTrialValue = {
  id: v4(),
};

const PROCEED_OTHER_TAB_FAILED_MESSAGE = {
  header: 'Fill in required fields before proceed',
  message: 'Please fill in all the required fields',
};

const retrieveTakeoverTrialData = (vessel) => {
  vessel.shop_trials =
    vessel.shop_trials !== undefined && vessel.shop_trials.length > 0
      ? vessel.shop_trials
      : [{ ...DefaultRowTrialValue }];
  vessel.sea_trials =
    vessel.sea_trials !== undefined && vessel.sea_trials.length > 0
      ? vessel.sea_trials.filter((sea_trial) => sea_trial.deleted_at === null)
      : [{ ...DefaultRowTrialValue }];
  vessel.maximum_continuous_rating_kw = vessel.maximum_continuous_rating_kw
    ? vessel.maximum_continuous_rating_kw
    : undefined;
  vessel.maximum_continuous_rating_rpm = vessel.maximum_continuous_rating_rpm
    ? vessel.maximum_continuous_rating_rpm
    : undefined;
};

const controller = new TakeOverController();

const Takeover = (props) => {
  const defaultDropDownValue = {
    emailTypes: [],
    flags: [],
    hmUnderwriters: [],
    owners: [],
    phoneTypes: [],
    piClubs: [],
    ihmProviders: [],
    portOfRegistrys: [],
    shipyards: [],
    vesselClasss: [],
    vesselServiceStatuss: [],
    vesselTypes: [],
    miscEngines: [],
    miscRegisteredOwners: [],
    miscManagers: [],
    miscFlagIspss: [],
    miscQis: [],
    miscSalvages: [],
    miscOperators: [],
    miscCurrencys: [],
    miscClassifications: [],
    miscClassificationSocietys: [],
    miscOsros: [],
    miscMediaResponses: [],
    miscManagementTypes: [],
    miscOtherContactss: [],
    emissionTypes: [],
  };
  const params = useParams();
  const { step = 'basic', vesselId, ownershipId } = params;
  const query = useLocation().search.substring(1);

  const history = useHistory();
  const [intialFinancialData, setIntialFinancialData] = useState({
    wages_treatment: 'pool',
    wages_accumulation: 'no',
    has_portage_bill: 'yes',
  });
  const [vessel, setVessel] = useState({
    shop_trials: [{ ...DefaultRowTrialValue }],
    sea_trials: [{ ...DefaultRowTrialValue }],
    ...intialFinancialData,
  });
  const [initialVessel, setInitialVessel] = useState(null);
  const [loading, setLoading] = useState(true);
  const [dropDownData, setDropDownData] = useState(defaultDropDownValue);
  const [error, setError] = useState(null);
  const [modalMessage, setModalMessage] = useState(null);
  const [triedValidate, setTriedValidate] = useState(false);
  const [isAllApproved, setIsAllApproved] = useState(false);
  const [userMemberships, setUserMemberships] = useState(null);
  const [hasRole, setHasRole] = useState(false);
  const [fieldsDistinctValues, setFieldsDistinctValues] = useState(null);
  const [isPageViewInvoked, setPageViewInvoked] = useState(false);
  const [title, setPageTitle] = useState('Add Vessel');
  const [typeAheadValues, setTypeAheadValues] = useState({});
  const [disableFields, setDisableFields] = useState(true);
  const [submitError, setSubmitError] = useState();
  const editStartTime = moment().valueOf();

  useEffect(() => {
    if (!isPageViewInvoked) {
      let pageTitle = 'Add Vessel';
      if (vesselId) {
        setPageTitle('Edit Vessel');
        pageTitle = 'Edit Vessel';
      }
      try {
        props.ga4react?.pageview(history.location.pathname, '', pageTitle);
        setPageViewInvoked(true);
      } catch (e) {
        console.log('error on google analytics', e);
      }
    }
  }, [isPageViewInvoked, setPageViewInvoked, setPageTitle]);

  const ga4EventTrigger = (action, category, label) => {
    try {
      props.ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log('error on google analytics', error);
    }
  };

  const eventTracker = (type, value) => {
    switch (type) {
      case 'tabNavigation':
        ga4EventTrigger('Tab', 'Nav', `${title} - ${value}`);
        break;
      case 'routeTo':
        ga4EventTrigger('Breadcrumb', 'Nav', `${title} - ${value}`);
        break;
      case 'submit':
        ga4EventTrigger('Save', title, value);
        break;
      case 'routeToDetailsPage':
        ga4EventTrigger('Save', title, value);
        break;
      case 'routeToListPage':
        ga4EventTrigger('Save', title, value);
        break;
      case 'editVessel':
        ga4EventTrigger(
          'Time Spent',
          `Vessel ${vesselId ? 'Edit' : 'Create'} - ${value}`,
          `Vessel ${vesselId ? 'Edit' : 'Create'} - ${value}`,
        );
        break;
      default:
        ga4EventTrigger(title, value);
        break;
    }
  };

  const breadCrumbsItems = useMemo(
    () =>
      ownershipId
        ? [
            { title: 'Vessel', label: 'To List Page', link: `${PARIS2_URL}/vessel` },
            {
              title: vessel?.name ?? '- - -',
              label: 'Details',
              link: `/vessel/ownership/details/${ownershipId}`,
            },
            {
              title: 'Edit a Vessel',
              label: 'Edit a Vessel',
              link: '#',
            },
          ]
        : [
            { title: 'Vessel', label: 'To List Page', link: `${PARIS2_URL}/vessel` },
            {
              title: 'Create a Vessel',
              label: 'Create a Vessel',
              link: '#',
            },
          ],
    [vessel, ownershipId],
  );

  const onBreadcrumbClick = (label) => {
    eventTracker('routeTo', toString(label));
  };

  useEffect(() => {
    const userHasRole = hasRoleToCreateOrEdit();
    setHasRole(userHasRole);
    if (userHasRole) {
      const copyOwnershipID = parseQuery(query).copyOwnershipID;
      if (copyOwnershipID) {
        loadVessel(copyOwnershipID, true);
      } else {
        loadVessel(ownershipId, false);
      }
    } else setLoading(false);
  }, [ownershipId, query]);

  const ERROR_FAILED_TO_CREATE_MESSAGE = {
    header: 'Vessel',
    message: vessel && vessel.id ? 'Edit process failed!' : 'Add process failed!',
  };

  const hasRoleToCreateOrEdit = () => {
    return (vesselId && props.roleConfig.vessel.edit) || props.roleConfig.vessel.create;
  };

  function loadVessel(ownershipId, isCopy) {
    (async () => {
      setLoading(true);
      try {
        const { vessel, dropDownData, isAllApproved, userMemberships, distinctFieldData } =
          await controller.onLoadPage(step, ownershipId, props.username);

        setUserMemberships(userMemberships);
        const filteredDistinctValue = {
          vessel_short_code: distinctFieldData.vessel_short_code.filter(
            (value) => value !== vessel?.vessel_short_code,
          ),
          vessel_account_code_new: distinctFieldData.vessel_account_code_new.filter(
            (value) => value !== vessel?.vessel_account_code_new,
          ),
        };

        setFieldsDistinctValues(filteredDistinctValue);
        if (vessel) {
          if (isCopy) {
            vessel.name = vessel.name + ' Copy';
            vessel.vesselId = null;
            vessel.imo_number = null;
            controller.vesselId = null;
            controller.onVesselCopy(vessel);
          }
          vessel.life_boat_capacity = parseFloat(vessel.life_boat_capacity).toString();
          vessel.length_oa = parseFloat(vessel.length_oa).toString();
          vessel.length_bp = parseFloat(vessel.length_bp).toString();
          vessel.depth = parseFloat(vessel.depth).toString();
          vessel.breadth_extreme = parseFloat(vessel.breadth_extreme).toString();
          vessel.summer_draft = parseFloat(vessel.summer_draft).toString();
          vessel.summer_dwt = parseFloat(vessel.summer_dwt).toString();
          vessel.international_grt = parseFloat(vessel.international_grt).toString();
          vessel.international_nrt = parseFloat(vessel.international_nrt).toString();
          vessel.service_speed = parseFloat(vessel.service_speed).toString();
          retrieveTakeoverTrialData(vessel);
          setIntialFinancialData({
            wages_treatment: vessel.wages_treatment,
            wages_accumulation: vessel.wages_accumulation,
            has_portage_bill: vessel.has_portage_bill,
          });

          setVessel(vessel);
          setInitialVessel(vessel);
          setIsAllApproved(isAllApproved);
        } else {
          onVesselChange('vessel_service_status_id', 2);
          onVesselChange('wages_treatment', 'pool');
          onVesselChange('wages_accumulation', 'no');
          onVesselChange('has_portage_bill', 'yes');
          onVesselChange('is_engine_consume_lng', null);
        }
        setDropDownData(dropDownData);
      } catch (error) {
        setError(error.message);
        console.error(error);
      }
      setLoading(false);
    })();
  }

  const onHideModalMessage = () => setModalMessage(null);

  const onVesselChange = (key, value) => {
    setVessel({
      ...vessel,
      ...controller.onVesselChange(key, value),
    });
  };

  const onCountryChange = async (country) => {
    if (country) {
      const getPortsResponse = await getPortsByCountry(`countryCode=${country.alpha2_code}`);
      const ports = getPortsResponse.data.ports.map((port) => port.name) ?? [];

      ports.sort((p1, p2) => (p1 > p2 ? 1 : -1));

      //to avoid prev port value (set prior to country field added in paris2) mix together with the country value set to give wrong country-port combination
      //clear the prev port anyway
      setVessel({
        ...vessel,
        flag_country: country.value,
        port_of_registry_text: null,
      });

      //this lines are important to "register" the change to the controller for later formulating the patch request
      controller.onVesselChange('flag_country', country.value);
      controller.onVesselChange('port_of_registry_text', null);

      setDropDownData({
        ...dropDownData,
        portOfRegistrys: ports,
      });
    } else {
      //clear the port when country dropdown is cleared
      setVessel({
        ...vessel,
        flag_country: null,
        port_of_registry_text: null,
      });

      //this lines are important to "register" the change to the controller for later formulating the patch request
      controller.onVesselChange('flag_country', null);
      controller.onVesselChange('port_of_registry_text', null);

      setDropDownData({
        ...dropDownData,
        portOfRegistrys: [],
      });
    }
  };

  const onSubmitVessel = async (values) => {
    eventTracker('submit', 'Submit Vessel');
    setLoading(true);
    try {
      const response = await controller.onSubmitVessel(values);
      const ownershipId = response.ownershipId;
      // proceed to detail page of the new/edited vessel
      history.push(`/vessel/ownership/details/${ownershipId}`);
    } catch (error) {
      setModalMessage(ERROR_FAILED_TO_CREATE_MESSAGE);
    } finally {
      setLoading(false);
    }
  };

  const onSubmitVesselOnly = async () => {
    try {
      const isVesselIdNull = this.vesselId !== null; // NOSONAR
      //No sonar scanning as removing this. may break the logic
      if (isVesselIdNull) {
        await controller.onSubmitVessel();
      }
    } catch (error) {}
  };

  const onTypeAheadInputValueChange = (key, value) => {
    setTypeAheadValues({ ...typeAheadValues, [key]: value });
  };

  const compareTypeAheadInputValues = () => {
    const errorList = { ...submitError };
    if (_.isEmpty(typeAheadValues)) {
      setSubmitError(errorList);
    } else {
      Object.keys(typeAheadValues).forEach((key) => {
        if (typeAheadValues[key] && !vessel[key]) {
          errorList[key] = `Please select the valid option for ${key}`;
        }
      });
      setSubmitError(errorList);
    }
  };

  useEffect(() => {
    compareTypeAheadInputValues();
  }, [typeAheadValues, vessel]);

  useEffect(() => {
    if (!_.isEmpty(intialFinancialData)) {
      setVessel({ ...vessel, ...intialFinancialData });
    }
  }, [step]);

  const NavBar = (props) => {
    const steps = [
      { link: 'basic', title: 'Basic' },
      { link: 'financial', title: 'Financial' },
      { link: 'contact', title: 'Contact' },
      { link: 'particulars', title: 'Particulars' },
      { link: 'office-data', title: 'Office Data' },
      { link: 'miscellaneous', title: 'Miscellaneous' },
      { link: 'photos', title: 'Photos' },
      { link: 'trial-data', title: 'Trial Data' },
    ];

    const NavBarItem = ({ data }) => {
      return (
        <Col className="p-0 m-0">
          <NavigationBarItem
            link={data.link}
            vesselId={vesselId}
            ownershipId={ownershipId}
            title={data.title}
            column
            sm={2}
            isActive={step === data.link}
            onValidateLink={props.validate}
            eventTracker={eventTracker}
          />
        </Col>
      );
    };

    NavBarItem.propTypes = {
      data: PropTypes.object,
    };

    return (
      <Row className="nav-bar">
        {steps.map((item, index) => {
          return <NavBarItem key={index} data={item} />;
        })}
      </Row>
    );
  };

  function parseQuery(queryString) {
    const query = {};
    const pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
    pairs.forEach((item) => {
      const pair = item.split('=');
      query[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
    });
    return query;
  }

  const onSubmit = (values) => {
    setTriedValidate(true);
    if (_.isEmpty(submitError)) {
      onSubmitVessel({
        ...values,
        is_engine_consume_lng: values.is_engine_consume_lng?.toString(),
      });
    }
  };

  const getSchema = () => {
    let schema = {
      owner_id: yup.string().required(),
      vessel_type_id: yup.string().required(),
      name: not_xss(yup.string().nullable()),
      techgroup: yup.string().when(['id'], {
        is: (id) => {
          return !id;
        },
        then: (schema) => yup.string().required(),
      }),
      vessel_hull_number: not_xss(yup.string().nullable()),
      call_sign: not_xss(yup.string().nullable()),
      vessel_tec_fac_code: not_xss(
        yup.string().max(5, 'Vessel Tel FAC Code must be at most 5 characters').nullable(),
      ),
      vessel_account_code: yup.number().min(0).nullable(),
      bhp: yup.number().positive().integer().min(0).nullable(),
      dwt: yup.number().positive().integer().min(0).nullable(),
      primary_email: not_xss(
        yup.string().nullable().matches(EMAIL_REGEXP, 'Invalid primary E-mail address'),
      ),
      primary_satc_email: not_xss(
        yup.string().nullable().matches(EMAIL_REGEXP, 'Invalid primary Sat C E-mail address'),
      ),
      vessel_short_code: not_xss(
        yup.string().max(3, 'Vessel Short Code must be at most 3 characters').nullable(),
      ).test(
        'vessel_short_code',
        'Duplicate input for Vessel Short Code',
        (value) => !fieldsDistinctValues?.vessel_short_code?.includes(value),
      ),
      vessel_account_code_new: not_xss(
        yup
          .string()
          .matches(/^\d+$/, 'Must contains only numbers')
          .min(4, 'Must be exactly 4 digits')
          .max(4, 'Must be exactly 4 digits')
          .nullable(),
      ).test(
        'vessel_account_code_new',
        'Duplicate input for Vessel Account Code New',
        (value) => !fieldsDistinctValues?.vessel_account_code_new?.includes(value),
      ),

      ...TrialFormModel,
      wages_treatment: yup.string().required(),
      has_portage_bill: yup.string().required(),
      is_engine_consume_lng: yup
        .string()
        .when(['status'], {
          is: (status) => {
            return status !== null && status !== undefined && status !== 'draft';
          },
          then: (schema) => yup.string().required('Please select Engine Consumes LNG?').nullable(),
        })
        .nullable(),
      lng_engine_category_main_engine: yup
        .string()
        .when(['is_engine_consume_lng', 'lng_engine_category_diesel_generator'], {
          is: (is_engine_consume_lng, lng_engine_category_diesel_generator) => {
            return is_engine_consume_lng === 'true' && !lng_engine_category_diesel_generator;
          },
          then: yup.string().required('Please select LNG Engine Category').nullable(),
        })
        .nullable(),
      lng_engine_category_diesel_generator: yup
        .string()
        .when(['is_engine_consume_lng', 'lng_engine_category_main_engine'], {
          is: (is_engine_consume_lng, lng_engine_category_main_engine) => {
            return is_engine_consume_lng === 'true' && !lng_engine_category_main_engine;
          },
          then: yup.string().required('Please select LNG Engine Category').nullable(),
        })
        .nullable(),
      ihm_provider_id: yup
        .string()
        .when(['status'], {
          is: (status) => {
            return status !== null && status !== undefined && status == 'active';
          },
          then: (schema) => yup.string().required('Please select IHM Provider').nullable(),
        })
        .nullable(),
      ihm_inspection_date: yup
        .string()
        .when(['status'], {
          is: (status) => {
            return status !== null && status !== undefined && status == 'active';
          },
          then: (schema) =>
            yup.string().required('Please select IHM Initial Inspection Date').nullable(),
        })
        .nullable(),
    };

    return yup.object().shape(
      schema,
      // Dependency cycle when validating objects. Pairing. Please see: https://github.com/jquense/yup/issues/193 for more information
      DependencyValidationPairing,
    );
  };

  const routeToListPage = () => {
    eventTracker('routeToListPage', 'To List Page');
    history.push(`/vessel/active-vessels`);
  };
  const [show, setModalShow] = useState(false);
  const handleModalClose = () => setModalShow(false);
  const renderView = () => {
    return hasRole && !vessel.isOwnershipChangePending ? (
      <Container>
        {error ? (
          error
        ) : (
          <Formik
            validationSchema={getSchema()}
            onSubmit={onSubmit}
            enableReinitialize={true}
            initialValues={vessel}
            validateOnChange={triedValidate}
            validateOnBlur={triedValidate}
          >
            {({
              handleSubmit,
              handleChange,
              handleBlur,
              values,
              touched,
              isValid,
              errors,
              setFieldValue,
              validateForm,
              validateField,
            }) => {
              const onValidateLink = (link) => {
                if (controller.step === link) {
                  return false;
                }
                if (!controller.vesselId) {
                  if (controller.step !== 'basic') {
                    controller.step = link;
                    return true;
                  }
                  validateForm().then((errors) => {
                    setTriedValidate(true);
                    const basicFormErrors = getBasicFormErrors(errors);
                    if (Object.keys(basicFormErrors).length > 0) {
                      setModalMessage(PROCEED_OTHER_TAB_FAILED_MESSAGE);
                    } else {
                      controller.step = link;
                      history.push(`/vessel/takeover/${link}`);
                    }
                  });

                  return false;
                }
                controller.step = link;
                return true;
              };

              const onInputChange = async (event) => {
                const errorList = _.omit(submitError, [event.target.name]);
                setSubmitError(errorList);
                if (event.target.name === 'is_engine_consume_lng') {
                  if (event.target.value !== 'true') {
                    onVesselChange('lng_engine_category_main_engine', null);
                    onVesselChange('lng_engine_category_diesel_generator', null);
                  }
                  setSubmitError([]);
                  validateForm();
                }
                await new Promise((resolve) => {
                  onVesselChange(event.target.name, event.target.value);
                  resolve();
                });
                if (event.targetOption) {
                  onCountryChange(event.targetOption[0]);
                }

                handleChange(event);
              };

              const onDateChange = (key, value) => {
                const errorList = _.omit(submitError, [key]);
                setSubmitError(errorList);
                onVesselChange(key, value);
              };

              const onInputArrayChange = async (arrayId, rowId, event) => {
                const key = event.target.name;
                const value = event.target.value;
                const arrayData = [...(vessel[arrayId] || [])];

                if (rowId >= arrayData.length) {
                  arrayData.push({
                    [key]: value,
                  });
                } else {
                  const itemData = arrayData[rowId];
                  itemData[key] = value;
                }

                await new Promise((resolve) => {
                  onVesselChange(arrayId, arrayData);
                  resolve();
                });

                setFieldValue(arrayId, arrayData);
                if (['phone_number', 'phone_type_id', 'email_type_id', 'email'].includes(key)) {
                  handleArrayInputChangeList();
                }
              };

              const onInputArrayRemoveRow = async (arrayId, rowId, event) => {
                const arrayData = [...(vessel[arrayId] || [])];

                if (rowId > -1) {
                  arrayData.splice(rowId, 1);
                }

                await new Promise((resolve) => {
                  onVesselChange(arrayId, arrayData);
                  resolve();
                });
                setFieldValue(arrayId, arrayData);
                handleArrayInputChangeList();
              };

              const handleTrialInput = (uuid, event, rowTrialType) => {
                const { name: key, value } = event.target;
                const newValue = vessel[rowTrialType].find((shopTrial) => shopTrial.id === uuid);
                newValue[key] = value;
                if (value === '') delete newValue[key];
                onVesselChange(rowTrialType, vessel[rowTrialType]);
                setFieldValue(rowTrialType, vessel[rowTrialType]);
                if (value !== '') {
                  const errorList = _.omit(submitError, [rowTrialType]);
                  setSubmitError(errorList);
                }
                validateForm();
              };

              const handleSeaTrialAddSection = () => {
                const defaultSeaTrialVal = { id: v4() };
                vessel.sea_trials = [defaultSeaTrialVal];
                onVesselChange('sea_trials', [defaultSeaTrialVal]);
                setFieldValue('sea_trials', [defaultSeaTrialVal]);

                onVesselChange('is_adding_new_sea_trials', true);
                setFieldValue('is_adding_new_sea_trials', true);
              };

              const handleUndoSeaTrialAddSection = (archivedSeaTrial) => {
                vessel.sea_trials = archivedSeaTrial;
                onVesselChange('sea_trials', archivedSeaTrial);
                setFieldValue('sea_trials', archivedSeaTrial);
              };

              const handleAddTrialRowInput = (rowTrialType) => {
                const newTrialRows = [...vessel[rowTrialType], { id: v4() }];
                onVesselChange(rowTrialType, newTrialRows);
                setFieldValue(rowTrialType, newTrialRows);
              };

              const handleRemoveTrialRowInput = (uuid, rowTrialType) => {
                const newTrialRows = [...vessel[rowTrialType]].filter(
                  (trialData) => trialData.id !== uuid,
                );
                onVesselChange(rowTrialType, newTrialRows);
                setFieldValue(rowTrialType, newTrialRows);
              };

              const onCheckChange = (event) => {
                const key = event.target.name;
                const value = event.target.value;
                onVesselChange(key, value);
                setFieldValue(key, value);
              };

              const onCustomInputChange = (key, value) => {
                onVesselChange(key, value);
                setFieldValue(key, value);
              };

              const saveButtonText = () => {
                return vessel.id ? 'Save' : 'Save and Request for approval';
              };

              const onSave = (event) => {
                let errorList = _.omit(submitError, [event?.target?.name]);
                validateForm().then((errors) => {
                  errorList = { ...errorList, ...errors };
                  setSubmitError(errorList);
                });
                handleSubmit();

                const timeSpent = moment
                  .duration(moment.utc(moment().valueOf() - editStartTime).format('HH:mm:ss'))
                  .asSeconds();
                eventTracker('editVessel', `${timeSpent}s`);
              };

              const formPageProps = {
                vessel,
                dropDownData,
                errors,
                isAllApproved,
                onCheckChange,
                onCustomInputChange,
                onInputChange,
                handleBlur,
                onInputArrayChange,
                onInputArrayRemoveRow,
                onDateChange,
                userMemberships,
                fieldsDistinctValues,
                submitError,
                onTypeAheadInputValueChange,
                disableFields,
                setDisableFields,
                initialVessel,
              };

              const handleArrayInputChangeList = () => {
                validateForm().then((errorsList) => {
                  setSubmitError({ ...errorsList });
                });
              };

              return (
                <Container>
                  <Row className="closeIcon">
                    <BreadcrumbHeader
                      items={breadCrumbsItems}
                      activeItem={vesselId ? 'Edit a Vessel' : 'Create a Vessel'}
                      onClick={onBreadcrumbClick}
                    />
                  </Row>

                  <NavBar validate={onValidateLink} />

                  <TakeoverFormErrorList errors={submitError} />

                  <Form noValidate onSubmit={onSave}>
                    <Switch>
                      <Route
                        exact
                        path={[
                          '/vessel/:vesselId?/:ownershipId?/takeover/basic',
                          '/vessel/:vesselId?/:ownershipId?/takeover',
                        ]}
                      >
                        <TakeoverBasicForm {...formPageProps} onVesselChange={onVesselChange} />
                      </Route>
                      <Route exact path="/vessel/:vesselId?/:ownershipId?/takeover/financial">
                        <TakeoverFinancialForm {...formPageProps} />
                      </Route>
                      <Route exact path="/vessel/:vesselId?/:ownershipId?/takeover/contact">
                        <TakeoverContactForm {...formPageProps} />
                      </Route>
                      <Route exact path="/vessel/:vesselId?/:ownershipId?/takeover/particulars">
                        <TakeoverParticularsForm {...formPageProps} />
                      </Route>
                      <Route exact path="/vessel/:vesselId?/:ownershipId?/takeover/office-data">
                        <TakeoverOfficeDataForm {...formPageProps} />
                      </Route>
                      <Route exact path="/vessel/:vesselId?/:ownershipId?/takeover/miscellaneous">
                        <TakeoverMiscellaneousForm {...formPageProps} />
                      </Route>
                      <Route exact path="/vessel/:vesselId?/:ownershipId?/takeover/photos">
                        <TakeoverPhotoForm
                          {...formPageProps}
                          onSubmitVesselOnly={onSubmitVesselOnly}
                        />
                      </Route>
                      <Route exact path="/vessel/:vesselId?/:ownershipId?/takeover/trial-data">
                        <TakeoverTrialForm
                          {...formPageProps}
                          onTrialRowInput={handleTrialInput}
                          onSubmitVesselOnly={onSubmitVesselOnly}
                          onAddMoreTrial={handleAddTrialRowInput}
                          onRemoveTrial={handleRemoveTrialRowInput}
                          onAddSeaTrialSection={handleSeaTrialAddSection}
                          onUndoSeaTrialAddSection={handleUndoSeaTrialAddSection}
                        />
                      </Route>
                    </Switch>
                    <BottomButton title={saveButtonText()} onClick={onSave} />
                  </Form>
                </Container>
              );
            }}
          </Formik>
        )}
        <Modal show={modalMessage !== null} onHide={onHideModalMessage} centered>
          <Modal.Header>
            <Modal.Title className="h5">{modalMessage?.header}</Modal.Title>
          </Modal.Header>
          <Modal.Body>{modalMessage?.message}</Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={onHideModalMessage}>
              Close
            </Button>
          </Modal.Footer>
        </Modal>
        <Modal show={show} centered>
          <Modal.Header>
            <Modal.Title className="h5">You Have Unsaved Input Data</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            Are you sure to quit this page? You will lose your unsaved input data.
          </Modal.Body>
          <Modal.Footer>
            <Button variant="primary" onClick={handleModalClose}>
              Cancel
            </Button>
            <Button variant="secondary" onClick={routeToListPage}>
              Confirm
            </Button>
          </Modal.Footer>
        </Modal>
      </Container>
    ) : (
      checkVesselOwnerShippending(vessel.isOwnershipChangePending)
    );
  };

  const renderTakerOverView = (statusCode) => {
    if ([404, 500].includes(statusCode)) {
      return <ErrorPage errorCode={statusCode} />;
    } else {
      return renderView();
    }
  };

  const checkVesselOwnerShippending = (pendingStatus) => {
    if (pendingStatus) {
      return <ErrorPage errorCode={404} />;
    } else {
      return <ErrorPage errorCode={403} />;
    }
  };

  return loading ? (
    <Container>
      <Spinner />
    </Container>
  ) : (
    renderTakerOverView(vessel.status)
  );
};

Takeover.propTypes = {
  ga4react: PropTypes.object,
  roleConfig: PropTypes.object,
  username: PropTypes.string,
  validate: PropTypes.any,
};

export default Takeover;
