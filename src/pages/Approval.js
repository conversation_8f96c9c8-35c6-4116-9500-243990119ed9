/* eslint-disable react/prop-types */
import React, { useState, useEffect, useMemo } from 'react';
import { Container, Col, Row, ButtonGroup, Modal, Button, Alert } from 'react-bootstrap';
import { useParams, useHistory } from 'react-router-dom';
import vesselService from '../service/vessel-service';
import vesselApprovalService from '../service/vessel-approval-service';
import ErrorAlert from '../component/ErrorAlert';
import { checkReviewStatus, getShortDate, sortOnGivenOrder } from '../util/view-utils';
import styleGuide from '../styleGuide';
import { Approve } from '../component/approval/Approve';
import { Reject } from '../component/approval/Reject';
import { Rework } from '../component/approval/Rework';
import { MoveToNewStatusSection } from '../component/approval/MoveToNewStatusSection';
import {
  FIRST_APPROVERS_GROUPS,
  FINAL_APPROVER,
  TECH_GROUP,
  approvalStatuses,
  vesselStatuses,
  MISSING_FIELD_DESCRIPTIONS,
  REQUIRED_FIELDS,
  REQUIRED_OWNERSHIP_FIELDS,
} from '../model/constants';
import Spinner from '../component/Spinner';
import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';

const { PARIS2_URL } = process.env;
const { Icon } = styleGuide;

const Approval = (props) => {
  const history = useHistory();
  let { vesselId } = useParams();
  const [vesselData, setVesselData] = useState(null);
  const [approvalData, setApprovalData] = useState(null);
  const [error, setError] = useState(null);
  const [actionLoadingFor, setActionLoadingFor] = useState({ rowId: null });
  const [uiProps, setUiProps] = useState(null);
  const [isFinalApprovalPending, setIsFinalApprovalPending] = useState(true);
  const [missingFields, setMissingFields] = useState(null);
  const [isPageViewInvoked, setPageViewInvoked] = useState(false);
  const startTime = moment().valueOf();
  const [staffData, setStaffData] = useState(null);
  const [showDateRequiredError, setShowDateRequiredError] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        props.ga4react?.pageview(history.location.pathname, '', 'Approval: Takeover a vessel');
        setPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  const ga4EventTrigger = (action, category, label) => {
    try {
      props.ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type, value) => {
    switch (type) {
      case 'buttonClick':
        ga4EventTrigger(value, 'Vessel Approval', 'Vessel Approval');
        break;
      case 'routeTo':
        ga4EventTrigger('Breadcrumb', 'Nav', `${title} - ${value}`);
        break;
      case 'createVessel':
        ga4EventTrigger(
          'Time Spent',
          `Vessel Approval - ${value}${moment
            .duration(moment.utc(moment().valueOf() - startTime).format('HH:mm:ss'))
            .asSeconds()}s`,
          `Vessel Approval - ${vesselData?.name}`,
        );
        break;
      default:
        ga4EventTrigger('Click', 'Vessel Approval', value);
        break;
    }
  };

  useEffect(() => {
    fetchVesselData();
  }, []);

  const fetchVesselData = async () => {
    try {
      setLoading(true);
      let vesselResponse = await vesselService.getVessel(vesselId);
      const vessel = vesselResponse.data;
      vessel.misc_classification_euets_verifier_id =
        vessel.ownerships[0]?.vessel_class_regulation[0]?.class_id;
      setVesselData(vessel);
      setStaffData(vessel.ownerships[0].fleet_staff?.[0]);
      let approvalResponse = await vesselApprovalService.getVesselApprovalData(vessel);
      setApprovalData(approvalResponse.data);

      let finalApprovalPending = getFinalApprovalPending(approvalResponse.data);
      setIsFinalApprovalPending(finalApprovalPending);
      const pendingStatus = vessel.pending_status ? vessel.pending_status : vessel.status;
      if (pendingStatus !== vesselStatuses.ARCHIVED)
        setMissingFieldsData(vessel, finalApprovalPending);

      setUiProps(getPropsForPageAction(pendingStatus));
    } catch (error) {
      setError('Oops, something went wrong. Please try again.');
      console.error(`vessel service error for ${vesselId}. Error: ${error}`);
    }
    setLoading(false);
  };

  const breadCrumbsItems = useMemo(
    () => [
      { title: 'Vessel', label: 'To List Page', link: `${PARIS2_URL}/vessel` },
      vesselId
        ? {
            title: vesselData?.name ?? '- - -',
            label: 'Details',
            link: `/vessel/ownership/details/${vesselData?.ownerships[0].id}`,
          }
        : null,
      {
        title: uiProps?.pageHeader,
        label: 'Approval',
        link: '#',
      },
    ],
    [uiProps, vesselData],
  );

  const onBreadcrumbClick = (label) => {
    eventTracker('routeTo', _.toString(label));
  };

  const setMissingFieldsData = (vessel, finalApprovalPending) => {
    const ownership = vessel.ownerships.length ? vessel.ownerships[0] : null;
    if (!finalApprovalPending) {
      const requiredFields = REQUIRED_FIELDS;
      const requiredOwnershipFields = REQUIRED_OWNERSHIP_FIELDS;
      const missedFields = requiredFields.filter((field) => vessel[field] == null);
      const missedOwnershipFields = requiredOwnershipFields.filter(
        (field) => _.get(vessel.ownerships[0], field, null) == null,
      );
      missedOwnershipFields.forEach((key) => missedFields.push(key));
      if (vessel['year_of_delivery'] == null && vessel['date_of_delivery'] == null) {
        missedFields.push('year_built_date_of_delivery');
      }
      if (!ownership['phones'] || ownership['phones'].length == 0) {
        missedFields.push('phones');
      }
      if (!ownership['emails'] || ownership['emails'].length == 0) {
        missedFields.push('emails');
      }
      setMissingFields(missedFields);
    }
  };

  const getFinalApprovalPending = (approvalData) => {
    const finalApprover = approvalData.find((data) => data.final_approver);
    return finalApprover && finalApprover.approval_status != approvalStatuses.APPROVED;
  };

  const visitDetailsPage = (vesselId) => {
    history.push(`/vessel/ownership/details/${vesselId}`);
  };

  const ButtonsOnStatus = (props) => {
    switch (props.rowData.approval_status) {
      case approvalStatuses.PENDING:
        return (
          <span>
            <ButtonGroup className="mr-2">
              <Approve
                dataTestId="fml-approval-approve"
                vessel={vesselData}
                approvalId={props.rowData.id}
                renderTable={() => fetchVesselData()}
                setLoadingFor={setActionLoadingFor}
                isFinalApprover={props.rowData.final_approver}
                eventTracker={eventTracker}
              />
            </ButtonGroup>
            {props.rowData.final_approver ? (
              <ButtonGroup className="mr-2">
                <Reject
                  dataTestId="fml-approval-reject"
                  vessel={vesselData}
                  approvalId={props.rowData.id}
                  renderTable={() => fetchVesselData()}
                  setLoadingFor={setActionLoadingFor}
                  eventTracker={eventTracker}
                />
              </ButtonGroup>
            ) : null}
          </span>
        );
      case approvalStatuses.REJECTED:
        return (
          <span>
            <ButtonGroup className="mr-1">
              <Rework
                vessel={vesselData}
                group={props.rowData.department}
                approvalId={props.rowData.id}
                renderTable={() => fetchVesselData()}
                setLoadingFor={setActionLoadingFor}
                eventTracker={eventTracker}
              />
            </ButtonGroup>
          </span>
        );
      default:
        return null;
    }
  };

  ButtonsOnStatus.propTypes = {
    rowData: PropTypes.object,
  };

  const TableHeaderRow = (props) => {
    return (
      <tr key="header">
        <th scope="col">{props.approverType}</th>
        <th scope="col">Name</th>
        <th scope="col">Last Update</th>
        <th scope="col">Status</th>
        <th scope="col">Remarks</th>
      </tr>
    );
  };

  TableHeaderRow.propTypes = {
    approverType: PropTypes.strng,
  };

  const hasApproverRoleInGroup = (rowData, roleConfig, vesselTechGroup) => {
    const isFinalApproverInFinalRow =
      rowData.department === FINAL_APPROVER && roleConfig.finalApprover;
    const rowGroupInApprovalGroups = roleConfig.approvalGroups.includes(rowData.department);

    if (isFinalApproverInFinalRow) return true;
    else
      return rowData.department === TECH_GROUP
        ? rowGroupInApprovalGroups &&
            roleConfig.vessel.approve &&
            roleConfig.techGroups.techGroups.includes(vesselTechGroup)
        : rowGroupInApprovalGroups;
  };

  const getDepartmentForRow = (dataForRole, techgroup) => {
    if (dataForRole.final_approver) return FINAL_APPROVER;
    else {
      return dataForRole.department === TECH_GROUP && techgroup
        ? techgroup
        : dataForRole.department;
    }
  };

  const TableDataRows = ({ techgroup, data, roleConfig, dataTestId }) => {
    return sortOnGivenOrder(data, FIRST_APPROVERS_GROUPS, 'department').map((dataForRole) => {
      const isRowStatusNotPending = dataForRole.approval_status !== approvalStatuses.PENDING;
      const department = getDepartmentForRow(dataForRole, techgroup);
      return (
        <tr key={dataForRole.id} className={dataForRole.approval_status}>
          <th>
            <span>{department}</span>
            {hasApproverRoleInGroup(dataForRole, roleConfig, techgroup) ? (
              <ButtonsOnStatus rowData={dataForRole} />
            ) : null}
          </th>
          <td>{isRowStatusNotPending ? dataForRole.changed_by_user : ''}</td>
          <td>
            {isRowStatusNotPending && dataForRole.updated_at
              ? getShortDate(dataForRole.updated_at)
              : ''}
          </td>
          <td data-testid={`${dataTestId}-status`}>
            {actionLoadingFor.rowId === dataForRole.id ? (
              <Spinner alignClass="align-left" />
            ) : (
              checkReviewStatus(dataForRole)
            )}
          </td>
          <td>
            {isRowStatusNotPending && dataForRole.remarks ? (
              <Remarks remarksData={dataForRole.remarks} />
            ) : (
              ''
            )}
          </td>
        </tr>
      );
    });
  };

  const approvalUIProps = [
    {
      action: vesselStatuses.ACTIVE,
      pageHeader: 'Approval: Takeover a vessel',
      dateSelectionWarning:
        'Select final takeover date first before moving vessel to active vessel list.',
      lastSection: {
        hasRoleToMove: props.roleConfig.vessel.moveToActive,
        header: '3. MOVE VESSEL TO ACTIVE LIST',
        topHeader: 'MOVE VESSEL TO ACTIVE LIST',
        buttonText: 'Submit',
        modalTitle: 'Confirm moving this vessel page to Active Vessel List?',
        confirmationText: 'This vessel has been moved to Active Vessel List.',
        showDate: true,
        dateKey: 'date_of_takeover',
        dateLabel: '(Actual) Date of Takeover*',
      },
    },
    {
      action: vesselStatuses.HANDED_OVER,
      pageHeader: 'Approval: Hand over a vessel',
      dateSelectionWarning:
        'Select final takeover date first before moving vessel to handover list.',
      lastSection: {
        hasRoleToMove: props.roleConfig.vessel.moveToHandover,
        header: '3. MOVE VESSEL TO HANDED OVER LIST',
        topHeader: 'MOVE VESSEL TO HANDED OVER LIST',
        buttonText: 'Submit',
        modalTitle: 'Confirm moving this vessel page to Handed over Vessel List?',
        confirmationText: 'This vessel has been moved to Handed Over Vessel List.',
        showDate: true,
        dateKey: 'date_of_handover',
        dateLabel: '(Actual) Date of Handover*',
      },
    },
    {
      action: vesselStatuses.ARCHIVED,
      pageHeader: 'Approval: Archive a vessel',
      lastSection: {
        hasRoleToMove: props.roleConfig.vessel.moveToArchival,
        header: '3. MOVE VESSEL TO ARCHIVE LIST',
        buttonText: 'Move to Archived Vessels',
        modalTitle: 'Confirm moving this vessel page to Archived Vessel List?',
        confirmationText: 'This vessel has been moved to Archived Vessel List.',
      },
    },
  ];

  const getPropsForPageAction = (action) => {
    return approvalUIProps.find(function (element) {
      return element.action == action;
    });
  };

  const PageView = () => {
    return (
      <>
        <Row>
          <Col>
            <div className="flex-between">
              <BreadcrumbHeader
                items={breadCrumbsItems}
                activeItem={uiProps?.pageHeader}
                onClick={onBreadcrumbClick}
              />
              <Icon
                icon="close"
                size={30}
                onClick={visitDetailsPage.bind(this, vesselData?.ownerships[0]?.id)}
              />
            </div>
          </Col>
        </Row>
        {loading ? (
          <Spinner />
        ) : (
          <>
            {missingFields && missingFields.length !== 0 && (
              <Row>
                <Alert className="approval_error_list" variant="danger">
                  <Alert.Heading>
                    To move vessel to {uiProps?.action?.replace(/_/g, ' ') || ''} list, correct the following in the vessel detail page:
                  </Alert.Heading>
                  <ul>
                    {missingFields.map((error, index) => {
                      return <li key={index}>{MISSING_FIELD_DESCRIPTIONS[error]}</li>;
                    })}
                  </ul>
                </Alert>
              </Row>
            )}
            {showDateRequiredError && (
              <Row>
                <Alert className="approval_error_list" variant="danger">
                  <Alert.Heading>{uiProps?.dateSelectionWarning}</Alert.Heading>
                </Alert>
              </Row>
            )}
            {uiProps?.lastSection?.hasRoleToMove &&
            !isFinalApprovalPending &&
            missingFields?.length === 0 ? (
              <Row>
                <Col>
                  <hr></hr>
                  <h6 className="vessel_approval__section-header">
                    {uiProps.lastSection.topHeader}{' '}
                  </h6>
                  <MoveToNewStatusSection
                    approvalData={approvalData}
                    vessel={vesselData}
                    renderTable={() => fetchVesselData()}
                    uiProps={uiProps.lastSection}
                    isFinalApprovalPending={isFinalApprovalPending}
                    eventTracker={eventTracker}
                    disableDate={isFinalApprovalPending || missingFields?.length !== 0}
                    setShowDateRequiredError={setShowDateRequiredError}
                  />
                </Col>
              </Row>
            ) : null}
            {approvalData ? (
              <>
                <Row>
                  <Col>
                    <hr></hr>
                    <h6 className="vessel_approval__section-header">1. REVIEW</h6>
                    <table className="table" id="test__firstApproval">
                      <thead>
                        <TableHeaderRow approverType="Reviewer" />
                      </thead>
                      <tbody>
                        <TableDataRows
                          techgroup={staffData?.tech_group}
                          data={approvalData.filter((data) => data.final_approver === false)}
                          roleConfig={props.roleConfig}
                          dataTestId="fml-approval-reviewer"
                        />
                        <tr></tr>
                      </tbody>
                    </table>
                  </Col>
                </Row>
                <Row>
                  <Col>
                    <hr></hr>
                    <h6 className="vessel_approval__section-header">2. FINAL APPROVAL</h6>
                    <table className="table" id="test__finalApproval">
                      <thead>
                        <TableHeaderRow approverType="Approver" />
                      </thead>
                      <tbody>
                        <TableDataRows
                          techgroup={staffData?.tech_group}
                          data={approvalData.filter((data) => data.final_approver === true)}
                          roleConfig={props.roleConfig}
                          dataTestId="fml-approval-final-approver"
                        />
                        <tr></tr>
                      </tbody>
                    </table>
                  </Col>
                </Row>

                {uiProps?.lastSection?.hasRoleToMove &&
                (isFinalApprovalPending || missingFields?.length !== 0) ? (
                  <Row>
                    <Col>
                      <h6 className="vessel_approval__section-header">
                        {uiProps.lastSection.header}{' '}
                      </h6>
                      <MoveToNewStatusSection
                        approvalData={approvalData}
                        vessel={vesselData}
                        renderTable={() => fetchVesselData()}
                        uiProps={uiProps.lastSection}
                        isFinalApprovalPending={isFinalApprovalPending}
                        eventTracker={eventTracker}
                        disableDate={isFinalApprovalPending || missingFields?.length !== 0}
                        setShowDateRequiredError={setShowDateRequiredError}
                      />
                    </Col>
                  </Row>
                ) : null}
              </>
            ) : null}
          </>
        )}
      </>
    );
  };

  return (
    <div className="vessel_approval">
      <Container>
        {error ? <ErrorAlert message={error} /> : null}
        {uiProps ? <PageView /> : null}
      </Container>
    </div>
  );
};

Approval.propTypes = {
  ga4react: PropTypes.shape({
    pageview: PropTypes.func,
    event: PropTypes.func,
    ...PropTypes.objectOf(PropTypes.func),
  }),
  roleConfig: PropTypes.object,
};

export default Approval;

const Remarks = (props) => {
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  return (
    <>
      <button type="button" className="btn btn-link p-0 text-left" onClick={handleShow}>
        <u>
          {props.remarksData.length > 40
            ? props.remarksData.substring(0, 40).trim().concat('...')
            : props.remarksData}
        </u>
      </button>

      <Modal className="action-modal" show={show} onHide={handleClose} centered>
        <Modal.Header>
          <Modal.Title className="h5">Remarks</Modal.Title>
        </Modal.Header>
        <Modal.Body>{props.remarksData}</Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

Remarks.propTypes = {
  remarksData: PropTypes.string,
};
