/* eslint-disable react/prop-types */
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import {
  Al<PERSON>,
  Button,
  ButtonGroup,
  ButtonToolbar,
  Col,
  Container,
  Dropdown,
  DropdownButton,
  Modal,
  Row,
  Tab,
} from 'react-bootstrap';
import { useHistory, useParams } from 'react-router-dom';
import _, { toString } from 'lodash';
import vesselService from '../service/vessel-service';
import vesselApprovalService from '../service/vessel-approval-service';
import VesselBasicViewData from '../model/VesselBasicViewData';
import VesselContactViewData from '../model/VesselContactViewData';
import VesselParticularViewData from '../model/VesselParticularViewData';
import VesselOfficeViewData from '../model/VesselOfficeViewData';
import VesselMiscellaneousViewData from '../model/VesselMiscellaneousViewData';
import Spinner from '../component/Spinner';
import ShipReport from '../component/ShipReport';
import ErrorAlert from '../component/ErrorAlert';
import moment from 'moment';
import styleGuide from '../styleGuide';
import ScrollArrow from '../component/BackToTopButton';
import {
  BUSINESS,
  CHIEF_ENGINEER,
  EORB_PRODUCT_NAME,
  EORB_STATUSES,
  LOCAL_STORAGE_FIELDS,
  MASTER,
  OWNERSHIP_PENDING,
  vesselStatuses,
} from '../model/constants';
import {
  exportToExcel,
  exportTableToExcel,
  formatExcelData,
  formatCertificateData,
} from '../util/excel-export';
import {certificates_coloum} from '../util/certificates-export/certifictes-columns'

import {exportTableToExcel2} from '../util/certificates-export/excel-export-v2' 
import * as ownershipService from '../service/ownership-service';
import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import TabWrapper from '../component/TabWrapper';
import VesselItineraryList from './VesselItineraryList';
import Contingency from './Contingency';
import { getLocalStorage } from '../util/local-storage-helper';
import { columns } from '../component/ItineraryList/ItineraryTable';
import VesselEmergencyDrills from '../component/emergencyDrills/VesselEmergencyDrills';
import { formatValue } from '../util/view-utils';
import AssignDrills from '../component/emergencyDrills/AssignDrills';
import { DetailContext } from '../context/DetailContext';
import VesselCertificates from '../component/certificates/VesselCertificates';
import FlagChangeDialog from '../component/FlagChange/FlagChangeDialog';
import FlagChangeConfirmDialog from '../component/FlagChange/FlagChangeConfirmDialog';
import AssignReplaceUser from '../component/assignOfficeData/AssignReplaceUser';
import { VesselContext } from '../context/VesselContext';
import ManualsList from '../component/drawingsAndManuals/ManualsList';
import AgentsCharterer from '../component/agentsAndCharterer/AgentsCharterer';
import MonthlyFinancialReports from './MonthlyFinancialReports';
import ChangeLogModal from '../component/changeHistory/ChangeHistoryModal';
import { ASSIGNUSERSTATUS } from '../constants/assign-user';
import ownerService from '../service/owner-service';
import PhotoGallery from '../component/PhotoGallery';
import PropTypes from 'prop-types';

const { ErrorPage } = styleGuide;
const { PARIS2_URL, PARIS_ONE_HOST } = process.env;


const Details = (props) => {
  const history = useHistory();
  let { ownershipId } = useParams();
  const [vesselOwnership, setVesselOwnership] = useState(null);
  const [modalData, setModalData] = useState(null);
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleModalShow = () => setShow(true);
  const [vesselStatusUpdated, setVesselStatusUpdated] = useState(false);
  const [approvalProps, setApprovalProps] = useState(null);
  const [vesselViewData, setVesselViewData] = useState(null);
  const [isOwnershipChangePending, setIsOwnershipChangePending] = useState(false);
  const [vesselData, setVesselData] = useState(null);
  const { step = 'general' } = useParams();
  const [activeTab, setActiveTab] = useState(step);
  const [isPageViewInvoked, setPageViewInvoked] = useState(false);
  const [lastPosition, setLastPosition] = useState({});
  const [lastItinerary, setLastItinerary] = useState([]);
  const [lastReport, setLastReport] = useState([]);
  const [master, setMaster] = useState(null);
  const [chiefEngineer, setChiefEngineer] = useState(null);
  const [vesselLists, setVesselLists] = useState([]);
  const [activePage, setActivePage] = useState('vessel-details');
  const [showFlagDialog, setShowFlagDialog] = useState(false);
  const [showFlagConfirmDialog, setShowFlagConfirmDialog] = useState(false);
  const [flagConfirmationData, setFlagConfirmationData] = useState({});
  const [drillExcelData, setDrillExcelData] = useState();
  const [displayState, setDisplayState] = useState('valid');
  const [disableOFRFinancialTab, setDisableOFRFinancialTab] = useState(false);
  const [disableOFRReopenReport, setDisableOFRReopenReport] = useState(false);
  const {
    setVesselName,
    setVesselEmail,
    setOwnershipId,
    excelColumns,
    ga4EventTrigger = () => {},
    roleConfig,
    setIsCurrentOwner,
    isAccessDenied,
    invalidVessel,
    handleError = () => {},
    setInvalidVessel,
    error,
    setError = () => {},
  } = useContext(DetailContext);
  const { assignUserActionStatus, setAssignUserActionStatus } = useContext(VesselContext);
  const { UPDATED_STATUS } = ASSIGNUSERSTATUS;
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [ownerVesselPreferenceLength, setOwnerVesselPreferenceLength] = useState(false)

  const [isLoading, setIsLoading] = useState(false);

  const DetailPageTabData = () => [
    {
      eventKey: 'general',
      tabName: 'General',
    },
    {
      eventKey: 'itinerary',
      tabName: 'Itinerary',
    },
    {
      eventKey: 'contingency',
      tabName: 'Contingency',
    },
    {
      eventKey: 'emergency-drills',
      tabName: 'Emergency Drills',
      hideTab: !roleConfig.drills.view,
    },
    {
      eventKey: 'manuals',
      tabName: 'Manuals',
    },
    {
      eventKey: 'certificates',
      tabName: 'Surveys and Certificates',
    },
    {
      eventKey: 'agents',
      tabName: 'Agents',
    },
    {
      eventKey: 'charterer',
      tabName: 'Charterer',
    },
    {
      eventKey: 'monthlyFinancialReports',
      tabName: 'Financial Reports',
      hideTab: !roleConfig.ownerReporting.view || !roleConfig.ownerReporting.canViewOFRTab ||
      !ownerVesselPreferenceLength,
    },
  ];

  useEffect(() => {
    if (
      (!roleConfig.ownerReporting.canViewOFRTab || disableOFRFinancialTab) &&
      history.location.pathname.includes('monthlyFinancialReports')
    ) {
      history.push(`/vessel/ownership/details/${ownershipId}`);
    }
  }, [history.location.pathname, disableOFRFinancialTab]);

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        props.ga4react?.pageview(history.location.pathname, '', 'Vessel Details');
        setPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  const breadCrumbsItems = useMemo(
    () => [
      { title: 'Vessel', label: 'To List Page', link: `${PARIS2_URL}/vessel` },
      {
        title: vesselOwnership?.name ?? '- - -',
        label: 'Details',
        link: '#',
      },
    ],
    [ownershipId, vesselOwnership],
  );

  const eventTracker = (type, value) => {
    switch (type) {
      case 'tabNavigation':
        ga4EventTrigger('Tab', 'Vessel Details - Nav', value);
        break;
      case 'navItemClick':
        ga4EventTrigger('Anchor Link', 'Nav', `Vessel Details - ${value}`);
        break;
      case 'back':
        ga4EventTrigger('Back', 'Nav', 'Vessel Details');
        break;
      case 'approve':
        ga4EventTrigger('View', 'Approval Details', `Vessel Details - ${value}`);
        break;
      case 'more':
        ga4EventTrigger('More', 'Menu', 'Vessel List Page');
        break;
      case 'exportToExcel':
        ga4EventTrigger('Export to Excel', 'Vessel Menu', 'Vessel List Page');
        break;
      case 'print':
        ga4EventTrigger('Print', 'Vessel Menu', 'Vessel List Page');
        break;
      case 'handover':
        ga4EventTrigger('Hand Over Vessel', 'Vessel Menu', 'Vessel List Page');
        break;
      case 'archive':
        ga4EventTrigger('Archive Vessel', 'Vessel Menu', 'Vessel List Page');
        break;
      case 'copyVessel':
        ga4EventTrigger('Copy as New Vessel', 'Vessel Menu', 'Vessel List Page');
        break;
      case 'trialData':
        ga4EventTrigger('Trial Data', 'Vessel Menu', 'Vessel List Page');
        break;
      case 'changeOwner':
        ga4EventTrigger('Change Ownership', 'Vessel Menu', 'Vessel List Page');
        break;
      case 'goTo':
        ga4EventTrigger('Link to Inspections-Audits', 'Vessel Menu', 'Vessel List Page');
        break;
      case 'edit':
        ga4EventTrigger('Open', 'Edit Vessel', 'Vessel List Page');
        break;
      case 'routeTo':
        ga4EventTrigger('Breadcrumb', 'Nav', `Vessel List Page - ${value}`);
        break;
      default:
        ga4EventTrigger('Click', value, 'Vessel Details');
        break;
    }
  };

  const onBreadcrumbClick = (label) => {
    eventTracker('routeTo', toString(label));
  };

  const goToSeafarerReportsPage = () => {
    const queryParam = `vessel_name=${encodeURIComponent(vesselOwnership?.name)}`;
    history.push(`/seafarer-reports/modeller?${queryParam}`);
  };

  const setSignedOnSeafarerData = (resp) => {
    if (resp.length) {
      const crewData = resp.filter((data) => [MASTER, CHIEF_ENGINEER].includes(data.rank_value));
      setMaster(crewData.find((data) => data.rank_value === MASTER));
      setChiefEngineer(crewData.find((data) => data.rank_value === CHIEF_ENGINEER));
    }
  };

  const getShipReportData = async (imo) => {
    try {
      const promises = [
        vesselService.getLastPosition(imo),
        vesselService.getItinerary(
          ownershipId,
          {
            pageIndex: 0,
            pageSize: 3,
            sortBy: [{ id: 'estimated_departure', desc: false }],
          },
          `estimated_departure=${moment().format('YYYY-MM-DD')},`,
        ),
        vesselService.getTechnicalReports(
          'position',
          ownershipId,
          {
            pageIndex: 0,
            pageSize: 1,
            sortBy: [{ id: 'gmt', desc: true }],
          },
          '',
        ),
      ];
      if (roleConfig.crewAssignment || roleConfig.vessel.view) {
        const query = `rank=Master&rank=Chief%20Engineer&vessel_ownership_id=${ownershipId}&current_status=signed_on`;
        promises.push(vesselService.getSignedOnSeafarer(query));
      }
      const [
        lastPositionResponse,
        lastItineraryResponse,
        lastReportResponse,
        crewAssignmentResponse,
      ] = await Promise.allSettled(promises);
      setLastPosition(lastPositionResponse.value.data);
      if (lastItineraryResponse.status === 'fulfilled') {
        setLastItinerary(
          lastItineraryResponse.value.data?.results.sort(
            (a, b) => Date.parse(a.estimated_departure) - Date.parse(b.estimated_departure),
          ),
        );
      } else if (lastItineraryResponse.status === 'rejected') {
        handleError(lastItineraryResponse.reason.response);
      }
      if (lastReportResponse.status === 'fulfilled') {
        setLastReport(lastReportResponse.value.data?.results);
      } else if (lastReportResponse.status === 'rejected') {
        handleError(lastReportResponse.reason.response);
      }
      if (crewAssignmentResponse?.status === 'fulfilled') {
        setSignedOnSeafarerData(crewAssignmentResponse.value.data);
      } else if (crewAssignmentResponse?.status === 'rejected') {
        handleError(crewAssignmentResponse.reason.response);
      }
    } catch (error) {
      handleError(error.response);
      console.log('error occurred in getting ship report for id:', ownershipId);
      console.error(error);
    }
  };

  const fetchOwnershipVessel = async () => {
    setIsLoading(true);
    try {
      if (roleConfig.vessel.view) {
        setVesselOwnership(null);
        const response = await vesselService.getOwnershipVessel(ownershipId);
        if (response?.data?.owner_end_date) {
          const respVesselAccountDetails = await ownerService.getVesselAccountDetails(
            response?.data?.vessel?.id,
            ownershipId,
            response?.data?.owner?.ship_party_id,
          );
          if (respVesselAccountDetails?.data?.result === false) {
            setDisableOFRReopenReport(true);
          }
        }
        if (isEmptyObject(response.data)) {
          setInvalidVessel(true);
        } else {
          const vesselPref = await ownerService.getOwnerVesselPref(
            response.data.vessel_account_code_new,
            ownershipId,
            response?.data?.owner?.ship_party_id,
          );
          setOwnerVesselPreferenceLength(!!(vesselPref?.data[0]?.vesselCode));
          if (!vesselPref?.data?.length) {
            setDisableOFRFinancialTab(true);
          } else {
            const monthValue = vesselPref?.data[0]?.preferenceValue;
            if (moment().isBefore(moment(`01-${monthValue}`), 'month')) {
              setDisableOFRFinancialTab(true);
            }
          }
          setVesselOwnership(response.data);
          setVesselName(response.data.name);
          setVesselEmail(response.data.emails?.find((email) => email.email_type_id === 1)?.email);
          setIsCurrentOwner(response.data.owner_end_date === null);
          const showInputPendingAlert = await shouldShowInputPendingAlert(response.data);
          if (response.data.vessel?.pending_status) {
            setApprovalProps(
              getCurrentApprovalProp(response.data.vessel.pending_status, showInputPendingAlert),
            );
          } else {
            const isOwnershipPending = await isOwnershipPendingForVessel(response.data);
            const isActiveVessel =
              !response.data.owner_end_date && !response.data.registered_owner_end_date;
            if (isOwnershipPending) {
              setIsOwnershipChangePending(true);
              if (isActiveVessel) {
                setApprovalProps(getCurrentApprovalProp(OWNERSHIP_PENDING, showInputPendingAlert));
              }
            }
          }
          getShipReportData(response.data.vessel.imo_number);

          setVesselViewData(buildViewData(response.data));
          setVesselData(response?.data);
        }
      }
    } catch (error) {
      handleError(error.response);
      console.error(`Get vessel ownership by ID: ${ownershipId} failed. Error: ${error}`);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchOwnershipVessel();
  }, [vesselStatusUpdated, ownershipId]);

  useEffect(() => {
    if (assignUserActionStatus?.status === UPDATED_STATUS || vesselStatusUpdated) {
      fetchOwnershipVessel();
    }
  }, [vesselStatusUpdated, assignUserActionStatus]);

  const isOwnershipPendingForVessel = async (data) => {
    const { data: isOwnershipPending } = await ownershipService.isVesselOwnershipPending(
      data.vessel_id,
    );
    return isOwnershipPending;
  };
  const isEmptyObject = (obj) => {
    return Object.keys(obj).length === 0;
  };

  const shouldShowInputPendingAlert = async (vesselOwnership) => {
    return vesselOwnership.pending_status === vesselStatuses.ACTIVE
      ? Promise.all([
          vesselApprovalService.isVesselAllApproved(
            vesselOwnership.vessel_id,
            vesselOwnership.pending_status,
          ),
          vesselService.isAnyInputPending(vesselOwnership.vessel_id),
        ]).then((results) => results[0].data && results[1].data)
      : false;
  };

  const buildViewData = (vessel) => {
    return {
      basic: { title: 'Basic', data: VesselBasicViewData(vessel) },
      contact: { title: 'Contact', data: VesselContactViewData(vessel) },
      particulars: {
        title: 'Particulars',
        data: VesselParticularViewData(vessel),
      },
      office: {
        title: 'Office Data',
        data: VesselOfficeViewData(vessel, setAssignUserActionStatus, roleConfig),
      },
      misc: {
        title: 'Miscellaneous',
        data: VesselMiscellaneousViewData(vessel),
      },
    };
  };

  const visitUpdateVessel = () => {
    eventTracker('edit', 'Edit Vessel');
    history.push(`/vessel/${vesselOwnership?.vessel_id}/${vesselOwnership?.id}/takeover/basic`);
  };

  const visitApprovalFlow = () => {
    eventTracker('approve', approvalProps?.actionTitle);
    if (isOwnershipChangePending)
      history.push(`/vessel/ownership/${vesselOwnership?.vessel_id}/approval`);
    else history.push(`/vessel/details/${vesselOwnership?.vessel_id}/approval`);
  };

  const handlePrint = () => {
    const returnPrintEventText = () => {
      switch (activeTab) {
        case 'general':
          return 'Vessel Details';
        case 'emergency-drills':
          return DetailPageTabData().find((item) => item.eventKey === activeTab).tabName;
        case 'certificates':
          return 'Vessel Surveys Certificates';
        default:
          return (
            'Vessel ' +
            DetailPageTabData().find((item) => item.eventKey === activeTab).tabName
          );
      }
    };
    ga4EventTrigger('Print', `${returnPrintEventText()} - Menu`, vesselOwnership?.name);
    window.print();
  };

  const handleCopyVessel = () => {
    eventTracker('copyVessel', 'Copy Vessel');
    history.push(
      `/vessel/takeover/basic?copyVesselID=${vesselOwnership?.vessel_id}&copyOwnershipID=${vesselOwnership?.id}`,
    );
  };

  const handleChangeOwnership = () => {
    eventTracker('changeOwner', 'Change Ownership');
    history.push(`/vessel/ownership/${vesselOwnership?.vessel_id}?ownershipId=${ownershipId}`);
  };

  const handleTrialDataLink = () => {
    eventTracker('trialData', 'Trial Data');
    history.push(`/vessel/details/${ownershipId}/trial-data`);
  };

  const handleChangeFlagClick = () => {
    setShowFlagDialog(true);
  };

  const handleArchiveVessel = () => {
    eventTracker('archive', 'Archive Vessel');
    setModalData({
      title: 'Confirm proceeding the Archive Process?',
      body: 'Are you sure you want to request approval for archiving this vessel?',
      onConfirm: () => requestForVesselPendingAction(vesselStatuses.ARCHIVED),
    });
    handleModalShow();
  };

  const handleHandedOver = async () => {
    eventTracker('handover', 'Vessel Handover');
    setModalData({
      title: 'Confirm proceeding the Hand Over Process?',
      body: 'Are you sure you want to request approval for handing over this vessel?',
      onConfirm: () => requestForVesselPendingAction(vesselStatuses.HANDED_OVER),
    });
    handleModalShow();
  };
  const getInspectionCount = (inspections) => {
    return (
      (inspections?.['IN_PROGRESS'] || 0) +
      (inspections?.['PENDING_APPROVAL'] || 0) +
      (inspections?.['ACKNOWLEDGED'] || 0)
    );
  };
  const handleCheckCanHandedOver = async () => {
    setModalData({
      title: '',
      body: '',
      isLoading: true,
      hideConfirm: true,
    });
    handleModalShow();

    const result = await vesselService.canDoHandover(vesselOwnership?.vessel_id);
    const stats = result.data.stats;
    if (result.data?.canDoHandOver) {
      handleHandedOver();
    } else {
      setModalData({
        title: 'Warning: Vessel Handover Not Allowed',
        body: (
          <div className="alert alert-danger" role="alert">
            <p> The vessel cannot be handed over at the moment due to ongoing inspections:</p>
            <ul>
              {getInspectionCount(stats?.['AUDIT']) ? (
                <li style={{ marginLeft: '15px', color: '#6e0e2d' }}>
                  <span style={{ fontWeight: 'bold', display: 'inline' }}>
                    {getInspectionCount(stats?.['AUDIT'])}{' '}
                  </span>
                  <span style={{ display: 'inline' }}>
                    {' '}
                    from the Quality Management System (QMS) department.{' '}
                  </span>
                </li>
              ) : (
                ''
              )}

              {getInspectionCount(stats?.['VIR']) ? (
                <li style={{ marginLeft: '15px', color: '#6e0e2d' }}>
                  <span style={{ fontWeight: 'bold', display: 'inline' }}>
                    {getInspectionCount(stats?.['VIR'])}
                  </span>{' '}
                  <span style={{ display: 'inline' }}> from the Technical department.</span>
                </li>
              ) : (
                ''
              )}
            </ul>

            <p>
              Please ensure that all inspections are completed and closed before initiating the
              handover process. Thank you for your attention to this matter.
            </p>
          </div>
        ),
        isLoading: false,
        hideConfirm: true,
      });
      handleModalShow();
    }
  };

  const requestForVesselPendingAction = async (action) => {
    handleClose();
    try {
      const response = await vesselApprovalService.requestApprovalFor(
        vesselOwnership?.vessel_id,
        action,
      );
      if (response.status == 200) {
        setVesselStatusUpdated(true);
      } else {
        throw Error(`Server response is not OK!`);
      }
    } catch (error) {
      handleError(error.response);
      console.error(
        `request vessel handover approval for ID: ${vesselOwnership?.vessel_id} failed. Error: ${error}`,
      );
    }
  };

  const RenderTableRow = (props) => {
    return props.data.map((key, index) => {
      return (
        <tr key={index}>
          <td className="details_page__row-name">{key.label}</td>
          <td className="details_page__row-value">
            {key.value}
            <span> {key.unit}</span>
          </td>
        </tr>
      );
    });
  };

  function getCurrentApprovalProp(vesselPendingStatus, showMissingInputAlert) {
    const draftAlert = showMissingInputAlert
      ? {
          message: 'Some required information in the page is missing',
          type: 'danger',
        }
      : {
          message: 'This vessel page is a draft and pending for approval.',
          type: 'info',
        };

    switch (vesselPendingStatus) {
      case vesselStatuses.ACTIVE:
        return {
          pendingStatus: vesselStatuses.ACTIVE,
          alert: draftAlert,
          actionTitle: 'Approval Details',
          toolbarButtonProps: null,
        };
      case vesselStatuses.HANDED_OVER:
        return {
          pendingStatus: vesselStatuses.HANDED_OVER,
          alert: {
            message: 'This vessel page is pending for hand over.',
            type: 'info',
          },
          actionTitle: 'Hand Over Approval',
          toolbarButtonTitle: 'Hand Over Vessel',
        };
      case vesselStatuses.ARCHIVED:
        return {
          pendingStatus: vesselStatuses.ARCHIVED,
          alert: {
            message: 'This vessel page is pending for archive.',
            type: 'info',
          },
          actionTitle: 'Archive Approval',
          toolbarButtonTitle: 'Archive Vessel',
        };
      case OWNERSHIP_PENDING:
        return {
          pendingStatus: vesselStatuses.ACTIVE,
          alert: {
            message: 'This vessel is changing ownership.',
            type: 'info',
          },
          actionTitle: 'Approval Details',
          toolbarButtonProps: null,
        };
    }
  }

  const VesselStatusAlert = () => {
    return approvalProps ? (
      <Alert
        variant={approvalProps.alert.type}
        className="no-print details_page__draft-alert"
        id="test__status-alert"
        data-testid="data-test-id-test-status-alert"
      >
        {approvalProps.alert.message}
      </Alert>
    ) : null;
  };

  const ApprovalButton = () => {
    return approvalProps ? (
      <ButtonGroup className="mr-2" onClick={visitApprovalFlow} id="test_approval-button">
        <Button data-testid="fml-vesselDetail-approval" variant="primary">
          {approvalProps.actionTitle}
        </Button>
      </ButtonGroup>
    ) : null;
  };

  const isVesselReadyToHandOver = () =>
    vesselOwnership &&
    vesselOwnership.vessel.status === vesselStatuses.ACTIVE &&
    !vesselOwnership.vessel.pending_status &&
    !isOwnershipChangePending &&
    !vesselOwnership.owner_end_date &&
    !vesselOwnership.registered_owner_end_date;

  const isVesselReadyToArchive = () =>
    vesselOwnership &&
    vesselOwnership.vessel.status === vesselStatuses.HANDED_OVER &&
    !vesselOwnership.vessel.pending_status;

  const isVesselReadyToChangeOwnership = () =>
    vesselOwnership &&
    vesselOwnership.vessel.status === vesselStatuses.ACTIVE &&
    vesselOwnership.vessel.pending_status === null &&
    !isOwnershipChangePending &&
    roleConfig.approvalGroups.includes(BUSINESS);

  const canEdit = () => {
    return roleConfig.vessel.edit && !isOwnershipChangePending && !vesselOwnership?.is_archived;
  };

  const goToInspectionListPage = () => {
    eventTracker('goTo', 'Go To Inspection/Audits');
    const queryParam = `?vesselName=${vesselOwnership?.name}`;
    history.push(`/inspections-audits${queryParam}`);
  };

  const goToTechnicalReportsPage = () => {
    ga4EventTrigger('View Technical Report', 'Vessel Menu', 'Vessel Details');
    history.push(`/vessel/report/technical/position?&vessel_ownership_id=${vesselOwnership?.id}`);
  };

  const goToEnvironmentalReportsPage = () => {
    ga4EventTrigger('View Environmental Report', 'Vessel Menu', 'Vessel Details');
    history.push(`/vessel/report/environmental/marpol?&vessel_ownership_id=${vesselOwnership?.id}`);
  };

  const goToFinancialReportsPage = () => {
    ga4EventTrigger('View Financial Report', 'Vessel Menu', 'Vessel Details');
    history.push(`/vessel/report/financial/accounts?&vessel_ownership_id=${vesselOwnership?.id}`);
  };

  const goToOwnerReportsPage = () => {
    ga4EventTrigger('View Owner Report', 'Vessel Menu', 'Vessel Details');
    history.push(`/vessel/report/financial/technical?&vessel_ownership_id=${vesselOwnership?.id}`);
  };

  const goToCashCallReportsPage = () => {
    ga4EventTrigger('View Cash Call Report', 'Vessel Menu', 'Vessel Details');
    history.push(`/vessel/report/financial/cash-call?&vessel_ownership_id=${vesselOwnership?.id}`);
  };

  const goToVoyageHistoryPage = () => {
    history.push(
      `/vessel/report/technical/voyage-history?vessel_ownership_id=${vesselOwnership?.id}`,
    );
  };

  const eorbVesselProduct = useMemo(() => {
    let product = null;
    if (vesselOwnership?.vessel?.vessel_product?.length) {
      const foundProduct = vesselOwnership.vessel.vessel_product.find(
        (product) => product.product_name === EORB_PRODUCT_NAME,
      );
      if (!_.isEmpty(foundProduct)) {
        product = foundProduct;
      }
    }
    return product;
  }, [vesselOwnership]);

  const eorbLink = useCallback(
    () =>
      eorbVesselProduct?.status === EORB_STATUSES.ACTIVE
        ? window.open(eorbVesselProduct.link, '_blank')
        : null,
    [eorbVesselProduct],
  );

  const ButtonsToolbar = () => {
    return (
      <ButtonToolbar className="toolbar-allignment no-print">
        {roleConfig.vessel.viewApproval ? <ApprovalButton /> : null}
        {canEdit() ? (
          <ButtonGroup className="mr-2" onClick={visitUpdateVessel}>
            <Button variant="outline-primary" data-testid="edit-detail-btn">
              Edit
            </Button>
          </ButtonGroup>
        ) : null}
        <DropdownButton
          variant="outline-primary"
          id="dropdown-basic-button"
          data-testid="test-id-dropdown-basic-btn"
          onToggle={(isOpen) => {
            if (isOpen) ga4EventTrigger('More', 'Vessel Menu', 'Vessel Details');
          }}
          title="More"
        >
          {roleConfig.seafarerReportModellerView && (
            <Dropdown.Item onClick={goToSeafarerReportsPage}>Seafarer Reports</Dropdown.Item>
          )}
          <Dropdown.Item onClick={goToTechnicalReportsPage}>Technical Reports</Dropdown.Item>
          <Dropdown.Item onClick={goToEnvironmentalReportsPage}>
            Environmental Reports
          </Dropdown.Item>
          <Dropdown.Item onClick={goToFinancialReportsPage}>Financial Reports</Dropdown.Item>
          <Dropdown.Item onClick={goToOwnerReportsPage}>Owner Reports</Dropdown.Item>
          <Dropdown.Item onClick={goToCashCallReportsPage}>Cash Call Reports</Dropdown.Item>
          <Dropdown.Item onClick={goToVoyageHistoryPage}>Voyage History</Dropdown.Item>
          {roleConfig?.eorb?.view && (
            <>
              <hr />
              <Dropdown.Item onClick={eorbLink}>
                {eorbVesselProduct?.status === EORB_STATUSES.ACTIVE ? (
                  <a
                    title={`Last Synchronization: ${moment(
                      eorbVesselProduct.synchronized_at,
                    ).format('DD/MM/YYYY')}`}
                  >
                    e-ORB
                  </a>
                ) : (
                  <div
                    title={
                      eorbVesselProduct?.status === EORB_STATUSES.PENDING
                        ? 'Pending installation'
                        : 'e-ORB not installed'
                    }
                    className="eorb-disabled-class"
                  >
                    e-ORB
                  </div>
                )}
              </Dropdown.Item>
            </>
          )}
          <hr />
          {roleConfig.viewInspections ? (
            <Dropdown.Item onClick={goToInspectionListPage}>Inspections/Audit</Dropdown.Item>
          ) : null}
          <Dropdown.Item
            onClick={async () => {
              switch (activeTab) {
                case 'itinerary': {
                  ga4EventTrigger('Export to Excel', 'Vessel Itinerary - Menu', 'Export to Excel');
                  exportTableToExcel(
                    {
                      itinerary: {
                        jsonData: getLocalStorage(LOCAL_STORAGE_FIELDS.itineraryDataKey),
                        columns: columns,
                        title: 'Itinerary',
                      },
                    },
                    vesselOwnership?.name && `Vessel Name-${vesselOwnership.name}`,
                  );
                  break;
                }
                case 'contingency': {
                  ga4EventTrigger(
                    'Export to Excel',
                    'Vessel Contingency - Menu',
                    'Export to Excel',
                  );
                  exportTableToExcel(
                    {
                      position: formatExcelData(LOCAL_STORAGE_FIELDS.contingencyPositionDataKey),
                      contact: formatExcelData(LOCAL_STORAGE_FIELDS.contingencyContactDataKey),
                    },
                    vesselOwnership?.name && `Vessel Name-${vesselOwnership.name}`,
                  );
                  break;
                }
                case 'emergency-drills': {
                  ga4EventTrigger(
                    'Export to Excel',
                    'Emergency Drills - Menu',
                    vesselOwnership?.name,
                  );
                  exportTableToExcel(
                    {
                      drill: formatExcelData('', drillExcelData),
                    },
                    vesselOwnership?.name && `Vessel Name-${vesselOwnership.name}`,
                  );
                  break;
                }
                case 'certificates': {
                  ga4EventTrigger(
                    'Export to Excel',
                    'Vessel Surveys Certificates - Menu',
                    vesselOwnership?.name,
                  );
                  const response = await vesselService.getVesselCertificates(
                    vesselOwnership?.vessel_id,
                    {},
                    '',
                  );
                  exportTableToExcel2(
                    {
                      certificates: {
                        jsonData: formatCertificateData(response.data.results),
                        columns: certificates_coloum,
                      },
                    },
                    vesselOwnership?.name && `Vessel Name-${vesselOwnership.name}`,
                  );
                  break;
                }
                case 'agents': {
                  ga4EventTrigger('Export to Excel', 'Vessel Agent - menu', vesselOwnership?.name);
                  const agentResponse = await vesselService.getAgentsList('', ownershipId);
                  exportTableToExcel(
                    {
                      agents: {
                        jsonData: agentResponse.data.results,
                        columns: excelColumns,
                        title: 'Agents',
                      },
                    },
                    vesselOwnership?.name && `Vessel Name-${vesselOwnership.name}`,
                  );
                  break;
                }
                case 'charterer': {
                  ga4EventTrigger(
                    'Export to Excel',
                    'Vessel Charterer - menu',
                    vesselOwnership?.name,
                  );
                  const charteresResponse = await vesselService.getChartererList('', ownershipId);
                  exportTableToExcel(
                    {
                      charteres: {
                        jsonData: charteresResponse.data.results,
                        columns: excelColumns,
                        title: 'Charterers',
                      },
                    },
                    vesselOwnership?.name && `Vessel Name-${vesselOwnership.name}`,
                  );
                  break;
                }
                default: {
                  ga4EventTrigger('Export to Excel', 'Vessel Details - Menu', 'Export to Excel');
                  exportToExcel(
                    vesselViewData,
                    vesselOwnership?.name && `Vessel Name-${vesselOwnership.name}`,
                  );
                }
              }
            }}
          >
            Export to Excel
          </Dropdown.Item>
          <Dropdown.Item onClick={handlePrint}>Print</Dropdown.Item>
          {isVesselReadyToHandOver() && roleConfig.vessel.requestHandOver ? (
            <Dropdown.Item
              data-testid="fml-vesselDetail-handOverVessel"
              onClick={handleCheckCanHandedOver}
            >
              Hand Over Vessel
            </Dropdown.Item>
          ) : null}
          {isVesselReadyToArchive() && roleConfig.vessel.requestArchival ? (
            <Dropdown.Item
              data-testid="fml-vesselDetail-archiveVessel"
              onClick={handleArchiveVessel}
            >
              Archive Vessel
            </Dropdown.Item>
          ) : null}
          {roleConfig.vessel.create ? (
            <Dropdown.Item
              data-testid="fml-vesselDetail-copyAsNewVessel"
              onClick={handleCopyVessel}
            >
              Copy as New Vessel
            </Dropdown.Item>
          ) : null}
          {isVesselReadyToChangeOwnership() ? (
            <Dropdown.Item onClick={handleChangeOwnership}>Change Ownership</Dropdown.Item>
          ) : null}
          <Dropdown.Item onClick={handleTrialDataLink}>Trial Data</Dropdown.Item>
          {roleConfig.vessel.changeFlag ? (
            <Dropdown.Item
              data-testid="fml-vesselDetail-changeFlag"
              onClick={() => {
                ga4EventTrigger('Change Vessel Flag', 'Flag Change - menu ', vesselOwnership?.name);
                handleChangeFlagClick();
              }}
            >
              Change Flag
            </Dropdown.Item>
          ) : null}
          <Dropdown.Item
            data-testid="fml-vesselDetail-changeHistory"
            onClick={() => setShowHistoryModal(true)}
          >
            Activity Log
          </Dropdown.Item>
        </DropdownButton>
      </ButtonToolbar>
    );
  };

  // eslint-disable-next-line react/prop-types
  const ModalComponent = ({ data }) => {
    return (
      <Modal className="action-modal" show={show} onHide={handleClose} centered>
        <Modal.Header>
          <Modal.Title className="h5">{data?.title}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <span>{data?.body}</span>
          {data?.isLoading && <Spinner />}
        </Modal.Body>
        <Modal.Footer>
          <Button data-testid="fml-confirmProceed-cancel" variant="primary" onClick={handleClose}>
            {data?.hideConfirm ? 'OK' : 'Cancel'}
          </Button>
          {!data?.hideConfirm && (
            <Button
              data-testid="fml-confirmProceed-confirm"
              variant="secondary"
              onClick={data?.onConfirm}
            >
              Confirm
            </Button>
          )}
        </Modal.Footer>
      </Modal>
    );
  };

  ModalComponent.propTypes = {
    data: PropTypes.object,
  };

  const EditedLabel = ({ action, name, date }) => {
    let formattedDate = '';

    if (date) {
      formattedDate = moment(date).format('DD MMM YYYY');
    }

    let labelStr;
    if (name) {
      labelStr = `${action} by ${name} on ${formattedDate}`;
    } else {
      labelStr = `${action} on ${formattedDate}`;
    }

    return <p className="details_page__edited_label">{labelStr}</p>;
  };

  EditedLabel.propTypes = {
    action: PropTypes.string,
    name: PropTypes.string,
    date: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
  };

  const TableSection = (props) => {
    return (
      <Row>
        <Col>
          <table className="table table-hover">
            <thead className="details_page__table_head">
              <tr>
                <th id={props.id} colSpan="2">
                  {props.title}
                </th>
              </tr>
            </thead>
            <tbody>
              <RenderTableRow data={props.data} />
            </tbody>
          </table>
        </Col>
      </Row>
    );
  };

  TableSection.propTypes = {
    id: PropTypes.string,
    title: PropTypes.title,
    data: PropTypes.object,
  };

  const handleTabSelect = (key) => {
    eventTracker('tabNavigation', key);

    if (key !== activeTab) {
      setError(null);
      setActiveTab('');
      setActiveTab(key);
      if (activeTab === 'itinerary') {
        localStorage.removeItem(LOCAL_STORAGE_FIELDS.showFutureItineraryKey);
      }
    }

    return history.push(`/vessel/ownership/details/${ownershipId}/${key}`);
  };

  const vesselEmergencyDrillList = useMemo(() => {
    return (
      <VesselEmergencyDrills
        vesselLists={vesselLists}
        ownershipId={ownershipId}
        setDrillExcelData={setDrillExcelData}
      />
    );
  }, [vesselLists, ownershipId]);

  const RenderVessel = useMemo(() => {
    return (
      <div>
        <Tab.Container activeKey={activeTab} defaultActiveKey="all">
          <Row className="no-print mt-4 tab-wrapper">
            <Col>
              <TabWrapper
                handleTabSelect={handleTabSelect}
                data={
                  disableOFRFinancialTab
                    ? DetailPageTabData().filter(
                        (item) => item.eventKey !== 'monthlyFinancialReports',
                      )
                    : DetailPageTabData()
                }
                step={step}
                setActiveTab={setActiveTab}
                activeTab={activeTab}
              />
            </Col>
          </Row>
        </Tab.Container>
        {activeTab === 'itinerary' && <VesselItineraryList ownershipId={ownershipId} />}
        {activeTab === 'contingency' && (
          <Contingency ownershipId={ownershipId} vesselId={vesselOwnership?.vessel_id} />
        )}
        {activeTab === 'emergency-drills' && vesselEmergencyDrillList}
        {activeTab === 'manuals' && <ManualsList />}
        {activeTab === 'certificates' && (
          <VesselCertificates vesselId={vesselOwnership?.vessel_id} techGroup={vesselOwnership?.fleet_staff?.tech_group} />
        )}
        {_.includes(['agents', 'charterer'], activeTab) && (
          <AgentsCharterer type={activeTab === 'agents' ? 'Agent' : 'Charterer'} />
        )}
        {activeTab === 'monthlyFinancialReports' &&
          roleConfig.ownerReporting.view &&
          roleConfig.ownerReporting.canViewOFRTab &&
          !disableOFRFinancialTab && (
            <MonthlyFinancialReports
              vesselOwnership={vesselOwnership}
              vesselId={ownershipId}
              disableReopen={disableOFRReopenReport}
            />
          )}
        {activeTab === 'general' &&
          (isLoading ? (
            <Spinner />
          ) : (
            <Row>
              <Col md={6} lg={6} xl={4} xxl={4}>
                <Row>
                  <Col className="no-print">
                    <Row>
                      <Col>
                        <PhotoGallery
                          data={vesselOwnership?.images}
                          vesselStatus={vesselOwnership?.status}
                          onError={handleError}
                        />
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        {vesselData &&
                          vesselData?.owner_end_date === null &&
                          vesselData?.is_archived === false && (
                            <ShipReport
                              lastPosition={lastPosition}
                              lastItinerary={lastItinerary}
                              lastReport={lastReport}
                              master={master}
                              chiefEngineer={chiefEngineer}
                              ownershipId={ownershipId}
                              ga4EventTrigger={ga4EventTrigger}
                              vesselData={vesselData}
                              history={history}
                            />
                          )}
                      </Col>
                    </Row>
                  </Col>
                </Row>
              </Col>

              <Col md={6} lg={6} xl={8} xxl={8}>
                <Row>
                  <Col className="details_page__section col-auto">
                    <TableSection
                      id="basic"
                      title={_.get(vesselViewData, 'basic.title')}
                      data={_.get(vesselViewData, 'basic.data')}
                    />
                    <TableSection
                      id="contact"
                      title={_.get(vesselViewData, 'contact.title')}
                      data={_.get(vesselViewData, 'contact.data')}
                    />
                  </Col>
                  <Col className="details_page__section col-auto">
                    <TableSection
                      id="particulars"
                      title={_.get(vesselViewData, 'particulars.title')}
                      data={_.get(vesselViewData, 'particulars.data')}
                    />

                    <TableSection
                      id="officedata"
                      title={_.get(vesselViewData, 'office.title')}
                      data={_.get(vesselViewData, 'office.data')}
                    />

                    <TableSection
                      id="miscellaneous"
                      title={_.get(vesselViewData, 'misc.title')}
                      data={_.get(vesselViewData, 'misc.data')}
                    />

                    <Row>
                      <EditedLabel
                        action="Created"
                        name={_.get(vesselOwnership, 'vessel.created_by_user_info.full_name')}
                        date={_.get(vesselOwnership, 'created_at')}
                      />
                    </Row>
                    {_.get(vesselOwnership, 'vessel.updated_by_hash') && (
                      <Row>
                        <EditedLabel
                          action="Updated"
                          name={_.get(vesselOwnership, 'vessel.updated_by_user_info.full_name')}
                          date={_.get(vesselOwnership, 'updated_at')}
                        />
                      </Row>
                    )}
                  </Col>
                </Row>
              </Col>
            </Row>
          ))}
        <ModalComponent data={modalData} />
      </div>
    );
  }, [
    activeTab,
    step,
    vesselOwnership,
    modalData,
    vesselViewData,
    vesselData,
    lastPosition,
    lastItinerary,
    lastReport,
    master,
    chiefEngineer,
    isLoading,
    show,
  ]);

  const getVesselLists = async () => {
    try {
      const response = await vesselService.getAllVessels();
      const data = response.data.results?.map((vessel) => {
        return { id: vessel.id, value: formatValue(vessel.name) + `(${vessel.id})` };
      });
      setVesselLists(data);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getVesselLists();
    setOwnershipId(ownershipId);
  }, []);

  useEffect(() => {
    if (step === 'assign-drills') {
      setActivePage('assign-drills');
    } else {
      setActivePage('vessel-details');
    }
  }, [step]);

  const fetchVesselUrl = useMemo(() => {
    switch (activeTab) {
      case 'general':
        return `${PARIS_ONE_HOST}/fml/PARIS?display=ship&shipid=${vesselData?.vessel?.ref_id}`;
      case 'itinerary':
        return `${PARIS_ONE_HOST}/fml/PARIS?display=itinerary&history=1&shipid=${vesselData?.vessel?.ref_id}`;
      case 'emergency-drills':
        return `${PARIS_ONE_HOST}/fml/PARIS?display=drills&type=list&shipid=${vesselData?.vessel?.ref_id}`;
      case 'certificates':
        return `${PARIS_ONE_HOST}/fml/PARIS?display=survey&allid=0&sortid=0&shipid=${vesselData?.vessel?.ref_id}`;
      case 'manuals':
        return ` ${PARIS_ONE_HOST}/fml/PARIS?display=drawings&allid=0&sortid=0&shipid=${vesselData?.vessel?.ref_id}`;
      case 'agents':
        return `${PARIS_ONE_HOST}/fml/PARIS?display=agent&type=list&shipid=${vesselData?.vessel?.ref_id}`;
      case 'charterer':
        return `${PARIS_ONE_HOST}/fml/PARIS?display=agent&type=chartererlist&shipid=${vesselData?.vessel?.ref_id}`;
      case 'contingency':
        return `${PARIS_ONE_HOST}/fml/PARIS?display=contingency&shipid=${vesselData?.vessel?.ref_id}`;
      default:
        return `${PARIS_ONE_HOST}/fml/PARIS?display=ship&shipid=${vesselData?.vessel?.ref_id}`;
    }
  }, [activeTab, vesselData]);

  const handleFlagChangeDialogSubmit = (data) => {
    setShowFlagDialog(false);
    setFlagConfirmationData(data);
    setShowFlagConfirmDialog(true);
  };

  const handleFlagChangeDialogCancel = () => {
    setShowFlagDialog(false);
    setFlagConfirmationData({});
  };

  const handleFlagChangeConfirmDialogSubmit = () => {
    setShowFlagConfirmDialog(false);
    fetchOwnershipVessel();
  };

  const handleFlagChangeConfirmDialogCancel = () => {
    setShowFlagConfirmDialog(false);
  };

  const ContainerElements = {
    valid: (
      <>
        {activePage === 'assign-drills' && (
          <AssignDrills
            ownershipId={ownershipId}
            ownershipName={vesselOwnership?.name}
            ga4EventTrigger={ga4EventTrigger}
          />
        )}

        {activePage === 'vessel-details' && (
          <>
            {error && <ErrorAlert message={error} />}
            {!_.isEmpty(vesselOwnership) && vesselViewData ? (
              <div>
                <VesselStatusAlert />
                <div className="details_page">
                  <div className="d-flex">
                    <div className="flex-grow-1">
                      <Row className="details-breadcrumb">
                        <Col className="col-md-auto align-header">
                          <BreadcrumbHeader
                            items={breadCrumbsItems}
                            activeItem={vesselOwnership?.name}
                            onClick={onBreadcrumbClick}
                          />
                        </Col>
                      </Row>
                      {roleConfig.paris1View && (
                        <Row>
                          <Col className="col-md-auto align-header">
                            <a
                              target="_blank"
                              href={fetchVesselUrl}
                              type="absolute"
                              className="paris1-link-detail-page"
                              rel="noreferrer"
                            >
                              View PARIS 1.0 Detail Page
                            </a>
                          </Col>
                        </Row>
                      )}
                    </div>
                    <ButtonsToolbar />
                  </div>
                  {RenderVessel}
                </div>
              </div>
            ) : (
              <Spinner />
            )}
            <FlagChangeDialog
              showModal={showFlagDialog}
              handleModalSubmit={handleFlagChangeDialogSubmit}
              handleModalCancel={handleFlagChangeDialogCancel}
              data={vesselData}
            />
            <ChangeLogModal
              roleConfig={roleConfig}
              showModal={showHistoryModal}
              setShowModal={setShowHistoryModal}
            />
            <FlagChangeConfirmDialog
              showModal={showFlagConfirmDialog}
              handleModalSubmit={handleFlagChangeConfirmDialogSubmit}
              handleModalCancel={handleFlagChangeConfirmDialogCancel}
              data={flagConfirmationData}
            />
            <AssignReplaceUser />
            <ScrollArrow />
          </>
        )}
      </>
    ),
    accessDenied: <ErrorPage errorCode={403} />,
    invalidVessel: <ErrorPage errorCode={404} />,
  };

  useEffect(() => {
    if (roleConfig.vessel.view && !isAccessDenied) {
      if (invalidVessel) {
        setDisplayState('invalidVessel');
      } else {
        setDisplayState('valid');
      }
    } else {
      setDisplayState('accessDenied');
    }
  }, [isAccessDenied, invalidVessel, roleConfig.vessel.view]);

  return <Container>{ContainerElements[displayState]}</Container>;
};

Details.propTypes = {
  ga4react: PropTypes.shape({
    pageview: PropTypes.func,
    event: PropTypes.func,
    ...PropTypes.objectOf(PropTypes.func),
  }),
  data: PropTypes.arrayOf(PropTypes.object),
};

export default Details;
