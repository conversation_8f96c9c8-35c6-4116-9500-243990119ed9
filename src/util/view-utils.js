import _ from 'lodash';
import moment from 'moment';
import * as momenttz from 'moment-timezone';
import React from 'react';
import { OverlayTrigger, Tooltip } from 'react-bootstrap';
import CompareButtonsBar from '../component/TechnicalReports/CompareReports/CompareButtonsBar';
import { Dash } from '../model/utils';
import { approvalStatuses } from '../model/constants';
import parsePhoneNumber from 'libphonenumber-js';
import { ASSIGNUSERSTATUS, POD_MANAGER_SUB_ROLES } from '../constants/assign-user';
import { Highlighter } from 'react-bootstrap-typeahead';
import { SUPERINTENDENT_ONBOARDING_GROUP } from '../constants/certificates';
import { Icon } from '../styleGuide';
import PropTypes from 'prop-types';

const { ASSIGN_STATUS, VESSEL_ASSIGN } = ASSIGNUSERSTATUS;

export const getShortDate = (dateValue) => {
  const d = new Date(dateValue);

  const dateString =
    d.toLocaleString('en-US', { day: '2-digit' }) +
    ' ' +
    d.toLocaleString('en-US', { month: 'short' }) +
    ', ' +
    d.getFullYear();
  return dateString;
};

export const sortOnGivenOrder = (a, order, key) => {
  const map = order.reduce((r, v, i) => {
    r[v] = i;
    return r;
  }, {});
  return a.sort((a, b) => map[a[key]] - map[b[key]]);
};

export const formatDate = (date, resultFormat = 'DD MMM YYYY', defaultValue = Dash) => {
  if (date && moment(date).isValid()) {
    return moment(date).parseZone(date).format(resultFormat);
  }
  return defaultValue;
};

export const formatValue = (data, defaultValue = Dash) => {
  return data ?? defaultValue;
};

export const formatNumber = (num, defaultValue = '0.00', roundValue = 2) => {
  if (num === defaultValue) {
    return num;
  }
  return num && !_.isNaN(_.toNumber(num)) ? _.round(_.toNumber(num), roundValue) : defaultValue;
};

export const formatBDNNumber = (num, defaultValue = '0.00') => {
  if (num === defaultValue) {
    return num;
  }
  return num && !_.isNaN(_.toNumber(num)) ? _.toNumber(num) : defaultValue;
};

export const formatFloat = (val, defaultValue = '0.00', roundValue = 2) => {
  if (val === defaultValue) {
    return val;
  }
  if (val && !isNaN(Number(val))) {
    return parseFloat(val).toFixed(roundValue);
  } else {
    return defaultValue;
  }
};

export const getTimeDiffWithUTC = () => {
  const zone_name = momenttz.tz.guess();
  const timezone = momenttz.tz(zone_name).zoneAbbr();
  return `UTC (${moment().format('Z')} hrs to ${timezone})`;
};

export function stringAsDate(value, parseZone = true) {
  if ((typeof value === 'string' || value instanceof String) && value != '') {
    return parseZone ? moment.parseZone(value).toDate() : moment(value).toDate();
  } else {
    return undefined;
  }
}

export const addDays = (date, days) => {
  return moment(date).add(days, 'days').format('DD MMM YYYY');
};

export const fetchIcon = (column) => {
  let iconName = 'sort-off';
  if (column.canSort && column.isSorted) iconName = 'sort-descending';
  if (column.canSort && column.isSortedDesc) iconName = 'sort-ascending';
  return iconName;
};

export const highlightText = (cellText = '', keyword = '') => (
  <Highlighter className="search-text-highlight" search={keyword.toString()}>
    {cellText ? cellText.toString() : '---'}
  </Highlighter>
);

export const assignReplaceUser = (data, setAssignUserActionStatus, type, label, keyword = '') => {
  return data.full_name && !POD_MANAGER_SUB_ROLES.includes(data.subrole) ? (
    <OverlayTrigger
      placement="bottom"
      overlay={
        <Tooltip id="desc_tooltip" className="details-field-tooltip">
          Click to Replace
        </Tooltip>
      }
    >
      <span
        className="underline-link-text fw-bolder d-inline"
        onClick={(event) => {
          event.stopPropagation();
          setAssignUserActionStatus({
            status: VESSEL_ASSIGN,
            role: type,
            data: data,
            label: label,
          });
        }}
        aria-hidden="true"
      >
        {highlightText(data.full_name, keyword)}
      </span>
    </OverlayTrigger>
  ) : (
    <OverlayTrigger
      placement="bottom"
      overlay={
        <Tooltip id="desc_tooltip" className="details-field-tooltip">
          Click to Assign
        </Tooltip>
      }
    >
      <span
        className="underline-link-text fw-bolder d-inline"
        onClick={(event) => {
          event.stopPropagation();
          setAssignUserActionStatus({
            status: ASSIGN_STATUS,
            role: type,
            data: data,
            label: label,
          });
        }}
        aria-hidden="true"
      >
        {data.full_name ? highlightText(data.full_name, keyword) : '(Not assigned)'}
      </span>
    </OverlayTrigger>
  );
};

export const parseDMS = ({ latitude, longitude }) => {
  const ConvertDMSToDD = ([degrees, minutes, seconds], direction) => {
    let dd = Number(degrees) + Number(minutes) / 60 + Number(seconds || 0) / (60 * 60);
    if (direction === 'S' || direction === 'W') {
      dd = dd * -1;
    }
    return dd;
  };
  const partsLat = latitude.split(/[^\w?]+/);
  const partsLng = longitude.split(/[^\w?]+/);
  const lat = ConvertDMSToDD(
    [...partsLat.slice(0, partsLat.length - 1)],
    partsLat[partsLat.length - 1],
  );
  const lng = ConvertDMSToDD(
    [...partsLng.slice(0, partsLng.length - 1)],
    partsLng[partsLng.length - 1],
  );
  return lat + ',' + lng;
};

export const findSubtype = (array, id) => array.find((element) => element.id == id);

const getReportValue = (dataArray, v, i) => {
  if (!dataArray[v]) return '';

  const currentValue = dataArray[v][i]?.value;
  const groupLabel = dataArray[v][i]?.group_label;

  if (currentValue === 'Fuel not listed' && groupLabel === 'bunkering') {
    return (
      dataArray[v].find(
        (item) => item.group_label === 'bunkering' && item.edit_label?.includes('Other Fuel Type'),
      )?.value || ''
    );
  } else if (currentValue === 'Fuel not listed') return dataArray[v][i + 1]?.value || '';
  else return currentValue || '';
};

export const transpose = (dataArray) => {
  return dataArray.length > 0
    ? [
        ...dataArray[0].map((field, i) => {
          const values = [];
          return {
            field: {
              name: field.label,
              header: field.validation_json?.header,
              hidden: field.validation_json?.hidden ?? false,
            },
            order: field.order,
            ...[0, 1, 2, 3].reduce((r, v) => {
              values.push(
                dataArray[v] && field.label === dataArray[v][i]?.label && dataArray[v][i],
              );
              return {
                ...r,
                values: values,
                ['report' + (v + 1)]: getReportValue(dataArray, v, i),
              };
            }, {}),
          };
        }),
      ].sort((a, b) => a.order - b.order)
    : [];
};

export const reportCols = (
  name,
  reports,
  showButtons,
  getButtons,
  includeHover = true,
  fromExcel = false,
  tooltip = '',
) => {
  const colButtonData = [];
  const colData = [];
  for (let i = 0; i <= 3; i++) {
    colButtonData?.push({
      Header: () => (
        <div key={i}>
          {reports[i] && <CompareButtonsBar buttons={getButtons(reports[i], i)} />}
          <span>{`Report ${i + 1}`}</span>
        </div>
      ),
      accessor: `report${i + 1}`,
      customClass: 'details_page__row-value',
    });

    colData?.push({
      Header: `Report ${i + 1}`,
      accessor: `report${i + 1}`,
      customClass: 'details_page__row-value',
      name: `report${i + 1}`,
    });
  }

  fromExcel &&
    colData?.splice(0, 0, {
      Header: name?.toUpperCase(),
      customClass: 'details_page__row-value',
      accessor: name,
      name: name,
    });
  const cols = showButtons ? colButtonData : colData;
  const headerData = includeHover
    ? {
        Header: () => (
          <div key={name} className="compare-page-header">
            {name.length > 66 && !tooltip ? (
              <OverlayTrigger
                overlay={
                  <Tooltip id="desc_tooltip" className="compare-page-header-tooltip">
                    {name}
                  </Tooltip>
                }
                placement="bottom"
              >
                <div className="compare-page-header-text-truncate">{name.toUpperCase()}</div>
              </OverlayTrigger>
            ) : (
              <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                <div>{name.toUpperCase()}</div>
                {tooltip && (
                  <OverlayTrigger
                    placement="right"
                    overlay={
                      <Tooltip id="desc_tooltip" className="tooltip">
                        {tooltip}
                      </Tooltip>
                    }
                  >
                    <div
                      className="paris2-tooltip-icon"
                      style={{ marginLeft: '10px', cursor: 'pointer' }}
                    >
                      <Icon icon="info" />
                    </div>
                  </OverlayTrigger>
                )}
              </div>
            )}
          </div>
        ),
        accessor: (row) =>
          row.field.header ? (
            <div className="details_page__sub-header">{row.field.header}</div>
          ) : (
            <div>{row.field.name}</div>
          ),
        customClass: 'details_page__row-name',
        id: 'name',
        style: { borderRight: '2px solid #1f4a70', minWidth: '210px' },
      }
    : {
        Header: `${name.toUpperCase()}`,
        accessor: (row) => (row.field.header ? row.field.header : row.field.name),
        name: `${name}`,
      };
  return [
    {
      ...headerData,
    },
    ...cols,
  ];
};

export const PageNum = ({ active, disabled, children, onClick, dataTestId }) => {
  const className = `page-num page-num-${active ? 'active' : 'inactive'} page-num-${
    disabled ? 'disabled' : 'enabled'
  }`;
  return (
    <div className={className} onClick={onClick} data-testid={dataTestId} aria-hidden="true">
      {children}
    </div>
  );
};

PageNum.propTypes = {
  active: PropTypes.number,
  disabled: PropTypes.number,
  children: PropTypes.string,
  onClick: PropTypes.func,
  dataTestId: PropTypes.string,
};

export const getHighlightColor = (currentValue, parameter, path) => {
  const value = parameter?.find((item) => item.path === path);
  if (value) {
    if (isNaN(currentValue?.toString())) {
      return 'text-warning';
    } else if (_.inRange(parseFloat(currentValue), value?.min_value, value?.max_value)) {
      return '';
    } else {
      return 'text-danger';
    }
  }
};

export const isFloatValue = (value) => {
  return Number(value) === value && value % 1 !== 0;
};

export const getDueDateTextColor = (date, customDays = 0) => {
  const noOfDays = moment(date).diff(moment(new Date()), 'days') + customDays;
  if (noOfDays <= 0) {
    return 'text-danger';
  } else if (noOfDays <= 30) {
    return 'text-warning';
  } else if (noOfDays <= 60) {
    return 'text-success';
  } else {
    return 'text-dark';
  }
};

export const isEllipsisActive = (index) => {
  const id = document.getElementById(index);
  return id?.offsetHeight < id?.scrollHeight || id?.offsetWidth < id?.scrollWidth;
};

export const getEmailMessage = (
  from,
  reportData,
  controlParameters,
  reportDate,
  report,
  vessel_name,
  techGroup,
) => {
  const values = [];
  controlParameters?.forEach((param) => {
    const value = _.get(reportData, param.path);
    let isValid = true;
    if (value === undefined) {
      isValid = param?.min_value <= 0;
    } else if (!_.inRange(parseFloat(value), param?.min_value, param?.max_value)) {
      isValid = false;
    }
    if (isValid) {
      return;
    }
    values.push({
      isRange: value && value < param?.min_value ? true : param?.min_value !== 0,
      minValue: param?.min_value,
      maxValue: param?.max_value,
      value: value ?? 0,
      fieldName: param?.label,
    });
  });
  const message = `${values
    ?.map((param, index) => {
      const data = param.isRange
        ? `${index + 1}. ${param.fieldName} value ${param.value} should be between ${
            param.minValue
          } and ${param.maxValue}\n`
        : `${index + 1}. ${param.fieldName} value ${param.value} should be less than ${
            param.maxValue
          }\n`;
      return data;
    })
    ?.join('')}`;
  const content = {
    from,
    subject: `${vessel_name} Control Parameters for ${report} report on ${formatDate(reportDate)}`,
    recipient: _.get(reportData, 'email'),
    cc: '',
    message: `Please note that the following items are outside their control parameters. \n${message}\nBest Regards\nFML / ${techGroup}`,
  };
  return content;
};

export const checkReviewStatus = (dataForRole) =>
  !dataForRole.final_approver && dataForRole.approval_status === approvalStatuses.APPROVED
    ? 'Reviewed'
    : dataForRole.approval_status;

export const checkSortBy = (sortDesc) => {
  if (sortDesc) return 'desc';
  else return 'asc';
};
export function formatInternationalPhoneNumber(phone) {
  if (!phone) return Dash;
  const parsedPhoneNo = parsePhoneNumber(`+${phone}`);
  if (!parsedPhoneNo) return phone; // Just return the phone if it's not a valid international phone number
  return parsedPhoneNo.formatInternational();
}

export const isOnboardingSuptd = (roleConfig) => {
  return roleConfig?.groups?.includes(SUPERINTENDENT_ONBOARDING_GROUP);
};

export default {
  getShortDate,
  sortOnGivenOrder,
  formatDate,
  formatValue,
  parseDMS,
  reportCols,
  PageNum,
  getHighlightColor,
  isFloatValue,
  getDueDateTextColor,
  isEllipsisActive,
  getEmailMessage,
  checkReviewStatus,
  checkSortBy,
  formatInternationalPhoneNumber,
  isOnboardingSuptd,
};
