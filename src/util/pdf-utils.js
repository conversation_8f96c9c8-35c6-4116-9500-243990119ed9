const { PARIS2_URL } = process.env;

export const headerSection = async (
  doc,
  intialStartXPosition,
  intialStartYPosition,
  mainHeader,
  date,
  pageWidth,
  vesselName,
) => {
  doc.setFontSize(16);
  const fleetHeading = 'Fleet Management Limited';
  doc.text(fleetHeading, intialStartXPosition, intialStartYPosition);
  intialStartYPosition += doc.getTextDimensions(fleetHeading).h + 10;
  const img = new Image();
  const promise = new Promise((resolve) => {
    img.onload = function () {
      resolve();
    };
    img.onerror = function () {
      console.error('Error loading image');
      resolve();
    };
  });
  img.src = `${PARIS2_URL}/vessel/fleet-logo-symbol.png`;
  await promise;
  doc.addImage(img, 'PNG', pageWidth - 20, 13, 20, 20);
  doc.setFontSize(18);
  doc.text(mainHeader, (pageWidth - doc.getTextDimensions(mainHeader).w) / 2, intialStartYPosition);
  intialStartYPosition += doc.getTextDimensions(mainHeader).h + 10;
  doc.setFontSize(12);
  doc.text(`Ship's Name:`, intialStartXPosition, intialStartYPosition);
  const widthShipValue = doc.getTextDimensions(`Ship's Name: `).w;
  doc.setFont('helvetica', 'normal');
  doc.text(vesselName, intialStartXPosition + widthShipValue, intialStartYPosition);
  doc.setFont('helvetica', 'bold');
  const widthDateLabel = doc.getTextDimensions(`Date:  `).w;
  const widthDateValue = doc.getTextDimensions(`${date}`).w;
  doc.text(`Date: `, pageWidth - (widthDateLabel + widthDateValue), intialStartYPosition);
  doc.setFont('helvetica', 'normal');
  doc.text(`${date}`, pageWidth - widthDateValue, intialStartYPosition);
  return intialStartYPosition;
};

export const footerSection = (doc, intialStartXPosition, intialStartYPosition, pageWdithHalf) => {
  const footerTextOne = `Master: `;
  const footerTextTwo = `Chief Engineer: `;
  const footerTextThree = `Name `;
  const footerFillLine = '__________________';
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  intialStartYPosition += 30;
  doc.text(footerTextOne, intialStartXPosition, intialStartYPosition);
  const masterWidth = doc.getTextDimensions(footerTextOne).w;
  doc.text(footerFillLine, intialStartXPosition + masterWidth, intialStartYPosition);
  doc.text(footerTextTwo, intialStartXPosition + pageWdithHalf, intialStartYPosition);
  const cheifWidth = doc.getTextDimensions(footerTextTwo).w;
  doc.text(footerFillLine, intialStartXPosition + cheifWidth + pageWdithHalf, intialStartYPosition);
  intialStartYPosition += doc.getTextDimensions(footerTextOne).h + 3;
  doc.text(footerTextThree, intialStartXPosition, intialStartYPosition);
  doc.text(footerTextThree, intialStartXPosition + pageWdithHalf, intialStartYPosition);
};

export default {
  headerSection,
  footerSection,
};
