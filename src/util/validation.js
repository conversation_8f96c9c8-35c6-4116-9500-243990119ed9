import * as yup from 'yup';

const XSS_REG = /<(\/|[^/>][^>]+|\/[^>][^>]+)>/g; //NOSONAR

export const not_xss = (schema) =>
  schema.test('not-xss', '${path} is not valid text input', (value) => {
    return !value?.match(XSS_REG);
  });

export function filterByExcludedKeys(object, keysToExclude) {
  return Object.keys(object)
    .filter((key) => !keysToExclude.includes(key))
    .reduce((obj, key) => {
      obj[key] = object[key];
      return obj;
    }, {});
}

export function getBasicFormErrors(errors) {
  const excludedFields = [
    'maximum_continuous_rating_kw',
    'maximum_continuous_rating_rpm',
    'shop_trials',
    'sea_trials',
    'vessel_account_code',
    'vessel_account_code_new',
    'dwt',
    'ihm_inspection_date',
    'ihm_provider_id',
    'primary_email',
    'primary_satc_email',
  ];
  const basicFormErrors = filterByExcludedKeys(errors, excludedFields);

  return { ...basicFormErrors };
}

const isEveryItemUndefined = (items) => items.every((item) => item === undefined);

const isItemHasNoValue = (item) => item !== null && item !== '' && item !== undefined;

const isTrialRowHasInput = (trialData) => Object.keys(trialData).length > 1;

const isTrialRowsHasAtleastOneInput = ({ shopTrials, seaTrials }) =>
  shopTrials.some(isTrialRowHasInput) || seaTrials.some(isTrialRowHasInput);

export const TrialFormModel = {
  maximum_continuous_rating_kw: yup
    .number()
    .when(['shop_trials', 'sea_trials', 'maximum_continuous_rating_rpm'], {
      is: (shopTrials, seaTrials, mcr_rpm) => {
        if (isEveryItemUndefined([shopTrials, seaTrials, mcr_rpm])) {
          return false;
        }
        if (isItemHasNoValue(mcr_rpm)) return true;
        return isTrialRowsHasAtleastOneInput({ shopTrials, seaTrials });
      },
      then: yup.number().required(),
    }),
  maximum_continuous_rating_rpm: yup
    .number()
    .when(['shop_trials', 'sea_trials', 'maximum_continuous_rating_kw'], {
      is: (shopTrials, seaTrials, mcr_kw) => {
        if (isEveryItemUndefined([shopTrials, seaTrials, mcr_kw])) {
          return false;
        }
        if (isItemHasNoValue(mcr_kw)) return true;
        return isTrialRowsHasAtleastOneInput({ shopTrials, seaTrials });
      },
      then: yup.number().required(),
    }),
  shop_trials: yup
    .array()
    .when(['maximum_continuous_rating_kw', 'maximum_continuous_rating_rpm', 'sea_trials'], {
      is: (mcr_kw, mcr_rpm, seaTrials) => {
        if (isEveryItemUndefined([mcr_kw, mcr_rpm, seaTrials])) {
          return false;
        }
        const sea_trials = seaTrials ?? [];
        const hasAtleastOneSeaTrialInput = sea_trials.some(isTrialRowHasInput);
        if (hasAtleastOneSeaTrialInput) return true;
        return [mcr_kw, mcr_rpm].some(isItemHasNoValue);
      },
      then: yup
        .array()
        .min(1)
        .of(
          yup.object().shape({
            load: yup.number().positive().required(),
            corrected_sfoc: yup.number().positive().required(),
          }),
        ),
    }),
  sea_trials: yup
    .array()
    .when(['maximum_continuous_rating_kw', 'maximum_continuous_rating_rpm', 'shop_trials'], {
      is: (mcr_kw, mcr_rpm, shopTrials) => {
        if (isEveryItemUndefined([mcr_kw, mcr_rpm, shopTrials])) {
          return false;
        }
        const shop_trials = shopTrials ?? [];
        const hasAtleastOneShopTrialInput = shop_trials.some(isTrialRowHasInput);
        if (hasAtleastOneShopTrialInput) return true;
        return [mcr_kw, mcr_rpm].some(isItemHasNoValue);
      },
      then: yup
        .array()
        .min(1)
        .of(
          yup.object().shape({
            load: yup.number().positive().required(),
            corrected_sfoc: yup.number().positive().required(),
            kw: yup.number().positive().required(),
            rpm: yup.number().positive().required(),
            speed: yup.number().positive().required(),
            tons_per_hour: yup.number().positive().required(),
          }),
        ),
    }),
};

// Dependency cycle when validating objects. Pairing. Please see: https://github.com/jquense/yup/issues/193 for more information
export const DependencyValidationPairing = [
  ['maximum_continuous_rating_kw', 'maximum_continuous_rating_rpm'],
  ['maximum_continuous_rating_kw', 'shop_trials'],
  ['maximum_continuous_rating_rpm', 'shop_trials'],
  ['maximum_continuous_rating_kw', 'sea_trials'],
  ['maximum_continuous_rating_rpm', 'sea_trials'],
  ['shop_trials', 'sea_trials'],
  ['id'],
  ['is_engine_consume_lng', 'lng_engine_category_diesel_generator'],
  ['is_engine_consume_lng', 'lng_engine_category_main_engine'],
  ['lng_engine_category_diesel_generator', 'lng_engine_category_main_engine'],
  ['status'],
];
