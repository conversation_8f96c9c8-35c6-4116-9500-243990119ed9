import menu_items from '../component/vesselList/MenuList';
import { LOCAL_STORAGE_FIELDS, vesselListTabKeys } from '../model/constants';

export const setLocalStorage = (key, value) => {
  localStorage.setItem(key, JSON.stringify(value));
};

export const getLocalStorage = (key) => {
  if (!localStorage.getItem(key)) return undefined;
  return JSON.parse(localStorage.getItem(key));
};

export const isKeyStored = (key, tab, sub_key) => {
  const value = getLocalStorage(key);
  if (!value) return false;
  if (!value[tab]) return false;
  return !!value[tab][sub_key];
};

export const storeColumns = (tab, cols) => {
  const storage_cols = [];
  cols.forEach((item) => {
    storage_cols.push(item.Header);
  });
  const cur_obj = getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey) || {};
  setLocalStorage(LOCAL_STORAGE_FIELDS.masterKey, {
    ...cur_obj,
    [tab]: modifyTabData(LOCAL_STORAGE_FIELDS.tableSelectedColumns, storage_cols, cur_obj[tab]),
  });
};

export const retrieveColumns = (tab, roles, setAssignUserActionStatus) => {
  const selected_col_heads = getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey)[tab][
    LOCAL_STORAGE_FIELDS.tableSelectedColumns
  ];
  let filtered_selected_col_heads = !roles?.eorb?.view
    ? selected_col_heads.filter((head) => 'e-ORB' !== head)
    : selected_col_heads;
  const selected_cols = [];
  menu_items(setAssignUserActionStatus, roles).forEach((col) => {
    if (col.Header && filtered_selected_col_heads.includes(col.Header)) selected_cols.push(col);
  });
  return selected_cols;
};
export const modifyTabData = (col, value, tab_obj) => {
  return { ...tab_obj, [col]: value };
};
export const storePageNumber = (tab, page_no) => {
  const cur_obj = getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey) || {};
  setLocalStorage(LOCAL_STORAGE_FIELDS.masterKey, {
    ...cur_obj,
    [tab]: modifyTabData(LOCAL_STORAGE_FIELDS.tablePageIndex, page_no, cur_obj[tab]),
  });
};
export const storePageSize = (tab, pageSize) => {
  const cur_obj = getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey) || {};
  setLocalStorage(LOCAL_STORAGE_FIELDS.masterKey, {
    ...cur_obj,
    [tab]: modifyTabData(LOCAL_STORAGE_FIELDS.tablePageSize, pageSize, cur_obj[tab]),
  });
};

export const storePageSort = (pageSort, masterKey) => {
  const cur_obj = getLocalStorage(masterKey) || {};
  setLocalStorage(masterKey, {
    ...cur_obj,
    [LOCAL_STORAGE_FIELDS.tablePageSort]: pageSort,
  });
};

// Reset page number to 0 on all tabs
export const resetAllTabs = () => {
  Object.values(vesselListTabKeys).forEach((tab) => {
    storePageNumber(tab, 0);
  });
};
export const getPageSize = (tab) => {
  return Number(
    getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey)[tab][LOCAL_STORAGE_FIELDS.tablePageSize],
  );
};

export const getPageNumber = (tab) => {
  return Number(
    getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey)[tab][LOCAL_STORAGE_FIELDS.tablePageIndex],
  );
};

export const getPageSort = (masterKey) => {
  const table_obj = getLocalStorage(masterKey);
  return table_obj ? table_obj[LOCAL_STORAGE_FIELDS.tablePageSort] || [] : [];
};

export const getPageTableState = (tab) => {
  const pageSize = isKeyStored(
    LOCAL_STORAGE_FIELDS.masterKey,
    tab,
    LOCAL_STORAGE_FIELDS.tablePageSize,
  )
    ? getPageSize(tab)
    : 10;
  const pageIndex = isKeyStored(
    LOCAL_STORAGE_FIELDS.masterKey,
    tab,
    LOCAL_STORAGE_FIELDS.tablePageIndex,
  )
    ? getPageNumber(tab)
    : 0;
  return { pageSize, pageIndex };
};

export const storeQuery = (query) => {
  const cur_obj = getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey) || {};
  setLocalStorage(LOCAL_STORAGE_FIELDS.masterKey, {
    ...cur_obj,
    [LOCAL_STORAGE_FIELDS.advancedSearchParams]: query,
  });
};

export const getQuery = () => {
  const cur_obj = getLocalStorage(LOCAL_STORAGE_FIELDS.masterKey) || {};
  return cur_obj[LOCAL_STORAGE_FIELDS.advancedSearchParams] || '';
};
