import _ from 'lodash';
import XLSX from 'sheetjs-style';
import {
  insertRangeSumCalculation,
  insertIntoCell,
  firstFieldCalculation,
  insertStyleBorder,
  insertStyleCell,
  VoyageCo2Calculation,
  fetchCellAddress,
  insertSumCalculationWithString,
} from './excel-export';
import { CH4_N2O_RELEASE_YEAR } from './mrv';

export const exportToMrvExcel = (excelData) => {
  const wb = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet([[]], { cellStyles: true });
  XLSX.utils.book_append_sheet(wb, worksheet, `MRV_${excelData.vesselName.substring(0, 27)}`);
  if (!worksheet['!merges']) worksheet['!merges'] = [];

  const columnsWidth = Array.from({ length: 93 }, () => ({ wch: 16 }));
  worksheet['!cols'] = columnsWidth;

  // common method to get index of total cell field for MRV report tables
  const fetchParams = (params) => {
    return params.map((item) => {
      if (item.type === 'port') return portTableTotalCellAddress[item.id];
      if (item.type === 'voyage') return voyageTableTotalCellAddress[item.id];
      if (item.type === 'total') return totalTableTotalCellAddress[item.id];
    });
  };

  // common method to get index of each cell field for MRV report tables
  const fetchEachCellParams = (params, offset, row) => {
    return params.map((item) => XLSX.utils.encode_cell({ c: item + offset, r: row }));
  };

  // insert vessel, IMO number, port of registry
  const config = {
    cells: [
      {
        position: {
          r: 4,
          c: 1,
        },
        value: 'Vessel',
      },
      {
        position: {
          c: 2,
          r: 4,
        },
        value: excelData.vesselName,
      },
      /*{ note there is no data fo this info, remove hardcode to prevent user confusion
        position: {
          c: 1,
          r: 6,
        },
        value: 'IMO Number',
      },
      {
        position: {
          c: 2,
          r: 6,
        },
        value: '9263186',
      },
      {
        position: {
          c: 1,
          r: 8,
        },
        value: 'Port of registry',
      },
      {
        position: {
          c: 2,
          r: 8,
        },
        value: 'Majuro',
      },*/
      {
        position: {
          c: 1,
          r: 14,
        },
        value: '(a)',
      },

      // cells with style
      {
        position: {
          c: 2,
          r: 12,
        },
        value: 'Voyage-Port data Part 1',
        style: [true, '0000ff', 'FFFFFF', false, 'left'],
      },
      {
        position: {
          c: 2,
          r: 14,
        },
        value: 'Please provide the ships voyage and port data in the table below.',
        style: [true, undefined, undefined, false, 'left'],
      },
      {
        position: {
          c: 2,
          r: 15,
        },
        value:
          'Please fill in the table below. If you need additional rows, please insert them above the "end of list" row. In that case the formula for the totals will work correctly. Note that the formulas are protected.',
        style: [false, 'FFFFFF', '333399', false, 'left'],
      },
      {
        position: {
          c: 9,
          r: 18,
        },
        value: 'Monitoring during voyage',
        style: [true, 'FFFFFF', undefined, false],
      },

      // cells with styles and border
      {
        position: {
          c: 13,
          r: 17,
        },
        value: 'Data required on an voyage basis',
        style: [false, 'FFD9E1F2'],
        border: ['top', 'right'],
        size: 4,
      },
      {
        position: {
          c: 13,
          r: 18,
        },
        value: 'Data calculated on an annual basis',
        style: [false, '99cc00'],
        border: ['top', 'right', 'bottom'],
        size: 4,
      },
    ],
    mergeCells: [
      // voyage port data part-1
      { s: { r: 12, c: 2 }, e: { r: 12, c: 13 } },
      // instruction heading cell merge
      { s: { r: 14, c: 2 }, e: { r: 14, c: 7 } },
      // insert instruction to fill data in Table
      { s: { r: 15, c: 2 }, e: { r: 15, c: 20 } },
      //Data monitoring during voyage heading
      { s: { r: 18, c: 9 }, e: { r: 18, c: 11 } },
      //insert voyage table data sub-headings
      { s: { r: 17, c: 13 }, e: { r: 17, c: 17 } },
      { s: { r: 18, c: 13 }, e: { r: 18, c: 17 } },
    ],
  };

  if(excelData.isHavingPartialVoyage || excelData.portIsHavingPartialVoyage ){
    config.cells.push(
      {
        position: {
          c: 2,
          r: 16,
        },
        value:
          '*Partial calculation has applied: Only consumption happened in the year has been included.',
        style: [false, 'FFFFFF', '333399', false, 'left'],
      },
    )
    config.mergeCells.push(
      { s: { r: 16, c: 2 }, e: { r: 16, c: 20 } }
    )
  }

  const insertDataIntoCells = (config, worksheet) => {
    config.cells?.forEach((cell) => {
      if (_.has(cell, 'border')) {
        [...Array(cell.size)].forEach((_, i) => {
          const field = { ...cell.position };
          field.c = field.c + i + 1;
          insertStyleBorder(field, worksheet, '', cell.border);
        });
        insertStyleCell(cell.position, worksheet, cell.value, ...cell.style);
      } else if (_.has(cell, 'style')) {
        insertStyleCell(cell.position, worksheet, cell.value, ...cell.style);
      } else {
        insertIntoCell(cell.position, worksheet, cell.value);
      }
    });
    if (_.has(config, 'mergeCells')) {
      worksheet['!merges'] = config.mergeCells;
    }
  };

  //insert columns for voyage Table
  let voyageColsStartRow = 21;

  excelData.voyageData?.forEach((item, index) => {
    insertStyleCell(
      {
        c: index + 2,
        r: voyageColsStartRow,
      },
      worksheet,
      item.column,
      true,
      'FFFFCC99',
    );
  });
  excelData.isThatVoyagePartial?.forEach((item, index) =>{
      const isThatVoyagePartialCell =
          {
            position: {
              c:1,
              r: voyageColsStartRow + 1 + index,
            },
            value: item ? '*' : ''
          }
      const cell_ref = XLSX.utils.encode_cell(isThatVoyagePartialCell.position);
      XLSX.utils.sheet_add_aoa(worksheet, [[isThatVoyagePartialCell.value]], { origin: cell_ref });
      worksheet[cell_ref].s = {
        alignment: {
          wrapText: true,
          horizontal: 'right',
        },
      }
  })
  //insert data for voyage Table
  excelData.voyageData?.forEach((item, index) => {
    item.data?.forEach((i, indexData) => {
      const voyageCellAddress = {
        c: index + 2,
        r: voyageColsStartRow + 1 + indexData,
      };
      insertStyleCell(voyageCellAddress, worksheet, i, false, item.color ? item.color : '');

      // voyage table each cell formuals calculation
      if (item.cell_relation) {
        item.cell_relation.formula(
          worksheet,
          [
            ...fetchEachCellParams(
              item.cell_relation.params,
              2,
              voyageColsStartRow + 1 + indexData,
            ),
          ],
          XLSX.utils.encode_cell(voyageCellAddress),
        );
      }
    });
  });

  // total calcualtion for voyage fields
  const voyageTableTotalCellAddress = fetchCellAddress(
    excelData.voyageData,
    voyageColsStartRow,
    excelData.voyagePageIndex + 1,
    2,
  );

  //  VOYAGE TABLE INDEX CALCULATION
  const voyagCo2BetweemPortndex = _.findIndex(excelData.voyageData, (o) => {
    return o.column == 'CO2 emitted on voyage between EU Ports (tons)';
  });

  //TOTAL INDEX CALCULATION TABLE
  const totalCo2EmittedIndexCal = _.findIndex(excelData.totalCalList, (o) => {
    if (excelData.filterYear >= CH4_N2O_RELEASE_YEAR) return o.column == 'Total GHG (CO2 equivalents) emitted (tons)';
    else return o.column == 'Total CO2 emitted (tons)';
  });

  excelData.voyageData?.forEach((item, index) => {
    insertStyleCell(
      {
        c: index + 2,
        r: voyageColsStartRow + excelData.voyagePageIndex + 1,
      },
      worksheet,
      index + 2 === 2 ? 'TOTALS' : '',
      true,
      item.totalCellColor,
    );

    // find address of voyage Table start Cell and end Cell
    const voyageStartCell = XLSX.utils.encode_cell({ c: index + 2, r: voyageColsStartRow + 1 });
    const voyageEndCell = XLSX.utils.encode_cell({
      c: index + 2,
      r: voyageColsStartRow + excelData.voyagePageIndex,
    });

    // Voyage table total field calculation method
    if (item.relation) {
      item.relation.formula(
        worksheet,
        [...fetchParams(item.relation.params)],
        voyageTableTotalCellAddress[index],
      );
    }

    switch (item.total) {
      case 'sum':
        insertRangeSumCalculation(
          worksheet,
          voyageStartCell,
          voyageEndCell,
          voyageTableTotalCellAddress[index],
        );
        break;
      case 'sumWithString':
        insertSumCalculationWithString(
          worksheet,
          voyageStartCell,
          voyageEndCell,
          voyageTableTotalCellAddress[index],
        );
      break;
      case 'firstFieldCalculation':
        firstFieldCalculation(worksheet, voyageStartCell, voyageTableTotalCellAddress[index]);
        break;
      case 'voyageCo2Cal':
        VoyageCo2Calculation(
          worksheet,
          voyageStartCell,
          voyageEndCell,
          voyageTableTotalCellAddress[voyagCo2BetweemPortndex],
          voyageTableTotalCellAddress[index],
        );
        break;
      default:
        break;
    }
  });

  // Port Table
  let portColsStartRow = voyageColsStartRow + excelData.voyagePageIndex + 4;
  // push  Port table data sub-headings in config
  config.mergeCells.push({
    s: { r: portColsStartRow, c: 13 },
    e: { r: portColsStartRow, c: 17 },
  });
  config.cells.push({
    position: {
      c: 13,
      r: portColsStartRow,
    },
    value: 'Data required in Port',
    style: [false, 'e4dfec'],
    border: ['top', 'right'],
    size: 4,
  });

  // push other subheading in config
  config.mergeCells.push({
    s: { r: portColsStartRow + 1, c: 13 },
    e: { r: portColsStartRow + 1, c: 17 },
  });
  config.cells.push({
    position: {
      c: 13,
      r: portColsStartRow + 1,
    },
    value: 'Data calculated on an annual basis',
    style: [false, 'ccffcc'],
    border: ['top', 'right', 'bottom'],
    size: 4,
  });

  // push Data monitoring during port heading in config
  config.mergeCells.push({
    s: { r: portColsStartRow + 1, c: 9 },
    e: { r: portColsStartRow + 1, c: 11 },
  });
  config.cells.push({
    position: {
      c: 9,
      r: portColsStartRow + 1,
    },
    value: 'Monitoring during port',
    style: [true, undefined, undefined, false],
  });

  // insert columns for port Table
  excelData.portData?.forEach((item, index) => {
    insertStyleCell(
      {
        c: index + 2,
        r: portColsStartRow + 3,
      },
      worksheet,
      item.column,
      true,
      'FFFFCC99',
    );
  });

  excelData.forPortIsThatVoyagePartial?.forEach((item, index) =>{
    const portCellAddress = {
      c: 1,
      r: portColsStartRow + 1 + index,
    };
    const isThatPortPartialCell =
        {
          position: {
            c:1,
            r: portColsStartRow + 4 + index,
          },
          value: item ? '*' : ''
        }
    const cell_ref = XLSX.utils.encode_cell(isThatPortPartialCell.position);
    XLSX.utils.sheet_add_aoa(worksheet, [[isThatPortPartialCell.value]], { origin: cell_ref });
    worksheet[cell_ref].s = {
      alignment: {
        wrapText: true,
        horizontal: 'right',
      },
    }
  })

  // insert data for port Table
  excelData.portData?.forEach((item, index) => {
    if (item.data) {
      item.data?.forEach((i, indexData) => {
        const portCellAddress = {
          c: index + 2,
          r: portColsStartRow + 4 + indexData,
        };
        insertStyleCell(portCellAddress, worksheet, i, false, item.color ? item.color : '');

        // port table each cell formuals calculation
        if (item.cell_relation) {
          item.cell_relation.formula(
            worksheet,
            [
              ...fetchEachCellParams(
                item.cell_relation.params,
                2,
                portColsStartRow + 4 + indexData,
              ),
            ],
            XLSX.utils.encode_cell(portCellAddress),
          );
        }
      });
    } else {
      [...Array(excelData.portPageIndex)].forEach((_, indexData) => {
        insertStyleCell(
          {
            c: index + 2,
            r: portColsStartRow + 4 + indexData,
          },
          worksheet,
          '',
          false,
          'FFFFFF',
        );
      });
    }
  });

  // Total calculation for port table
  const portTableTotalCellAddress = fetchCellAddress(
    excelData.portData,
    portColsStartRow,
    excelData.portPageIndex + 4,
    2,
  );

  excelData.portData.forEach((item, index) => {
    insertStyleCell(
      {
        c: index + 2,
        r: portColsStartRow + excelData.portPageIndex + 4,
      },
      worksheet,
      index + 2 === 2 ? 'TOTALS' : '',
      true,
      item.totalCellColor,
    );

    // find address of voyage Table start Cell and end Cell
    const portStartCell = XLSX.utils.encode_cell({
      c: index + 2,
      r: portColsStartRow + 4,
    });
    const portEndCell = XLSX.utils.encode_cell({
      c: index + 2,
      r: portColsStartRow + 3 + excelData.portPageIndex,
    });

    // Port table field calculation method
    if (item.relation) {
      item.relation.formula(
        worksheet,
        [...fetchParams(item.relation.params)],
        portTableTotalCellAddress[index],
      );
    }

    switch (item.total) {
      case 'sum':
        insertRangeSumCalculation(
          worksheet,
          portStartCell,
          portEndCell,
          portTableTotalCellAddress[index],
        );
        break;
      case 'sumWithString':
        insertSumCalculationWithString(
          worksheet,
          portStartCell,
          portEndCell,
          portTableTotalCellAddress[index],
        );
        break;
      case 'firstFieldCalculation':
        firstFieldCalculation(worksheet, portStartCell, portTableTotalCellAddress[index]);
        break;
      default:
        break;
    }
  });

  // heading for third table to calculate total
  let mergeVoyagePortTableStartRow = portColsStartRow + excelData.portPageIndex + 8;

  // push Totals as required in config
  config.mergeCells.push({
    s: { r: mergeVoyagePortTableStartRow, c: 2 },
    e: { r: mergeVoyagePortTableStartRow, c: 25 },
  });
  config.cells.push({
    position: {
      c: 2,
      r: mergeVoyagePortTableStartRow,
    },
    value: 'Totals as required by the EU MRV regulation:',
    style: [true, '0000ff', 'FFFFFF', false, 'left'],
  });

  // push Totals as required in config
  config.mergeCells.push({
    s: { r: mergeVoyagePortTableStartRow, c: 26 },
    e: { r: mergeVoyagePortTableStartRow, c: 39 },
  });
  config.cells.push({
    position: {
      c: 26,
      r: mergeVoyagePortTableStartRow,
    },
    value: 'Average energy efficiency',
    style: [true, '0000ff', 'FFFFFF'],
  });

  // columns for third table
  excelData.totalCalList?.forEach((item, index) => {
    insertStyleCell(
      {
        c: index + 8,
        r: mergeVoyagePortTableStartRow + 1,
      },
      worksheet,
      item.column,
      true,
      'FFFFCC99',
    );
  });

  // push merge blank cells in config
  config.mergeCells.push({
    s: { r: mergeVoyagePortTableStartRow + 1, c: 2 },
    e: { r: mergeVoyagePortTableStartRow + 1, c: 7 },
  });

  // push Report year totals merge cell in config
  config.mergeCells.push({
    s: { r: mergeVoyagePortTableStartRow + 2, c: 2 },
    e: { r: mergeVoyagePortTableStartRow + 2, c: 7 },
  });
  config.cells.push({
    position: {
      c: 2,
      r: mergeVoyagePortTableStartRow + 2,
    },
    value: 'Report year totals:',
    style: [true, undefined, undefined, true, 'left'],
    border: ['top', 'right', 'bottom', 'left'],
    size: 5,
  });

  //TOTAL CALCULATION FOR THIRD TABLE
  let totalColsStartRow = portColsStartRow + excelData.portPageIndex + 8;
  const totalTableTotalCellAddress = fetchCellAddress(
    excelData.totalCalList,
    totalColsStartRow,
    2,
    8,
  );

  excelData.totalCalList.forEach((item, index) => {
    if (index >= 0) {
      insertStyleCell(
        {
          c: index + 8,
          r: mergeVoyagePortTableStartRow + 2,
        },
        worksheet,
        item.total ?? '',
        false,
        item.totalCellColor,
      );
    }

    // Total table field calculation method
    if (item.relation) {
      item.relation.formula(
        worksheet,
        [...fetchParams(item.relation.params)],
        totalTableTotalCellAddress[index],
      );
    }
  });

  // total CO2 calculation heading
  mergeVoyagePortTableStartRow = mergeVoyagePortTableStartRow + 4;
  // push b in config cell
  config.cells.push({
    position: {
      c: 1,
      r: mergeVoyagePortTableStartRow,
    },
    value: '(b)',
  });

  // push total co2 heading in config cell

  config.mergeCells.push({
    s: { r: mergeVoyagePortTableStartRow, c: 2 },
    e: { r: mergeVoyagePortTableStartRow, c: 11 },
  });
  config.cells.push({
    position: {
      c: 2,
      r: mergeVoyagePortTableStartRow,
    },
    value: excelData.filterYear >= CH4_N2O_RELEASE_YEAR ? 'Total CO2(e) (tons) reported to the competent authority:' : 'Total CO2 (tons) reported to the competent authority:',
    style: [true, 'FFFFFF', '000000', true, 'left'],
    border: ['top', 'right', 'bottom', 'left'],
    size: 9,
  });

  //total CO2 calculation count value

  const totalCo2Reported = XLSX.utils.encode_cell({
    c: 12,
    r: mergeVoyagePortTableStartRow,
  });

  insertStyleCell(
    {
      c: 12,
      r: mergeVoyagePortTableStartRow,
    },
    worksheet,
    '',
    true,
    'ccffcc',
  );

  firstFieldCalculation(
    worksheet,
    totalTableTotalCellAddress[totalCo2EmittedIndexCal],
    totalCo2Reported,
  );

  // call function to insert data in cells
  insertDataIntoCells(config, worksheet);
  XLSX.writeFile(wb, `${excelData?.fileName}.xlsx`);
};
