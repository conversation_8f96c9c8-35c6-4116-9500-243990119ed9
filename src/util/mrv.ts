export const CH4_N2O_RELEASE_YEAR = 2024;

export const mrvColumnsToBeRemovedForPastYears = [
  'me_lng',
  'lng_engine_category_main_engine_emission_factor',
  'ge_lng',
  'lng_engine_category_diesel_generator_emission_factor',
  'ch4_hfo_emission_factor',
  'ch4_hfo_emitted',
  'n2o_hfo_emission_factor',
  'n2o_hfo_emitted',
  'ch4_lfo_emission_factor',
  'ch4_lfo_emitted',
  'n2o_lfo_emission_factor',
  'n2o_lfo_emitted',
  'ch4_mgo_emission_factor',
  'ch4_mgo_emitted',
  'n2o_mgo_emission_factor',
  'n2o_mgo_emitted',
  'ch4_lng_emission_factor',
  'ch4_lng_emitted',
  'n2o_lng_emission_factor',
  'n2o_lng_emitted',
  'ch4_lpg_propane_emission_factor',
  'ch4_lpg_propane_emitted',
  'n2o_lpg_propane_emission_factor',
  'n2o_lpg_propane_emitted',
  'ch4_lpg_butane_emission_factor',
  'ch4_lpg_butane_emitted',
  'n2o_lpg_butane_emission_factor',
  'n2o_lpg_butane_emitted',
  'ch4_methanol_emission_factor',
  'ch4_methanol_emitted',
  'n2o_methanol_emission_factor',
  'n2o_methanol_emitted',
  'ch4_ethanol_emission_factor',
  'ch4_ethanol_emitted',
  'n2o_ethanol_emission_factor',
  'n2o_ethanol_emitted',
  'other_fuel_name',
  'other_fuel_cons',
  'other_fuel_co2_emission_factor',
  'other_fuel_co2_emitted',
  'ch4_total_emitted',
  'n2o_total_emitted',
  'ch4_total_co2_emitted',
  'n2o_total_co2_emitted',
  'total_ghg',
];

const CO2_EMITTED_VOYAGE_BETWEEN_EU_PORT = 'co2_emitted_voyage_between_eu_port';
const CO2_EMITTED_VOYAGE_FROM_EU_PORT = 'co2_emitted_voyage_from_eu_port';
const CO2_EMITTED_VOYAGE_TO_EU_PORT = 'co2_emitted_voyage_to_eu_port';
const CO2_EMITTED_PER_DISTANCE = 'co2_emitted_per_distance';
const CO2_EMITTED_PER_TRANSPORT_WORK = 'co2_emitted_per_transport_work';

export const getCustomColumnHeader = (columnID, year) => {
  if (year < CH4_N2O_RELEASE_YEAR) {
    switch (columnID) {
      case CO2_EMITTED_VOYAGE_BETWEEN_EU_PORT: {
        return 'CO\u2082 emitted on voyage between EU Ports (tons)';
      }
      case CO2_EMITTED_VOYAGE_FROM_EU_PORT: {
        return 'CO\u2082 emitted on voyage departed from EU Port (tons)';
      }
      case CO2_EMITTED_VOYAGE_TO_EU_PORT: {
        return 'CO\u2082 emitted on voyage arrived to EU Port (tons)';
      }
      case CO2_EMITTED_PER_DISTANCE: {
        return 'CO\u2082 emissions per distance (tn/nm)';
      }
      case CO2_EMITTED_PER_TRANSPORT_WORK: {
        return 'CO\u2082 emissions per transport work (gCO\u2082/tn*nm)';
      }
      default:
        return '';
    }
  } else {
    switch (columnID) {
      case CO2_EMITTED_VOYAGE_BETWEEN_EU_PORT: {
        return 'CO\u2082(e) emitted on voyage between EU Ports (tons)';
      }
      case CO2_EMITTED_VOYAGE_FROM_EU_PORT: {
        return 'CO\u2082(e) emitted on voyage departed from EU Port (tons)';
      }
      case CO2_EMITTED_VOYAGE_TO_EU_PORT: {
        return 'CO\u2082(e) emitted on voyage arrived to EU Port (tons)';
      }
      case CO2_EMITTED_PER_DISTANCE: {
        return 'CO\u2082(e) emissions per distance (tn/nm)';
      }
      case CO2_EMITTED_PER_TRANSPORT_WORK: {
        return 'CO\u2082(e) emissions per transport work (gCO\u2082/tn*nm)';
      }
      default:
        return '';
    }
  }
};
