export const QUERY_TYPE_LIKE = 'like';
export const QUERY_TYPE_MATCH = 'match';
export const QUERY_TYPE_RANGE = 'range';

const SEARCH_TYPES = [
  // Basic
  {
    type: 'vessel_name',
    name: 'Vessel Name',
    section: 'Basic',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'name',
  },
  {
    type: 'imo_number',
    name: 'IMO Number',
    section: 'Basic',
    inputType: 'number',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'imo_number',
  },
  {
    type: 'owners',
    name: 'Owners',
    section: 'Basic',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'owner',
  },
  {
    type: 'vesselTypes',
    name: 'Vessel Type',
    section: 'Basic',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel_type',
  },
  {
    type: 'emissionTypes',
    name: 'Emission Type',
    section: 'Basic',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'emission_type',
  },
  {
    type: 'shipyards',
    name: 'Shipyard',
    section: 'Basic',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'shipyard_text',
  },
  {
    type: 'vessel_hull_number',
    name: 'Vessel Hull Number',
    section: 'Basic',
    inputType: 'text',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel_hull_number',
  },
  {
    type: 'year_built',
    name: 'Year Built / Date of Delivery',
    section: 'Basic',
    inputType: 'year',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'date_of_delivery',
  },
  {
    type: 'date_of_takeover',
    name: 'Date of Takeover',
    section: 'Basic',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'date_of_takeover',
  },
  {
    type: 'date_of_handover',
    name: 'Date of Handover',
    section: 'Basic',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'date_of_handover',
  },
  {
    type: 'expected_date_of_takeover',
    name: 'Expected Date of Takeover',
    section: 'Basic',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'expected_date_of_takeover',
  },
  // Basic 2
  {
    type: 'flags',
    name: 'Flag',
    section: 'Basic 2',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'flag',
  },
  {
    type: 'portOfRegistrys',
    name: 'Port of registry',
    section: 'Basic 2',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'port_of_registry',
  },
  {
    type: 'callSign',
    name: 'Call Sign',
    section: 'Basic 2',
    inputType: 'text',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'call_sign',
  },
  {
    type: 'class_notation',
    name: 'Class Notation',
    section: 'Basic 2',
    inputType: 'text',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'notation',
  },
  {
    type: 'hmUnderwriters',
    name: 'H & M Underwriter',
    section: 'Basic 2',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'h_m_underwriter',
  },
  {
    type: 'piClubs',
    name: 'P & I Club',
    section: 'Basic 2',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'p_i_club',
  },
  {
    type: 'euVerifiers',
    name: 'EU Verifier',
    section: 'Basic 2',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel_class_regulation',
  },
  // Contact
  //NOTE: in future search by phone number, email
  // {
  //   type: 'emailTypes',
  //   name: 'Email',
  //   section: 'Contact',
  //   inputType: 'dropdown',
  //   queryType: QUERY_TYPE_MATCH,
  //   queryKey: 'email_type',
  // },
  // {
  //   type: 'phoneTypes',
  //   name: 'Phone',
  //   section: 'Contact',
  //   inputType: 'dropdown',
  //   queryType: QUERY_TYPE_MATCH,
  //   queryKey: 'phone_type',
  // },
  // External platforms
  {
    type: 'eorbStatuses',
    name: 'e-ORB',
    section: 'External Platforms',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel_product_status',
  },
  // Particulars
  {
    type: 'life_boat_capacity',
    name: 'Life Boat Capacity',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'life_boat_capacity',
  },
  {
    type: 'length_oa',
    name: 'Length O.A.',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'length_oa',
  },
  {
    type: 'length_bp',
    name: 'Length B.P.',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'length_bp',
  },
  {
    type: 'depth',
    name: 'Depth',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'depth',
  },
  {
    type: 'breadth_extreme',
    name: 'Breadth (Extreme)',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'breadth_extreme',
  },
  {
    type: 'summer_draft',
    name: 'Summer Draft',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'summer_draft',
  },
  {
    type: 'summer_dwt',
    name: 'Summer DWT',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'summer_dwt',
  },
  {
    type: 'international_grt',
    name: 'International GRT',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'international_grt',
  },
  {
    type: 'international_nrt',
    name: 'International NRT',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'international_nrt',
  },
  {
    type: 'service_speed',
    name: 'Service Speed',
    section: 'Particulars',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'service_speed',
  },
  {
    type: 'tech_group',
    name: 'Tech Group',
    section: 'Office Data',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'fleet_staff_tech_group',
  },
  {
    type: 'superintendent',
    name: 'Superintendent',
    section: 'Office Data',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'fleet_staff_superintendent_id',
  },
  {
    type: 'qhse',
    name: 'QHSE',
    section: 'Office Data',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'fleet_staff_qhse_id',
  },
  {
    type: 'operations',
    name: 'Operations',
    section: 'Office Data',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'fleet_staff_operation_id',
  },
  {
    type: 'accountant',
    name: 'Vessel Accountant',
    section: 'Office Data',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'fleet_staff_accountant_id',
  },
  {
    type: 'payroll',
    name: 'Payroll Accountant',
    section: 'Office Data',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'fleet_staff_payroll_id',
  },
  {
    type: 'buyers',
    name: 'Procurement',
    section: 'Office Data',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'fleet_staff_buyer_id',
  },
];

export default () => SEARCH_TYPES;
