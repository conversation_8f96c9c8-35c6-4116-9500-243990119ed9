import { euVoyageConfig } from './eu-voyage-config';
import { euPendingVoyageConfig } from './eu-pending-voyage-config';
import { euPortConfig } from './eu-port-config';
import { euTotalSummaryConfig } from './eu-total-summary-config';
import moment from 'moment';

export const getEUETSExcelData = ({
  vesselName,
  voyage,
  pendingVoyages,
  port,
  vesselOwnershipData,
  reportingYear,
  reportingDuration,
  etsRate,
  etsRateDate,
  filterData,
  topSectionData,
}) => ({
  fileName: `EU Emission Reports_${vesselName}_${moment(filterData.endDate).format('YYYYMM')}_${moment().format('DD-MM-YY-hh-mm')}`,
  reportingYear,
  reportingDuration,
  vesselName: vesselName,
  voyagePageIndex: voyage.length,
  pendingVoyagePageIndex: pendingVoyages.length,
  portPageIndex: port.length,
  vesselOwnershipData,
  etsRate,
  etsRateDate,
  voyageData: euVoyageConfig(voyage, filterData),
  pendingVoyageData: euPendingVoyageConfig(pendingVoyages, filterData),
  portData: euPortConfig(port, filterData),
  totalCalList: euTotalSummaryConfig(filterData),
  topSectionData,
});
