import XLSX from 'sheetjs-style';
import {
  insertRangeSumCalculation,
  firstFieldCalculation,
  insertStyleCell,
  fetchCellAddress,
} from '../excel-export';
import { EUETSComments } from './eu-ets-comments';
import { ETSCommonExportUtil } from './ets-common-export-util';
import { EUETSIdentification } from './eu-ets-identification';
import { formatNumber } from '../view-utils';
import { WELL_TO_WAKE_GHG_INTENSITY_VALUES } from '../../../src/constants/fuel-eu-constants';
const TABLE_HEADING_STYLES = [true, 'B4C6E7', undefined, true, 'center', 'center', '14'];
const EU_ETS_HEADING_STYLES = [true, 'EEECE1', undefined, true, 'center', 'center', '12'];
const FUEL_EU_HEADING_STYLES = [true, 'DAEEF3', undefined, true, 'center', 'center', '12'];
const REFERENCE_BDN_HEADING_STYLES = [true, 'F2F2F2', undefined, true, 'center', 'center', '12'];

export class EUETSExcelExport {
  excelData;
  wb;
  config;
  voyageTableTotalCellAddress = {};
  portTableTotalCellAddress = {};
  totalTableTotalCellAddress = {};
  constructor(excelData) {
    this.excelData = excelData;
  }
  setConfig() {
    const {
      nameOfShip,
      lfoEmissionFactor,
      lngEmissionFactor,
      methanolEmissionFactor,
      hfoEmissionFactor,
      mgoEmissionFactor,
      lpgPropaneEmissionFactor,
      ethanolEmissionFactor,
      lpgButaneEmissionFactor,
      verifierName,
      totalEUAAccrued,
      averageGHGIntensity,
      totalComplianceBalance,
      totalEstimateFuelEUPenalty,
    } = this.excelData.topSectionData;
    this.config = {
      cells: [
        // cells with style
        {
          position: {
            c: 1,
            r: 2,
          },
          value: 'EU: Voyage - Port Data',
          style: [true, '0000FF', 'FFFFFF', true, 'left', undefined, '12'],
        },
        {
          position: {
            c: 1,
            r: 4,
          },
          value: `Ship's voyage and port data in the table below .`,
          style: [true, undefined, undefined, false, 'left', 'center', '10'],
        },
        {
          position: {
            c: 1,
            r: 5,
          },
          value:
            'Please fill in the table below. If you need additional rows, please insert them above the "end of list" row. In that case the formula for the totals will work correctly. Note that the formulas are protected.',
          style: [false, 'FFFFFF', '333399', false, 'left', 'center', '8'],
        },
        {
          position: {
            c: 9,
            r: 14,
          },
          value: 'Monitoring During Voyage: Completed',
          style: TABLE_HEADING_STYLES,
        },
        // ship name row
        {
          position: {
            c: 1,
            r: 7,
          },
          value: `Name of Ship`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '13'],
        },
        {
          position: {
            c: 2,
            r: 7,
          },
          value: `${nameOfShip}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // table headers
        {
          position: {
            c: 1,
            r: 8,
          },
          value: `Reference Value`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '13'],
        },
        {
          position: {
            c: 2,
            r: 8,
          },
          value: `Fuel`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '13'],
        },
        {
          position: {
            c: 3,
            r: 8,
          },
          value: `CO2 Emission Factors`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '13'],
        },
        {
          position: {
            c: 4,
            r: 8,
          },
          value: `WtW GHG Intensity`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '13'],
        },
        {
          position: {
            c: 5,
            r: 8,
          },
          value: `Fuel`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '13'],
        },
        {
          position: {
            c: 6,
            r: 8,
          },
          value: `CO2 Emission Factors`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '13'],
        },
        {
          position: {
            c: 7,
            r: 8,
          },
          value: `WtW GHG Intensity`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '13'],
        },
        // LFO row
        {
          position: {
            c: 2,
            r: 9,
          },
          value: `LFO`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 3,
            r: 9,
          },
          value: `${lfoEmissionFactor}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 4,
            r: 9,
          },
          value: `${WELL_TO_WAKE_GHG_INTENSITY_VALUES.LFO}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // MGO row
        {
          position: {
            c: 5,
            r: 9,
          },
          value: `MGO/MDO`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 6,
            r: 9,
          },
          value: `${mgoEmissionFactor}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 7,
            r: 9,
          },
          value: `${WELL_TO_WAKE_GHG_INTENSITY_VALUES.MGO}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // LNG row
        {
          position: {
            c: 2,
            r: 10,
          },
          value: `LNG`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 3,
            r: 10,
          },
          value: `${lngEmissionFactor}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 4,
            r: 10,
          },
          value: `${WELL_TO_WAKE_GHG_INTENSITY_VALUES.LNG}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // LPG (Propane) row
        {
          position: {
            c: 5,
            r: 10,
          },
          value: `LPG (PROPANE)`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 6,
            r: 10,
          },
          value: `${lpgPropaneEmissionFactor}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 7,
            r: 10,
          },
          value: `${WELL_TO_WAKE_GHG_INTENSITY_VALUES.LPG_PROPANE}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        //METHANOL row
        {
          position: {
            c: 2,
            r: 11,
          },
          value: `METHANOL`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 3,
            r: 11,
          },
          value: `${methanolEmissionFactor}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 4,
            r: 11,
          },
          value: `${WELL_TO_WAKE_GHG_INTENSITY_VALUES.METHANOL_NG}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // ETHANOL row
        {
          position: {
            c: 5,
            r: 11,
          },
          value: `ETHANOL`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 6,
            r: 11,
          },
          value: `${ethanolEmissionFactor}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 7,
            r: 11,
          },
          value: `n/a`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // HFO row
        {
          position: {
            c: 2,
            r: 12,
          },
          value: `HFO`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 3,
            r: 12,
          },
          value: `${hfoEmissionFactor}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 4,
            r: 12,
          },
          value: `${WELL_TO_WAKE_GHG_INTENSITY_VALUES.HFO}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // LPG (Butane) row
        {
          position: {
            c: 5,
            r: 12,
          },
          value: `LPG (BUTANE)`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 6,
            r: 12,
          },
          value: `${lpgButaneEmissionFactor}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 7,
            r: 12,
          },
          value: `${WELL_TO_WAKE_GHG_INTENSITY_VALUES.LPG_BUTANE}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // Verifier name row
        {
          position: {
            c: 1,
            r: 13,
          },
          value: `Verifier name`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '13'],
        },
        {
          position: {
            c: 2,
            r: 13,
          },
          value: `${verifierName}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // Carbon Rate row
        {
          position: {
            c: 9,
            r: 7,
          },
          value: `Carbon Rate (€/ton): `,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 10,
            r: 7,
          },
          value: `${this.excelData.etsRate} (as of ${this.excelData.etsRateDate})`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // Total EUAs Accrued row
        {
          position: {
            c: 9,
            r: 8,
          },
          value: `Total EUAs Accrued`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 10,
            r: 8,
          },
          value: `${formatNumber(totalEUAAccrued, '0.00', 5)}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // Average GHG Intensity (gCO2eq/MJ) row
        {
          position: {
            c: 11,
            r: 7,
          },
          value: `Average GHG Intensity (gCO2eq/MJ)`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 12,
            r: 7,
          },
          value: `${formatNumber(averageGHGIntensity, '0.00', 5)}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // Compliance Surplus (tCO2eq) row
        {
          position: {
            c: 11,
            r: 8,
          },
          value: `Compliance Surplus (tCO2eq)`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 12,
            r: 8,
          },
          value:
            totalComplianceBalance >= 0 ? formatNumber(totalComplianceBalance, '0.00', 5) : '-',
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },
        // Total FuelEU Penalty Accrued (€) row
        {
          position: {
            c: 11,
            r: 9,
          },
          value: `Total FuelEU Penalty Accrued (€)`,
          style: [true, 'B7B7B7', undefined, true, 'left', 'center', '12'],
        },
        {
          position: {
            c: 12,
            r: 9,
          },
          value: `${formatNumber(Math.max(totalEstimateFuelEUPenalty, 0), '0.00', 5)}`,
          style: [true, undefined, undefined, true, 'left', 'center', '12'],
        },

        // cells with styles and border
      ],
      mergeCells: [
        // voyage port data part-1
        { s: { r: 2, c: 1 }, e: { r: 2, c: 10 } },
        // instruction heading cell merge
        { s: { r: 4, c: 1 }, e: { r: 4, c: 10 } },
        // insert instruction to fill data in Table
        { s: { r: 5, c: 1 }, e: { r: 5, c: 10 } },
        //Data monitoring during voyage heading
        { s: { r: 14, c: 9 }, e: { r: 14, c: 14 } },
        // Carbon rate
        // { s: { r: 7, c: 8 }, e: { r: 7, c: 9 } },
        // Total EUAs Accrued
        // { s: { r: 8, c: 8 }, e: { r: 8, c: 9 } },
        // Name of Ship
        { s: { r: 7, c: 2 }, e: { r: 7, c: 3 } },

        //insert voyage table data sub-headings
        // { s: { r: 17, c: 13 }, e: { r: 17, c: 17 } },
        // { s: { r: 18, c: 13 }, e: { r: 18, c: 17 } },
      ],
    };
  }
  renderVoyageTable = (dataWorksheet) => {
    let voyageColsStartRow = 16;
    this.excelData.voyageData?.forEach((item, index) => {
      insertStyleCell(
        {
          c: index + 2,
          r: voyageColsStartRow,
        },
        dataWorksheet,
        item.column,
        true,
        item.headingColor || 'FFF4B083',
        undefined,
        true,
        'center',
        'center',
      );
    });

    this.config.cells.push({
      position: {
        c: 23,
        r: 15,
      },
      value: 'EU_ETS',
      style: EU_ETS_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: 15, c: 23 },
      e: { r: 15, c: 26 },
    });

    this.config.cells.push({
      position: {
        c: 27,
        r: 15,
      },
      value: 'FuelEU Maritime',
      style: FUEL_EU_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: 15, c: 27 },
      e: { r: 15, c: 30 },
    });

    this.config.cells.push({
      position: {
        c: 31,
        r: 15,
      },
      value: 'Reference Cells (Hidden by default)',
      style: REFERENCE_BDN_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: 15, c: 31 },
      e: { r: 15, c: 37 },
    });

    // Initialize column width configuration for hiding specific columns
    if (!dataWorksheet['!cols']) {
      dataWorksheet['!cols'] = [];
    }
    const columnsToHide = [31, 32, 33, 34, 35, 36, 37, 38, 39];
    columnsToHide.forEach((colIndex) => {
      dataWorksheet['!cols'][colIndex] = { hidden: true };
    });

    //insert data for voyage Table
    this.excelData.voyageData?.forEach((item, index) => {
      item.data?.forEach((i, indexData) => {
        const voyageCellAddress = {
          c: index + 2,
          r: voyageColsStartRow + 1 + indexData,
        };
        insertStyleCell(voyageCellAddress, dataWorksheet, i, false, item.color ? item.color : '');

        // voyage table each cell formuals calculation
        if (item.cell_relation) {
          item.cell_relation.formula(
            dataWorksheet,
            [
              ...this.fetchEachCellParams(
                item.cell_relation.params,
                2,
                voyageColsStartRow + 1 + indexData,
              ),
            ],
            XLSX.utils.encode_cell(voyageCellAddress),
          );
        }
      });
    });
    if (this.excelData.voyageData.length && this.excelData.voyageData[0].data.length) {
      // total calcualtion for voyage fields
      this.voyageTableTotalCellAddress = fetchCellAddress(
        this.excelData.voyageData,
        voyageColsStartRow,
        this.excelData.voyagePageIndex + 1,
        2,
      );

      this.excelData.voyageData?.forEach((item, index) => {
        insertStyleCell(
          {
            c: index + 2,
            r: voyageColsStartRow + this.excelData.voyagePageIndex + 1,
          },
          dataWorksheet,
          index + 2 === 2 ? 'TOTALS' : '',
          true,
          item.totalCellColor,
        );

        // find address of voyage Table start Cell and end Cell
        const voyageStartCell = XLSX.utils.encode_cell({ c: index + 2, r: voyageColsStartRow + 1 });
        const voyageEndCell = XLSX.utils.encode_cell({
          c: index + 2,
          r: voyageColsStartRow + this.excelData.voyagePageIndex,
        });
        const totalCellAddress = this.voyageTableTotalCellAddress[index];
        // Voyage table total field calculation method
        if (item.relation) {
          item.relation.formula(
            dataWorksheet,
            [...this.fetchParams(item.relation.params)],
            totalCellAddress,
          );
        }
        switch (item.total) {
          case 'sum':
            insertRangeSumCalculation(
              dataWorksheet,
              voyageStartCell,
              voyageEndCell,
              totalCellAddress,
            );
            break;
          case 'firstFieldCalculation':
            firstFieldCalculation(dataWorksheet, voyageStartCell, totalCellAddress);
            break;
          default:
            break;
        }
      });
    }
    return voyageColsStartRow;
  };

  renderPendingVoyage = (dataWorksheet, voyageColsStartRow) => {
    let pendingVoyageColStartRow = voyageColsStartRow + this.excelData.voyagePageIndex + 2;
    this.config.cells.push({
      position: {
        c: 9,
        r: pendingVoyageColStartRow + 1,
      },
      value: 'Monitoring During Voyage: Pending (FOR INFORMATION ONLY)',
      style: TABLE_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: pendingVoyageColStartRow + 1, c: 9 },
      e: { r: pendingVoyageColStartRow + 1, c: 14 },
    });

    this.config.cells.push({
      position: {
        c: 23,
        r: pendingVoyageColStartRow + 2,
      },
      value: 'EU_ETS',
      style: EU_ETS_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: pendingVoyageColStartRow + 2, c: 23 },
      e: { r: pendingVoyageColStartRow + 2, c: 26 },
    });

    this.config.cells.push({
      position: {
        c: 27,
        r: pendingVoyageColStartRow + 2,
      },
      value: 'FuelEU Maritime',
      style: FUEL_EU_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: pendingVoyageColStartRow + 2, c: 27 },
      e: { r: pendingVoyageColStartRow + 2, c: 30 },
    });

    this.config.cells.push({
      position: {
        c: 31,
        r: pendingVoyageColStartRow + 2,
      },
      value: 'Reference Cells (Hidden by default)',
      style: REFERENCE_BDN_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: pendingVoyageColStartRow + 2, c: 31 },
      e: { r: pendingVoyageColStartRow + 2, c: 37 },
    });

    // insert columns for port Table
    this.excelData.pendingVoyageData?.forEach((item, index) => {
      insertStyleCell(
        {
          c: index + 2,
          r: pendingVoyageColStartRow + 3,
        },
        dataWorksheet,
        item.column,
        true,
        item.headingColor || 'FFF4B083',
        undefined,
        undefined,
        'center',
        'center',
      );
    });
    pendingVoyageColStartRow = pendingVoyageColStartRow + 3;

    this.excelData.pendingVoyageData?.forEach((item, index) => {
      if (item.data) {
        item.data?.forEach((i, indexData) => {
          const portCellAddress = {
            c: index + 2,
            r: pendingVoyageColStartRow + 1 + indexData,
          };
          insertStyleCell(portCellAddress, dataWorksheet, i, false, item.color ? item.color : '');

          // port table each cell formuals calculation
          if (item.cell_relation) {
            item.cell_relation.formula(
              dataWorksheet,
              [
                ...this.fetchEachCellParams(
                  item.cell_relation.params,
                  2,
                  pendingVoyageColStartRow + 1 + indexData,
                ),
              ],
              XLSX.utils.encode_cell(portCellAddress),
            );
          }
        });
      } else {
        [...Array(this.excelData.pendingVoyagePageIndex)].forEach((_, indexData) => {
          insertStyleCell(
            {
              c: index + 2,
              r: pendingVoyageColStartRow + 4 + indexData,
            },
            dataWorksheet,
            '',
            false,
            'FFFFFF',
          );
        });
      }
    });
    return pendingVoyageColStartRow;
  };

  renderPortTable = (dataWorksheet, pendingVoyageColStartRow) => {
    let portColsStartRow = pendingVoyageColStartRow + this.excelData.pendingVoyagePageIndex + 1;
    this.config.cells.push({
      position: {
        c: 9,
        r: portColsStartRow + 1,
      },
      value: 'Monitoring During Port',
      style: TABLE_HEADING_STYLES,
    });
    // push Data monitoring during port heading in config
    this.config.mergeCells.push({
      s: { r: portColsStartRow + 1, c: 9 },
      e: { r: portColsStartRow + 1, c: 14 },
    });

    // insert columns for port Table
    this.excelData.portData?.forEach((item, index) => {
      let adjustedColumnIndex = index + 2;

      if (item.column === 'GHG (gCO2eq)'){
        adjustedColumnIndex += 7;
      }
      // Check if the column name starts with "Other Fuel -" and adjust the index
      if (
        /^Other Fuel -.+/.test(item.column) &&
        item.column !== 'Other Fuel Name' &&
        item.column !== 'Other Fuel - Consumed (M/T)'
      ) {
        adjustedColumnIndex += 7;
      }

      insertStyleCell(
        {
          c: adjustedColumnIndex,
          r: portColsStartRow + 3,
        },
        dataWorksheet,
        item.column,
        true,
        item.headingColor || 'FFF4B083',
        undefined,
        undefined,
        'center',
        'center',
      );
    });

    this.config.cells.push({
      position: {
        c: 17,
        r: portColsStartRow + 2,
      },
      value: 'EU_ETS',
      style: EU_ETS_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: portColsStartRow + 2, c: 17 },
      e: { r: portColsStartRow + 2, c: 19 },
    });

    this.config.cells.push({
      position: {
        c: 20,
        r: portColsStartRow + 2,
      },
      value: 'FuelEU Maritime',
      style: FUEL_EU_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: portColsStartRow + 2, c: 20 },
      e: { r: portColsStartRow + 2, c: 23 },
    });

    this.config.cells.push({
      position: {
        c: 31,
        r: portColsStartRow + 2,
      },
      value: 'Reference Cells (Hidden by default)',
      style: REFERENCE_BDN_HEADING_STYLES,
    });
    this.config.mergeCells.push({
      s: { r: portColsStartRow + 2, c: 31 },
      e: { r: portColsStartRow + 2, c: 37 },
    });

    portColsStartRow = portColsStartRow + 3;
    // insert data for port Table
    this.excelData.portData?.forEach((item, index) => {
      let adjustedColumnIndex = index + 2;

      if (item.column === 'GHG (gCO2eq)'){
        adjustedColumnIndex += 7;
      }
      // Skip six columns for "Other Fuel -" prefixed columns
      if (
        /^Other Fuel -.+/.test(item.column) &&
        item.column !== 'Other Fuel Name' &&
        item.column !== 'Other Fuel - Consumed (M/T)'
      ) {
        adjustedColumnIndex += 7;
      }

      if (item.data) {
        item.data?.forEach((i, indexData) => {
          const portCellAddress = {
            c: adjustedColumnIndex,
            r: portColsStartRow + 1 + indexData,
          };
          insertStyleCell(portCellAddress, dataWorksheet, i, false, item.color ? item.color : '');

          // port table each cell formuals calculation
          if (item.cell_relation) {
            item.cell_relation.formula(
              dataWorksheet,
              [
                ...this.fetchEachCellParams(
                  item.cell_relation.params,
                  2,
                  portColsStartRow + 1 + indexData,
                ),
              ],
              XLSX.utils.encode_cell(portCellAddress),
            );
          }
        });
      } else {
        [...Array(this.excelData.portPageIndex)].forEach((_, indexData) => {
          insertStyleCell(
            {
              c: adjustedColumnIndex,
              r: portColsStartRow + 4 + indexData,
            },
            dataWorksheet,
            '',
            false,
            'FFFFFF',
          );
        });
      }
    });

    if (this.excelData.portData.length && this.excelData.portData[0].data.length) {
      // Total calculation for port table
      this.portTableTotalCellAddress = fetchCellAddress(
        this.excelData.portData,
        portColsStartRow,
        this.excelData.portPageIndex + 1,
        2,
      );

      this.excelData.portData.forEach((item, index) => {
        let adjustedColumnIndex = index + 2;

        if (item.column === 'GHG (gCO2eq)'){
          adjustedColumnIndex += 7;
        }
        // Skip six columns for "Other Fuel -" prefixed columns
        if (
          /^Other Fuel -.+/.test(item.column) &&
          item.column !== 'Other Fuel Name' &&
          item.column !== 'Other Fuel - Consumed (M/T)'
        ) {
          adjustedColumnIndex += 7;
        }

        insertStyleCell(
          {
            c: adjustedColumnIndex,
            r: portColsStartRow + this.excelData.portPageIndex + 1,
          },
          dataWorksheet,
          adjustedColumnIndex === 2 ? 'TOTALS' : '',
          true,
          item.totalCellColor,
        );

        // find address of voyage Table start Cell and end Cell
        const portStartCell = XLSX.utils.encode_cell({
          c: adjustedColumnIndex,
          r: portColsStartRow + 1,
        });
        const portEndCell = XLSX.utils.encode_cell({
          c: adjustedColumnIndex,
          r: portColsStartRow + this.excelData.portPageIndex,
        });

        // Port table field calculation method
        if (item.relation) {
          item.relation.formula(
            dataWorksheet,
            [...this.fetchParams(item.relation.params)],
            XLSX.utils.encode_cell({
              c: adjustedColumnIndex,
              r: portColsStartRow + this.excelData.portPageIndex + 1,
            }),
          );
        }

        switch (item.total) {
          case 'sum':
            insertRangeSumCalculation(
              dataWorksheet,
              portStartCell,
              portEndCell,
              XLSX.utils.encode_cell({
                c: adjustedColumnIndex,
                r: portColsStartRow + this.excelData.portPageIndex + 1,
              }),
            );
            break;
          case 'firstFieldCalculation':
            firstFieldCalculation(
              dataWorksheet,
              portStartCell,
              XLSX.utils.encode_cell({
                c: adjustedColumnIndex,
                r: portColsStartRow + this.excelData.portPageIndex + 1,
              }),
            );
            break;
          default:
            break;
        }
      });
    }
    return portColsStartRow;
  };

  renderSummaryTable = (dataWorksheet, portColsStartRow) => {
    let mergeVoyagePortTableStartRow = portColsStartRow + this.excelData.portPageIndex + 3;
    this.config.cells.push({
      position: {
        c: 9,
        r: mergeVoyagePortTableStartRow,
      },
      value: 'Voyage-Port Summary',
      style: TABLE_HEADING_STYLES,
    });

    // push Totals as required in config
    this.config.mergeCells.push({
      s: { r: mergeVoyagePortTableStartRow, c: 9 },
      e: { r: mergeVoyagePortTableStartRow, c: 14 },
    });
    mergeVoyagePortTableStartRow = mergeVoyagePortTableStartRow + 1;
    // columns for third table
    this.excelData.totalCalList?.forEach((item, index) => {
      insertStyleCell(
        {
          c: index + 6,
          r: mergeVoyagePortTableStartRow + 1,
        },
        dataWorksheet,
        item.column,
        true,
        item.headingColor || 'FFF4B083',
        undefined,
        undefined,
        'center',
        'center',
      );
    });
    if (
      !(
        (this.excelData.portData.length && this.excelData.portData[0].data.length) ||
        (this.excelData.voyageData.length && this.excelData.voyageData[0].data.length)
      )
    ) {
      return;
    }
    //TOTAL CALCULATION FOR THIRD TABLE
    let totalColsStartRow = mergeVoyagePortTableStartRow;
    mergeVoyagePortTableStartRow = mergeVoyagePortTableStartRow + 1;

    this.totalTableTotalCellAddress = fetchCellAddress(
      this.excelData.totalCalList,
      totalColsStartRow,
      2,
      6,
    );

    this.excelData.totalCalList.forEach((item, index) => {
      if (index >= 0) {
        insertStyleCell(
          {
            c: index + 6,
            r: mergeVoyagePortTableStartRow + 1,
          },
          dataWorksheet,
          item.total ?? '',
          false,
          item.totalCellColor,
        );
      }

      // Total table field calculation method
      let relationParams = [...item.relation.params];
      if (this.excelData.portData.length && !this.excelData.portData[0].data.length) {
        relationParams = item.relation.params.filter((x) => x.type !== 'port');
      }
      const params = this.fetchParams(relationParams);
      if (item.relation && params) {
        item.relation.formula(dataWorksheet, [...params], this.totalTableTotalCellAddress[index]);
      }
    });
  };

  fetchEachCellParams = (params, offset, row) => {
    return params.map((item) => XLSX.utils.encode_cell({ c: item + offset, r: row }));
  };

  createWBAndSheets = () => {
    this.wb = XLSX.utils.book_new();
    const dataWorksheet = XLSX.utils.json_to_sheet([[]], { cellStyles: true });
    const commentWorksheet = XLSX.utils.json_to_sheet([[]], { cellStyles: true });
    const identificationWorksheet = XLSX.utils.json_to_sheet([[]], { cellStyles: true });

    XLSX.utils.book_append_sheet(
      this.wb,
      identificationWorksheet,
      'Identification and Description',
    );
    XLSX.utils.book_append_sheet(this.wb, dataWorksheet, 'Voyage-Port data');
    XLSX.utils.book_append_sheet(this.wb, commentWorksheet, 'Comments');

    new EUETSComments(commentWorksheet).build();
    new EUETSIdentification(identificationWorksheet, this.excelData).build();
    if (!dataWorksheet['!merges']) dataWorksheet['!merges'] = [];

    const columnsWidth = Array.from({ length: 55 }, () => ({ wch: 16 }));
    dataWorksheet['!cols'] = columnsWidth;
    return { dataWorksheet, commentWorksheet, identificationWorksheet };
  };

  fetchParams = (params) => {
    return params.map((item) => {
      if (item.type === 'port') return this.portTableTotalCellAddress[item.id];
      if (item.type === 'voyage') return this.voyageTableTotalCellAddress[item.id];
      if (item.type === 'total') return this.totalTableTotalCellAddress[item.id];
    });
  };

  export = () => {
    this.setConfig();
    const { dataWorksheet } = this.createWBAndSheets();

    const voyageColsStartRow = this.renderVoyageTable(dataWorksheet);
    const pendingVoyageColStartRow = this.renderPendingVoyage(dataWorksheet, voyageColsStartRow);
    const portColsStartRow = this.renderPortTable(dataWorksheet, pendingVoyageColStartRow);
    this.renderSummaryTable(dataWorksheet, portColsStartRow);

    ETSCommonExportUtil.insertDataIntoCells(this.config, dataWorksheet);
    XLSX.writeFile(this.wb, `${this.excelData?.fileName}.xlsx`);
  };
}
