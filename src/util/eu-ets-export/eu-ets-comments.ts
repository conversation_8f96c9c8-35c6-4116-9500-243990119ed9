import { ETSCommonExportUtil } from './ets-common-export-util';
export class EUETSComments {
  worksheet;
  constructor(worksheet) {
    this.worksheet = worksheet;
  }
  build() {
    ETSCommonExportUtil.buildHeader(this.worksheet, 1, 1, 'Comments', { hAlignment: 'left' });
    ETSCommonExportUtil.addText(this.worksheet, 1, 3, 'Space for further Comments:', {
      hAlignment: 'left',
      bold: true,
    });
    ETSCommonExportUtil.addText(this.worksheet, 1, 4, '', {
      hAlignment: 'left',
      bgColor: 'FFFFCC',
      border: true,
    });
    ETSCommonExportUtil.insertDataIntoCells(
      {
        mergeCells: [
          {
            s: { r: 1, c: 1 },
            e: { r: 1, c: 8 },
          },
          {
            s: { r: 3, c: 1 },
            e: { r: 3, c: 9 },
          },
          {
            s: { r: 4, c: 1 },
            e: { r: 30, c: 9 },
          },
        ],
      },
      this.worksheet,
    );
    return this.worksheet;
  }
}
