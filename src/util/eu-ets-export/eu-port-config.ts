import moment from 'moment';

import { getEU<PERSON><PERSON>Header, getEUAHeader, getTotalCO2EmittedHeader } from '../eu-ets';
import { formatBDNNumber, formatNumber, formatValue } from '../view-utils';
import { defaultTotalCell1, defaultTotalCell2, defaultTotalCell3 } from './cell-styles';
import {
  averageGHGIntensity,
  averageGHGTotalSummaryHelper,
  complianceBalance,
  estimateFuelEUPenalty,
  totalEnergyConsumed,
  totalFuelConsumed,
} from '../excel-export';

export const euPortConfig = (port, filterData) => {
  const filterYear = moment.utc(filterData?.startDate).format('YYYY');
  const datasource = filterData?.dataSource[0]?.value || 'PARIS';

  const portCol = [
    {
      id: 0,
      column: 'Voyage No.',
      data: port.map((i) => formatValue(i.voyage_id)),
      total: true,
      ...defaultTotalCell2,
    },
    {
      id: 1,
      column: 'Port Name',
      data: port.map((i) => i.port),
      total: true,
      ...defaultTotalCell3,
    },
    {
      id: 2,
      column: 'Arrival in the (EU) Port (Date & Time UTC)',
      data: port.map((i) => i.eu_port_departure_date),
      ...defaultTotalCell3,
    },

    {
      id: 3,
      column: 'Departure from the (EU) Port (Date & Time UTC)',
      data: port.map((i) => i.eu_port_arrival_date),
      ...defaultTotalCell3,
    },
    {
      id: 4,
      column: 'LFO Consumed (M/T)',
      data: port.map((i) => formatNumber(Number(i.lfo_cons))),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 5,
      column: 'MGO/MDO Consumed (M/T)',
      data: port.map((i) => formatNumber(Number(i.mgo_cons))),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 6,
      column: 'HFO Consumed (M/T)',
      data: port.map((i) => formatNumber(Number(i.hfo_cons))),
      total: 'sum',
      ...defaultTotalCell1,
    },

    {
      id: 7,
      column: 'LNG Consumed (M/T)',
      data: port.map((i) => formatNumber(Number(i.lng_cons))),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 8,
      column: 'Methanol Consumed (M/T)',
      data: port.map((i) => formatNumber(Number(i.methanol_cons))),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 9,
      column: 'LPG (Propane) Consumed (M/T)',
      data: port.map((i) => formatNumber(Number(i.lpg_propane_cons))),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 10,
      column: 'LPG (Butane) Consumed (M/T)',
      data: port.map((i) => formatNumber(Number(i.lpg_butane_cons))),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 11,
      column: 'Ethanol Consumed (M/T)',
      data: port.map((i) => formatNumber(Number(i.ethanol_cons))),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 12,
      column: 'Other Fuel Name',
      data: port.map((i) => formatValue(i.other_fuel_name)),
      ...defaultTotalCell1,
    },
    {
      id: 13,
      column: 'Other Fuel - Consumed (M/T)',
      data: port.map((i) => formatNumber(i.other_fuel_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 14,
      column: 'Total Fuel Consumed (M/T)',
      data: port.map((i) => formatNumber(i.total_fuel_consumed)),
      cell_relation: {
        formula: totalFuelConsumed,
        params: [4, 5, 6, 7, 8, 9, 10, 11, 13],
      },
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 15,
      column: getTotalCO2EmittedHeader(filterYear),
      data: port.map((i) => formatNumber(Number(i.total_co2_emitted))),
      total: 'sum',
      ...defaultTotalCell1,
      color: 'FFFFFF',
    },
    {
      id: 16,
      column: getEUAHeader(filterYear),
      data: port.map((i) => formatNumber(i.eua, '0.00', 5)),
      total: 'sum',
      headingColor: 'FFFF00',
      totalCellColor: 'FFFF00',
      color: 'FFFFFF',
    },
    {
      id: 17,
      column: getEUACostHeader(filterYear),
      data: port.map((i) => formatNumber(i.eua_cost, '0.00', 5)),
      total: 'sum',
      headingColor: 'FFFF00',
      totalCellColor: 'FFFF00',
      color: 'FFFFFF',
    },
    {
      id: 18,
      column: 'Total Energy Consumed (MJ)',
      data: port.map((i) => formatNumber(i.total_energy_consumed)),
      cell_relation:
        Number(filterYear) > 2024 && datasource !=='NAVTOR'
          ? {
              formula: totalEnergyConsumed,
              params: [4, 5, 6, 7, 8, 9, 10, 13, 32],
            }
          : undefined,
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 19,
      column: 'Average GHG Intensity (gCO2eq/MJ)',
      data: port.map((i) => formatNumber(i.average_ghg_intensity)),
      cell_relation:
        Number(filterYear) > 2024 && datasource !=='NAVTOR'
          ? {
              formula: averageGHGIntensity,
              params: [4, 5, 6, 7, 8, 9, 10, 13, 18, 32, 36],
            }
          : undefined,
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 20,
      column: 'Compliance Balance (tCO2eq)',
      data: port.map((i) => formatNumber(i.compliance_balance)),
      cell_relation:
        Number(filterYear) > 2024 && datasource !=='NAVTOR'
          ? {
              formula: complianceBalance,
              params: [18, 19],
            }
          : undefined,
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 21,
      column: 'Estimate FuelEU Penalty (€)',
      data: port.map((i) => formatNumber(i.estimate_fuel_eu_penalty, '0.00', 5)),
      cell_relation:
        Number(filterYear) > 2024 && datasource !=='NAVTOR'
          ? {
              formula: estimateFuelEUPenalty,
              params: [19, 20],
            }
          : undefined,
      total: 'sum',
      headingColor: 'FFFF00',
      totalCellColor: 'FFFF00',
      color: 'FFFFFF',
    },
    {
      id: 22,
      column: 'GHG (gCO2eq)',
      data: port.map((i) => formatNumber(i.total_energy_consumed)),
      cell_relation:
        Number(filterYear) > 2024
          ? {
              formula: averageGHGTotalSummaryHelper,
              params: [18, 19],
            }
          : undefined,
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 23,
      column: 'Other Fuel - TtW CO2 Emission Factor',
      data: port.map((i) => formatBDNNumber(i.ttw_co2_emission_factor)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 24,
      column: 'Other Fuel - EU GHG Intensity (gCO2eq/MJ)',
      data: port.map((i) => formatBDNNumber(i.eu_ghg_intensity)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 25,
      column: 'Other Fuel - EU Lower Calorific Value (MJ/g)',
      data: port.map((i) => formatBDNNumber(i.eu_lower_calorific_value)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 26,
      column: 'Other Fuel - Parent Fuel',
      data: port.map((i) => formatValue(i.parent_fuel)),
      ...defaultTotalCell1,
    },
    {
      id: 27,
      column: 'Other Fuel - Blend Percentage (%)',
      data: port.map((i) => formatBDNNumber(i.biofuel_percentage)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 28,
      column: 'Other Fuel - Compliance Balance (tCO2eq)',
      data: port.map((i) => formatBDNNumber(i.other_fuel_compliance_balance)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 29,
      column: 'Other Fuel - Blended EU GHG Intensity (gCO2eq/MJ)',
      data: port.map((i) => formatBDNNumber(i.blended_eu_ghg_intensity)),
      total: 'sum',
      ...defaultTotalCell1,
    },
  ];
  return portCol;
};
