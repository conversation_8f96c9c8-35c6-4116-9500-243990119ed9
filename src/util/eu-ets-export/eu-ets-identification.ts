import { ETSCommonExportUtil } from './ets-common-export-util';
const IDENTIFICATION_FORM_DETAILS = [
  {
    index: '(a)',
    question: 'Please enter the name of the ship',
    info: 'The name of the ship as indicated in the Safety Management Certificate.',
    key: 'name',
  },
  {
    index: '(b)',
    question: 'Please enter the IMO number of the ship',
    info: 'The IMO number of the ship as indicated in the Safety Management Certificate.',
    key: 'vessel.imo_number',
  },
  {
    index: '(c)',
    question: 'Please enter the port of registry or home port of the ship',
    info: 'The port of registry as indicated in the Safety Management Certificate.',
    key: 'flags[0].port_desc',
  },
  {
    index: '(d)',
    question: 'Please enter ice class of the ship, as applicable',
    info: 'The ice class notation assigned to the ship by the competent national authorities of the flag State or an organisation recognised by that State.',
  },
  {
    index: '(e)',
    question:
      'Please enter the Energy Efficiency Design Index (EEDI) or the Estimated Index Value (EIV) as per MEPC.215 (63), where applicable',
    info: 'The attained EEDI value as indicated in the IEEC.',
  },
  {
    index: '(f)',
    question: 'Please enter the name of the shipowner:',
    info: 'The name as indicated at the Certificate of Registry.',
    key: 'owner.value',
  },
  {
    index: '(g)',
    question: 'Please enter address of the ship owner and his principal place of business:',
    info: 'The address as indicated at the Certificate of Registry.',
  },
  {
    index: '(h)',
    question: 'Please enter the name of the company (if not the ship owner):',
    info: 'This name should be the legal entity carrying out the shipping activities.',
  },
  {
    index: '(i)',
    question:
      'Please enter address of the company (if not the shipowner) and its principal place of business:',
    info: 'The address of the legal entity carrying out the shipping activities.',
  },
];
const REPORTING_FORM_DETAILS = [
  {
    index: '(a)',
    question: 'Reporting year:',
  },
];
export class EUETSIdentification {
  worksheet;
  excelData;
  config = {
    mergeCells: [
      {
        s: { r: 1, c: 1 },
        e: { r: 1, c: 7 },
      },
      {
        s: { r: 3, c: 2 },
        e: { r: 3, c: 7 },
      },
      {
        s: { r: 8, c: 2 },
        e: { r: 8, c: 7 },
      },
    ],
  };
  constructor(worksheet, excelData) {
    this.worksheet = worksheet;
    this.excelData = excelData;
  }
  build() {
    ETSCommonExportUtil.addText(this.worksheet, 1, 1, 'GENERAL INFORMATION ABOUT THIS REPORT', {
      hAlignment: 'left',
      underline: true,
      border: false,
      fontSize: '14',
    });

    this.buildReportingForm();
    this.buildIdentificationForm();
    ETSCommonExportUtil.insertDataIntoCells(this.config, this.worksheet);
    return this.worksheet;
  }
  buildReportingForm() {
    ETSCommonExportUtil.buildHeader(this.worksheet, 1, 3, '1', {
      bold: true,
      fontSize: '12',
      hAlignment: 'center',
      vAlignment: 'center',
    });
    ETSCommonExportUtil.buildHeader(this.worksheet, 2, 3, 'Reporting Year', {
      hAlignment: 'left',
      bold: true,
      fontSize: '12',
    });

    let rowNo = 5;
    ETSCommonExportUtil.addText(this.worksheet, 1, rowNo, '(a)', {
      fontSize: '10',
      border: false,
    });
    ETSCommonExportUtil.addText(this.worksheet, 2, rowNo, 'Reporting year:', {
      fontSize: '10',
      hAlignment: 'left',
      border: false,
    });

    ETSCommonExportUtil.addText(this.worksheet, 7, rowNo, this.excelData.reportingYear, {
      fontSize: '10',
      hAlignment: 'left',
      bgColor: 'FFFFCC',
      border: true,
    });
    this.mergeFormCells(rowNo, 6);

    rowNo = rowNo + 1;

    ETSCommonExportUtil.addText(this.worksheet, 1, rowNo, '(b)', {
      fontSize: '10',
      border: false,
    });
    ETSCommonExportUtil.addText(this.worksheet, 2, rowNo, 'Report Duration (From-To) :', {
      fontSize: '10',
      hAlignment: 'left',
      border: false,
    });

    ETSCommonExportUtil.addText(this.worksheet, 7, rowNo, this.excelData.reportingDuration, {
      fontSize: '10',
      hAlignment: 'left',
      bgColor: 'FFFFCC',
      border: true,
    });

    this.mergeFormCells(rowNo, 6);
  }
  buildIdentificationForm() {
    let identificationRowStart = 10;
    const identificationHeaderRow = 8;
    ETSCommonExportUtil.buildHeader(this.worksheet, 1, identificationHeaderRow, '2', {
      bold: true,
      fontSize: '12',
      hAlignment: 'center',
      vAlignment: 'center',
    });
    ETSCommonExportUtil.buildHeader(
      this.worksheet,
      2,
      identificationHeaderRow,
      'Identification of the ship and the company',
      {
        hAlignment: 'left',
        bold: true,
        fontSize: '12',
      },
    );

    for (const formDetail of IDENTIFICATION_FORM_DETAILS) {
      ETSCommonExportUtil.addText(this.worksheet, 1, identificationRowStart, formDetail.index, {
        fontSize: '10',
        border: false,
      });
      ETSCommonExportUtil.addText(this.worksheet, 2, identificationRowStart, formDetail.question, {
        fontSize: '10',
        hAlignment: 'left',
        border: false,
      });
      let value = formDetail.value ? formDetail.value : '';
      if (formDetail.key) {
        value = _.get(this.excelData.vesselOwnershipData, formDetail.key);
      }
      ETSCommonExportUtil.addText(this.worksheet, 7, identificationRowStart, value, {
        fontSize: '10',
        hAlignment: 'left',
        bgColor: 'FFFFCC',
        border: true,
      });

      this.mergeFormCells(identificationRowStart, 6);

      if (formDetail.info) {
        identificationRowStart = identificationRowStart + 1;
        ETSCommonExportUtil.addText(this.worksheet, 2, identificationRowStart, formDetail.info, {
          fontSize: '8',
          hAlignment: 'left',
          italic: true,
          border: false,
          color: '333399',
        });
        this.mergeFormCells(identificationRowStart, 7);
      }
      identificationRowStart = identificationRowStart + 1;
    }
  }

  mergeFormCells(row, col) {
    this.config.mergeCells.push({
      s: { r: row, c: 2 },
      e: { r: row, c: col },
    });
  }
}
