import { insertIntoCell, insertStyleBorder, insertStyleCell } from '../excel-export';
export class ETSCommonExportUtil {
  static insertStyleCellHelper = (
    dataWorksheet,
    cellAddress,
    data,
    { bold, color, bgColor, border, hAlignment, vAlignment, fontSize, underline, italic },
  ) => {
    insertStyleCell(
      cellAddress,
      dataWorksheet,
      data,

      bold,
      bgColor,
      color,
      border,
      hAlignment,
      vAlignment,
      fontSize,
      underline,
      italic,
    );
  };

  static addText = (worksheet, c, r, data, style) => {
    ETSCommonExportUtil.insertStyleCellHelper(
      worksheet,
      {
        c,
        r,
      },
      data,

      {
        bold: false,
        hAlignment: 'center',
        vAlignment: 'center',
        ...style,
      },
    );
  };

  static buildHeader = (worksheet, c, r, data, style) => {
    ETSCommonExportUtil.insertStyleCellHelper(
      worksheet,
      {
        c,
        r,
      },
      data,

      {
        bold: true,
        bgColor: '0000FF',
        color: 'FFFFFF',
        hAlignment: 'center',
        vAlignment: 'center',
        ...style,
      },
    );
  };

  static insertDataIntoCells = (config, worksheet) => {
    config.cells?.forEach((cell) => {
      if (_.has(cell, 'border')) {
        [...Array(cell.size)].forEach((_, i) => {
          const field = { ...cell.position };
          field.c = field.c + i + 1;
          insertStyleBorder(field, worksheet, '', cell.border);
        });
        insertStyleCell(cell.position, worksheet, cell.value, ...cell.style);
      } else if (_.has(cell, 'style')) {
        insertStyleCell(cell.position, worksheet, cell.value, ...cell.style);
      } else {
        insertIntoCell(cell.position, worksheet, cell.value);
      }
    });
    if (_.has(config, 'mergeCells')) {
      worksheet['!merges'] = config.mergeCells;
    }
  };
}
