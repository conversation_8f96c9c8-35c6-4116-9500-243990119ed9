import moment from 'moment';

import { getEUACostHeader, getEUAHeader, getTotalCO2EmittedHeader } from '../eu-ets';
import { formatBDNNumber, formatNumber, formatValue } from '../view-utils';
import { defaultTotalCell1, defaultTotalCell2 } from './cell-styles';
import {
  averageGHGIntensity,
  averageGHGTotalSummaryHelper,
  complianceBalance,
  estimateFuelEUPenalty,
  totalEnergyConsumed,
  totalFuelConsumed,
} from '../excel-export';

export const euVoyageConfig = (voyage, filterData) => {
  const filterYear = moment.utc(filterData?.startDate).format('YYYY');
  const datasource = filterData?.dataSource[0]?.value || 'PARIS';
  const voyageCol = [
    {
      id: 0,
      column: 'Voyage No.',
      data: voyage.map((i) => formatValue(i.voyage_id)),
      ...defaultTotalCell2,
    },
    {
      id: 1,
      column: 'Port of Departure',
      data: voyage.map((i) => formatValue(i.port_departure)),
      ...defaultTotalCell1,
    },
    {
      id: 2,
      column: 'Port of Arrival',
      data: voyage.map((i) => formatValue(i.port_arrival)),
      ...defaultTotalCell1,
    },
    {
      id: 3,
      column: 'Leg Start Date / Leg End Date',
      data: voyage.map((i) => formatValue(i.leg_date)),
      ...defaultTotalCell1,
    },
    {
      id: 4,
      column: 'Hour of Departure (UTC)',
      data: voyage.map((i) => formatValue(i.departure_hour)),
      ...defaultTotalCell1,
    },

    {
      id: 5,
      column: 'Hour of Arrival (UTC)',
      data: voyage.map((i) => formatValue(i.arrival_hour)),
      ...defaultTotalCell1,
    },
    {
      id: 6,
      column: 'Time Spent at Sea (hours)',
      data: voyage.map((i) => formatNumber(i.time_spent_sea)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 7,
      column: 'Distance (nm)',
      data: voyage.map((i) => formatNumber(i.distance)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 8,
      column: 'Cargo Carried (tn)',
      data: voyage.map((i) => formatValue(i.cargo_carried, '0.0')),
      ...defaultTotalCell1,
    },
    {
      id: 9,
      column: 'Voyage Type (L/B)',
      data: voyage.map((i) => formatValue(i.voyage_type)),
      ...defaultTotalCell1,
    },
    {
      id: 10,
      column: 'LFO Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.lfo_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 11,
      column: 'MGO/MDO Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.mgo_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 12,
      column: 'HFO Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.hfo_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },

    {
      id: 13,
      column: 'LNG Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.lng_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 14,
      column: 'Methanol Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.methanol_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 15,
      column: 'LPG (Propane) Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.lpg_propane_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 16,
      column: 'LPG (Butane) Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.lpg_butane_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 17,
      column: 'Ethanol Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.ethanol_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 18,
      column: 'Other Fuel Name',
      data: voyage.map((i) => formatValue(i.other_fuel_name)),
      ...defaultTotalCell1,
    },
    {
      id: 19,
      column: 'Other Fuel - Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.other_fuel_cons)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 20,
      column: 'Total Fuel Consumed (M/T)',
      data: voyage.map((i) => formatNumber(i.total_fuel_consumed)),
      cell_relation: {
        formula: totalFuelConsumed,
        params: [10, 11, 12, 13, 14, 15, 16, 17, 19],
      },
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 21,
      column: getTotalCO2EmittedHeader(filterYear),
      data: voyage.map((i) => formatNumber(i.total_co2_emitted)),
      total: 'sum',
      ...defaultTotalCell1,
      color: 'FFFFFF',
    },
    {
      id: 22,
      column: getEUAHeader(filterYear),
      data: voyage.map((i) => formatNumber(i.eua, '0.00', 5)),
      total: 'sum',
      headingColor: 'FFFF00',
      totalCellColor: 'FFFF00',
      color: 'FFFFFF',
    },
    {
      id: 24,
      column: getEUACostHeader(filterYear),
      data: voyage.map((i) => formatNumber(i.eua_cost, '0.00', 5)),
      total: 'sum',
      headingColor: 'FFFF00',
      totalCellColor: 'FFFF00',
      color: 'FFFFFF',
    },
    {
      id: 24,
      column: 'Total Energy Consumed (MJ)',
      data: voyage.map((i) => formatNumber(i.total_energy_consumed)),
      cell_relation:
        Number(filterYear) > 2024 && datasource !=='NAVTOR'
          ? {
              formula: totalEnergyConsumed,
              params: [10, 11, 12, 13, 14, 15, 16, 19, 31],
            }
          : undefined,
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 25,
      column: 'Average GHG Intensity (gCO2eq/MJ)',
      data: voyage.map((i) => formatNumber(i.average_ghg_intensity)),
      cell_relation:
        Number(filterYear) > 2024 && datasource !=='NAVTOR'
          ? {
              formula: averageGHGIntensity,
              params: [10, 11, 12, 13, 14, 15, 16, 19, 25, 31, 35],
            }
          : undefined,
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 26,
      column: 'Compliance Balance (tCO2eq)',
      data: voyage.map((i) => formatNumber(i.compliance_balance)),
      cell_relation:
        Number(filterYear) > 2024 && datasource !=='NAVTOR'
          ? {
              formula: complianceBalance,
              params: [25, 26, 36],
            }
          : undefined,
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 27,
      column: 'Estimate FuelEU Penalty (€)',
      data: voyage.map((i) => formatNumber(i.estimate_fuel_eu_penalty, '0.00', 5)),
      cell_relation:
        Number(filterYear) > 2024 && datasource !=='NAVTOR'
          ? {
              formula: estimateFuelEUPenalty,
              params: [26, 27],
            }
          : undefined,
      total: 'sum',
      headingColor: 'FFFF00',
      totalCellColor: 'FFFF00',
      color: 'FFFFFF',
    },
    {
      id: 28,
      column: 'Other Fuel - TtW CO2 Emission Factor',
      data: voyage.map((i) => formatBDNNumber(i.ttw_co2_emission_factor)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 29,
      column: 'Other Fuel - EU GHG Intensity (gCO2eq/MJ)',
      data: voyage.map((i) => formatBDNNumber(i.eu_ghg_intensity)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 30,
      column: 'Other Fuel - EU Lower Calorific Value (MJ/g)',
      data: voyage.map((i) => formatBDNNumber(i.eu_lower_calorific_value)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 31,
      column: 'Other Fuel - Parent Fuel',
      data: voyage.map((i) => formatValue(i.parent_fuel)),
      ...defaultTotalCell1,
    },
    {
      id: 32,
      column: 'Other Fuel - Blend Percentage (%)',
      data: voyage.map((i) => formatBDNNumber(i.biofuel_percentage)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 33,
      column: 'Other Fuel - Compliance Balance (tCO2eq)',
      data: voyage.map((i) => formatBDNNumber(i.other_fuel_compliance_balance)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 34,
      column: 'Other Fuel - Blended EU GHG Intensity (gCO2eq/MJ)',
      data: voyage.map((i) => formatBDNNumber(i.blended_eu_ghg_intensity)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 35,
      column: 'applicable EUA Percentage (%)',
      data: voyage.map((i) => formatNumber(i.applicable_eua_percent)),
      total: 'sum',
      ...defaultTotalCell1,
    },
    {
      id: 36,
      column: 'GHG (gCO2eq)',
      data: voyage.map((i) => formatNumber(i.total_energy_consumed)),
      cell_relation:
        Number(filterYear) > 2024
          ? {
              formula: averageGHGTotalSummaryHelper,
              params: [25, 26],
            }
          : undefined,
      total: 'sum',
      ...defaultTotalCell1,
    },
  ];
  if (['2023', '2024', '2025'].includes(filterYear)) {
    voyageCol.splice(22, 0, {
      id: 23,
      column: 'Base EU Allowance',
      data: voyage.map((i) => formatValue(i.base_eua)),
      total: 'sum',
      ...defaultTotalCell1,
      headingColor: '00B050',
    });
  }
  return voyageCol;
};
