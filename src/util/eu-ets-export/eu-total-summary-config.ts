import moment from 'moment';

import { getEUACostHeader, getEUAHeader } from '../eu-ets';
import { averageGHGIntensityTotalSummary, estimateFuelEUPenaltyTotalSummary, totalCalculation } from '../excel-export';
import { defaultTotalCell4 } from './cell-styles';

export const euTotalSummaryConfig = (filterData) => {
  const filterYear = moment.utc(filterData?.startDate).format('YYYY');

  const summaryCol = [
    {
      id: 1,
      column: 'LFO Consumed (M/T)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 10, type: 'voyage' },
          { id: 4, type: 'port' },
        ],
      },
      ...defaultTotalCell4,
    },
    {
      id: 2,
      column: 'MGO/MDO Consumed (M/T)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 11, type: 'voyage' },
          { id: 5, type: 'port' },
        ],
      },
      ...defaultTotalCell4,
    },
    {
      id: 0,
      column: 'HFO Consumed (M/T)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 12, type: 'voyage' },
          { id: 6, type: 'port' },
        ],
      },
      ...defaultTotalCell4,
    },
    {
      id: 3,
      column: 'LNG Consumed (M/T)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 13, type: 'voyage' },
          { id: 7, type: 'port' },
        ],
      },
      ...defaultTotalCell4,
    },
    {
      id: 6,
      column: 'Methanol Consumed (M/T)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 14, type: 'voyage' },
          { id: 8, type: 'port' },
        ],
      },
      ...defaultTotalCell4,
    },
    {
      id: 4,
      column: 'LPG (Propane) Consumed (M/T)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 15, type: 'voyage' },
          { id: 9, type: 'port' },
        ],
      },
      ...defaultTotalCell4,
    },
    {
      id: 5,
      column: 'LPG (Butane) Consumed (M/T)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 16, type: 'voyage' },
          { id: 10, type: 'port' },
        ],
      },
      ...defaultTotalCell4,
    },
    {
      id: 7,
      column: 'Ethanol Consumed (M/T)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 17, type: 'voyage' },
          { id: 11, type: 'port' },
        ],
      },
      ...defaultTotalCell4,
    },
    {
      id: 8,
      column: 'Other Fuel Consumed (M/T)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 19, type: 'voyage' },
          { id: 15, type: 'port' },
        ],
      },
      ...defaultTotalCell4,
    },
    {
      id: 9,
      column: getEUAHeader(filterYear),
      relation: {
        formula: totalCalculation,
        params: [
          { id: 23, type: 'voyage' },
          { id: 16, type: 'port' },
        ],
      },
      headingColor: 'FFFF00',
      totalCellColor: 'FFFF00',
    },
    {
      id: 10,
      column: getEUACostHeader(filterYear),
      relation: {
        formula: totalCalculation,
        params: [
          { id: 24, type: 'voyage' },
          { id: 17, type: 'port' },
        ],
      },
      headingColor: 'FFFF00',
      totalCellColor: 'FFFF00',
    },
    {
      id: 11,
      column: 'Average GHG Intensity (gCO2eq/MJ)',
      relation: Number(filterYear) ? {
        formula: averageGHGIntensityTotalSummary,
        params: [
          { id: 25, type: 'voyage' },
          { id: 37, type: 'voyage' },
          { id: 18, type: 'port' },
          { id: 29, type: 'port' },
        ],
      } : undefined,
      ...defaultTotalCell4,
    },
    {
      id: 12,
      column: `Estimate FuelEU Penalty (€)`,
      relation: Number(filterYear) ? {
        formula: estimateFuelEUPenaltyTotalSummary,
        params: [
          { id: 28, type: 'voyage' },
          { id: 21, type: 'port' },
        ],
      } : undefined,
      headingColor: 'FFFF00',
      totalCellColor: 'FFFF00',
    },
  ];
  return summaryCol;
};
