import { formatNumber, formatValue, formatFloat } from './view-utils';
import {
  co2EmissionPerDistCalculation,
  co2EmissionPerTransportWorkCalculation,
  co2EmittedBetweenPortsCellCal,
  co2EmittedPortsCellCal,
  firstFieldCalculation,
  fuelConsumpPerDistCellCalcuation,
  fuelConsumpPerTransportCellCalcuation,
  multiplicationCalculation,
  portTotalCo2EmittedCellCalcuation,
  totalCalculation,
  voyageTotalCo2EmittedCellCalcuation,
  totalGhgCo2EquivalentEmittedCellCalculation,
  multiplicationWithConstantForCh4EmittedCalculation,
  multiplicationWithConstantForN2oEmittedCalculation,
  multiplicationCalculationForLngCo2OrN2oEmitted,
  multiplicationCalculationForLngCh4Emitted,
  portTotalCh4EmittedCellCalcuation,
  portTotalN2oEmittedCellCalcuation,
  portTotalGhgEmittedCellCalcuation,
} from './excel-export';
import { CH4_N2O_RELEASE_YEAR } from './mrv';
import moment from 'moment';

const defaultTotalCell1 = {
  color: 'FFD9E1F2',
  totalCellColor: 'fcd5b4',
};

const defaultTotalCell2 = {
  color: 'FFD9E1F2',
  totalCellColor: 'ffff99',
};

const defaultTotalCell3 = {
  color: 'FFFFFF99',
  totalCellColor: '99cc00',
};

const defaultTotalCell4 = {
  color: 'FFD9E1F2',
  totalCellColor: '99cc00',
};

const defaultTotalCell5 = {
  color: 'FFFFFF99',
  totalCellColor: 'ffff99',
};

const defaultTotalCell6 = {
  color: 'FFFFFF00',
  totalCellColor: 'fcd5b4',
};

const defaultTotalCell7 = {
  color: 'e4dfec',
  totalCellColor: 'fcd5b4',
};

const defaultTotalCell8 = {
  color: 'e4dfec',
  totalCellColor: 'ffff99',
};

const defaultTotalCell9 = {
  color: 'ffffff',
  totalCellColor: 'fcd5b4',
};

const defaultTotalCell10 = {
  color: 'ffff99',
  totalCellColor: 'ffff99',
};

const defaultTotalCell11 = {
  color: 'FFFFFF00',
  totalCellColor: 'ccffcc',
};

export const getVoyageColsBeforeCh4N2oReleaseYear = (voyage) => {
  return [
    {
      id: 0,
      column: 'Voyage (i)',
      data: voyage.map((i, index) => index + 1),
      ...defaultTotalCell6,
    },
    {
      id: 1,
      column: 'Port of departure',
      data: voyage.map((i) => formatValue(i.port_departure)),
      ...defaultTotalCell1,
    },
    {
      id: 2,
      column: 'EU port of departure?',
      data: voyage.map((i) => formatValue(i.eu_port_departure === 'Yes' ? '1' : '0')),
      ...defaultTotalCell1,
    },
    {
      id: 3,
      column: 'Port of arrival',
      data: voyage.map((i) => formatValue(i.port_arrival)),
      ...defaultTotalCell1,
    },
    {
      id: 4,
      column: 'EU port of arrival?',
      data: voyage.map((i) => formatValue(i.eu_port_arrival === 'Yes' ? '1' : '0')),
      ...defaultTotalCell1,
    },
    {
      id: 5,
      column: 'Leg start date',
      data: voyage.map((i) => formatValue(i.leg_date.split('/')[0])),
      ...defaultTotalCell1,
    },
    {
      id: 6,
      column: 'Hour of departure(UTC)',
      data: voyage.map((i) => formatValue(i.departure_hour)),
      ...defaultTotalCell1,
    },
    {
      id: 7,
      column: 'Leg end date',
      data: voyage.map((i) => formatValue(i.leg_date.split('/')[1])),
      ...defaultTotalCell1,
    },
    {
      id: 8,
      column: 'Hour of arrival(UTC)',
      data: voyage.map((i) => formatValue(i.arrival_hour)),
      ...defaultTotalCell1,
    },
    {
      id: 9,
      column: 'Time spent at sea (hours)',
      data: voyage.map((i) => formatNumber(i.time_spent_sea)),
      total: 'sumWithString',
      ...defaultTotalCell4,
    },
    {
      id: 10,
      column: 'DISTANCE (nm)',
      data: voyage.map((i) => formatFloat(i.distance, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell4,
    },
    {
      id: 11,
      column: 'Cargo Carried (tn)',
      data: voyage.map((i) => formatValue(i.cargo_carried)),
      ...defaultTotalCell1,
    },
    {
      id: 12,
      column: 'VOYAGE TYPE',
      data: voyage.map((i) => formatValue(i.voyage_type)),
      ...defaultTotalCell1,
    },
    {
      id: 13,
      column: 'HFO CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.hfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 14,
      column: 'HFO Emission Factor',
      data: voyage.map((i) => formatFloat(i.hfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 15,
      column: 'HFO CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.hfo_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [13, 14],
      },
      ...defaultTotalCell5,
    },
    {
      id: 16,
      column: 'LFO CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.lfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 17,
      column: 'LFO Emission Factor',
      data: voyage.map((i) => formatFloat(i.lfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 18,
      column: 'LFO CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.lfo_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [16, 17],
      },
      ...defaultTotalCell5,
    },
    {
      id: 19,
      column: 'MGO CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.mgo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 20,
      column: 'MGO Emission Factor',
      data: voyage.map((i) => formatFloat(i.mgo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 21,
      column: 'MGO CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.mgo_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [19, 20],
      },
      ...defaultTotalCell5,
    },
    {
      id: 22,
      column: 'LNG CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.lng_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 23,
      column: 'LNG Emission Factor',
      data: voyage.map((i) => formatFloat(i.lng_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 24,
      column: 'LNG CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.lng_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [22, 23],
      },
      ...defaultTotalCell5,
    },
    {
      id: 25,
      column: 'LPG (Propane) CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.lpg_propane_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 26,
      column: 'LPG (Propane) Emission Factor',
      data: voyage.map((i) => formatFloat(i.lpg_propane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 27,
      column: 'LPG (Propane) CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.lpg_propane_co2_emitted, '0.00000', 5)),
      cell_relation: {
        formula: multiplicationCalculation,
        params: [25, 26],
      },
      total: 'sum',
      ...defaultTotalCell5,
    },
    {
      id: 28,
      column: 'LPG (Butane) CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.lpg_butane_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 29,
      column: 'LPG (Butane) Emission Factor',
      data: voyage.map((i) => formatFloat(i.lpg_butane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 30,
      column: 'LPG (Butane) CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.lpg_butane_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [28, 29],
      },
      ...defaultTotalCell5,
    },
    {
      id: 31,
      column: 'Methanol CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.methanol_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 32,
      column: 'Methanol Emission Factor',
      data: voyage.map((i) => formatFloat(i.methanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 33,
      column: 'Methanol CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.methanol_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [31, 32],
      },
      ...defaultTotalCell5,
    },
    {
      id: 34,
      column: 'Ethanol CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.ethanol_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 35,
      column: 'Ethanol Emission Factor',
      data: voyage.map((i) => formatFloat(i.ethanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 36,
      column: 'Ethanol CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ethanol_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [34, 35],
      },
      ...defaultTotalCell5,
    },
    {
      id: 37,
      column: 'Total CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.total_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: voyageTotalCo2EmittedCellCalcuation,
        params: [15, 18, 21, 24, 27, 30, 33, 36],
      },
      ...defaultTotalCell3,
    },
    {
      id: 38,
      column: 'CO2 emitted on voyage between EU Ports (tons)',
      data: voyage.map((i) =>
        formatFloat(
          i.eu_port_arrival === 'Yes' && i.eu_port_departure === 'Yes'
            ? i.co2_emitted_voyage_between_eu_port
            : 0,
          '0.00000',
          5,
        ),
      ),
      total: 'sum',
      ...defaultTotalCell3,
    },
    {
      id: 39,
      column: 'CO2 emitted on voyage departed from EU Port (tons)',
      data: voyage.map((i) =>
        formatFloat(
          i.eu_port_arrival === 'No' && i.eu_port_departure === 'Yes'
            ? i.co2_emitted_voyage_from_eu_port
            : 0,
          '0.00000',
          5,
        ),
      ),
      total: 'voyageCo2Cal',
      ...defaultTotalCell3,
    },
    {
      id: 40,
      column: 'CO2 emitted on voyage arrived to EU Port (tons)',
      data: voyage.map((i) =>
        formatFloat(
          i.eu_port_arrival === 'Yes' && i.eu_port_departure === 'No'
            ? i.co2_emitted_voyage_to_eu_port
            : 0,
          '0.00000',
          5,
        ),
      ),
      total: 'voyageCo2Cal',
      ...defaultTotalCell3,
    },
    {
      id: 41,
      column: 'Transport Work (tn*nm)',
      data: voyage.map((i) => formatFloat(i.transport_work, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [10, 11],
      },
      ...defaultTotalCell3,
    },
    {
      id: 42,
      column: 'Fuel consumption per distance (tn/nm)',
      data: voyage.map((i) => formatFloat(i.fuel_cons_per_distance, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: fuelConsumpPerDistCellCalcuation,
        params: [13, 16, 19, 22, 25, 28, 31, 34, 10],
      },
      ...defaultTotalCell3,
    },
    {
      id: 43,
      column: 'Fuel consumption per transport work (gr/tn*nm)',
      data: voyage.map((i) => formatFloat(i.fuel_cons_per_transport_work, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: fuelConsumpPerTransportCellCalcuation,
        params: [13, 16, 19, 22, 25, 28, 31, 34, 41],
      },
      ...defaultTotalCell3,
    },
    {
      id: 44,
      column: 'CO2 emissions per distance (tn/nm)',
      data: voyage.map((i) => formatFloat(i.co2_emitted_per_distance, '0.00000', 5)),
      relation: {
        formula: co2EmissionPerDistCalculation,
        params: [
          { id: 37, type: 'voyage' },
          { id: 10, type: 'voyage' },
        ],
      },
      cell_relation: {
        formula: co2EmissionPerDistCalculation,
        params: [37, 10],
      },
      ...defaultTotalCell3,
    },
    {
      id: 45,
      column: 'CO2 emissions per transport work (gCO2/tn*nm)',
      data: voyage.map((i) => formatFloat(i.co2_emitted_per_transport_work, '0.00000', 5)),
      relation: {
        formula: co2EmissionPerTransportWorkCalculation,
        params: [
          { id: 37, type: 'voyage' },
          { id: 41, type: 'voyage' },
        ],
      },
      cell_relation: {
        formula: co2EmissionPerTransportWorkCalculation,
        params: [37, 41],
      },
      ...defaultTotalCell3,
    },
    {
      id: 46,
      column: 'Boiler consumption for cargo heating HFO',
      data: voyage.map((i) => formatFloat(i.boiler_hfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
    {
      id: 47,
      column: 'Boiler consumption for cargo heating MGO/MDO',
      data: voyage.map((i) => formatFloat(i.boiler_mgo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
    {
      id: 48,
      column: 'Boiler consumption for cargo heating Fuel 3',
      data: voyage.map((i) => formatFloat(i.boiler_fuel3_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
  ];
};

export const getVoyageColsAfterCh4N2oReleaseYear = (voyage) => {
  return [
    {
      id: 0,
      column: 'Voyage (i)',
      data: voyage.map((i, index) => index + 1),
      ...defaultTotalCell6,
    },
    {
      id: 1,
      column: 'Port of departure',
      data: voyage.map((i) => formatValue(i.port_departure)),
      ...defaultTotalCell1,
    },
    {
      id: 2,
      column: 'EU port of departure?',
      data: voyage.map((i) => formatValue(i.eu_port_departure === 'Yes' ? '1' : '0')),
      ...defaultTotalCell1,
    },
    {
      id: 3,
      column: 'Port of arrival',
      data: voyage.map((i) => formatValue(i.port_arrival)),
      ...defaultTotalCell1,
    },
    {
      id: 4,
      column: 'EU port of arrival?',
      data: voyage.map((i) => formatValue(i.eu_port_arrival === 'Yes' ? '1' : '0')),
      ...defaultTotalCell1,
    },
    {
      id: 5,
      column: 'Leg start date',
      data: voyage.map((i) => formatValue(i.leg_date.split('/')[0])),
      ...defaultTotalCell1,
    },
    {
      id: 6,
      column: 'Hour of departure(UTC)',
      data: voyage.map((i) => formatValue(i.departure_hour)),
      ...defaultTotalCell1,
    },
    {
      id: 7,
      column: 'Leg end date',
      data: voyage.map((i) => formatValue(i.leg_date.split('/')[1])),
      ...defaultTotalCell1,
    },
    {
      id: 8,
      column: 'Hour of arrival (UTC)',
      data: voyage.map((i) => formatValue(i.arrival_hour)),
      ...defaultTotalCell1,
    },
    {
      id: 9,
      column: 'Time spent at sea (hours)',
      data: voyage.map((i) => formatNumber(i.time_spent_sea)),
      total: 'sumWithString',
      ...defaultTotalCell4,
    },
    {
      id: 10,
      column: 'DISTANCE (nm)',
      data: voyage.map((i) => formatFloat(i.distance, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell4,
    },
    {
      id: 11,
      column: 'Cargo Carried (tn)',
      data: voyage.map((i) => formatValue(i.cargo_carried)),
      ...defaultTotalCell1,
    },
    {
      id: 12,
      column: 'VOYAGE TYPE',
      data: voyage.map((i) => formatValue(i.voyage_type)),
      ...defaultTotalCell1,
    },
    {
      id: 13,
      column: 'HFO CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.hfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 14,
      column: 'HFO CO2 Emission Factor',
      data: voyage.map((i) => formatFloat(i.hfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 15,
      column: 'HFO CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.hfo_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [13, 14],
      },
      ...defaultTotalCell5,
    },
    {
      id: 16,
      column: 'HFO CH4 Emission Factor',
      data: voyage.map((i) => formatFloat(i.ch4_hfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 17,
      column: 'HFO CH4 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_hfo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [13, 16],
      },
      ...defaultTotalCell5,
    },
    {
      id: 18,
      column: 'HFO N2O Emission Factor',
      data: voyage.map((i) => formatFloat(i.n2o_hfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 19,
      column: 'HFO N2O emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_hfo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [13, 18],
      },
      ...defaultTotalCell5,
    },
    {
      id: 20,
      column: 'LFO CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.lfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 21,
      column: 'LFO CO2 Emission Factor',
      data: voyage.map((i) => formatFloat(i.lfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 22,
      column: 'LFO CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.lfo_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [20, 21],
      },
      ...defaultTotalCell5,
    },
    {
      id: 23,
      column: 'LFO CH4 Emission Factor',
      data: voyage.map((i) => formatFloat(i.ch4_lfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 24,
      column: 'LFO CH4 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_lfo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [20, 23],
      },
      ...defaultTotalCell5,
    },
    {
      id: 25,
      column: 'LFO N2O Emission Factor',
      data: voyage.map((i) => formatFloat(i.n2o_lfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 26,
      column: 'LFO N2O emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_lfo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [20, 25],
      },
      ...defaultTotalCell5,
    },
    {
      id: 27,
      column: 'MGO CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.mgo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 28,
      column: 'MGO CO2 Emission Factor',
      data: voyage.map((i) => formatFloat(i.mgo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 29,
      column: 'MGO CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.mgo_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [27, 28],
      },
      ...defaultTotalCell5,
    },
    {
      id: 30,
      column: 'MGO CH4 Emission Factor',
      data: voyage.map((i) => formatFloat(i.ch4_mgo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 31,
      column: 'MGO CH4 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_mgo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [27, 30],
      },
      ...defaultTotalCell5,
    },
    {
      id: 32,
      column: 'MGO N2O Emission Factor',
      data: voyage.map((i) => formatFloat(i.n2o_mgo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 33,
      column: 'MGO N2O emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_mgo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [27, 32],
      },
      ...defaultTotalCell5,
    },
    {
      id: 34,
      column: 'LNG CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.lng_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 35,
      column: 'LNG CONSUMPTION - Main Engine (tons)',
      data: voyage.map((i) => formatFloat(i.me_lng, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
    {
      id: 36,
      column: 'Slippage Coefficient - Main Engine',
      data: voyage.map((i) => formatFloat(i.lng_engine_category_main_engine_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell3,
    },
    {
      id: 37,
      column: 'LNG CONSUMPTION - Diesel Generator (tons)',
      data: voyage.map((i) => formatFloat(i.ge_lng, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
    {
      id: 38,
      column: 'Slippage Coefficient - Diesel Generator',
      total: 'firstFieldCalculation',
      data: voyage.map((i) => formatFloat(i.lng_engine_category_diesel_generator_emission_factor, '0.00000', 5)),
      ...defaultTotalCell3,
    },
    {
      id: 39,
      column: 'LNG CO2 Emission Factor',
      data: voyage.map((i) => formatFloat(i.lng_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 40,
      column: 'LNG CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.lng_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculationForLngCo2OrN2oEmitted,
        params: [39, 36, 38, 35, 37, 34],
      },
      ...defaultTotalCell5,
    },
    {
      id: 41,
      column: 'LNG CH4 Emission Factor',
      data: voyage.map((i) => formatFloat(i.ch4_lng_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 42,
      column: 'LNG CH4 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_lng_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculationForLngCh4Emitted,
        params: [41, 36, 38, 35, 37, 34],
      },
      ...defaultTotalCell5,
    },
    {
      id: 43,
      column: 'LNG N2O Emission Factor',
      data: voyage.map((i) => formatFloat(i.n2o_lng_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 44,
      column: 'LNG N2O emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_lng_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculationForLngCo2OrN2oEmitted,
        params: [43, 36, 38, 35, 37, 34],
      },
      ...defaultTotalCell5,
    },
    {
      id: 45,
      column: 'LPG (Propane) CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.lpg_propane_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 46,
      column: 'LPG (Propane) CO2 Emission Factor',
      data: voyage.map((i) => formatFloat(i.lpg_propane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 47,
      column: 'LPG (Propane) CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.lpg_propane_co2_emitted, '0.00000', 5)),
      cell_relation: {
        formula: multiplicationCalculation,
        params: [45, 46],
      },
      total: 'sumWithString',
      ...defaultTotalCell5,
    },
    {
      id: 48,
      column: 'LPG (Propane) CH4 Emission Factor',
      data: voyage.map((i) => formatFloat(i.ch4_lpg_propane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 49,
      column: 'LPG (Propane) CH4 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_lpg_propane_emitted, '0.00000', 5)),
      cell_relation: {
        formula: multiplicationCalculation,
        params: [45, 48],
      },
      total: 'sumWithString',
      ...defaultTotalCell5,
    },
    {
      id: 50,
      column: 'LPG (Propane) N2O Emission Factor',
      data: voyage.map((i) => formatFloat(i.n2o_lpg_propane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 51,
      column: 'LPG (Propane) N2O emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_lpg_propane_emitted, '0.00000', 5)),
      cell_relation: {
        formula: multiplicationCalculation,
        params: [45, 50],
      },
      total: 'sumWithString',
      ...defaultTotalCell5,
    },
    {
      id: 52,
      column: 'LPG (Butane) CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.lpg_butane_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 53,
      column: 'LPG (Butane) CO2 Emission Factor',
      data: voyage.map((i) => formatFloat(i.lpg_butane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 54,
      column: 'LPG (Butane) CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.lpg_butane_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [52, 53],
      },
      ...defaultTotalCell5,
    },
    {
      id: 55,
      column: 'LPG (Butane) CH4 Emission Factor',
      data: voyage.map((i) => formatFloat(i.ch4_lpg_butane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 56,
      column: 'LPG (Butane) CH4 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_lpg_butane_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [52, 55],
      },
      ...defaultTotalCell5,
    },
    {
      id: 57,
      column: 'LPG (Butane) N2O Emission Factor',
      data: voyage.map((i) => formatFloat(i.n2o_lpg_butane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 58,
      column: 'LPG (Butane) N2O emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_lpg_butane_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [52, 57],
      },
      ...defaultTotalCell5,
    },
    {
      id: 59,
      column: 'Methanol CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.methanol_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 60,
      column: 'Methanol CO2 Emission Factor',
      data: voyage.map((i) => formatFloat(i.methanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 61,
      column: 'Methanol CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.methanol_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [59, 60],
      },
      ...defaultTotalCell5,
    },
    {
      id: 62,
      column: 'Methanol CH4 Emission Factor',
      data: voyage.map((i) => formatFloat(i.ch4_methanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 63,
      column: 'Methanol CH4 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_methanol_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [59, 62],
      },
      ...defaultTotalCell5,
    },
    {
      id: 64,
      column: 'Methanol N2O Emission Factor',
      data: voyage.map((i) => formatFloat(i.n2o_methanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 65,
      column: 'Methanol N2O emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_methanol_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [59, 64],
      },
      ...defaultTotalCell5,
    },
    {
      id: 66,
      column: 'Ethanol CONSUMPTION (tons)',
      data: voyage.map((i) => formatFloat(i.ethanol_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell2,
    },
    {
      id: 67,
      column: 'Ethanol CO2 Emission Factor',
      data: voyage.map((i) => formatFloat(i.ethanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 68,
      column: 'Ethanol CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ethanol_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [66, 67],
      },
      ...defaultTotalCell5,
    },
    {
      id: 69,
      column: 'Ethanol CH4 Emission Factor',
      data: voyage.map((i) => formatFloat(i.ch4_ethanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 70,
      column: 'Ethanol CH4 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_ethanol_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [66, 69],
      },
      ...defaultTotalCell5,
    },
    {
      id: 71,
      column: 'Ethanol N2O Emission Factor',
      data: voyage.map((i) => formatFloat(i.n2o_ethanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 72,
      column: 'Ethanol N2O emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_ethanol_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [66, 71],
      },
      ...defaultTotalCell5,
    },
    {
      id: 73,
      column: 'Other fuel Name',
      data: voyage.map((i) => formatValue(i.other_fuel_name)),
      ...defaultTotalCell1,
    },
    {
      id: 74,
      column: 'Other fuel Consumptions',
      data: voyage.map((i) => formatValue(i.other_fuel_cons)),
      ...defaultTotalCell1,
    },
    {
      id: 75,
      column: 'Other fuel CO2 Emission Factor',
      data: voyage.map((i) => formatFloat(i.other_fuel_co2_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 76,
      column: 'Other fuel CO2 Emitted (tons)',
      data: voyage.map((i) => formatFloat(i.other_fuel_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [74, 75],
      },
      ...defaultTotalCell5,
    },
    {
      id: 77,
      column: 'Total CO2 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.total_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: voyageTotalCo2EmittedCellCalcuation,
        params: [15, 22, 29, 36, 47, 54, 61, 68],
      },
      ...defaultTotalCell3,
    },
    {
      id: 78,
      column: 'Total CH4 emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_total_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: voyageTotalCo2EmittedCellCalcuation,
        params: [17, 24, 31, 42, 49, 56, 63, 70],
      },
      ...defaultTotalCell3,
    },
    {
      id: 79,
      column: 'Total N2O emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_total_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: voyageTotalCo2EmittedCellCalcuation,
        params: [19, 26, 33, 44, 51, 58, 65, 72],
      },
      ...defaultTotalCell3,
    },
    {
      id: 80,
      column: 'Total CH4 (CO2 equivalents) emitted (tons)',
      data: voyage.map((i) => formatFloat(i.ch4_total_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationWithConstantForCh4EmittedCalculation,
        params: [78]
      },
      ...defaultTotalCell3,
    },
    {
      id: 81,
      column: 'Total N2O (CO2 equivalents) emitted (tons)',
      data: voyage.map((i) => formatFloat(i.n2o_total_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationWithConstantForN2oEmittedCalculation,
        params: [79]
      },
      ...defaultTotalCell3,
    },
    {
      id: 82,
      column: 'Total GHG (CO2 equivalents) emitted (tons)',
      data: voyage.map((i) => formatFloat(i.total_ghg, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: totalGhgCo2EquivalentEmittedCellCalculation,
        params: [77, 80, 81],
      },
      ...defaultTotalCell3,
    },
    {
      id: 83,
      column: 'CO2(e) emitted on voyage between EU Ports (tons)',
      data: voyage.map((i) => formatFloat(i.co2_emitted_voyage_between_eu_port, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: co2EmittedBetweenPortsCellCal,
        params: [2, 4, 78],
      },
      ...defaultTotalCell3,
    },
    {
      id: 84,
      column: 'CO2(e) emitted on voyage departed from EU Port (tons)',
      data: voyage.map((i) => formatNumber(i.co2_emitted_voyage_from_eu_port)),
      total: 'sumWithString',
      cell_relation: {
        formula: co2EmittedPortsCellCal,
        params: [2, 82],
      },
      ...defaultTotalCell3,
    },
    {
      id: 85,
      column: 'CO2(e) emitted on voyage arrived to EU Port (tons)',
      data: voyage.map((i) => formatNumber(i.co2_emitted_voyage_to_eu_port)),
      total: 'sumWithString',
      cell_relation: {
        formula: co2EmittedPortsCellCal,
        params: [4, 82],
      },
      ...defaultTotalCell3,
    },
    {
      id: 86,
      column: 'Transport Work (tn*nm)',
      data: voyage.map((i) => formatFloat(i.transport_work, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [10, 11],
      },
      ...defaultTotalCell3,
    },
    {
      id: 87,
      column: 'Fuel consumption per distance (tn/nm)',
      data: voyage.map((i) => formatFloat(i.fuel_cons_per_distance, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: fuelConsumpPerDistCellCalcuation,
        params: [13, 20, 27, 34, 45, 52, 59, 66, 10],
      },
      ...defaultTotalCell3,
    },
    {
      id: 88,
      column: 'Fuel consumption per transport work (gr/tn*nm)',
      data: voyage.map((i) => formatFloat(i.fuel_cons_per_transport_work, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: fuelConsumpPerTransportCellCalcuation,
        params: [13, 20, 27, 34, 45, 52, 59, 66, 86],
      },
      ...defaultTotalCell3,
    },
    {
      id: 89,
      column: 'CO2(e) emissions per distance (tn/nm)',
      data: voyage.map((i) => formatFloat(i.co2_emitted_per_distance, '0.00000', 5)),
      relation: {
        formula: co2EmissionPerDistCalculation,
        params: [
          { id: 82, type: 'voyage' },
          { id: 10, type: 'voyage' },
        ],
      },
      cell_relation: {
        formula: co2EmissionPerDistCalculation,
        params: [82, 10],
      },
      ...defaultTotalCell3,
    },
    {
      id: 90,
      column: 'CO2(e) emissions per transport work (gCO2/tn*nm)',
      data: voyage.map((i) => formatFloat(i.co2_emitted_per_transport_work, '0.00000', 5)),
      relation: {
        formula: co2EmissionPerTransportWorkCalculation,
        params: [
          { id: 82, type: 'voyage' },
          { id: 86, type: 'voyage' },
        ],
      },
      cell_relation: {
        formula: co2EmissionPerTransportWorkCalculation,
        params: [82, 86],
      },
      ...defaultTotalCell3,
    },
    {
      id: 91,
      column: 'Boiler consumption for cargo heating HFO',
      data: voyage.map((i) => formatFloat(i.boiler_hfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
    {
      id: 92,
      column: 'Boiler consumption for cargo heating MGO/MDO',
      data: voyage.map((i) => formatFloat(i.boiler_mgo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
    {
      id: 93,
      column: 'Boiler consumption for cargo heating Fuel 3',
      data: voyage.map((i) => formatFloat(i.boiler_fuel3_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
  ];
};

export const getPortColsBeforeCh4N2oReleaseYear = (port) => {
  return [
    {
      id: 0,
      column: 'Leg / voyage or day (i)',
      data: port.map((i, index) => index + 1),
      total: true,
      ...defaultTotalCell6,
    },
    {
      id: 1,
      column: 'Port Name',
      data: port.map((i) => i.port),
      total: true,
      ...defaultTotalCell7,
    },
    {
      id: 2,
      column: '',
      data: null,
      total: true,
      ...defaultTotalCell7,
    },
    {
      id: 3,
      column: 'Arrival in the (EU) port',
      data: port.map((i) => i.eu_port_arrival_date),
      ...defaultTotalCell7,
    },
    {
      id: 4,
      column: '',
      data: null,
      ...defaultTotalCell7,
    },
    {
      id: 5,
      column: 'Departure in the (EU) port',
      data: port.map((i) => i.eu_port_departure_date),
      ...defaultTotalCell7,
    },
    {
      id: 6,
      column: 'Hour of arrival(UTC)',
      data: port.map((i) => formatValue(i.arrival_hour)),
      ...defaultTotalCell9,
    },
    {
      id: 7,
      column: '',
      data: null,
      ...defaultTotalCell9,
    },
    {
      id: 8,
      column: 'Hour of departure(UTC)',
      data: port.map((i) => formatValue(i.departure_hour)),
      ...defaultTotalCell9,
    },
    {
      id: 9,
      column: 'Time spent at land (hours)',
      data: port.map((i) => formatNumber(i.time_spent_land)),
      total: 'sumWithString',
      ...defaultTotalCell9,
    },
    {
      id: 10,
      column: 'DISTANCE (nm)',
      data: port.map((i) => formatFloat(i.distance, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell9,
    },
    {
      id: 11,
      column: '',
      data: null,
      ...defaultTotalCell9,
    },
    {
      id: 12,
      column: '',
      data: null,
      ...defaultTotalCell9,
    },
    {
      id: 13,
      column: 'HFO CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.hfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 14,
      column: 'HFO Emission Factor',
      data: port.map((i) => formatFloat(i.hfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 15,
      column: 'HFO CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.hfo_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [13, 14],
      },
      ...defaultTotalCell10,
    },
    {
      id: 16,
      column: 'LFO CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.lfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 17,
      column: 'LFO Emission Factor',
      data: port.map((i) => formatFloat(i.lfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 18,
      column: 'LFO CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.lfo_co2_emitted, '0.00000', 5)),
      relation: {
        formula: multiplicationCalculation,
        params: [
          { id: 16, type: 'port' },
          { id: 17, type: 'port' },
        ],
      },
      cell_relation: {
        formula: multiplicationCalculation,
        params: [16, 17],
      },
      ...defaultTotalCell10,
    },
    {
      id: 19,
      column: 'MGO CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.mgo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 20,
      column: 'MGO Emission Factor',
      data: port.map((i) => formatFloat(i.mgo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 21,
      column: 'MGO CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.mgo_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [19, 20],
      },
      ...defaultTotalCell10,
    },
    {
      id: 22,
      column: 'LNG CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.lng_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 23,
      column: 'LNG Emission Factor',
      data: port.map((i) => formatFloat(i.lng_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 24,
      column: 'LNG CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.lng_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [21, 22],
      },
      ...defaultTotalCell10,
    },
    {
      id: 25,
      column: 'LPG (Propane) CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.lpg_propane_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 26,
      column: 'LPG (Propane) Emission Factor',
      data: port.map((i) => formatFloat(i.lpg_propane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 27,
      column: 'LPG (Propane) CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.lpg_propane_co2_emitted, '0.00000', 5)),
      relation: {
        formula: multiplicationCalculation,
        params: [
          { id: 25, type: 'port' },
          { id: 26, type: 'port' },
        ],
      },
      cell_relation: {
        formula: multiplicationCalculation,
        params: [25, 26],
      },
      ...defaultTotalCell10,
    },
    {
      id: 28,
      column: 'LPG (Butane) CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.lpg_butane_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 29,
      column: 'LPG (Butane) Emission Factor',
      data: port.map((i) => formatFloat(i.lpg_butane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 30,
      column: 'LPG (Butane) CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.lpg_butane_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [28, 29],
      },
      ...defaultTotalCell10,
    },
    {
      id: 31,
      column: 'Methanol CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.methanol_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 32,
      column: 'Methanol Emission factor',
      data: port.map((i) => formatFloat(i.methanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 33,
      column: 'Methanol CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.methanol_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [31, 32],
      },
      ...defaultTotalCell10,
    },
    {
      id: 34,
      column: 'Ethanol CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.ethanol_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 35,
      column: 'Ethanol Emission factor',
      data: port.map((i) => formatFloat(i.ethanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 36,
      column: 'Ethanol CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.ethanol_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [34, 35],
      },
      ...defaultTotalCell10,
    },
    {
      id: 37,
      column: 'Boiler consumption for cargo heating HFO',
      data: port.map((i) => formatFloat(i.boiler_hfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 38,
      column: 'Boiler consumption for cargo heating MGO/MDO',
      data: port.map((i) => formatFloat(i.boiler_mgo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 39,
      column: 'Boiler consumption for cargo heating Fuel 3',
      data: port.map((i) => formatFloat(i.boiler_fuel3_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 40,
      column: 'Total CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.total_co2_emitted, '0.00000', 5)),
      total: 'sum',
      color: 'ffff99',
      cell_relation: {
        formula: portTotalCo2EmittedCellCalcuation,
        params: [15, 18, 21, 24],
      },
      totalCellColor: 'ccffcc',
    },
  ];
};

export const getPortColsAfterCh4N2oReleaseYear = (port) => {
  return [
    {
      id: 0,
      column: 'Leg / voyage or day (i)',
      data: port.map((i, index) => index + 1),
      total: true,
      ...defaultTotalCell6,
    },
    {
      id: 1,
      column: 'Port Name',
      data: port.map((i) => i.port),
      total: true,
      ...defaultTotalCell7,
    },
    {
      id: 2,
      column: '',
      data: null,
      total: true,
      ...defaultTotalCell7,
    },
    {
      id: 3,
      column: 'Arrival in the (EU) port',
      data: port.map((i) => i.eu_port_arrival_date),
      ...defaultTotalCell7,
    },
    {
      id: 4,
      column: '',
      data: null,
      ...defaultTotalCell7,
    },
    {
      id: 5,
      column: 'Departure in the (EU) port',
      data: port.map((i) => i.eu_port_departure_date),
      ...defaultTotalCell7,
    },
    {
      id: 6,
      column: 'Hour of arrival(UTC)',
      data: port.map((i) => formatValue(i.arrival_hour)),
      ...defaultTotalCell9,
    },
    {
      id: 7,
      column: '',
      data: null,
      ...defaultTotalCell9,
    },
    {
      id: 8,
      column: 'Hour of departure(UTC)',
      data: port.map((i) => formatValue(i.departure_hour)),
      ...defaultTotalCell9,
    },
    {
      id: 9,
      column: 'Time spent at land (hours)',
      data: port.map((i) => formatNumber(i.time_spent_land)),
      total: 'sumWithString',
      ...defaultTotalCell9,
    },
    {
      id: 10,
      column: 'DISTANCE (nm)',
      data: port.map((i) => formatFloat(i.distance, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell9,
    },
    {
      id: 11,
      column: '',
      data: null,
      ...defaultTotalCell9,
    },
    {
      id: 12,
      column: '',
      data: null,
      ...defaultTotalCell9,
    },
    {
      id: 13,
      column: 'HFO Consumption (tons)',
      data: port.map((i) => formatFloat(i.hfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 14,
      column: 'HFO CO2 Emission Factor',
      data: port.map((i) => formatFloat(i.hfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 15,
      column: 'HFO CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.hfo_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [13, 14],
      },
      ...defaultTotalCell10,
    },
    {
      id: 16,
      column: 'HFO CH4 Emission Factor',
      data: port.map((i) => formatFloat(i.ch4_hfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 17,
      column: 'HFO CH4 emitted (tons)',
      data: port.map((i) => formatFloat(i.ch4_hfo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [13, 16],
      },
      ...defaultTotalCell5,
    },
    {
      id: 18,
      column: 'HFO N2O Emission Factor',
      data: port.map((i) => formatFloat(i.n2o_hfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 19,
      column: 'HFO N2O emitted (tons)',
      data: port.map((i) => formatFloat(i.n2o_hfo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [13, 18],
      },
      ...defaultTotalCell5,
    },
    {
      id: 20,
      column: 'LFO CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.lfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 21,
      column: 'LFO CO2 Emission Factor',
      data: port.map((i) => formatFloat(i.lfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 22,
      column: 'LFO CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.lfo_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [20, 21],
      },
      ...defaultTotalCell10,
    },
    {
      id: 23,
      column: 'LFO CH4 Emission Factor',
      data: port.map((i) => formatFloat(i.ch4_lfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 24,
      column: 'LFO CH4 emitted (tons)',
      data: port.map((i) => formatFloat(i.ch4_lfo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [20, 23],
      },
      ...defaultTotalCell5,
    },
    {
      id: 25,
      column: 'LFO N2O Emission Factor',
      data: port.map((i) => formatFloat(i.n2o_lfo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 26,
      column: 'LFO N2O emitted (tons)',
      data: port.map((i) => formatFloat(i.n2o_lfo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [20, 25],
      },
      ...defaultTotalCell5,
    },
    {
      id: 27,
      column: 'MGO CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.mgo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 28,
      column: 'MGO CO2 Emission Factor',
      data: port.map((i) => formatFloat(i.mgo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 29,
      column: 'MGO CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.mgo_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [27, 28],
      },
      ...defaultTotalCell10,
    },
    {
      id: 30,
      column: 'MGO CH4 Emission Factor',
      data: port.map((i) => formatFloat(i.ch4_mgo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 31,
      column: 'MGO CH4 emitted (tons)',
      data: port.map((i) => formatFloat(i.ch4_mgo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [27, 30],
      },
      ...defaultTotalCell5,
    },
    {
      id: 32,
      column: 'MGO N2O Emission Factor',
      data: port.map((i) => formatFloat(i.n2o_mgo_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 33,
      column: 'MGO N2O emitted (tons)',
      data: port.map((i) => formatFloat(i.n2o_mgo_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [27, 32],
      },
      ...defaultTotalCell5,
    },
    {
      id: 34,
      column: 'LNG CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.lng_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 35,
      column: 'LNG CONSUMPTION - Main Engine (tons)',
      data: port.map((i) => formatFloat(i.me_lng, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
    {
      id: 36,
      column: 'Slippage Coefficient - Main Engine',
      data: port.map((i) => formatFloat(i.lng_engine_category_main_engine_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell3,
    },
    {
      id: 37,
      column: 'LNG CONSUMPTION - Diesel Generator (tons)',
      data: port.map((i) => formatFloat(i.ge_lng, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell3,
    },
    {
      id: 38,
      column: 'Slippage Coefficient - Diesel Generator',
      data: port.map((i) => formatFloat(i.lng_engine_category_diesel_generator_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell3,
    },
    {
      id: 39,
      column: 'LNG CO2 Emission Factor',
      data: port.map((i) => formatFloat(i.lng_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 40,
      column: 'LNG CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.lng_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculationForLngCo2OrN2oEmitted,
        params: [39, 36, 38, 35, 37, 34],
      },
      ...defaultTotalCell10,
    },
    {
      id: 41,
      column: 'LNG CH4 Emission Factor',
      data: port.map((i) => formatFloat(i.ch4_lng_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 42,
      column: 'LNG CH4 emitted (tons)',
      data: port.map((i) => formatFloat(i.ch4_lng_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculationForLngCh4Emitted,
        params: [41, 36, 38, 35, 37, 34],
      },
      ...defaultTotalCell5,
    },
    {
      id: 43,
      column: 'LNG N2O Emission Factor',
      data: port.map((i) => formatFloat(i.n2o_lng_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 44,
      column: 'LNG N2O emitted (tons)',
      data: port.map((i) => formatFloat(i.n2o_lng_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculationForLngCo2OrN2oEmitted,
        params: [43, 36, 38, 35, 37, 34],
      },
      ...defaultTotalCell5,
    },
    {
      id: 45,
      column: 'LPG (Propane) CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.lpg_propane_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 46,
      column: 'LPG (Propane) CO2 Emission Factor',
      data: port.map((i) => formatFloat(i.lpg_propane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 47,
      column: 'LPG (Propane) CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.lpg_propane_co2_emitted, '0.00000', 5)),
      relation: {
        formula: multiplicationCalculation,
        params: [
          { id: 45, type: 'port' },
          { id: 46, type: 'port' },
        ],
      },
      cell_relation: {
        formula: multiplicationCalculation,
        params: [45, 46],
      },
      ...defaultTotalCell10,
    },
    {
      id: 48,
      column: 'LPG (Propane) CH4 Emission Factor',
      data: port.map((i) => formatFloat(i.ch4_lpg_propane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 49,
      column: 'LPG (Propane) CH4 emitted (tons)',
      data: port.map((i) => formatFloat(i.ch4_lpg_propane_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [45, 48],
      },
      ...defaultTotalCell5,
    },
    {
      id: 50,
      column: 'LPG (Propane) N2O Emission Factor',
      data: port.map((i) => formatFloat(i.n2o_lpg_propane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 51,
      column: 'LPG (Propane) N2O emitted (tons)',
      data: port.map((i) => formatFloat(i.n2o_lpg_propane_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [45, 50],
      },
      ...defaultTotalCell5,
    },
    {
      id: 52,
      column: 'LPG (Butane) CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.lpg_butane_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 53,
      column: 'LPG (Butane) CO2 Emission Factor',
      data: port.map((i) => formatFloat(i.lpg_butane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 54,
      column: 'LPG (Butane) CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.lpg_butane_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [52, 53],
      },
      ...defaultTotalCell10,
    },
    {
      id: 55,
      column: 'LPG (Butane) CH4 Emission Factor',
      data: port.map((i) => formatFloat(i.ch4_lpg_butane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 56,
      column: 'LPG (Butane) CH4 emitted (tons)',
      data: port.map((i) => formatFloat(i.ch4_lpg_butane_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [52, 55],
      },
      ...defaultTotalCell5,
    },
    {
      id: 57,
      column: 'LPG (Butane) N2O Emission Factor',
      data: port.map((i) => formatFloat(i.n2o_lpg_butane_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 58,
      column: 'LPG (Butane) N2O emitted (tons)',
      data: port.map((i) => formatFloat(i.n2o_lpg_butane_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [52, 57],
      },
      ...defaultTotalCell5,
    },
    {
      id: 59,
      column: 'Methanol CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.methanol_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 60,
      column: 'Methanol CO2 Emission factor',
      data: port.map((i) => formatFloat(i.methanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 61,
      column: 'Methanol CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.methanol_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [59, 60],
      },
      ...defaultTotalCell10,
    },
    {
      id: 62,
      column: 'Methanol CH4 Emission Factor',
      data: port.map((i) => formatFloat(i.ch4_methanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 63,
      column: 'Methanol CH4 emitted (tons)',
      data: port.map((i) => formatFloat(i.ch4_methanol_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [59, 62],
      },
      ...defaultTotalCell5,
    },
    {
      id: 64,
      column: 'Methanol N2O Emission Factor',
      data: port.map((i) => formatFloat(i.n2o_methanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 65,
      column: 'Methanol N2O emitted (tons)',
      data: port.map((i) => formatFloat(i.n2o_methanol_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [59, 64],
      },
      ...defaultTotalCell5,
    },
    {
      id: 66,
      column: 'Ethanol CONSUMPTION (tons)',
      data: port.map((i) => formatFloat(i.ethanol_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 67,
      column: 'Ethanol CO2 Emission factor',
      data: port.map((i) => formatFloat(i.ethanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell8,
    },
    {
      id: 68,
      column: 'Ethanol CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.ethanol_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [66, 67],
      },
      ...defaultTotalCell10,
    },
    {
      id: 69,
      column: 'Ethanol CH4 Emission Factor',
      data: port.map((i) => formatFloat(i.ch4_ethanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 70,
      column: 'Ethanol CH4 emitted (tons)',
      data: port.map((i) => formatFloat(i.ch4_ethanol_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [66, 69],
      },
      ...defaultTotalCell5,
    },
    {
      id: 71,
      column: 'Ethanol N2O Emission Factor',
      data: port.map((i) => formatFloat(i.n2o_ethanol_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 72,
      column: 'Ethanol N2O emitted (tons)',
      data: port.map((i) => formatFloat(i.n2o_ethanol_emitted, '0.00000', 5)),
      total: 'sumWithString',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [66, 71],
      },
      ...defaultTotalCell5,
    },
    {
      id: 73,
      column: 'Other fuel Name',
      data: port.map((i) => formatValue(i.other_fuel_name)),
      ...defaultTotalCell1,
    },
    {
      id: 74,
      column: 'Other fuel Consumptions',
      data: port.map((i) => formatValue(i.other_fuel_cons)),
      ...defaultTotalCell1,
    },
    {
      id: 75,
      column: 'Other fuel CO2 Emission Factor',
      data: port.map((i) => formatFloat(i.other_fuel_co2_emission_factor, '0.00000', 5)),
      total: 'firstFieldCalculation',
      ...defaultTotalCell2,
    },
    {
      id: 76,
      column: 'Other fuel CO2 Emitted (tons)',
      data: port.map((i) => formatFloat(i.other_fuel_co2_emitted, '0.00000', 5)),
      total: 'sum',
      cell_relation: {
        formula: multiplicationCalculation,
        params: [74, 75],
      },
      ...defaultTotalCell5,
    },
    {
      id: 77,
      column: 'Total CO2 emitted (tons)',
      data: port.map((i) => formatFloat(i.total_co2_emitted, '0.00000', 5)),
      total: 'sumWithString',
      color: 'ffff99',
      cell_relation: {
        formula: portTotalCo2EmittedCellCalcuation,
        params: [15, 22, 29, 40, 47, 54, 61, 68],
      },
      totalCellColor: 'ccffcc',
    },
    {
      id: 78,
      column: 'Total CH4 emitted (tons)',
      data: port.map((i) => formatFloat(i.ch4_total_emitted, '0.00000', 5)),
      total: 'sumWithString',
      color: 'ffff99',
      cell_relation: {
        formula: portTotalCh4EmittedCellCalcuation,
        params: [17, 24, 31, 42, 49, 56, 63, 70],
      },
      totalCellColor: 'ccffcc',
    },
    {
      id: 79,
      column: 'Total N2O emitted (tons)',
      data: port.map((i) => formatFloat(i.n2o_total_emitted, '0.00000', 5)),
      total: 'sumWithString',
      color: 'ffff99',
      cell_relation: {
        formula: portTotalN2oEmittedCellCalcuation,
        params: [19, 26, 33, 44, 51, 58, 65, 72],
      },
      totalCellColor: 'ccffcc',
    },
    {
      id: 80,
      column: 'Total GHG (CO2 equivalents) emitted (tons)',
      data: port.map((i) => formatFloat(i.total_ghg, '0.00000', 5)),
      total: 'sumWithString',
      color: 'ffff99',
      cell_relation: {
        formula: portTotalGhgEmittedCellCalcuation,
        params: [77, 78, 79],
      },
      totalCellColor: 'ccffcc',
    },
    {
      id: 81,
      column: 'Boiler consumption for cargo heating HFO',
      data: port.map((i) => formatFloat(i.boiler_hfo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 82,
      column: 'Boiler consumption for cargo heating MGO/MDO',
      data: port.map((i) => formatFloat(i.boiler_mgo_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
    {
      id: 83,
      column: 'Boiler consumption for cargo heating Fuel 3',
      data: port.map((i) => formatFloat(i.boiler_fuel3_cons, '0.00000', 5)),
      total: 'sumWithString',
      ...defaultTotalCell8,
    },
  ];
};

export const getTotalCalListColsBeforeCh4N2oReleaseYear = () => {
  return [
    {
      id: 0,
      column: 'TOTAL HFO CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 13, type: 'port' },
          { id: 13, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 1,
      column: 'HFO Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 14, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 2,
      column: 'TOTAL LFO CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 16, type: 'port' },
          { id: 16, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 3,
      column: 'LFO Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 17, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 4,
      column: 'TOTAL MGO CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 19, type: 'port' },
          { id: 19, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 5,
      column: 'MGO Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 20, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 6,
      column: 'TOTAL LNG CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 22, type: 'port' },
          { id: 22, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 7,
      column: 'LNG Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 23, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 8,
      column: 'TOTAL LPG(Propane) CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 25, type: 'port' },
          { id: 25, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 9,
      column: 'LPG(Propane) Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 26, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 10,
      column: 'TOTAL LPG(Butane) CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 28, type: 'port' },
          { id: 28, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 11,
      column: 'LPG(Butane) Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 29, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 12,
      column: 'Total Methanol CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 31, type: 'port' },
          { id: 31, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 13,
      column: 'Methanol Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 32, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 14,
      column: 'Total Ethanol CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 34, type: 'port' },
          { id: 34, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 15,
      column: 'Ethanol Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 35, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 16,
      column: 'Total CO2 emitted during all voyages (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 37, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 17,
      column: 'Total CO2 emitted on voyage between EU ports (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 38, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 18,
      column: 'Total CO2 emitted on voyage departed from EU port (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 39, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 19,
      column: 'Total CO2 emitted on voyage arrived to EU port (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 40, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 20,
      column: 'Total CO2 emitted in ports (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 40, type: 'port' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 21,
      column: 'Total CO2 emitted (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 16, type: 'total' },
          { id: 20, type: 'total' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 22,
      column: 'Total distance travelled (n.m)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 10, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 23,
      column: 'Total time spent at sea (hours)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 9, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 24,
      column: 'Total transport work (tn*nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 41, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 25,
      column: 'Fuel consumption per distance (tn/nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 42, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 26,
      column: 'Fuel consumption per transport work (gr/tn*nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 43, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 27,
      column: 'CO2 emissions per distance (tn/nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 44, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 28,
      column: 'CO2 emissions per transport work (g CO2 / tn*nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 45, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 29,
      column: 'TOTAL HFO CARGO HEATING CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 46, type: 'voyage' },
          { id: 37, type: 'port' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 30,
      column: 'TOTAL MDO/MGO CARGO HEATING CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 47, type: 'voyage' },
          { id: 38, type: 'port' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 31,
      column: 'TOTAL FUEL3 CARGO HEATING CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 48, type: 'voyage' },
          { id: 39, type: 'port' },
        ],
      },
      ...defaultTotalCell11,
    },
  ];
};

export const getTotalCalListColsAfterCh4N2oReleaseYear = () => {
  return [
    {
      id: 0,
      column: 'TOTAL HFO CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 13, type: 'port' },
          { id: 13, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 1,
      column: 'HFO CO2 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 14, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 2,
      column: 'HFO CH4 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 16, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 3,
      column: 'HFO N2O Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 18, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 4,
      column: 'TOTAL LFO CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 20, type: 'port' },
          { id: 20, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 5,
      column: 'LFO CO2 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 21, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 6,
      column: 'LFO CH4 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 23, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 7,
      column: 'LFO N2O Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 25, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 8,
      column: 'TOTAL MGO CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 27, type: 'port' },
          { id: 27, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 9,
      column: 'MGO CO2 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 28, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 10,
      column: 'MGO CH4 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 30, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 11,
      column: 'MGO N2O Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 32, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 12,
      column: 'TOTAL LNG CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 34, type: 'port' },
          { id: 34, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 13,
      column: 'LNG CO2 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 35, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 14,
      column: 'LNG CH4 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 41, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 15,
      column: 'LNG N2O Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 43, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 16,
      column: 'TOTAL LPG (Propane) CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 45, type: 'port' },
          { id: 45, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 17,
      column: 'LPG(Propane) CO2 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 46, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 18,
      column: 'LPG (Propane) CH4 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 48, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 19,
      column: 'LPG (Propane) N2O Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 50, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 20,
      column: 'TOTAL LPG (Butane) CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 52, type: 'port' },
          { id: 52, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 21,
      column: 'LPG (Butane) CO2 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 53, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 22,
      column: 'LPG (Butane) CH4 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 55, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 23,
      column: 'LPG (Butane) N2O Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 57, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 24,
      column: 'Total Methanol CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 59, type: 'port' },
          { id: 59, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 25,
      column: 'Methanol CO2 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 60, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 26,
      column: 'Methanol CH4 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 62, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 27,
      column: 'Methanol N2O Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 64, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 28,
      column: 'Total Ethanol CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 66, type: 'port' },
          { id: 66, type: 'voyage' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 29,
      column: 'Ethanol CO2 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 67, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 30,
      column: 'Ethanol CH4 Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 69, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 31,
      column: 'Ethanol N2O Emission Factor',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 71, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 32,
      column: 'Total CO2 emitted during all voyages (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 73, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 33,
      column: 'Total CH4 emitted during all voyages (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 74, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 34,
      column: 'Total N2O emitted during all voyages (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 75, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 35,
      column: 'Total GHG (CO2 equivalents) emitted during all voyages (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 78, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 36,
      column: 'Total CO2(e) emitted on voyage between EU port (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 79, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 37,
      column: 'Total CO2(e) emitted on voyage departed from EU port (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 80, type: 'voyage' }], 
      },
      ...defaultTotalCell11,
    },
    {
      id: 38,
      column: 'Total CO2(e) emitted on voyage arrived to EU port (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 81, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 39,
      column: 'Total CO2 emitted in ports (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 73, type: 'port' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 40,
      column: 'Total CH4 emitted in ports (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 74, type: 'port' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 41,
      column: 'Total N2O emitted in ports (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 75, type: 'port' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 42,
      column: 'Total GHG (CO2 equivalents) emitted in ports (tons)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 76, type: 'port' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 43,
      column: 'Total GHG (CO2 equivalents) emitted (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 76, type: 'port' }, 
          { id: 78, type: 'voyage' }
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 44,
      column: 'Total distance travelled (n.m)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 10, type: 'voyage' },
          { id: 10, type: 'port' }
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 45,
      column: 'Total time spent at sea (hours)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 9, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 46,
      column: 'Total transport work (tn*nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 82, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 47,
      column: 'Fuel consumption per distance (tn/nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 83, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 48,
      column: 'Fuel consumption per transport work (gr/tn*nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 84, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 49,
      column: 'CO2(e) emissions per distance (tn/nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 85, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 50,
      column: 'CO2(e) emissions per transport work (g CO2 / tn*nm)',
      relation: {
        formula: firstFieldCalculation,
        params: [{ id: 86, type: 'voyage' }],
      },
      ...defaultTotalCell11,
    },
    {
      id: 51,
      column: 'TOTAL HFO CARGO HEATING CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 87, type: 'voyage' },
          { id: 77, type: 'port' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 52,
      column: 'TOTAL MDO/MGO CARGO HEATING CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 88, type: 'voyage' },
          { id: 78, type: 'port' },
        ],
      },
      ...defaultTotalCell11,
    },
    {
      id: 53,
      column: 'TOTAL FUEL3 CARGO HEATING CONSUMPTION (tons)',
      relation: {
        formula: totalCalculation,
        params: [
          { id: 89, type: 'voyage' },
          { id: 79, type: 'port' },
        ],
      },
      ...defaultTotalCell11,
    },
  ];
};

/**
 * Retrieves the MRV Excel data based on the provided parameters.
 * 
 * @param {string} vesselName - The name of the vessel.
 * @param {Array} voyage - An array of voyage data.
 * @param {Array} port - An array of port data.
 * @param {Date} startDate - The start date for filtering the data.
 * @returns {Object} - An object containing the MRV Excel data.
 */
export const getMrvExcelData = (vesselName, voyage, port, startDate) => {

  const filterYear = startDate ? moment.utc(startDate).format('YYYY') : CH4_N2O_RELEASE_YEAR;
  
  return {
    fileName: `MRV Emission report_${vesselName}`,
    vesselName: vesselName,
    voyagePageIndex: voyage.length,
    portPageIndex: port.length,
    isHavingPartialVoyage: voyage.some((data) => data.is_partial_voyage),
    isThatVoyagePartial: voyage.map((data) => data.is_partial_voyage),
    portIsHavingPartialVoyage: port.some((data) => data.is_partial_voyage),
    forPortIsThatVoyagePartial: port.map((data) => data.is_partial_voyage),
    filterYear,
    // The voyageData, portData and totalCalList is determined based on the filterYear. If the filterYear is less than the CH4_N2O_RELEASE_YEAR, 
    // it calls the getVoyageColsBeforeCh4N2oReleaseYear function with the voyage as an argument. 
    // Otherwise, it calls the getVoyageColsAfterCh4N2oReleaseYear function with the voyage as an argument.
    voyageData: Number(filterYear) < CH4_N2O_RELEASE_YEAR ? getVoyageColsBeforeCh4N2oReleaseYear(voyage) : getVoyageColsAfterCh4N2oReleaseYear(voyage),
    portData: Number(filterYear) < CH4_N2O_RELEASE_YEAR ? getPortColsBeforeCh4N2oReleaseYear(port) : getPortColsAfterCh4N2oReleaseYear(port),
    totalCalList: Number(filterYear) < CH4_N2O_RELEASE_YEAR ? getTotalCalListColsBeforeCh4N2oReleaseYear() : getTotalCalListColsAfterCh4N2oReleaseYear(),
  }
};