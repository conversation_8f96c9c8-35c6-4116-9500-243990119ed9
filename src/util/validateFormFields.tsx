import _ from 'lodash';

// validate required
export const validateField = (field, value, label) => ({
  [field]: _.isEmpty(value) ? `${label} is a required field` : undefined,
});

export const findDecimalCount = (value) => {
  let index = value.toString().indexOf('.');
  return index != -1 ? value.length - index - 1 : 0;
};

// validate numbers
export const validateNumber = (value, data, customPath = '') => {
  const { path, label, validation_json, path_v2, path_v3 } = data;
  let pathKey = customPath ?? path;
  if (path_v2) {
    pathKey = customPath ?? path_v2;
  }
  if (path_v3) {
    pathKey = customPath ?? path_v3;
  }
  const { min, max, precision, maxLength } = validation_json;
  if (maxLength) {
    const valueLength = value.toString().replace('-', '').split('.').join('').length;
    if (valueLength > maxLength) {
      return {
        [pathKey]: `${label} should contain at most ${maxLength} digits including decimal places`,
      };
    }
  } else if (!_.isEmpty(value) && !(+value >= min && +value <= max)) {
    return {
      [pathKey]: `${label} should be between ${min} and ${max}`,
    };
  } else if (findDecimalCount(value) > precision) {
    return {
      [pathKey]:
        precision === 0
          ? `${label} must not allow to enter decimal values. `
          : `${label} must allow only ${precision} decimal place`,
    };
  }
  return validateField(pathKey, value.toString(), label);
};

//validate input

export const validateInput = (value, data) => {
  const { path, label, limit, path_v2, path_v3 } = data;
  let modifiedPath = path;
  if (path_v2) {
    modifiedPath = path_v2;
  }
  if (path_v3) {
    modifiedPath = path_v3;
  }
  if (!_.isEmpty(value) && value.length > limit) {
    return {
      [modifiedPath]: `${label} must be less then 500 `,
    };
  }

  return validateField(modifiedPath, value, label);
};

//validate form

export const validateForm = (type, data, errors, value, customPath = '') => {
  let newErrors = {
    ...errors,
  };

  switch (type) {
    case 'number':
      newErrors = { ...newErrors, ...validateNumber(value, data) };
      break;
    case 'countryport':
      newErrors = { ...newErrors, ...validateField(customPath, value, data?.label) };
      break;
    case 'degree':
    case 'minute':
      newErrors = { ...newErrors, ...validateNumber(value, data, `${customPath}-${type}`) };
      break;
    case 'datetime':
      if (data.validation_json.subType !== 'gmt') {
        newErrors = {
          ...newErrors,
          ...validateField(data?.path || data.path_v2, value, data?.label),
        };
      }
      break;
    case 'typeahead':
    case 'textarea':
      newErrors = {
        ...newErrors,
        ...validateField(data?.path || data.path_v2, value, data?.label),
      };
      break;
    case 'input':
      newErrors = { ...newErrors, ...validateInput(value, data) };
      break;
  }
  newErrors = _.pickBy(newErrors, (i) => i !== undefined);
  return newErrors;
};
