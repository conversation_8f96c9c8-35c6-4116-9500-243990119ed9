export const getEUAHeader = (year) => {
  return `${year} EU Allowance`;
};

export const getEUACostHeader = (year) => {
  return `Estimate ${year} EUA Cost (€)`;
};

export const isValidFileFormat = (file, formats) => {
  return formats.some((format) => file.name.toLowerCase().endsWith(format.toLowerCase()));
};

export const isValidFileSize = (file, validFileSize) => {
  const fileSizeKiloBytes = file.size / 1024;
  const fileSizeMegaBytes = fileSizeKiloBytes / 1024;
  if (fileSizeMegaBytes > validFileSize) {
    return false;
  }
  return true;
};

export const getTotalCO2EmittedHeader = (year) => {
  if (year >= 2026) return 'Total CO2(e) Emitted (tons)';
  return 'Total CO2 Emitted (tons)';
};
