export const tidyVesselListData = (data) => {
    if (data.columns.some((column) => column.id === 'qhse')) {
        data.columns.splice(
          data.columns.findIndex((column) => column.id === 'qhse'),
          1,
          {
            type: 'join_item',
            Header: 'QHSE Deputy General Manager',
            name: 'fleet_staff.qhse_deputy_general_manager.full_name',
          },
          {
            type: 'join_item',
            Header: 'QHSE Manager',
            name: 'fleet_staff.qhse_manager.full_name',
          },
        );
    };
    if (data.columns.some((column) => column.id === 'operations')) {
        data.columns.splice(
          data.columns.findIndex((column) => column.id === 'operations'),
          1,
          {
            type: 'join_item',
            Header: 'Operations Director',
            name: 'fleet_staff.operation_director.full_name',
          },
          {
            type: 'join_item',
            Header: 'Operations Manager',
            name: 'fleet_staff.operation_manager.full_name',
          },
        );
    };
    if (data.columns.some((column) => column.id === 'vessel_accountants')) {
        data.columns.splice(
          data.columns.findIndex((column) => column.id === 'vessel_accountants'),
          1,
          {
            type: 'join_item',
            Header: 'Primary Vessel Accountant',
            name: 'fleet_staff.primary_accountant.full_name',
          },
          {
            type: 'join_item',
            Header: 'Secondary Vessel Accountant',
            name: 'fleet_staff.secondary_accountant.full_name',
          },
        );
    };
    if (data.columns.some((column) => column.id === 'payroll_accountants')) {
        data.columns.splice(
          data.columns.findIndex((column) => column.id === 'payroll_accountants'),
          1,
          {
            type: 'join_item',
            Header: 'Primary Payroll Accountant',
            name: 'fleet_staff.primary_payroll.full_name',
          },
          {
            type: 'join_item',
            Header: 'Secondary Payroll Accountant',
            name: 'fleet_staff.secondary_payroll.full_name',
          },
        );
    };
    if (data.columns.some((column) => column.id === 'buyers')) {
        data.columns.splice(
          data.columns.findIndex((column) => column.id === 'buyers'),
          1,
          {
            type: 'join_item',
            Header: 'Senior Lead Buyer',
            name: 'fleet_staff.buyer_senior_lead.full_name',
          },
          {
            type: 'join_item',
            Header: 'Lead Buyer',
            name: 'fleet_staff.buyer_lead.full_name',
          },
          {
            type: 'join_item',
            Header: 'Buyer',
            name: 'fleet_staff.buyer.full_name',
          },
          {
            type: 'join_item',
            Header: 'Senior Lead Buyer Email',
            name: 'fleet_staff.buyer_senior_lead.email',
          },
          {
            type: 'join_item',
            Header: 'Lead Buyer Email',
            name: 'fleet_staff.buyer_lead.email',
          },
          {
            type: 'join_item',
            Header: 'Buyer Email',
            name: 'fleet_staff.buyer.email',
          }
        );
    };
};