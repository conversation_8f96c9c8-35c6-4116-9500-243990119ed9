import XLSX from 'sheetjs-style';
import INTERVAL_UNIT from '../constants/interval-unit';
import { getLocalStorage } from './local-storage-helper';
import { formatDate, formatNumber, formatValue } from './view-utils';
import { tidyVesselListData } from './export-vessel-list';
import _ from 'lodash';
import {
  LOWER_CALORIFIC_VALUES,
  GHG_INTENSITY_TARGET_VALUES,
  WELL_TO_WAKE_GHG_INTENSITY_VALUES,
} from '../constants/fuel-eu-constants';

export const exportToExcel = (jsonData, fileName) => {
  const wb = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet([[]]);
  XLSX.utils.book_append_sheet(wb, worksheet, 'Sheet1');

  const wscols = [{ wch: 25 }, { wch: 50 }];
  worksheet['!cols'] = wscols;

  addInfoToFile([], fileName, worksheet, { c: 0, r: 0 });

  let lastCell = { c: 0, r: 2 };
  Object.keys(jsonData).forEach((section) => {
    lastCell = {
      c: 0,
      r:
        addInfoToFile(jsonData[section].data, jsonData[section].title, worksheet, lastCell).lastCell
          ?.r + 2,
    };
  });
  XLSX.writeFile(wb, `${fileName}.xlsx`);
  return result;
};

export const exportTableToExcel = (data, fileName) => {
  const wb = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet([[]]);
  XLSX.utils.book_append_sheet(wb, worksheet, 'Sheet1');
  const initialCell = { c: 0, r: 2 };
  let r = initialCell.r;
  if (fileName != undefined) {
    const cell_ref = XLSX.utils.encode_cell(initialCell);
    XLSX.utils.sheet_add_aoa(worksheet, [[fileName]], { origin: cell_ref });
    r = r + 1;
  }
  const wscols = [];
  let lastCell = { c: 0, r: r + 2 };
  Object.keys(data).forEach((section) => {
    const loadTableDataResult = loadTableData(data[section], worksheet, lastCell);
    lastCell = {
      c: loadTableDataResult.lastCell?.c,
      r: loadTableDataResult.lastCell?.r + 2,
    };
  });
  for (let i = 0; i < lastCell.c; i++) {
    wscols.push({ wch: 25 });
  }
  worksheet['!cols'] = wscols;
  XLSX.writeFile(wb, `${fileName}.xlsx`);
};

export const insertIntoCell = (cell_address, worksheet, data) => {
  const cell_ref = XLSX.utils.encode_cell(cell_address);
  XLSX.utils.sheet_add_aoa(worksheet, [[data]], { origin: cell_ref });
};

const loadColumnHeader = (column, worksheet, c, r) => {
  let cell_address;
  let cell_ref;
  if (column.columns) {
    const merge = { s: { r: r - 1, c: c }, e: { r: r - 1, c: c + 1 } };
    if (!worksheet['!merges']) worksheet['!merges'] = [];
    worksheet['!merges'].push(merge);
    cell_address = { r: r - 1, c: c };
    cell_ref = XLSX.utils.encode_cell(cell_address);
    XLSX.utils.sheet_add_aoa(worksheet, [[column.Header]], { origin: cell_ref });
    let addedColumns = 0;
    column.columns.forEach((item) => {
      if (item.name) {
        insertIntoCell({ c: c + addedColumns, r: r }, worksheet, item.Header);
        addedColumns = addedColumns + 1;
      }
    });
    c = c + addedColumns;
  } else if (column.name) {
    insertIntoCell({ c: c, r: r }, worksheet, column.Header);
    c = c + 1;
  }
  return c;
};

const formatColumnData = (column, item, c_index, r_index) => {
  let value = '';
  switch (column.type) {
    case 'date':
      value = formatDate(item[column.name], 'DD MMM YYYY HH:mm');
      break;
    case 'number':
      value = formatNumber(item[column.name], 0);
      break;
    case 'last_arr_item':
      value = _.get(_.last(item[column.name]), column.subName, '---');
      break;
    case 'join_arr_item':
      value = formatValue(item[column.name]?.map((i) => i[column?.subName]).join(', '), '---');
      break;
    case 'join_staff_item':
      value = _.get(item, column.name, '---') + ' , ' + _.get(item, column.subName, '---');
      break;
    case 'join_item':
      value = _.get(item, column.name, '---');
      break;
    case 'join_contact_items':
      value = formatValue(
        item[column.name]
          ?.map((i) => `${_.get(i, column?.subName1, '---')}: ${i[column?.subName2]}`)
          .join(', '),
        '---',
      );
      break;
    case 'item_value':
      value = _.get(item, column.name, '---');
      break;
    default:
      if (c_index === 0) {
        value = formatValue(
          column.name === 'index' ? r_index + 1 : item[column.name] || item?.field?.name,
        );
      } else {
        value = formatValue(item[column.name]);
      }
  }
  return value;
};

const loadColumnData = (column, item, r_index, c_index, worksheet, r) => {
  if (column.columns) {
    let addedColumns = 0;
    column.columns.forEach((col) => {
      if (col.name) {
        const value = formatColumnData(col, item, c_index + addedColumns, r_index);
        insertIntoCell({ c: c_index + addedColumns, r: r }, worksheet, value);
        addedColumns = addedColumns + 1;
      }
    });
    c_index = c_index + addedColumns;
  } else if (column.name) {
    const value = formatColumnData(column, item, c_index, r_index);
    insertIntoCell({ c: c_index, r: r }, worksheet, value);
    c_index += 1;
  }
  return c_index;
};

const loadTableData = (data, worksheet, initialCell) => {
  let r = initialCell.r;
  if (data.title != undefined) {
    const cell_ref = XLSX.utils.encode_cell(initialCell);
    XLSX.utils.sheet_add_aoa(worksheet, [[data.title]], { origin: cell_ref });
    r = r + 1;
  }
  let c = 0;
  tidyVesselListData(data);
  data.columns.forEach((column) => {
    c = loadColumnHeader(column, worksheet, c, r);
  });
  r = r + 1;
  data.jsonData?.forEach((item, r_index) => {
    let c_index = 0;
    data.columns.forEach((column) => {
      c_index = loadColumnData(column, item, r_index, c_index, worksheet, r);
    });
    r = r + 1;
  });
  const lastCell = { c: data.columns.length, r: r };
  return { sheet: worksheet, lastCell: lastCell };
};

const addInfoToFile = (data, title, worksheet, initialCell) => {
  if (worksheet === undefined || initialCell === undefined) {
    return;
  }

  const c = initialCell.c;
  let r = initialCell.r;

  if (title != undefined) {
    const cell_ref = XLSX.utils.encode_cell(initialCell);
    XLSX.utils.sheet_add_aoa(worksheet, [[title]], { origin: cell_ref });
    r = r + 1;
  }

  data.forEach((item) => {
    let cell_address = { c: c, r: r };
    let cell_ref = XLSX.utils.encode_cell(cell_address);
    XLSX.utils.sheet_add_aoa(worksheet, [[item.label]], { origin: cell_ref });

    cell_address = { c: c + 1, r: r };
    cell_ref = XLSX.utils.encode_cell(cell_address);
    XLSX.utils.sheet_add_aoa(worksheet, [[`${item.value} ${item.unit || ''}`]], {
      origin: cell_ref,
    });

    r = r + 1;
  });

  const lastCell = { c: c, r: r };

  return { sheet: worksheet, lastCell: lastCell };
};

export const formatExcelData = (key, excelData = []) => {
  const data = _.isEmpty(excelData) ? getLocalStorage(key) : excelData;
  let modifiedJsonData = [];
  data.jsonData.forEach((item) => {
    let modifiedData = {};
    switch (data.title) {
      case 'POSITION REPORT':
        data.columns.forEach((column) => {
          switch (column.name) {
            case 'report_date':
              modifiedData.report_date = formatDate(item.gmt, 'DD MMM YYYY');
              break;
            case 'smt':
              modifiedData.smt = formatDate(item.smt, 'HH:mm');
              break;
            case 'gmt':
              modifiedData.gmt = formatDate(item.gmt, 'HH:mm');
              break;
            case 'location':
              modifiedData.location = item.report_type;
              break;
            case 'position':
              modifiedData.position =
                item.report_json.general.latitude + item.report_json.general.longitude;
              break;
            case 'speed':
              modifiedData.speed = item.report_json.general.average_speed;
              break;
            case 'etc':
              modifiedData.etc = formatDate(item.report_json.other.etc, 'DD MMM YYYY HH:mm');
              break;
            case 'port':
              modifiedData.port = item.report_json.general.port;
              break;
            case 'eta':
              modifiedData.eta = formatDate(item.report_json.other.eta, 'DD MMM YYYY HH:mm');
              break;
            case 'next_port':
              modifiedData.next_port = item.report_json.general.next_port;
              break;
            default:
              modifiedData[column.name] = item[column.name];
              break;
          }
        });
        break;
      case 'CONTACT':
        data.columns.forEach((column) => {
          switch (column.name) {
            case 'party_type':
              modifiedData.party_type = item.ship_party_type.name;
              break;
            case 'party_name':
              modifiedData.party_name = item.name;
              break;
            case 'contact_person':
              modifiedData.contact_person = item.ship_party_contact_person[0]?.contact_person_name;
              break;
            case 'phone':
              modifiedData.phone = item.telephone_office;
              break;
            case 'phone_after_hrs':
              modifiedData.phone_after_hrs = item.telephone_office_after_hours;
              break;
            case 'mobile':
              modifiedData.mobile = item.ship_party_contact_person[0]?.mobile_phone;
              break;
            case 'email':
              modifiedData.email =
                item.ship_party_office_emails.length > 0
                  ? item.ship_party_office_emails.map((email) => email.office_email).join(', ')
                  : item.ship_party_office_emails[0]?.office_email;
              break;
            default:
              modifiedData[column.name] = item[column.name];
              break;
          }
        });
        break;
      case 'EMERGENCY DRILL':
        data.columns.forEach((column) => {
          switch (column.name) {
            case 'name':
              modifiedData.name = formatValue(_.get(item, 'vessel_drill.name'));
              break;
            case 'due_date':
              modifiedData.due_date = item?.due_date ? formatDate(item?.due_date) : 'Not Done';
              break;
            case 'description':
              modifiedData.description = _.get(item, 'vessel_drill.description');
              break;
            case 'last_date':
              modifiedData.last_date = formatDate(_.get(item, 'vessel_drill_history.drill_date'));
              break;
            case 'period':
              modifiedData.period =
                _.get(item, 'vessel_drill.interval') +
                ' ' +
                INTERVAL_UNIT[_.get(item, 'vessel_drill.interval_unit')];
              break;
            case 'remarks':
              modifiedData.remarks = _.get(item, 'vessel_drill_history.remark');
              break;
            default:
              modifiedData[column.name] = item[column.name];
              break;
          }
        });
        break;
      default:
        modifiedData = item;
        break;
    }
    modifiedJsonData.push(modifiedData);
  });
  data.jsonData = modifiedJsonData;
  return data;
};

export const formatCertificateData = (certificates) => {
  return certificates.map((cert) => {
    if (cert.grace && cert.grace_unit) {
      cert.grace_period = `${cert.grace}${cert.grace_unit.charAt(0)}`;
    }
    return cert;
  });
};

export const exportToImoExcel = (excelData) => {
  const wb = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet([[]]);
  XLSX.utils.book_append_sheet(wb, worksheet, 'IMO DCS');
  worksheet['!rows'] = [
    { hpt: 14.5 },
    { hpt: 14.5 },
    { hpt: 14.5 },
    { hpt: 14.5 },
    { hpt: 64 },
    { hpt: 153 },
    { hpt: 153 },
  ];

  insertIntoCell(
    {
      c: 0,
      r: 0,
    },
    worksheet,
    'STANDARDIZED DATA REPORTING FORMAT FOR THE DATA COLLECTION SYSTEM',
  );

  insertSearchDate(worksheet, excelData);
  const merge1 = { s: { r: 4, c: 1 }, e: { r: 4, c: 10 } };
  const merge2 = { s: { r: 4, c: 13 }, e: { r: 4, c: 14 } };
  [...Array(9)].forEach((_, i) => {
    const cell = { ...merge1.s };
    cell.c = cell.c + i + 1;
    insertStyleBorder(cell, worksheet, '', i == 8 ? ['top', 'right'] : ['top']);
  });
  [...Array(1)].forEach((_, i) => {
    const cell = { ...merge2.s };
    cell.c = cell.c + i + 1;
    insertStyleBorder(cell, worksheet, '', i == 0 ? ['top', 'right'] : ['top']);
  });
  if (!worksheet['!merges']) worksheet['!merges'] = [];
  worksheet['!merges'].push(merge1);
  insertStyleCell({ c: 1, r: 4 }, worksheet, 'FUEL OIL CONSUMPTION (t)', true, 'FFD9E1F2');
  worksheet['!merges'].push(merge2);
  insertStyleCell({ c: 13, r: 4 }, worksheet, 'POWER OUTPUT (RATED POWER) (KW)', true, 'FFD9E1F2');

  excelData?.data?.forEach((item, index) => {
    insertStyleCell(
      {
        c: index,
        r: 5,
      },
      worksheet,
      item.column,
    );
  });
  excelData?.data?.forEach((item, index) => {
    insertStyleCell(
      {
        c: index,
        r: 6,
      },
      worksheet,
      item.data,
      false,
    );
  });

  XLSX.writeFile(wb, `${excelData.fileName}.xlsx`);
};

export const exportToWasteAnalysisExcel = (excelData) => {
  const wb = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet([[]]);
  XLSX.utils.book_append_sheet(wb, worksheet, 'Waste Stream Analysis');
  worksheet['!rows'] = [
    { hpt: 14.5 },
    { hpt: 14.5 },
    { hpt: 14.5 },
    { hpt: 14.5 },
    { hpt: 14.5 },
    { hpt: 64 },
    { hpt: 153 },
    { hpt: 153 },
  ];

  const row_column_one = 5;

  const column_one = [
    {
      header: 'TIME',
      merge: { s: { r: row_column_one, c: 3 }, e: { r: row_column_one, c: 4 } },
      bgColor: 'FFD9E1F2',
      mergeCell: 2,
    },
    {
      header: 'DISTANCE',
      merge: { s: { r: row_column_one, c: 5 }, e: { r: row_column_one, c: 5 } },
      bgColor: 'FFFCE4D6',
      mergeCell: 1,
    },
    {
      header: 'VOYAGES',
      merge: { s: { r: row_column_one, c: 6 }, e: { r: row_column_one, c: 7 } },
      bgColor: 'FFFFCC99',
      mergeCell: 2,
    },
    {
      header: 'CARGO',
      merge: { s: { r: row_column_one, c: 8 }, e: { r: row_column_one, c: 9 } },
      bgColor: 'FFCCFFCC',
      mergeCell: 2,
    },
    {
      header: 'FUEL',
      merge: { s: { r: row_column_one, c: 10 }, e: { r: row_column_one, c: 11 } },
      bgColor: 'FFCC99FF',
      mergeCell: 2,
    },
    {
      header: 'CO2',
      merge: { s: { r: row_column_one, c: 12 }, e: { r: row_column_one, c: 15 } },
      bgColor: 'FF00F66F',
      mergeCell: 4,
    },
    {
      header: 'SOX',
      merge: { s: { r: row_column_one, c: 16 }, e: { r: row_column_one, c: 17 } },
      bgColor: 'FFFFFF99',
      mergeCell: 2,
    },
    {
      header: 'NOX',
      merge: { s: { r: row_column_one, c: 18 }, e: { r: row_column_one, c: 19 } },
      bgColor: 'FFFF00FF',
      mergeCell: 2,
    },
    {
      header: 'BILGE',
      merge: { s: { r: row_column_one, c: 20 }, e: { r: row_column_one, c: 20 } },
      bgColor: 'FFFF8080',
      mergeCell: 1,
    },
    {
      header: 'SLUDGE',
      merge: { s: { r: row_column_one, c: 21 }, e: { r: row_column_one, c: 21 } },
      bgColor: 'FFC0C0C0',
      mergeCell: 1,
    },
    {
      header: 'GARBAGE - GENERAL',
      merge: { s: { r: row_column_one, c: 22 }, e: { r: row_column_one, c: 22 } },
      bgColor: 'FFFFFF00',
      mergeCell: 1,
    },
    {
      header: 'GARBAGE - DRY CARGO RESIDUES',
      merge: { s: { r: row_column_one, c: 23 }, e: { r: row_column_one, c: 23 } },
      bgColor: 'FFFFFF00',
      mergeCell: 1,
    },
    {
      header: 'GARBAGE - FOOD WASTE',
      merge: { s: { r: row_column_one, c: 24 }, e: { r: row_column_one, c: 24 } },
      bgColor: 'FFFFFF00',
      mergeCell: 1,
    },
    {
      header: 'REFRIGERANTS (ODS)',
      merge: { s: { r: row_column_one, c: 25 }, e: { r: row_column_one, c: 25 } },
      bgColor: 'FF00CCFF',
      mergeCell: 1,
    },
    {
      header: 'ANNEX 1 SLOPS',
      merge: { s: { r: row_column_one, c: 26 }, e: { r: row_column_one, c: 26 } },
      bgColor: 'FF993300',
      mergeCell: 1,
    },
  ];

  insertIntoCell(
    {
      c: 0,
      r: 0,
    },
    worksheet,
    'ANNUAL WASTE STREAM, FUEL CONSUMPTION AND EMISSIONS REPORT',
  );

  insertSearchDate(worksheet, excelData);

  const merge1 = { s: { r: 4, c: 3 }, e: { r: 4, c: 17 } };
  const merge2 = { s: { r: 4, c: 18 }, e: { r: 4, c: 26 } };
  [...Array(15)].forEach((_, i) => {
    const cell = { ...merge1.s };
    cell.c = cell.c + i + 1;
    insertStyleBorder(cell, worksheet, '', ['top']);
  });
  [...Array(8)].forEach((_, i) => {
    const cell = { ...merge2.s };
    cell.c = cell.c + i + 1;
    insertStyleBorder(cell, worksheet, '', i == 7 ? ['top', 'right'] : ['top']);
  });
  if (!worksheet['!merges']) worksheet['!merges'] = [];
  worksheet['!merges'].push(merge1);
  insertStyleCell({ c: 3, r: 4 }, worksheet, 'DATA UPDATED PER VOYAGE');
  worksheet['!merges'].push(merge2);
  insertStyleCell({ c: 18, r: 4 }, worksheet, 'DATA UPDATED PER MONTH');

  column_one.forEach((item, index) => {
    if (item.mergeCell > 1) {
      [...Array(item.mergeCell - 1)].forEach((_, i) => {
        const cell = { ...item.merge.s };
        cell.c = cell.c + i + 1;
        insertStyleBorder(cell, worksheet, '');
      });
    }
    worksheet['!merges'].push(item.merge);
    insertStyleCell(item.merge.s, worksheet, item.header, true, item.bgColor);
  });

  excelData.data.forEach((item, index) => {
    insertStyleCell(
      {
        c: index,
        r: 6,
      },
      worksheet,
      item.column,
      !item.header,
      _.find(column_one, ['header', item.header])?.bgColor,
    );
  });
  excelData.data.forEach((item, index) => {
    insertStyleCell(
      {
        c: index,
        r: 7,
      },
      worksheet,
      item.data,
      false,
    );
  });

  XLSX.writeFile(wb, `${excelData?.fileName}.xlsx`);
};

export const insertStyleBorder = (
  cell_address,
  worksheet,
  data,
  borderList = ['right', 'left', 'top', 'bottom'],
) => {
  const cell_ref = XLSX.utils.encode_cell(cell_address);
  XLSX.utils.sheet_add_aoa(worksheet, [[data]], { origin: cell_ref });
  const border = {};
  borderList.forEach((item) => {
    border[item] = {
      style: 'thin',
      color: '000000',
    };
  });
  worksheet[cell_ref].s = {
    border: border,
  };
};

// function to get total field cell address for MRV report voyage, port and total table
export const fetchCellAddress = (data, startRow, pageIndex, colStart) => {
  return data?.map((item, index) => {
    const cellAddress = {
      c: index + colStart,
      r: startRow + pageIndex,
    };
    return XLSX.utils.encode_cell(cellAddress);
  });
};

// MRV VOYAGE TABLE CALCULATION
export const insertRangeSumCalculation = (worksheet, startCell, endCell, totalCell) => {
  XLSX.utils.sheet_set_array_formula(worksheet, totalCell, `SUM(${startCell}:${endCell})`);
};
export const insertSumCalculationWithString = (worksheet, startCell, endCell, totalCell) => {
  //Reference: https://a4accounting.com.au/how-to-sum-text-numbers-in-excel/
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    totalCell,
    `SUMPRODUCT((${startCell}:${endCell})*1)`,
  );
};

export const firstFieldCalculation = (worksheet, startCell, totalCell) => {
  XLSX.utils.sheet_set_array_formula(worksheet, `${totalCell}`, `${startCell}`);
};

export const VoyageCo2Calculation = (worksheet, startCell, endCell, totalPrevCell, totalCell) => {
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `SUM(${startCell}:${endCell})-${totalPrevCell}`,
  );
};

// calculate voyage Distance co2 cell and total calculation using same formula
export const co2EmissionPerDistCalculation = (worksheet, params, totalCell) => {
  const [totalCo2EmittedCell, distCell] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `ROUND(IF(ISERROR(${totalCo2EmittedCell}/${distCell}),0,${totalCo2EmittedCell}/${distCell}), 5)`,
  );
};

// calculate voyage transport co2 cell and total calculation using same formula
export const co2EmissionPerTransportWorkCalculation = (worksheet, params, totalCell) => {
  const [totalCo2EmittedCell, transportWorkCell] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `ROUND(IF(ISERROR(${totalCo2EmittedCell}*1000000/${transportWorkCell}),0,${totalCo2EmittedCell}*1000000/${transportWorkCell}), 5)`,
  );
};

// cell calculation formualas
export const voyageTotalCo2EmittedCellCalcuation = (worksheet, params, totalCell) => {
  const [
    hfoCo2Cell,
    lfoCo2Cell,
    mgoCo2Cell,
    lngCo2Cell,
    lpgpropaneCo2Cell,
    lpgButaneCo2Cell,
    methanolCell = 0,
    ethanolCell = 0,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `${hfoCo2Cell}+${lfoCo2Cell}+${mgoCo2Cell}+${lngCo2Cell}+${lpgpropaneCo2Cell}+${lpgButaneCo2Cell}+${methanolCell}+${ethanolCell}`,
  );
};

export const totalGhgCo2EquivalentEmittedCellCalculation = (worksheet, params, totalCell) => {
  const [totalCo2Cell, totalCh4Cell, totalN2oCell] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `${totalCo2Cell}+${totalCh4Cell}+${totalN2oCell}`,
  );
};

export const co2EmittedBetweenPortsCellCal = (worksheet, params, totalCell) => {
  const [euDepartCell, euArrivalCell, totalCo2Cell] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `IF(${euDepartCell}+${euArrivalCell}=2,${totalCo2Cell},0)`,
  );
};

export const timeSpentAtLandPortsCellCal = (worksheet, params, totalCell) => {
  const [arrivalHourCell, departureHourCell] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `HOUR(${departureHourCell}-${arrivalHourCell}) + ROUND(MINUTE(${departureHourCell}-${arrivalHourCell})/60, 1)`,
  );
};

export const co2EmittedPortsCellCal = (worksheet, params, totalCell) => {
  const [euCell, totalCo2Cell] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `IF(${euCell}=1,${totalCo2Cell},0)`,
  );
};

export const fuelConsumpPerDistCellCalcuation = (worksheet, params, totalCell) => {
  const [
    hfoCell,
    lfoCell,
    mgoCell,
    lngCell,
    lpgPropaneCell,
    lpgButaneCo2Cell,
    methanolCell = 0,
    ethanolCell = 0,
    distCell = 0,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `ROUND(IF(ISERROR(((${hfoCell}+${lfoCell}+${mgoCell}+${lngCell}+${lpgPropaneCell}+${lpgButaneCo2Cell}+${methanolCell}+${ethanolCell})/${distCell})),0,((${hfoCell}+${lfoCell}+${mgoCell}+${lngCell}+${lpgPropaneCell}+${lpgButaneCo2Cell}+${methanolCell}+${ethanolCell})/${distCell})), 5)`,
  );
};

export const fuelConsumpPerTransportCellCalcuation = (worksheet, params, totalCell) => {
  const [
    hfoCell,
    lfoCell,
    mgoCell,
    lngCell,
    lpgPropaneCell,
    lpgButaneCo2Cell,
    methanolCell,
    ethanolCell,
    transportCell,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `ROUND(IF(ISERROR((${hfoCell}+${lfoCell}+${mgoCell}+${lngCell}+${lpgPropaneCell}+${lpgButaneCo2Cell}+${methanolCell}+${ethanolCell})*1000000/${transportCell}),0,(${hfoCell}+${lfoCell}+${mgoCell}+${lngCell}+${lpgPropaneCell}+${lpgButaneCo2Cell}+${methanolCell}+${ethanolCell})*1000000/${transportCell}), 5)`,
  );
};

export const portTotalCo2EmittedCellCalcuation = (worksheet, params, totalCell) => {
  const [
    hfoCo2Cell = 0,
    lfoCo2Cell = 0,
    mgoCo2Cell = 0,
    lngCo2Cell = 0,
    lpgpropaneCo2Cell = 0,
    lpgButaneCo2Cell = 0,
    methanolCo2Cell = 0,
    ethanolCo2Cell = 0,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `${hfoCo2Cell}+${lfoCo2Cell}+${mgoCo2Cell}+${lngCo2Cell}+${lpgpropaneCo2Cell}+${lpgButaneCo2Cell}+${methanolCo2Cell}+${ethanolCo2Cell}`,
  );
};

export const portTotalCh4EmittedCellCalcuation = (worksheet, params, totalCell) => {
  const [
    hfoCh4Cell,
    lfoCh4Cell,
    mgoCh4Cell,
    lngCh4Cell,
    lpgpropaneCh4Cell,
    lpgButaneCh4Cell,
    methanolCh4Cell,
    ethanolCh4Cell,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `${hfoCh4Cell}+${lfoCh4Cell}+${mgoCh4Cell}+${lngCh4Cell}+${lpgpropaneCh4Cell}+${lpgButaneCh4Cell}+${methanolCh4Cell}+${ethanolCh4Cell}`,
  );
};

export const portTotalN2oEmittedCellCalcuation = (worksheet, params, totalCell) => {
  const [
    hfoN2oCell,
    lfoN2oCell,
    mgoN2oCell,
    lngN2oCell,
    lpgpropaneN2oCell,
    lpgButaneN2oCell,
    methanolN2oCell,
    ethanolN2oCell,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `${hfoN2oCell}+${lfoN2oCell}+${mgoN2oCell}+${lngN2oCell}+${lpgpropaneN2oCell}+${lpgButaneN2oCell}+${methanolN2oCell}+${ethanolN2oCell}`,
  );
};

export const portTotalGhgEmittedCellCalcuation = (worksheet, params, totalCell) => {
  const [totalCo2Cell, totalCh4Cell, totalN2oCell] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `${totalCo2Cell}+${totalCh4Cell}+${totalN2oCell}`,
  );
};

export const totalCalListTotalCo2EmittedCellCalculation = (worksheet, params, totalCell) => {
  const [totalGhgAllVoyagesCell, totalGhgCell] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `SUMPRODUCT((${totalGhgAllVoyagesCell}:${totalGhgCell})*1)`,
  );
};

export const multiplicationCalculation = (worksheet, params, totalCell) => {
  const [startCell, endCell] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `ROUND(${startCell}*${endCell}, 5)`,
  );
};

export const multiplicationCalculationForLngCo2OrN2oEmitted = (worksheet, params, totalCell) => {
  const [
    lngEmissionFactorCell,
    lngMainEngineEmissionFactorCell,
    lngDieselEngineEmissionFactorCell,
    meLngCell,
    geLngCell,
    totalLngConsumptionCell,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `(${meLngCell}-(${meLngCell}*${lngMainEngineEmissionFactorCell})/100)*${lngEmissionFactorCell}+(${geLngCell}-(${geLngCell}*${lngDieselEngineEmissionFactorCell})/100)*${lngEmissionFactorCell}+(${totalLngConsumptionCell}-${meLngCell}-${geLngCell})*${lngEmissionFactorCell}`,
  );
};

export const multiplicationCalculationForLngCh4Emitted = (worksheet, params, totalCell) => {
  const [
    lngEmissionFactorCell,
    lngMainEngineEmissionFactorCell,
    lngDieselEngineEmissionFactorCell,
    meLngCell,
    geLngCell,
    totalLngConsumptionCell,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `(${meLngCell}-(${meLngCell}*${lngMainEngineEmissionFactorCell})/100)*${lngEmissionFactorCell} + ((${meLngCell}*${lngMainEngineEmissionFactorCell})/100)+ (${geLngCell}-(${geLngCell}*${lngDieselEngineEmissionFactorCell})/100)*${lngEmissionFactorCell}+ ((${geLngCell}*${lngDieselEngineEmissionFactorCell})/100)+ (${totalLngConsumptionCell}-${meLngCell}-${geLngCell})*${lngEmissionFactorCell}`,
  );
};

export const multiplicationWithConstantForCh4EmittedCalculation = (
  worksheet,
  params,
  totalCell,
) => {
  const [totalCh4Emitted] = params;
  XLSX.utils.sheet_set_array_formula(worksheet, `${totalCell}`, `${totalCh4Emitted} * 28`);
};

export const multiplicationWithConstantForN2oEmittedCalculation = (
  worksheet,
  params,
  totalCell,
) => {
  const [totalN2oEmitted] = params;
  XLSX.utils.sheet_set_array_formula(worksheet, `${totalCell}`, `${totalN2oEmitted} * 265`);
};

//TOTAL CALCULATION FOR MRV REPORT
export const totalCalculation = (worksheet, params, totalCell) => {
  const concatCell = params.join('+');
  XLSX.utils.sheet_set_array_formula(worksheet, `${totalCell}`, concatCell);
};

export const insertStyleCell = (
  cell_address,
  worksheet,
  data,
  bold = true,
  bgColor = 'FFFFFFFF',
  fontColor = '00000000',
  border = true,
  textAlignment = 'center',
  verticalAlignment = undefined,
  fontSize = undefined,
  underline = false,
  italic = false,
) => {
  const cell_ref = XLSX.utils.encode_cell(cell_address);
  XLSX.utils.sheet_add_aoa(worksheet, [[data ?? '']], { origin: cell_ref });
  worksheet[cell_ref].s = {
    fill: {
      fgColor: { rgb: bgColor },
    },
    font: {
      bold: bold,
      color: { rgb: fontColor },
      sz: fontSize,
      underline,
      italic,
    },
    alignment: {
      wrapText: true,
      horizontal: textAlignment,
      vertical: verticalAlignment,
    },
    border: border
      ? {
          right: {
            style: 'thin',
            color: '000000',
          },
          left: {
            style: 'thin',
            color: '000000',
          },
          top: {
            style: 'thin',
            color: '000000',
          },
          bottom: {
            style: 'thin',
            color: '000000',
          },
        }
      : undefined,
  };
};

const insertSearchDate = (worksheet, excelData) => {
  insertIntoCell(
    {
      c: 10,
      r: 1,
    },
    worksheet,
    'YEAR',
  );

  insertIntoCell(
    {
      c: 11,
      r: 1,
    },
    worksheet,
    'MONTH',
  );

  insertIntoCell(
    {
      c: 13,
      r: 1,
    },
    worksheet,
    'YEAR',
  );

  insertIntoCell(
    {
      c: 14,
      r: 1,
    },
    worksheet,
    'MONTH',
  );

  insertIntoCell(
    {
      c: 8,
      r: 2,
    },
    worksheet,
    'SEARCH FROM',
  );

  insertIntoCell(
    {
      c: 10,
      r: 2,
    },
    worksheet,
    excelData?.startDate?.year,
  );

  insertIntoCell(
    {
      c: 11,
      r: 2,
    },
    worksheet,
    excelData?.startDate?.month,
  );

  insertIntoCell(
    {
      c: 12,
      r: 2,
    },
    worksheet,
    'TO',
  );

  insertIntoCell(
    {
      c: 13,
      r: 2,
    },
    worksheet,
    excelData?.endDate?.year,
  );

  insertIntoCell(
    {
      c: 14,
      r: 2,
    },
    worksheet,
    excelData?.endDate?.month,
  );
};

// EU-ETS excel formulas

export const totalFuelConsumed = (worksheet, params, totalCell) => {
  const [cell1, cell2, cell3, cell4, cell5, cell6, cell7, cell8, cell9] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `${cell1}+${cell2}+${cell3}+${cell4}+${cell5}+${cell6}+${cell7}+${cell8}+${cell9}`,
  );
};

export const totalEnergyConsumed = (worksheet, params, totalCell) => {
  const [
    lfoCons,
    mgoMdoCons,
    hfoCons,
    lngCons,
    methanolCons,
    lpgPropaneCons,
    lpgButaneCons,
    otherFuelCons,
    otherFuelLCV,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `(${lfoCons}*${LOWER_CALORIFIC_VALUES.LFO}+${mgoMdoCons}*${LOWER_CALORIFIC_VALUES.MGO}+${hfoCons}*${LOWER_CALORIFIC_VALUES.HFO}+${lngCons}*${LOWER_CALORIFIC_VALUES.LNG}+${methanolCons}*${LOWER_CALORIFIC_VALUES.METHANOL_NG}+${lpgPropaneCons}*${LOWER_CALORIFIC_VALUES.LPG_PROPANE}+${lpgButaneCons}*${LOWER_CALORIFIC_VALUES.LPG_BUTANE}+${otherFuelCons}*${otherFuelLCV})*POWER(10,6)`,
  );
};

export const complianceBalance = (worksheet, params, totalCell) => {
  const [totalEnergyConsumed, averageGHGIntensity, applicableEUAPercent = 100] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `(${totalEnergyConsumed}*(${GHG_INTENSITY_TARGET_VALUES[2025]}-${averageGHGIntensity}))/POWER(10,6)*(${applicableEUAPercent}/100)`,
  );
};

export const averageGHGIntensity = (worksheet, params, totalCell) => {
  const [
    lfoCons,
    mgoMdoCons,
    hfoCons,
    lngCons,
    methanolCons,
    lpgPropaneCons,
    lpgButaneCons,
    otherFuelCons,
    totalEnergyCons,
    otherFuelLcv,
    otherFuelComplianceBalance,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `IFERROR(((${lfoCons}*${LOWER_CALORIFIC_VALUES.LFO}*POWER(10,6)*${WELL_TO_WAKE_GHG_INTENSITY_VALUES.LFO}+` +
      `${mgoMdoCons}*${LOWER_CALORIFIC_VALUES.MGO}*POWER(10,6)*${WELL_TO_WAKE_GHG_INTENSITY_VALUES.MGO}+` +
      `${hfoCons}*${LOWER_CALORIFIC_VALUES.HFO}*POWER(10,6)*${WELL_TO_WAKE_GHG_INTENSITY_VALUES.HFO}+` +
      `${lngCons}*${LOWER_CALORIFIC_VALUES.LNG}*POWER(10,6)*${WELL_TO_WAKE_GHG_INTENSITY_VALUES.LNG}+` +
      `${methanolCons}*${LOWER_CALORIFIC_VALUES.METHANOL_NG}*POWER(10,6)*${WELL_TO_WAKE_GHG_INTENSITY_VALUES.METHANOL_NG}+` +
      `${lpgPropaneCons}*${LOWER_CALORIFIC_VALUES.LPG_PROPANE}*POWER(10,6)*${WELL_TO_WAKE_GHG_INTENSITY_VALUES.LPG_PROPANE}+` +
      `${lpgButaneCons}*${LOWER_CALORIFIC_VALUES.LPG_BUTANE}*POWER(10,6)*${WELL_TO_WAKE_GHG_INTENSITY_VALUES.LPG_BUTANE}+` +
      `${otherFuelLcv}*${otherFuelComplianceBalance}*${otherFuelCons}*POWER(10,6)))/${totalEnergyCons},0)`,
  );
};

export const estimateFuelEUPenalty = (worksheet, params, totalCell) => {
  const [averageGHGIntensity, complianceBalance] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `IFERROR(ROUND((${complianceBalance}/${averageGHGIntensity}*2400/41000*POWER(10,6)*-1),3),0)`,
  );
};

export const averageGHGIntensityTotalSummary = (worksheet, params, totalCell) => {
  const [
    totalEnergyConsVoyage,
    productOfTotalEnergyandAverageGHGVoyage,
    totalEnergyConsPort,
    productOfTotalEnergyandAverageGHGPort,
  ] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `ROUND((IFERROR(${productOfTotalEnergyandAverageGHGVoyage},0)+IFERROR(${productOfTotalEnergyandAverageGHGPort},0))/(IFERROR(${totalEnergyConsVoyage},0)+IFERROR(${totalEnergyConsPort},0)),4)`,
  );
};

export const estimateFuelEUPenaltyTotalSummary = (worksheet, params, totalCell) => {
  const [estimateFuelEUPenaltyVoyage, estimateFuelEUPenaltyPort] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `IFERROR(IF(IFERROR(${estimateFuelEUPenaltyPort},0)+IFERROR(${estimateFuelEUPenaltyVoyage},0)<0,0,ROUND(IFERROR(${estimateFuelEUPenaltyPort},0)+IFERROR(${estimateFuelEUPenaltyVoyage},0),2)),0)`,
  );
};

export const averageGHGTotalSummaryHelper = (worksheet, params, totalCell) => {
  const [totalEnergyCons, averageGHGIntensity] = params;
  XLSX.utils.sheet_set_array_formula(
    worksheet,
    `${totalCell}`,
    `IFERROR(${totalEnergyCons},0)*IFERROR(${averageGHGIntensity},0)`,
  );
};
