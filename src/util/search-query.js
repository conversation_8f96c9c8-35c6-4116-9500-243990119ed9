import searchTypes, { QUERY_TYPE_LIKE, QUERY_TYPE_MATCH, QUERY_TYPE_RANGE } from './search-types';
import moment from 'moment';
import { DROPDOWN_EORB_STATUS_MAPPING, EORB_PRODUCT_NAME } from '../model/constants';

export const getSearchTypes = searchTypes;

export const fleetStaffTypes = {
  superintendent: 'superintendent',
  qhse: ['qhse_manager', 'qhse_deputy_general_manager'],
  operations: [
    'operation_manager_tanker',
    'operation_director_tanker',
    'operation_manager_dry',
    'operation_director_dry',
  ],
  accountant: 'accountant',
  payroll: 'payroll',
  buyers: ['buyer', 'buyer_lead', 'buyer_senior_lead'],
};

const getDateRange = (startDate, endDate) => {
  let min = startDate ? moment(startDate).format('YYYY-MM-DD') : 'Invalid date';
  let max = endDate ? moment(endDate).format('YYYY-MM-DD') : 'Invalid date';

  if (min === 'Invalid date' && max === 'Invalid date') return null;
  if (min === 'Invalid date') min = '';
  if (max === 'Invalid date') max = '';

  return { min, max };
};

export const getValueFromCriteriaItem = (item) => {
  let value = '';
  switch (item.type.inputType) {
    case 'dropdown':
      value = [...Object.keys(fleetStaffTypes)].includes(item.type.type)
        ? item.subtype.id
        : item.subtype.value;
      break;
    case 'text':
      value = item.subtype;
      break;
    case 'number_range': {
      const min = item.subtype.min;
      const max = item.subtype.max;
      value = { min, max };
      break;
    }
    case 'date':
      value = getDateRange(item.subtype.startDate, item.subtype.endDate) || '';
      break;
    case 'year': {
      const year = moment(item.subtype).format('YYYY');
      if (year !== 'Invalid date') value = { min: `${year}-01-01`, max: `${year}-12-31` };
      break;
    }
    default:
      value = item.subtype ?? '';
  }
  return value;
};

export const putValueToCriteriaItem = (item, value) => {
  switch (item.type.inputType) {
    case 'dropdown':
      item.subtype = { value };
      break;
    case 'text':
      item.subtype = value;
      break;
    case 'number_range':
      item.subtype = {
        ...value,
      };
      break;
    case 'date': {
      let startDate = moment(value.min).toDate();
      let endDate = moment(value.max).toDate();
      if (startDate.toString() === 'Invalid Date' && endDate.toString() === 'Invalid Date') break;
      else if (startDate.toString() === 'Invalid Date') item.subtype = { endDate };
      else if (endDate.toString() === 'Invalid Date') item.subtype = { startDate };
      else item.subtype = { startDate, endDate };
      break;
    }
    case 'year':
      item.subtype = moment(value.min).toDate() == 'Invalid Date' ? '' : moment(value.min).toDate();
      break;
    default:
      item.subtype = value;
  }
  return item;
};

export const mapSearchCriteriaToVesselQuery = (searchCriteria) => {
  const query = searchCriteria
    .reduce((arr, item) => {
      const {
        type: { queryType, queryKey },
      } = item;
      const queryItems = [];

      let finalQueryKey = queryKey;
      // get value
      let value = getValueFromCriteriaItem(item);

      if (queryKey === 'vessel_product_status') {
        if (DROPDOWN_EORB_STATUS_MAPPING[value] !== 'not_installed') {
          queryItems.push({
            key: 'vessel_product_name',
            value: EORB_PRODUCT_NAME,
          });
          finalQueryKey = 'vessel_product_status';
          value = DROPDOWN_EORB_STATUS_MAPPING[value];
        } else {
          finalQueryKey = 'no_vessel_product';
          value = true;
        }
      }
      if (queryKey === 'vessel_class_regulation') {
        if (item?.subtype?.id) {
          value = item.subtype.id;
        }
      }

      // push query items
      switch (queryType) {
        case QUERY_TYPE_LIKE:
          queryItems.push({
            key: finalQueryKey,
            value: value ?? '',
          });
          break;
        case QUERY_TYPE_RANGE:
          if (value) {
            queryItems.push({
              key: `${finalQueryKey}`,
              value: `${value.min},${value.max}`,
            });
          }
          break;
        case QUERY_TYPE_MATCH:
        default:
          queryItems.push({
            key: finalQueryKey,
            value,
          });
      }
      return [...arr, ...queryItems];
    }, [])
    .map((item) => {
      const { key, value } = item;
      return `${key}=${encodeURIComponent(value)}`;
    })
    .join('&');

  return query;
};

const createRangeItem = ({ value, searchType, key }) => {
  const valuePair = value.split(',');
  return {
    searchType,
    key,
    value: {
      min: valuePair[0],
      max: valuePair[1],
    },
  };
};

export const mapVesselQueryToSearchCriteria = (vesselQuery, dropDownData) => {
  const criteria = decodeURIComponent(vesselQuery)
    .split('&')
    .map((param) => param.split('='))
    .reduce((arr, [key, value]) => {
      if (!key) return arr;
      const searchType = getSearchTypes().find((type) => type.queryKey === key);
      if (!searchType) return arr;
      const { queryType } = searchType;
      if (queryType === QUERY_TYPE_RANGE) {
        return [...arr, createRangeItem({ value, searchType, key })];
      }
      return [...arr, { searchType, key, value }];
    }, [])
    .map(({ searchType, value }) => {
      const item = {
        type: searchType,
      };
      putValueToCriteriaItem(item, value);
      if (searchType.inputType === 'dropdown' && dropDownData) {
        const dropDownOptions = dropDownData[searchType.type];
        if (!dropDownOptions) return item;
        const data = dropDownOptions.find(({ id, value }) =>
          [...Object.keys(fleetStaffTypes), 'tech_group'].includes(item.type.type)
            ? id === item.subtype.value
            : value === item.subtype.value,
        );
        if (!data) return item;
        item.subtype.id = data.id;
      }
      return item;
    });
  return criteria;
};
