export const certificates_coloum = [
    { Header: "Sr.No.", name: "index" }, 
    { Header: "Survey/Certificate", name: "name" }, 
    { Header: "Type", name: "type" }, 
    { Header: "Certificate Group", name: "group" }, 
    { Header: "Survey Department", name: "department" }, 
    { 
      Header: "Annual Audit/Inspection", 
      name: "Annual Audit/Inspection", 
      columns: [ 
        { Header: "Date of Survey", name: "audit_date", type:"date" }, 
        { Header: "Due Date", name: "audit_due_date", type:"date" }
      ]
    },
    { 
      Header: "Underway Audit/Inspection", 
      name: "Underway Audit/Inspection", 
      columns: [ 
        { Header: "Date of Survey", name: "underway_audit_date", type:"date" }, 
        { Header: "Due Date", name: "underway_audit_due_date", type:"date" }
      ]
    },
    { Header: "Grace Period", name: "grace_period" }, 
    { Header: "Status", name: "status" }, 
    { Header: "Planned Date of Inspection", name: "planned_date", type:"date" }, 
    { Header: "Port", name: "port" }, 
    { Header: "Remark", name: "remark" }
  ]


export const vessel_list_column = [
    {
        Header: 'No.',
        name: 'index',
    },
    {
      Header: 'Vessel',
      name: 'vessel_name',
    },
    {
      Header: 'Tech Group',
      name: 'tech_group',
    },
    {
      Header: 'Certificate Group',
      name: 'group',
    },
    {
      Header: 'Survey department',
      name: 'department',
    },
    {
      Header: 'Annual Audit/Inspection',
      name: 'annual',
      columns: [
        {
          Header: 'Date of Survey',
          name: 'audit_date',
          type:"date"
        },
        {
          Header: 'Due Date',
          name: 'audit_due_date',
          type:"date"
        },
      ],
    },
    {
      Header: 'Underway Audit/Inspection',
      name: 'underway',
      columns: [
        {
          Header: 'Date of Survey',
          name: 'underway_audit_date',
          type:"date"
        },
        {
          Header: 'Due Date',
          name: 'underway_audit_due_date',
          type:"date"
        },
      ],
    },
    {
      Header: 'Place of Survey',
      name: 'place',
    },
    {
      Header: 'Grace Period',
      name: 'grace_period',
    },
    {
      Header: 'Status',
      name: 'status',
    },
    {
      Header: 'Planned Date of Inspection',
      name: 'planned_date',
      type: 'date'
    },
    {
      Header: 'Port',
      name: 'port',
    },
    {
      Header: 'Remark',
      name: 'remark',
    }
  ];
  