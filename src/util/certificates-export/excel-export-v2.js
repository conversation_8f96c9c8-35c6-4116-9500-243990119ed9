import XLSX from 'sheetjs-style';
import { formatDate, formatNumber, formatValue } from '../view-utils';
import _ from 'lodash';
import { checkOperation } from './utils';

const headerStyle = {
  fill: { fgColor: { rgb: '538DD5' } }, // Blue background
  font: { bold: true, color: { rgb: 'FFFFFF' } }, // White bold text
  alignment: { horizontal: 'left', vertical: 'center' }, // Align left
};

export const exportTableToExcel2 = (data, fileName) => {
  const wb = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet([[]]);
  XLSX.utils.book_append_sheet(wb, worksheet, 'Sheet1');
  const initialCell = { c: 0, r: 2 };
  let r = initialCell.r;
  if (fileName != undefined) {
    const cell_ref = XLSX.utils.encode_cell(initialCell);
    XLSX.utils.sheet_add_aoa(worksheet, [[fileName]], { origin: cell_ref });
    worksheet[cell_ref].s = headerStyle;
    r = r + 1;
  }
  const wscols = [];
  let lastCell = { c: 0, r: r + 2 };
  Object.keys(data).forEach((section) => {
    const loadTableDataResult = loadTableData(data[section], worksheet, lastCell);
    lastCell = {
      c: loadTableDataResult.lastCell?.c,
      r: loadTableDataResult.lastCell?.r + 2,
    };
  });

  for (let i = 0; i < lastCell.c; i++) {
    wscols.push({ wch: 25 });
  }
  worksheet['!cols'] = wscols;
  XLSX.writeFile(wb, `${fileName}.xlsx`);
};

export const insertIntoCell = (cell_address, worksheet, data) => {
  const cell_ref = XLSX.utils.encode_cell(cell_address);
  XLSX.utils.sheet_add_aoa(worksheet, [[data]], { origin: cell_ref });
};

export const loadTableDetails = (worksheet, { name, type, department, crtGroup }) => {
  let c = 0,
    r = 2;
  const details = [
    ['Certificate/Survey Name', name],
    ['Type', type],
    ['Survey Department', checkOperation(department)],
    ['Certificate Group', crtGroup],
  ];

  details.forEach(([header, value], index) => {
    const row = r + index;
    // Insert header in Column A
    const cellRefHeader = XLSX.utils.encode_cell({ r: row, c: c });
    XLSX.utils.sheet_add_aoa(worksheet, [[header]], { origin: cellRefHeader });

    // Apply style to Column A
    if (!worksheet[cellRefHeader]) worksheet[cellRefHeader] = {};
    worksheet[cellRefHeader].s = headerStyle;

    // Insert value in Column B
    const cellRefValue = XLSX.utils.encode_cell({ r: row, c: c + 1 });
    XLSX.utils.sheet_add_aoa(worksheet, [[value]], { origin: cellRefValue });
  });

  return r + details.length;
};

const loadColumnHeader = (column, worksheet, c, r) => {
  let cell_address;
  let cell_ref;
  const headerStyle = {
    fill: { fgColor: { rgb: '538DD5' } }, // Background color
    font: { bold: true, color: { rgb: 'FFFFFF' } }, // White bold text
    alignment: { horizontal: 'center', vertical: 'center' }, // Center align
    border: {
      top: { style: 'thin', color: { rgb: '000000' } },
      left: { style: 'thin', color: { rgb: '000000' } },
      bottom: { style: 'thin', color: { rgb: '000000' } },
      right: { style: 'thin', color: { rgb: '000000' } },
    }, // Border style
  };
  if (column.columns) {
    const merge = { s: { r: r - 1, c: c }, e: { r: r - 1, c: c + 1 } };
    if (!worksheet['!merges']) worksheet['!merges'] = [];
    worksheet['!merges'].push(merge);
    cell_address = { r: r - 1, c: c };
    cell_ref = XLSX.utils.encode_cell(cell_address);
    XLSX.utils.sheet_add_aoa(worksheet, [[column.Header]], { origin: cell_ref });
    worksheet[cell_ref].s = headerStyle;
    let addedColumns = 0;
    column.columns.forEach((item) => {
      if (item.name) {
        cell_address = { r: r, c: c + addedColumns };
        cell_ref = XLSX.utils.encode_cell(cell_address);
        insertIntoCell({ c: c + addedColumns, r: r }, worksheet, item.Header);
        worksheet[cell_ref].s = headerStyle;
        addedColumns = addedColumns + 1;
      }
    });
    c = c + addedColumns;
  } else if (column.name) {
    cell_address = { r: r, c: c };
    cell_ref = XLSX.utils.encode_cell(cell_address);
    insertIntoCell({ c: c, r: r }, worksheet, column.Header);
    worksheet[cell_ref].s = headerStyle;
    c = c + 1;
  }

  // Add autofilter to the first row (header row)
  if (!worksheet['!autofilter']) worksheet['!autofilter'] = {};
  worksheet['!autofilter'].ref = XLSX.utils.encode_range({
    s: { r: r, c: 0 },
    e: { r: r, c: c - 1 },
  });

  return c;
};

const formatColumnData = (column, item, c_index, r_index) => {
  let value = '';
  switch (column.type) {
    case 'date':
      value = formatDate(item[column.name], 'DD MMM YYYY');
      break;
    case 'number':
      value = formatNumber(item[column.name], 0);
      break;
    case 'last_arr_item':
      value = _.get(_.last(item[column.name]), column.subName, '---');
      break;
    case 'join_arr_item':
      value = formatValue(item[column.name]?.map((i) => i[column?.subName]).join(', '), '---');
      break;
    case 'join_staff_item':
      value = _.get(item, column.name, '---') + ' , ' + _.get(item, column.subName, '---');
      break;
    case 'join_item':
      value = _.get(item, column.name, '---');
      break;
    case 'join_contact_items':
      value = formatValue(
        item[column.name]
          ?.map((i) => `${_.get(i, column?.subName1, '---')}: ${i[column?.subName2]}`)
          .join(', '),
        '---',
      );
      break;
    case 'item_value':
      value = _.get(item, column.name, '---');
      break;
    default:
      if (c_index === 0) {
        value = formatValue(
          column.name === 'index' ? r_index + 1 : item[column.name] || item?.field?.name,
        );
      } else {
        value = formatValue(item[column.name]);
      }
  }
  return value;
};

const loadColumnData = (column, item, r_index, c_index, worksheet, r) => {
  const columnStyle = {
    alignment: { horizontal: 'center', vertical: 'center' }, // Center align
    border: {
      top: { style: 'thin', color: { rgb: '000000' } },
      left: { style: 'thin', color: { rgb: '000000' } },
      bottom: { style: 'thin', color: { rgb: '000000' } },
      right: { style: 'thin', color: { rgb: '000000' } },
    },
  };
  if (column.columns) {
    let addedColumns = 0;
    column.columns.forEach((col) => {
      if (col.name) {
        const value = formatColumnData(col, item, c_index + addedColumns, r_index);
        insertIntoCell({ c: c_index + addedColumns, r: r }, worksheet, value);
        const cell_address = { r: r, c: c_index + addedColumns };
        const cell_ref = XLSX.utils.encode_cell(cell_address);
        worksheet[cell_ref].s = columnStyle; // Apply style to the cell
        addedColumns = addedColumns + 1;
      }
    });
    c_index = c_index + addedColumns;
  } else if (column.name) {
    let value = formatColumnData(column, item, c_index, r_index);
    if (column.name === 'department') {
      value = checkOperation(value);
    }
    insertIntoCell({ c: c_index, r: r }, worksheet, value);
    // Apply style to the inserted cell
    const cell_address = { r: r, c: c_index };
    const cell_ref = XLSX.utils.encode_cell(cell_address);
    worksheet[cell_ref].s = columnStyle; // Apply style to the cell
    c_index += 1;
  }
  return c_index;
};

const loadTableData = (data, worksheet, initialCell) => {
  let r = initialCell.r;
  let c = 0;
  if (data.title) {
    const cell_ref = XLSX.utils.encode_cell(initialCell);
    XLSX.utils.sheet_add_aoa(worksheet, [[data.title]], { origin: cell_ref });
    r = r + 1;
  } else if (data.tableinfo) {
    r = loadTableDetails(worksheet, data.tableinfo);
    r = r + 1;
  }
  console.log(data, data.column, data.jsonData);
  data.columns.forEach((column) => {
    c = loadColumnHeader(column, worksheet, c, r);
  });
  r = r + 1;
  data.jsonData?.forEach((item, r_index) => {
    let c_index = 0;
    data.columns.forEach((column) => {
      c_index = loadColumnData(column, item, r_index, c_index, worksheet, r);
    });
    r = r + 1;
  });
  const lastCell = { c: data.columns.length, r: r };
  return { sheet: worksheet, lastCell: lastCell };
};

const addInfoToFile = (data, title, worksheet, initialCell) => {
  if (worksheet === undefined || initialCell === undefined) {
    return;
  }

  const c = initialCell.c;
  let r = initialCell.r;

  if (title != undefined) {
    const cell_ref = XLSX.utils.encode_cell(initialCell);
    XLSX.utils.sheet_add_aoa(worksheet, [[title]], { origin: cell_ref });
    r = r + 1;
  }

  data.forEach((item) => {
    let cell_address = { c: c, r: r };
    let cell_ref = XLSX.utils.encode_cell(cell_address);
    XLSX.utils.sheet_add_aoa(worksheet, [[item.label]], { origin: cell_ref });

    cell_address = { c: c + 1, r: r };
    cell_ref = XLSX.utils.encode_cell(cell_address);
    XLSX.utils.sheet_add_aoa(worksheet, [[`${item.value} ${item.unit || ''}`]], {
      origin: cell_ref,
    });

    r = r + 1;
  });

  const lastCell = { c: c, r: r };

  return { sheet: worksheet, lastCell: lastCell };
};

export const formatCertificateData = (certificates) => {
  return certificates.map((cert, index) => {
    if (cert.grace && cert.grace_unit) {
      cert.grace_period = `${cert.grace}${cert.grace_unit.charAt(0)}`;
    } else {
      cert.grace_period = '---';
    }
    cert.index = index;
    return cert;
  });
};
