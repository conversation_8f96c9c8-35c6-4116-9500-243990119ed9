export const recipientListData = {
  results: [
    {
      id: 810,
      vessel_ownership_id: 1705,
      email_array: ['<EMAIL>'],
      cc_email_array: [''],
      subject: null,
      inactive: false,
      updated_at: '2023-07-17T13:40:47.079Z',
      created_at: '2023-02-13T22:45:57.337Z',
      vessel_accountants: {
        primary: {
          name: '<PERSON><PERSON><PERSON>',
          email: 'bhumits<PERSON><EMAIL>',
        },
        secondary: {
          name: '<PERSON><PERSON><PERSON><PERSON>',
          email: '<EMAIL>',
        },
      },
      vessel_name: '<PERSON><PERSON> (MMPL)',
      created_by: null,
      updated_by: '<EMAIL>',
    },
    {
      id: 2,
      vessel_ownership_id: 1381,
      email_array: ['313', '314', '315', '316', '317', '318', '319'],
      cc_email_array: ['Vicky<PERSON><EMAIL>'],
      subject: null,
      vessel_accountants: {},
      inactive: false,
      updated_at: '2022-12-08T03:14:47.576Z',
      created_at: '2022-12-08T03:14:47.576Z',
      vessel_name: 'Alpine Minute (2nd Old)',
      updated_by: null,
      created_by: null,
    },
    {
      id: 3,
      vessel_ownership_id: 353,
      email_array: ['349', '350', '351', '352', '353', '354', '355'],
      cc_email_array: ['<EMAIL>'],
      subject: null,
      vessel_accountants: {},
      inactive: false,
      updated_at: '2022-12-08T03:14:47.576Z',
      created_at: '2022-12-08T03:14:47.576Z',
      vessel_name: 'Alpine Magic',
      updated_by: null,
      created_by: null,
    },
    {
      id: 4,
      vessel_ownership_id: 440,
      email_array: ['418', '421', '422', '423', '424'],
      cc_email_array: ['<EMAIL>'],
      subject: null,
      vessel_accountants: {},
      inactive: false,
      updated_at: '2022-12-08T03:14:47.576Z',
      created_at: '2022-12-08T03:14:47.576Z',
      vessel_name: 'FPMC C Melody',
      updated_by: null,
      created_by: null,
    },
  ],
  total: 20,
};
