export const itineraryData = {
  results: [
    {
      vessel_ownership_id: 126,
      paris1_vessel_ref_id: 4458,
      itinerary_num_in_vessel: 480,
      estimated_arrival: '2022-04-20T08:00:00.000Z',
      estimated_berth: null,
      estimated_departure: '2022-07-21T16:00:00.000Z',
      deleted_at: null,
      country: 'Netherlands',
      berth: null,
      port: 'Rotterdam',
      vessel_itinerary_reasons: 'Bunkering, Crew change, Discharging Cargo',
      paris1_created_at: '2022-04-01T12:30:41.000Z',
      paris1_updated_at: '2022-04-01T12:30:41.000Z',
      created_by_hash: 'Sh** Ma**',
      updated_by_hash: 'Sh** Ma**',
      id: 234288,
      created_at: '2022-06-09T08:06:24.232Z',
      updated_at: '2022-06-09T10:12:47.000Z',
    },
    {
      vessel_ownership_id: 126,
      paris1_vessel_ref_id: 4458,
      itinerary_num_in_vessel: 479,
      estimated_arrival: '2022-04-04T19:00:00.000Z',
      estimated_berth: null,
      estimated_departure: '2022-07-07T06:00:00.000Z',
      deleted_at: null,
      country: 'Brazil',
      berth: null,
      port: 'Suape',
      vessel_itinerary_reasons: 'Discharging Cargo',
      paris1_created_at: '2022-04-01T12:30:41.000Z',
      paris1_updated_at: '2022-04-01T12:30:41.000Z',
      created_by_hash: 'Sh** Ma**',
      updated_by_hash: 'Sh** Ma**',
      id: 234287,
      created_at: '2022-06-09T08:06:24.232Z',
      updated_at: '2022-06-09T10:12:47.000Z',
    },
  ],
  total: 2,
};
