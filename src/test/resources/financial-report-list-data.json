{"results": [{"id": 227525, "vessel_ownership_id": 1315, "report_date": "2022-12-30T00:00:00.000Z", "url": "/ui2022/vessel/financial/202212120313136_voyage_report_sql.txt", "book_year": 2023, "book_month": 1, "inactive": false, "remark": "ML TEST 2222 333", "vessel_name": "Cano", "report_type": "Captain <PERSON> Account", "book_month_year": "2023-01-02T00:00:00.000Z", "email": "<EMAIL>", "updated_at": "2022-12-14T01:48:50.661Z", "created_at": "2022-12-13T09:42:04.875Z", "created_by": "paris2_service", "updated_by": "paris2_service"}, {"id": 227531, "vessel_ownership_id": 1315, "report_date": "2022-12-14T00:00:00.000Z", "url": "/ui2022/vessel/financial/202212140210395_202212130407302_paris2_cert_list.csv", "book_year": 2023, "book_month": 7, "inactive": false, "remark": "frontend test 1", "vessel_name": "Cano", "report_type": "Committed Costs", "book_month_year": "2023-07-02T00:00:00.000Z", "email": "<EMAIL>", "updated_at": "2022-12-14T02:10:40.883Z", "created_at": "2022-12-14T02:10:40.883Z", "created_by": "<EMAIL>", "updated_by": "<EMAIL>"}, {"id": 227530, "vessel_ownership_id": 1315, "report_date": "2022-12-14T00:00:00.000Z", "url": "/ui2022/vessel/financial/202212140209469_202212120313136_voyage_report_sql_(1).txt", "book_year": 2022, "book_month": 6, "inactive": false, "remark": "test", "vessel_name": "Cano", "report_type": "Accounting Report", "book_month_year": "2022-06-02T00:00:00.000Z", "email": "<EMAIL>", "updated_at": "2022-12-14T02:09:51.089Z", "created_at": "2022-12-14T02:09:51.089Z", "created_by": "<EMAIL>", "updated_by": "<EMAIL>"}, {"id": 227518, "vessel_ownership_id": 1932, "report_date": "2022-12-14T00:00:00.000Z", "url": "/ui2022/vessel/financial/202212130957559_IMO_DCS_Reports(4).xlsx", "book_year": 2020, "book_month": 1, "inactive": false, "remark": "test data", "vessel_name": "Beks Blue", "report_type": "Disbursements", "book_month_year": "2020-01-02T00:00:00.000Z", "email": "<EMAIL>", "updated_at": "2022-12-13T09:57:58.248Z", "created_at": "2022-12-13T07:03:00.639Z", "created_by": "<EMAIL>", "updated_by": "<EMAIL>"}, {"id": 227527, "vessel_ownership_id": 1933, "report_date": "2022-12-13T00:00:00.000Z", "url": "/ui2022/vessel/financial/202212130955425_Waste_Stream_Analysis(2).xlsx", "book_year": 2021, "book_month": 4, "inactive": false, "remark": "test data", "vessel_name": "Acer Arrow (MMPL)-u1", "report_type": "Committed Costs", "book_month_year": "2021-04-02T00:00:00.000Z", "email": "<EMAIL>", "updated_at": "2022-12-13T09:55:45.346Z", "created_at": "2022-12-13T09:55:45.346Z", "created_by": "<EMAIL>", "updated_by": "<EMAIL>"}, {"id": 227529, "vessel_ownership_id": 1932, "report_date": "2022-12-13T00:00:00.000Z", "url": "/ui2022/vessel/financial/202212130944481_Waste_Stream_Analysis(2).xlsx", "book_year": 2023, "book_month": 2, "inactive": false, "remark": "test data", "vessel_name": "Beks Blue", "report_type": "Captain <PERSON> Account", "book_month_year": "2023-02-02T00:00:00.000Z", "email": "<EMAIL>", "updated_at": "2022-12-14T01:59:33.979Z", "created_at": "2022-12-14T01:59:33.979Z", "created_by": "paris2_service", "updated_by": "paris2_service"}, {"id": 227526, "vessel_ownership_id": 1932, "report_date": "2022-12-13T00:00:00.000Z", "url": "/ui2022/vessel/financial/202212130944481_Waste_Stream_Analysis(2).xlsx", "book_year": 2023, "book_month": 2, "inactive": false, "remark": "test data", "vessel_name": "Beks Blue", "report_type": "Captain <PERSON> Account", "book_month_year": "2023-02-02T00:00:00.000Z", "email": "<EMAIL>", "updated_at": "2022-12-13T09:53:28.921Z", "created_at": "2022-12-13T09:53:28.921Z", "created_by": "paris2_service", "updated_by": "paris2_service"}], "total": 25}