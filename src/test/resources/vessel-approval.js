export const activePendingVessel = [
  {
    id: 11,
    status: 'draft',
    pending_status: 'active',
    misc_classification_euets_verifier_id: 1,
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
        vessel_class_regulation: [{ class_id: 5 }],
        fleet_staff: [
          {
            tech_group: 'techgroup1',
          },
        ],
      },
    ],
  },
];

export const handedOverPendingVessel = [
  {
    id: 11,
    status: 'active',
    pending_status: 'handed_over',
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
      },
    ],
  },
];

export const activeButHandedOverNotStartedResponse = [
  {
    id: 11,
    status: 'active',
    pending_status: null,
    misc_classification_euets_verifier_id: 1,
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
        vessel_class_regulation: [{ class_id: 5 }],
        fleet_staff: [
          {
            tech_group: 'techgroup1',
          },
        ],
      },
    ],
  },
];

export const handedOverButArchivalNotStartedResponse = [
  {
    id: 11,
    status: 'handed_over',
    pending_status: null,
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
      },
    ],
  },
];

export const archivalPendingVessel = [
  {
    id: 11,
    status: 'handed_over',
    pending_status: 'archived',
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
      },
    ],
  },
];

export const archivedVessel = [
  {
    id: 11,
    status: 'archived',
    pending_status: null,
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
      },
    ],
  },
];
