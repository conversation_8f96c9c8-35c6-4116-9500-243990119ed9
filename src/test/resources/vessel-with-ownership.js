export const activePendingVessel = [
  {
    id: 1,
    status: 'draft',
    pending_status: 'active',
    phones: [],
    emails: [],
    ownerships: [
      {
        id: 11,
        vessel_id: 1,
        tech_group: 'techgroup1',
        owner_start_date: null,
      },
    ],
  },
];

export const handedOverPendingVessel = [
  {
    id: 1,
    status: 'active',
    pending_status: 'handed_over',
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
        vessel_id: 1,
        owner_start_date: null,
      },
    ],
  },
];

export const activeButHandedOverNotStartedResponse = [
  {
    id: 1,
    status: 'active',
    pending_status: null,
    vessel: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
        vessel_id: 1,
        owner_start_date: null,
      },
    ],
  },
];

export const handedOverButArchivalNotStartedResponse = [
  {
    id: 1,
    status: 'handed_over',
    pending_status: null,
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
        vessel_id: 1,
        owner_start_date: null,
      },
    ],
  },
];

export const archivalPendingVessel = [
  {
    id: 1,
    status: 'handed_over',
    pending_status: 'archived',
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
        vessel_id: 1,
        owner_start_date: null,
      },
    ],
  },
];

export const archivedVessel = [
  {
    id: 1,
    status: 'archived',
    pending_status: null,
    ownerships: [
      {
        id: 11,
        name: 'Big Boat',
        tech_group: 'techgroup1',
        vessel_id: 1,
        owner_start_date: null,
      },
    ],
  },
];

export const editedVessel = [
  {
    // with created_by only
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    created_by: '<EMAIL>',
    created_at: '2021-05-10T01:12:23.345Z',
    vessel_id: 1,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: 'handed_over',
      date_of_delivery: null,
    },
  },
  {
    // with created_by and updated_by only
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    created_by: '<EMAIL>',
    created_at: '2021-05-10T01:12:23.345Z',
    updated_by: '<EMAIL>',
    updated_at: '2021-05-11T02:23:34.456Z',
    vessel_id: 1,
    vessel: {
      status: 'active',
      pending_status: 'handed_over',
      date_of_delivery: null,
    },
  },
  {
    // with created_by_user_info and updated_by_user_info
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    created_by: '<EMAIL>',
    created_at: '2021-05-10T01:12:23.345Z',
    updated_by: '<EMAIL>',
    updated_at: '2021-05-11T02:23:34.456Z',
    vessel_id: 1,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: 'handed_over',
      date_of_delivery: null,
    },
    created_by_user_info: {
      full_name: 'User A',
    },
    updated_by_user_info: {
      full_name: 'User B',
    },
  },
];

export const vesselList = {
  results: [
    {
      id: 126,
      expected_owner_start_date: '2008-10-10T00:00:00.000Z',
      owner_start_date: '2008-10-10T00:00:00.000Z',
      owner_end_date: null,
      expected_registered_owner_start_date: '2008-10-10T00:00:00.000Z',
      registered_owner_start_date: '2008-10-10T00:00:00.000Z',
      registered_owner_end_date: null,
      vessel_short_code: 'BSM',
      vessel_account_code_new: '2102',
      vessel_tel_fac_code: '1578',
      name: 'Sagami',
      tech_group: 'Tech T1',
      is_archived: false,
      service_status_id: 2,
      vessel_type_id: 1,
      flag_id: 25,
      flag_country: 'Panama',
      port_of_registry_text: 'Panama',
      call_sign: '3ETH',
      supdt_email: null,
      supdt_name: null,
      manager_id: 1,
      flag_isps_id: 2,
      classification_id: 1,
      operator_id: null,
      currency_id: 2,
      classification_society_id: 1,
      vessel_class_regulation: [],
      qi_id: 1,
      salvage_id: 5,
      osro_id: 1,
      media_response_id: 1,
      management_type_id: null,
      h_m_underwriter_id: 39,
      other_contacts_id: 2,
      vessel: {
        id: 2068,
        imo_number: '12345',
        vessel_hull_number: 'S-380',
        shipyard_text: 'Kitanihon Zosen',
        ref_id: 4458,
        status: 'active',
        call_sign: '3ETH',
        life_boat_capacity: '25.0000',
        date_of_delivery: null,
        length_oa: '170.0000',
        length_bp: '162.0000',
        depth: '16.0000',
        breadth_extreme: '26.6000',
        summer_draft: '11316.0000',
        summer_dwt: '33614.5800',
        supdt_name: null,
        supdt_email: null,
        international_grt: '19420.0000',
        international_nrt: '9793.0000',
        service_speed: '14.3000',
        vessel_account_code: 335,
        port_of_registry_text: 'Panama',
        emission_type: null,
        flag: {
          id: 25,
          value: 'Republic of Panama, Panama',
        },
        h_m_underwriter: {
          id: 39,
          value: 'Nipponkoa Insurance Co., Ltd.',
        },
        p_i_club: {
          id: 2,
          value: "Japan Ship Owners' Mutual P&I Association, Hong Kong",
        },
        vessel_product: [],
      },
      owner: {
        id: 1323,
        value: 'Automated-ShipParties-Original-BIZ-6ce0',
        ship_party_id: 1618,
      },
      registered_owner: {
        id: 8,
        value: 'Chemtrans Navigation SA',
        ship_party_id: 394,
      },
      emails: [
        {
          id: 2483,
          email: '<EMAIL>',
          email_type: {
            id: 1,
            value: 'E-Mail Address',
          },
        },
      ],
      phones: [
        {
          id: 4703,
          phone_number: '*********',
          phone_type: {
            id: 6,
            value: 'Sat C',
          },
        },
      ],
      expected_date_of_takeover: '2008-10-10T00:00:00.000Z',
      created_at: '2021-08-11T06:25:47.849Z',
      email: '<EMAIL>',
      phone_number: '*********',
      notation: null,
      date_of_takeover: '2008-10-10T00:00:00.000Z',
      ownership_change_request: null,
      ownership_ship_party_relation: [
        {
          ship_party_id: 1760,
          end_date: null,
          id: 247,
        },
      ],
      class_notations: [],
      vessel_type: {
        id: 1,
        value: 'Bulk Carrier',
        type: 'tanker',
      },
    },
    {
      id: 125,
      expected_owner_start_date: '2008-07-31T00:00:00.000Z',
      owner_start_date: '2008-07-31T00:00:00.000Z',
      owner_end_date: '2009-04-12T00:00:00.000Z',
      expected_registered_owner_start_date: '2008-07-31T00:00:00.000Z',
      registered_owner_start_date: '2008-07-31T00:00:00.000Z',
      registered_owner_end_date: '2009-04-12T00:00:00.000Z',
      vessel_short_code: 'CPF',
      vessel_account_code_new: null,
      vessel_tel_fac_code: '1571',
      name: 'Cala Portofino',
      tech_group: 'SG Tech D2',
      is_archived: true,
      service_status_id: 2,
      vessel_type_id: 5,
      flag_id: 11,
      flag_country: null,
      port_of_registry_text: 'Singapore',
      call_sign: '9V7628',
      supdt_email: null,
      supdt_name: null,
      manager_id: 1,
      flag_isps_id: 16,
      classification_id: 6,
      operator_id: null,
      currency_id: 2,
      classification_society_id: null,
      vessel_class_regulation: [],
      qi_id: null,
      salvage_id: null,
      osro_id: null,
      media_response_id: null,
      management_type_id: null,
      h_m_underwriter_id: null,
      other_contacts_id: 3,
      vessel: {
        id: 2067,
        imo_number: '9139086',
        vessel_hull_number: 'B 170 / 22',
        shipyard_text: null,
        ref_id: 4457,
        status: 'archived',
        call_sign: '9V7628',
        life_boat_capacity: null,
        date_of_delivery: '2002-01-15T00:00:00.000Z',
        length_oa: '184.1000',
        length_bp: '171.9400',
        depth: '13.5000',
        breadth_extreme: '25.3000',
        summer_draft: '9.8890',
        summer_dwt: null,
        supdt_name: null,
        supdt_email: null,
        international_grt: '16661.0000',
        international_nrt: '8641.0000',
        service_speed: null,
        vessel_account_code: 724,
        port_of_registry_text: 'Singapore',
        emission_type: null,
        flag: {
          id: 11,
          value: 'Maritime and Port Authority of Singapore',
        },
        h_m_underwriter: null,
        p_i_club: null,
        vessel_product: [],
      },
      owner: {
        id: 97,
        value: 'SeaChange Maritime LLC',
        ship_party_id: 103,
      },
      registered_owner: {
        id: 105,
        value: 'Plantation Key Pte Ltd',
        ship_party_id: 491,
      },
      emails: [
        {
          id: 2481,
          email: '<EMAIL>',
          email_type: {
            id: 2,
            value: 'Sat C E-Mail Address',
          },
        },
      ],
      phones: [
        {
          id: 4700,
          phone_number:
            'Voice: ********* / 11 / 12  Fax: *********  Data: *********  Tlx: *********  HSD64: *********',
          phone_type: {
            id: 5,
            value: 'Sat B',
          },
        },
      ],
      expected_date_of_takeover: '2008-07-31T00:00:00.000Z',
      created_at: '2021-08-11T06:25:47.847Z',
      email: '<EMAIL>',
      phone_number:
        'Voice: ********* / 11 / 12  Fax: *********  Data: *********  Tlx: *********  HSD64: *********',
      notation: null,
      date_of_takeover: '2008-07-31T00:00:00.000Z',
      ownership_change_request: null,
      ownership_ship_party_relation: [],
      class_notations: [],
      vessel_type: {
        id: 5,
        value: 'Container Vessel',
        type: 'dry',
      },
    },
  ],
};
