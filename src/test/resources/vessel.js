export const activePendingVessel = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    vessel_id: 1,
    vessel: {
      status: 'draft',
      pending_status: 'active',
      phones: [],
      emails: [],
      date_of_delivery: null,
    },
  },
];

export const handedOverPendingVessel = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    vessel_id: 1,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: 'handed_over',
      date_of_delivery: null,
    },
  },
];

export const activeButHandedOverNotStartedResponse = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    vessel_id: 1,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: null,
      date_of_delivery: null,
    },
  },
];

export const activeStatusButInternallyHandedOverVessel = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    owner_end_date: '2021-05-10T01:12:23.345Z',
    registered_owner_end_date: '2021-05-10T01:12:23.345Z',
    vessel_id: 1,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: null,
      date_of_delivery: null,
    },
  },
];

export const withNoOwnerEndDate = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    owner_end_date: null,
    registered_owner_end_date: '2021-05-10T01:12:23.345Z',
    vessel_id: 1,
    is_archived: false,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: null,
      date_of_delivery: null,
    },
  },
];

export const withOwnerEndDate = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    owner_end_date: '2021-05-10T01:12:23.345Z',
    registered_owner_end_date: '2021-05-10T01:12:23.345Z',
    vessel_id: 1,
    is_archived: false,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: null,
      date_of_delivery: null,
    },
  },
];

export const handedOverButArchivalNotStartedResponse = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    vessel_id: 1,
    owner_end_date: '2021-05-10T01:12:23.345Z',
    registered_owner_end_date: '2021-05-10T01:12:23.345Z',
    vessel: {
      id: 1,
      status: 'handed_over',
      pending_status: null,
      date_of_delivery: null,
    },
  },
];

export const archivalPendingVessel = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    vessel_id: 1,
    owner_end_date: '2021-05-10T01:12:23.345Z',
    registered_owner_end_date: '2021-05-10T01:12:23.345Z',
    vessel: {
      id: 1,
      status: 'handed_over',
      pending_status: 'archived',
      date_of_delivery: null,
    },
  },
];

export const archivedVessel = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    vessel_id: 1,
    owner_end_date: '2021-05-10T01:12:23.345Z',
    registered_owner_end_date: '2021-05-10T01:12:23.345Z',
    vessel: {
      id: 1,
      status: 'archived',
      pending_status: null,
      date_of_delivery: null,
    },
  },
];

export const editedVessel = [
  {
    // with created_by only
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    created_by: '<EMAIL>',
    created_at: '2021-05-10T01:12:23.345Z',
    vessel_id: 1,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: 'handed_over',
      date_of_delivery: null,
    },
  },
  {
    // with created_by and updated_by only
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    created_by: '<EMAIL>',
    created_at: '2021-05-10T01:12:23.345Z',
    updated_by: '<EMAIL>',
    updated_at: '2021-05-11T02:23:34.456Z',
    vessel_id: 1,
    vessel: {
      status: 'active',
      pending_status: 'handed_over',
      date_of_delivery: null,
    },
  },
  {
    // with created_by_user_info and updated_by_user_info
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    created_by: '<EMAIL>',
    created_at: '2021-05-10T01:12:23.345Z',
    updated_by: '<EMAIL>',
    updated_at: '2021-05-11T02:23:34.456Z',
    vessel_id: 1,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: 'handed_over',
      date_of_delivery: null,
    },
    created_by_user_info: {
      full_name: 'User A',
    },
    updated_by_user_info: {
      full_name: 'User B',
    },
  },
];

export const officeData = [
  {
    id: 11,
    name: 'Big Boat',
    tech_group: 'techgroup1',
    vessel_id: 1,
    owner_end_date: '2021-05-10T01:12:23.345Z',
    registered_owner_end_date: '2021-05-10T01:12:23.345Z',
    temp_ref_id: 22,
    vessel: {
      id: 1,
      status: 'active',
      pending_status: 'handed_over',
      date_of_delivery: null,
    },
    fleet_staff: {
      operation_director: {
        id: '9fc18422-5606-42d0-babc-971a13757061',
        full_name: 'Ajit Natu',
        email: '<EMAIL>',
      },
      qhse_deputy_general_manager: {
        id: 'dfa6b9a9-05ec-4cc2-afd2-dc2454b833d2',
        full_name: '<EMAIL> Reddy',
        email: '<EMAIL>',
      },
      operation_manager: {
        id: 'ebd0d144-d8c6-419d-9a26-c4cad5f43c7a',
        full_name: 'test tanker ops General Manager',
        email: null,
      },
      superintendent: {
        id: '4ab4b3a5-3e1e-4f35-909f-23c1649370a0',
        full_name: 'Demo SuperIntendant2',
        email: '<EMAIL>',
      },
      tech_group: 'Bunker Tech',
    },
  },
];
