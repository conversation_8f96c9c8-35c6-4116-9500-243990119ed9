{"result": [{"name": "AC_TEMP_1", "path": "report_json.temp_1.ac", "report_type": "performance", "attribute_id": 77, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"name": "BAL_AE", "path": "report_json.bal.ae", "report_type": "quarterly", "attribute_id": 163, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"name": "BAL_BOILER", "path": "report_json.bal.boiler", "report_type": "quarterly", "attribute_id": 164, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"name": "BOILERHFO", "path": "report_json.consumption.boiler.hfo", "report_type": "position", "attribute_id": 141, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"name": "BOILERMGO", "path": "report_json.consumption.boiler.mgo", "report_type": "position", "attribute_id": 140, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"name": "BUNKERMGOLOW", "path": "report_json.bunker.low_mgo", "report_type": "voyage", "attribute_id": 187, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"name": "CARGODISCHARGED", "path": "report_json.cargo.dis", "report_type": "voyage", "attribute_id": 196, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"name": "CATERING", "path": "report_json.expense.overtime.catering", "report_type": "monthly", "attribute_id": 48, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"name": "CHRTR_RADIO", "path": "report_json.expense.radio.chrtr", "report_type": "monthly", "attribute_id": 46, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"name": "COMP_1", "path": "report_json.pres_1.comp", "report_type": "performance", "attribute_id": 89, "vessel_ownership_id": 1799, "min_value": 0, "max_value": 999999, "requirement_of": null}, {"id": 765239, "name": "a2", "label": "Total Quantity Incinerated during the Month", "path": "report_json.a.2", "report_type": "marpol", "control_label": "Total Quantity Incinerated during the Month", "min_value": 0, "max_value": 99, "requirement_of": null, "attribute_id": 3823}, {"id": null, "name": "STEAMING", "label": "Total Steaming Time (Hr)", "path": "report_json.steam", "report_type": "monthly", "control_label": "TOTAL STEAMING TIME (HRS)", "min_value": 0, "max_value": 999999, "requirement_of": null, "attribute_id": 4466}], "total": 10}