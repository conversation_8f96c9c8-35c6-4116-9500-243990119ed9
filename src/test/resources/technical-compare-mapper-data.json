{"result": [{"id": 1379, "name": "TYPE", "label": "Type", "path": "report_type", "order": 1, "is_control": false, "validation_json": {"isEditable": false}, "group_label": "general", "edit_label": null}, {"id": 1380, "name": "SMT", "label": "Report Date Time (SMT)", "path": "smt", "order": 2, "is_control": false, "validation_json": {"type": "datetime", "nullable": false, "isEditable": true, "backendValidationFn": "validateSMTNotExcess24Hr"}, "group_label": "general", "edit_label": "Report Date Time (SMT)"}, {"id": 1381, "name": "GMT", "label": "Report Date Time (GMT)", "path": "gmt", "order": 3, "is_control": false, "validation_json": {"type": "datetime", "subType": "gmt", "nullable": false, "isEditable": true}, "group_label": "general", "edit_label": "Report Date Time (GMT)"}, {"id": 1382, "name": "LATITUDE", "label": "Latitude", "path": "report_json.general.latitude", "order": 4, "is_control": false, "validation_json": {"type": "coordinate", "subType": "latitude", "nullable": false, "isEditable": true}, "group_label": "general", "edit_label": "Latitude"}, {"id": 1383, "name": "LONGITUDE", "label": "Longitude", "path": "report_json.general.longitude", "order": 5, "is_control": false, "validation_json": {"type": "coordinate", "subType": "longitude", "nullable": false, "isEditable": true}, "group_label": "general", "edit_label": "Longitude"}, {"id": 1384, "name": "COUNTRY", "label": "Country", "path": "report_json.general.country", "order": 6, "is_control": false, "validation_json": {"type": "typeahead", "related": ["PORT"], "subType": "countryport", "isParent": true, "nullable": false, "isEditable": true, "report_type": "PORT"}, "group_label": "general", "edit_label": "Country"}, {"id": 1385, "name": "PORT", "label": "Port", "path": "report_json.general.port", "order": 7, "is_control": false, "validation_json": {"type": "typeahead", "related": ["COUNTRY"], "subType": "countryport", "nullable": false, "isEditable": true, "report_type": "PORT"}, "group_label": "general", "edit_label": "Port"}, {"id": 1386, "name": "OTHERETC", "label": "ETD", "path": "report_json.general.etd", "order": 8, "is_control": false, "validation_json": {"type": "datetime", "nullable": false, "isEditable": true, "report_type": "PORT"}, "group_label": "general", "edit_label": "ETD"}, {"id": 1387, "name": "OTHERNEXTCOUNTRY", "label": "Next Country", "path": "report_json.general.next_country", "order": 9, "is_control": false, "validation_json": {"type": "typeahead", "related": ["OTHERNEXTPORT"], "subType": "countryport", "isParent": true, "nullable": false, "isEditable": true, "report_type": "SEA"}, "group_label": "general", "edit_label": "Next Country"}, {"id": 1388, "name": "OTHERNEXTPORT", "label": "Next Port", "path": "report_json.general.next_port", "order": 10, "is_control": false, "validation_json": {"type": "typeahead", "related": ["OTHERNEXTCOUNTRY"], "subType": "countryport", "nullable": false, "isEditable": true, "report_type": "SEA"}, "group_label": "general", "edit_label": "Next Port"}, {"id": 1389, "name": "OTHERETA", "label": "ETA", "path": "report_json.general.eta", "order": 11, "is_control": false, "validation_json": {"type": "datetime", "isEditable": true, "report_type": "SEA"}, "group_label": "general", "edit_label": "ETA"}, {"id": 1390, "name": "GROUNDDIST", "label": "Distance Travelled over ground since last report", "path": "report_json.general.distance_over_ground", "order": 12, "is_control": false, "validation_json": {"max": 700, "min": 0, "type": "number", "nullable": false, "precision": 2, "isEditable": true}, "group_label": "general", "edit_label": "Distance Travelled over ground since last report"}, {"id": 2443, "name": "Main Engines", "label": "Main Engines", "path": null, "order": 1, "is_control": false, "validation_json": {"header": "Main Engines", "isEditable": false}, "group_label": "consumption", "edit_label": null}, {"id": 2444, "name": "MEFUEL3", "label": "Fuel3 Type", "path": "report_json.consumption.main_engine.fuel3", "order": 2, "is_control": false, "validation_json": {"enum": ["N/A", "LNG", "Light Fuel Oil", "Liquefied Petroleum Gas (Propane)", "Liquefied Petroleum Gas (Butane)", "Methanol", "Ethanol", "Other fuel with non-standard emission factor"], "type": "typeahead", "related": ["MEFUEL3CONSUMED"], "isParent": true, "nullable": false, "isEditable": true}, "group_label": "consumption", "edit_label": "Main Engine - Fuel3 Type"}, {"id": 2445, "name": "MEHFO", "label": "HFO Consumption (MT)", "path": "report_json.consumption.main_engine.hfo", "order": 3, "is_control": true, "validation_json": {"max": 250, "min": 0, "type": "number", "nullable": false, "precision": 2, "isEditable": true}, "group_label": "consumption", "edit_label": "Main Engine - HFO Consumption (MT)"}, {"id": 2446, "name": "MEMGO", "label": "MDO/MGO Consumption (MT)", "path": "report_json.consumption.main_engine.mgo", "order": 4, "is_control": true, "validation_json": {"max": 250, "min": 0, "type": "number", "nullable": false, "precision": 2, "isEditable": true}, "group_label": "consumption", "edit_label": "Main Engine - MDO/MGO Consumption (MT)"}, {"id": 2447, "name": "MEFUEL3CONSUMED", "label": "Fuel3 Consumption (MT)", "path": "report_json.consumption.main_engine.fuel_consumed", "order": 5, "is_control": false, "validation_json": {"max": 300, "min": 0, "type": "number", "related": ["MEFUEL3"], "precision": 2, "isEditable": true}, "group_label": "consumption", "edit_label": "Main Engine - Fuel3 Consumption (MT)"}, {"id": 1435, "name": "OTHERMECYL", "label": "MECYL L.O. Consumption (Ltrs)", "path": "report_json.other.mecyl", "order": 1, "is_control": true, "validation_json": {"max": 1500, "min": 0, "type": "number", "precision": 2, "isEditable": true}, "group_label": "other", "edit_label": "Other - MECYL L.O. Consumption (Ltrs)"}, {"id": 1436, "name": "OTHERFWCONSUMPTION", "label": "FW Consumption (MT)", "path": "report_json.other.fw_consumption", "order": 2, "is_control": true, "validation_json": {"max": 500, "min": 0, "type": "number", "precision": 2, "isEditable": true}, "group_label": "other", "edit_label": "Others - FW Consumption (MT)"}, {"id": 1437, "name": "OTHERFWPRODUCTION", "label": "FW Production (MT)", "path": "report_json.other.fw_production", "order": 3, "is_control": true, "validation_json": {"max": 50, "min": 0, "type": "number", "precision": 2, "isEditable": true}, "group_label": "other", "edit_label": "Others - FW Production (MT)"}, {"id": 1438, "name": "OTHERCALLUSPORT", "label": "Is <PERSON><PERSON><PERSON> Calling at a US Port in the Next 6 Days?", "path": "report_json.other.is_call_us_port", "order": 5, "is_control": false, "validation_json": {"type": "radio", "isEditable": true}, "group_label": "other", "edit_label": "Other - Is <PERSON><PERSON><PERSON> Calling at a US Port in the Next 6 Days?"}, {"id": 1439, "name": "OTHERINUSWATER", "label": "Is the Vessel in a US Port or in US Waters?", "path": "report_json.other.is_in_us_water", "order": 6, "is_control": false, "validation_json": {"type": "radio", "isEditable": true}, "group_label": "other", "edit_label": "Others - Is the Vessel in a US Port or in US Waters?"}, {"id": 1440, "name": "OTHERTRANSITINGGULF", "label": "Is Vessel Transiting Gulf of Aden or off Somalian Waters in Next 14 Days?", "path": "report_json.other.is_transiting_gulf", "order": 7, "is_control": false, "validation_json": {"type": "radio", "related": ["OTHERETAGULF", "OTHERETDGULF"], "isParent": true, "isEditable": true}, "group_label": "other", "edit_label": "Others - Is <PERSON><PERSON><PERSON> Transiting Gulf of Aden or off Somalian Waters in Next 14 Days?"}, {"id": 1441, "name": "OTHERETAGULF", "label": "ETA to Gulf of Aden Safety Corridor", "path": "report_json.other.eta_gulf", "order": 8, "is_control": false, "validation_json": {"type": "datetime", "related": ["OTHERTRANSITINGGULF", "OTHERETDGULF"], "isEditable": true}, "group_label": "other", "edit_label": "Others - ETA to Gulf of Aden Safety Corridor"}, {"id": 1442, "name": "OTHERETDGULF", "label": "ETD from Gulf of Aden Safety Corridor", "path": "report_json.other.etd_gulf", "order": 9, "is_control": false, "validation_json": {"type": "datetime", "related": ["OTHERTRANSITINGGULF", "OTHERETAGULF"], "isEditable": true}, "group_label": "other", "edit_label": "Others - ETD from Gulf of Aden Safety Corridor"}, {"id": 1443, "name": "OTHERCARGODISC", "label": "Cargo load/discharge Quantity Last 24 Hrs ", "path": "report_json.other.cargo_disc", "order": 10, "is_control": false, "validation_json": {"max": 450000, "min": 0, "type": "number", "precision": 2, "isEditable": true, "report_type": "PORT"}, "group_label": "other", "edit_label": "Others - Cargo load/discharge Quantity Last 24 Hrs (MT)"}, {"id": 1444, "name": "OTHERTOTALCARGODISC", "label": "Total Cargo load/discharge ", "path": "report_json.other.total_cargo_disc", "order": 11, "is_control": false, "validation_json": {"max": 450000, "min": 0, "type": "number", "precision": 3, "isEditable": true, "report_type": "PORT"}, "group_label": "other", "edit_label": "Others - Total Cargo load/discharge"}, {"id": 1445, "name": "OTHERBALLOADDISC", "label": "Balance to Cargo load/discharge", "path": "report_json.other.bal_load_disc", "order": 12, "is_control": false, "validation_json": {"max": 450000, "min": 0, "type": "number", "precision": 3, "isEditable": true, "report_type": "PORT"}, "group_label": "other", "edit_label": "Others - Balance to Cargo load/discharge"}, {"id": 1446, "name": "OTHERNOTSTOPPAGEDELAYDETAIL", "label": "Details of Stoppages/Delays not on Vessel A/C", "path": "report_json.other.not_stoppage_delay_detail", "order": 13, "is_control": false, "validation_json": {"type": "textarea", "limit": 500, "isEditable": true, "report_type": "PORT"}, "group_label": "other", "edit_label": "Others - Details of Stoppages/Delays not on Vessel A/C"}, {"id": 1447, "name": "OTHERSTOPPAGEDELAY", "label": "Any Stoppages/Delays to Vessel?", "path": "report_json.other.is_delay", "order": 14, "is_control": false, "validation_json": {"type": "radio", "related": ["OTHERSTOPPAGEDELAYDETAIL"], "isParent": true, "isEditable": true}, "group_label": "other", "edit_label": "Others - Any Stoppages/Delays to <PERSON><PERSON><PERSON>?"}, {"id": 1448, "name": "OTHERSTOPPAGEDELAYDETAIL", "label": "Details of Stoppages/Delays on Vessel A/C", "path": "report_json.other.delay_detail", "order": 15, "is_control": false, "validation_json": {"type": "textarea", "limit": 500, "related": ["OTHERSTOPPAGEDELAY"], "isEditable": true}, "group_label": "other", "edit_label": "Others - Details of Stoppages/Delays on Vessel A/C"}, {"id": 1449, "name": "OTHERDRAFTF", "label": "Draft F/ (M)", "path": "report_json.other.draft_f", "order": 16, "is_control": true, "validation_json": {"max": 25, "min": 0, "type": "number", "precision": 2, "isEditable": true, "report_type": "PORT"}, "group_label": "other", "edit_label": "Draft F/ (M)"}, {"id": 1450, "name": "OTHERDRAFTA", "label": "Draft A/ (M)", "path": "report_json.other.draft_a", "order": 17, "is_control": false, "validation_json": {"max": 25, "min": 0, "type": "number", "precision": 2, "isEditable": true, "report_type": "PORT"}, "group_label": "other", "edit_label": "Draft A/ (M)"}, {"id": 1451, "name": "OTHERMERPM", "label": "M/E RPM", "path": "report_json.other.me_rpm", "order": 18, "is_control": true, "validation_json": {"max": 300, "min": 0, "type": "number", "precision": 2, "isEditable": true, "report_type": "SEA"}, "group_label": "other", "edit_label": "Other - M/E RPM"}, {"id": 1452, "name": "OTHERTCRPM1", "label": "T/C RPM No.1", "path": "report_json.other.tc_rpm_1", "order": 19, "is_control": true, "validation_json": {"max": 25000, "min": 0, "type": "number", "precision": 2, "isEditable": true, "report_type": "SEA"}, "group_label": "other", "edit_label": "T/C RPM No.1"}, {"id": 1453, "name": "OTHERTCRPM2", "label": "T/C RPM No.2", "path": "report_json.other.tc_rpm_2", "order": 20, "is_control": true, "validation_json": {"max": 25000, "min": 0, "type": "number", "precision": 2, "isEditable": true, "report_type": "SEA"}, "group_label": "other", "edit_label": "T/C RPM No.2"}, {"id": 1454, "name": "OTHERTCRPM3", "label": "T/C RPM No.3", "path": "report_json.other.tc_rpm_3", "order": 21, "is_control": true, "validation_json": {"max": 25000, "min": 0, "type": "number", "precision": 2, "isEditable": true, "report_type": "SEA"}, "group_label": "other", "edit_label": "T/C RPM No.3"}, {"id": 1455, "name": "OTHERSCAVPRESSURE", "label": "SCAV Pressure (Bar)", "path": "report_json.other.scav_pressure", "order": 22, "is_control": true, "validation_json": {"max": 3, "min": 0, "type": "number", "precision": 2, "isEditable": true, "report_type": "SEA"}, "group_label": "other", "edit_label": "Other - SCAV Pressure (Bar)"}, {"id": 1456, "name": "OTHERBILGEWATER", "label": "Bilge Water ROB (MT)", "path": "report_json.other.bilge_water_rob", "order": 23, "is_control": true, "validation_json": {"max": 100, "min": 0, "type": "number", "nullable": false, "precision": 2, "isEditable": true}, "group_label": "other", "edit_label": "Other - Bilge Water ROB (MT)"}, {"id": 1457, "name": "OTHERSLUDGE", "label": "Sludge ROB (MT)", "path": "report_json.other.sludge_rob", "order": 24, "is_control": true, "validation_json": {"max": 200, "min": 0, "type": "number", "nullable": false, "precision": 2, "isEditable": true}, "group_label": "other", "edit_label": "Other - Sludge ROB (MT)"}, {"id": 1458, "name": "OTHERLASTSLUDGEPERIOD", "label": "Period Since Last Sludge Landing (Days)", "path": "report_json.other.last_sludge_period", "order": 25, "is_control": true, "validation_json": {"max": 730, "min": 0, "type": "number", "precision": 0, "isEditable": true}, "group_label": "other", "edit_label": "Other - Period Since Last Sludge Landing (Days)"}, {"id": 1459, "name": "OTHERISSENTAMVERREPORT", "label": "AMVER Report has been sent?", "path": "report_json.other.is_sent_amber_report", "order": 26, "is_control": false, "validation_json": {"type": "radio", "isEditable": true}, "group_label": "other", "edit_label": "Other - AMVER Report has been sent?"}, {"id": 1460, "name": "OTHERPOWERABSORPTION", "label": "Power Absorption", "path": "report_json.other.power_absorption", "order": 27, "is_control": false, "validation_json": {"max": 3, "min": 0, "type": "number", "nullable": false, "precision": 2, "isEditable": true, "report_type": "SEA"}, "group_label": "other", "edit_label": "Other - Power Absorption"}, {"id": 1461, "name": "OTHERPOWERKWH", "label": "Power (kW)", "path": "report_json.other.power_kwh", "order": 28, "is_control": false, "validation_json": {"max": 1000000, "min": 0, "type": "number", "nullable": false, "precision": 0, "isEditable": true, "report_type": "SEA"}, "group_label": "other", "edit_label": "Other - Power (kW)"}, {"id": 1462, "name": "OTHERREMARK", "label": "Remarks", "path": "report_json.other.remark", "order": 29, "is_control": false, "validation_json": {"type": "textarea", "isEditable": true}, "group_label": "other", "edit_label": "Other - Remarks"}], "total": 84}