import { getMonthDateRange } from '../../util/date';

describe('getMonthDateRange', () => {
  it('should return the correct start and end date for January 2025', () => {
    const [startDate, endDate] = getMonthDateRange(2025, 1);

    expect(startDate).toBe('2025-01-01');
    expect(endDate).toBe('2025-01-31');
  });

  it('should return the correct start and end date for February 2024 (leap year)', () => {
    const [startDate, endDate] = getMonthDateRange(2024, 2);

    expect(startDate).toBe('2024-02-01');
    expect(endDate).toBe('2024-02-29');
  });

  it('should return the correct start and end date for February 2023 (non-leap year)', () => {
    const [startDate, endDate] = getMonthDateRange(2023, 2);

    expect(startDate).toBe('2023-02-01');
    expect(endDate).toBe('2023-02-28');
  });

  it('should return the correct start and end date for December 2025', () => {
    const [startDate, endDate] = getMonthDateRange(2025, 12);

    expect(startDate).toBe('2025-12-01');
    expect(endDate).toBe('2025-12-31');
  });

  it('should handle invalid month input gracefully', () => {
    const [startDate, endDate] = getMonthDateRange(2025, 13);

    expect(startDate).toBe('Invalid date');
    expect(endDate).toBe('Invalid date');
  });

  it('should handle invalid year input gracefully', () => {
    const [startDate, endDate] = getMonthDateRange(null, 1);

    expect(startDate).toBe('Invalid date');
    expect(endDate).toBe('Invalid date');
  });

  it('should handle 0 as month input gracefully', () => {
    const [startDate, endDate] = getMonthDateRange(2025, 0);

    expect(startDate).toBe('Invalid date');
    expect(endDate).toBe('Invalid date');
  });

  it('should handle negative month input gracefully', () => {
    const [startDate, endDate] = getMonthDateRange(2025, -1);

    expect(startDate).toBe('Invalid date');
    expect(endDate).toBe('Invalid date');
  });

  it('should handle string inputs and treat them as invalid', () => {
    const [startDate, endDate] = getMonthDateRange('2025', 'Jan');

    expect(startDate).toBe('Invalid date');
    expect(endDate).toBe('Invalid date');
  });
});
