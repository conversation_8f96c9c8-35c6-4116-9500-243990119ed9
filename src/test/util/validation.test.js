import * as yup from 'yup';
import {
  filterByExcludedKeys,
  getBasicFormErrors,
  TrialFormModel,
  DependencyValidationPairing,
} from '../../util/validation';

describe('filterByExcludedKeys', () => {
  it('should filter out excluded keys from the object', () => {
    const object = { a: 1, b: 2, c: 3 };
    const keysToExclude = ['b'];
    const result = filterByExcludedKeys(object, keysToExclude);
    expect(result).toEqual({ a: 1, c: 3 });
  });

  it('should return the same object if no keys are excluded', () => {
    const object = { a: 1, b: 2, c: 3 };
    const keysToExclude = [];
    const result = filterByExcludedKeys(object, keysToExclude);
    expect(result).toEqual(object);
  });
});

describe('getBasicFormErrors', () => {
  it('should exclude specified fields from errors', () => {
    const errors = {
      maximum_continuous_rating_kw: 'Required',
      primary_email: 'Invalid email',
      other_field: 'Error',
    };
    const result = getBasicFormErrors(errors);
    expect(result).toEqual({ other_field: 'Error' });
  });

  it('should return an empty object if all fields are excluded', () => {
    const errors = {
      maximum_continuous_rating_kw: 'Required',
      primary_email: 'Invalid email',
    };
    const result = getBasicFormErrors(errors);
    expect(result).toEqual({});
  });
});

describe('TrialFormModel', () => {
  describe('maximum_continuous_rating_kw', () => {
    it('should be required if mcr_rpm is provided', async () => {
      const schema = yup.object().shape({
        maximum_continuous_rating_kw: TrialFormModel.maximum_continuous_rating_kw,
        maximum_continuous_rating_rpm: yup.number(),
      });

      await expect(
        schema.validate({
          maximum_continuous_rating_rpm: 1000,
        }),
      ).rejects.toThrow('maximum_continuous_rating_kw is a required field');
    });

    it('should not be required if no related fields are provided', async () => {
      const schema = yup.object().shape({
        maximum_continuous_rating_kw: TrialFormModel.maximum_continuous_rating_kw,
      });

      await expect(schema.validate({})).resolves.toBeDefined();
    });
  });

  describe('shop_trials', () => {
    it('should not be required if no related fields are provided', async () => {
      const schema = yup.object().shape({
        shop_trials: TrialFormModel.shop_trials,
      });

      await expect(schema.validate({})).resolves.toBeDefined();
    });
  });

  describe('sea_trials', () => {
    it('should not be required if no related fields are provided', async () => {
      const schema = yup.object().shape({
        sea_trials: TrialFormModel.sea_trials,
      });

      await expect(schema.validate({})).resolves.toBeDefined();
    });
  });
});

describe('DependencyValidationPairing', () => {
  it('should contain the correct pairs', () => {
    expect(DependencyValidationPairing).toEqual([
      ['maximum_continuous_rating_kw', 'maximum_continuous_rating_rpm'],
      ['maximum_continuous_rating_kw', 'shop_trials'],
      ['maximum_continuous_rating_rpm', 'shop_trials'],
      ['maximum_continuous_rating_kw', 'sea_trials'],
      ['maximum_continuous_rating_rpm', 'sea_trials'],
      ['shop_trials', 'sea_trials'],
      ['id'],
      ['is_engine_consume_lng', 'lng_engine_category_diesel_generator'],
      ['is_engine_consume_lng', 'lng_engine_category_main_engine'],
      ['lng_engine_category_diesel_generator', 'lng_engine_category_main_engine'],
      ['status'],
    ]);
  });
});