import { getShortDate, isOnboardingSuptd, sortOnGivenOrder } from '../../util/view-utils';

jest.mock('../../styleGuide');

describe('view utils', () => {
  it('should return short date', () => {
    expect(getShortDate('2020-02-09T09:21:16.938Z')).toEqual('09 Feb, 2020');
  });

  it('should return sorted json based on another array order', () => {
    const givenOrder = ['mango', 'cherry', 'banana'];
    const json = [
      { id: 1, name: 'cherry' },
      { id: 2, name: 'banana' },
      { id: 3, name: 'mango' },
    ];

    const actualSortedJson = [
      { id: 3, name: 'mango' },
      { id: 1, name: 'cherry' },
      { id: 2, name: 'banana' },
    ];
    const expectedSortedJson = sortOnGivenOrder(json, givenOrder, 'name');
    expect(expectedSortedJson).toEqual(actualSortedJson);
  });
});

describe('isOnboardingSuptd', () => {
  test('should return true when SUPERINTENDENT_ONBOARDING_GROUP is present in roleConfig.groups', () => {
    const roleConfig = {
      groups: ['/Department/Tech Group/SuperintendentOB', 'AnotherGroup']
    };
    expect(isOnboardingSuptd(roleConfig)).toBe(true);
  });

  test('should return falsy when SUPERINTENDENT_ONBOARDING_GROUP is not present in roleConfig.groups', () => {
    const roleConfig = {
      groups: ['SomeOtherGroup', 'AnotherGroup']
    };
    expect(isOnboardingSuptd(roleConfig)).toBe(false);
  });

  test('should return falsy when roleConfig.groups is undefined', () => {
    const roleConfig = {
      groups: undefined
    };
    expect(isOnboardingSuptd(roleConfig)).toBe(undefined);
  });

  test('should return falsy when roleConfig.groups is null', () => {
    const roleConfig = {
      groups: null
    };
    expect(isOnboardingSuptd(roleConfig)).toBe(undefined);
  });

  test('should return falsy when roleConfig is undefined', () => {
    const roleConfig = undefined;
    expect(isOnboardingSuptd(roleConfig)).toBe(undefined);
  });

  test('should return false when roleConfig is null', () => {
    const roleConfig = null;
    expect(isOnboardingSuptd(roleConfig)).toBe(undefined);
  });

  test('should return false when roleConfig.groups is an empty array', () => {
    const roleConfig = {
      groups: []
    };
    expect(isOnboardingSuptd(roleConfig)).toBe(false);
  });
});
