import {
  mapSearchCriteriaToVesselQuery,
  modifyJSONData,
} from '../../constants/itinerary-search-query';

describe('mapSearchCriteriaToVesselQuery', () => {
  it('should generate query for dropdown', () => {
    const searchCriteria = [
      {
        type: { inputType: 'dropdown', queryKey: 'type', queryType: 'LIKE' },
        subtype: { value: 'Bulk Carrier' },
      },
    ];
    const query = mapSearchCriteriaToVesselQuery(searchCriteria);
    expect(query).toBe('type=Bulk%20Carrier');
  });
});

describe('modifyJSONData', () => {
  it('should modify countries data', () => {
    const data = {
      countries: [
        { alpha2_code: 'IN', value: 'India' },
        { alpha2_code: 'US', value: 'United States' },
      ],
    };
    const result = modifyJSONData(data);
    expect(result.countries).toHaveLength(2);
    expect(result.countries[0]).toEqual({ id: 'IN', value: 'India' });
  });

  it('should modify ports data', () => {
    const data = {
      ports: [
        { city_code: 'NYC', name: 'New York' },
        { city_code: 'LAX', name: 'Los Angeles' },
      ],
    };
    const result = modifyJSONData(data);
    expect(result.ports).toHaveLength(2);
    expect(result.ports[0]).toEqual({ id: 'NYC', value: 'New York' });
  });

  it('should return undefined for invalid data', () => {
    const data = {};
    const result = modifyJSONData(data);
    expect(result).toBeUndefined();
  });
});
