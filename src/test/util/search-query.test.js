import * as searchQuery from '../../util/search-query';
import dropDownData from '../resources/drop-down-data.json';
import moment from 'moment';

describe('map search criteria to vessel query string', () => {
  it('map a single criteria to query', () => {
    const criteria = [
      {
        type: {
          type: 'vessel_name',
          name: 'Vessel Name',
          section: 'Basic',
          inputType: 'text',
          queryType: 'like',
          queryKey: 'name',
        },
        subtype: 'abc',
      },
    ];

    const result = searchQuery.mapSearchCriteriaToVesselQuery(criteria);
    expect(result).toEqual('name=abc');
  });

  it('getValueFromCriteriaItem for year type', () => {
    const item = {
      type: {
        inputType: 'year',
      },
      subtype: '2025-03-09',
    };

    const result = searchQuery.getValueFromCriteriaItem(item);

    expect(result).toEqual({
      min: '2025-01-01',
      max: '2025-12-31',
    });
  });

  it('map multiple criterias to query', () => {
    const criteria = [
      {
        type: {
          type: 'vesselTypes',
          name: 'Vessel Type',
          section: 'Basic',
          inputType: 'dropdown',
          queryType: 'match',
          queryKey: 'vessel_type',
        },
        subtype: {
          id: 6,
          value: 'vessel_type 5',
        },
      },
      {
        type: {
          type: 'vessel_name',
          name: 'Vessel Name',
          section: 'Basic',
          inputType: 'text',
          queryType: 'like',
          queryKey: 'name',
        },
        subtype: 'abc',
      },
      {
        type: {
          type: 'shipyards',
          name: 'Shipyard',
          section: 'Basic',
          inputType: 'text',
          queryType: 'like',
          queryKey: 'shipyard_text',
        },
        subtype: 'shipyard 2',
      },
      {
        type: {
          type: 'class_notation',
          name: 'Class Notation',
          section: 'Basic 2',
          inputType: 'text',
          queryType: 'match',
          queryKey: 'notation',
        },
        subtype: '123',
      },
      {
        type: {
          type: 'phoneTypes',
          name: 'Phone',
          section: 'Contact',
          inputType: 'dropdown',
          queryType: 'match',
          queryKey: 'phone_type',
        },
        subtype: {
          id: 4,
          value: 'phone_type 3',
        },
      },
      {
        type: {
          type: 'service_speed',
          name: 'Service Speed',
          section: 'Particulars',
          inputType: 'number_range',
          queryType: 'range',
          queryKey: 'service_speed',
        },
        subtype: {
          min: '122',
          max: '1000',
        },
      },
    ];

    const result = searchQuery.mapSearchCriteriaToVesselQuery(criteria);
    expect(result).toEqual(
      'vessel_type=vessel_type%205&name=abc&shipyard_text=shipyard%202&notation=123&phone_type=phone_type%203&service_speed=122%2C1000',
    );
  });
});

describe('map vessel name query param to search criteria', () => {
  it('map a single query param to criteria', () => {
    const query = 'name=abc';
    const result = searchQuery.mapVesselQueryToSearchCriteria(query);

    const expected = [
      {
        type: {
          type: 'vessel_name',
          name: 'Vessel Name',
          section: 'Basic',
          inputType: 'text',
          queryType: 'like',
          queryKey: 'name',
        },
        subtype: 'abc',
      },
    ];

    expect(result).toEqual(expected);
  });

  it('map service speed param to criteria', () => {
    const query = 'service_speed=122%2C1000';
    const result = searchQuery.mapVesselQueryToSearchCriteria(query, dropDownData.data);
    expect(result).toEqual([
      {
        type: {
          type: 'service_speed',
          name: 'Service Speed',
          section: 'Particulars',
          inputType: 'number_range',
          queryType: 'range',
          queryKey: 'service_speed',
        },
        subtype: {
          min: '122',
          max: '1000',
        },
      },
    ]);
  });

  it('map date of takeover param to criteria', () => {
    const query = 'date_of_takeover=2020-01-11%2C2020-02-15';
    const result = searchQuery.mapVesselQueryToSearchCriteria(query, dropDownData.data);
    expect(result).toEqual([
      {
        type: {
          type: 'date_of_takeover',
          name: 'Date of Takeover',
          section: 'Basic',
          inputType: 'date',
          queryType: 'range',
          queryKey: 'date_of_takeover',
        },
        subtype: {
          startDate: moment('2020-01-11').toDate(),
          endDate: moment('2020-02-15').toDate(),
        },
      },
    ]);
  });

  it('handle invalid dates in putValueToCriteriaItem', () => {
    const item = {
      type: {
        inputType: 'date',
        queryKey: 'date_of_takeover',
      },
      subtype: {},
    };
    const value = { min: 'invalid-date', max: 'invalid-date' };

    searchQuery.putValueToCriteriaItem(item, value);

    expect(item.subtype).toEqual({});
  });

  it('map date of takeover criteria to param', () => {
    const criteria = [
      {
        type: {
          type: 'date_of_takeover',
          name: 'Date of Takeover',
          section: 'Basic',
          inputType: 'date',
          queryType: 'range',
          queryKey: 'date_of_takeover',
        },
        subtype: {
          startDate: moment('2020-03-15').toDate(),
          endDate: moment('2020-05-20').toDate(),
        },
      },
    ];
    const result = searchQuery.mapSearchCriteriaToVesselQuery(criteria);
    expect(result).toEqual('date_of_takeover=2020-03-15%2C2020-05-20');
  });

  it('map multiple query params to criteria', () => {
    const query =
      'vessel_type=vessel_type%205&name=abc&shipyard_text=shipyard%202&notation=123&service_speed=122%2C1000';

    const result = searchQuery.mapVesselQueryToSearchCriteria(query, dropDownData.data);

    // expect result
    const expected = [
      {
        type: {
          type: 'vesselTypes',
          name: 'Vessel Type',
          section: 'Basic',
          inputType: 'dropdown',
          queryType: 'match',
          queryKey: 'vessel_type',
        },
        subtype: {
          id: 6,
          value: 'vessel_type 5',
        },
      },
      {
        type: {
          type: 'vessel_name',
          name: 'Vessel Name',
          section: 'Basic',
          inputType: 'text',
          queryType: 'like',
          queryKey: 'name',
        },
        subtype: 'abc',
      },
      {
        type: {
          type: 'shipyards',
          name: 'Shipyard',
          section: 'Basic',
          inputType: 'text',
          queryType: 'like',
          queryKey: 'shipyard_text',
        },
        subtype: 'shipyard 2',
      },
      {
        type: {
          type: 'class_notation',
          name: 'Class Notation',
          section: 'Basic 2',
          inputType: 'text',
          queryType: 'match',
          queryKey: 'notation',
        },
        subtype: '123',
      },
      {
        type: {
          type: 'service_speed',
          name: 'Service Speed',
          section: 'Particulars',
          inputType: 'number_range',
          queryType: 'range',
          queryKey: 'service_speed',
        },
        subtype: {
          min: '122',
          max: '1000',
        },
      },
    ];

    expect(result).toEqual(expected);
  });

  it('ignore unknown queryKey in mapVesselQueryToSearchCriteria', () => {
    const query = 'unknown_key=123';

    const result = searchQuery.mapVesselQueryToSearchCriteria(query, dropDownData.data);

    expect(result).toEqual([]);
  });
});
