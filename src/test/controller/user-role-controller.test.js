import UserRoleController from '../../controller/user-role-controller';
import ROLES from '../../constants/roles';

describe('UserRoleController', () => {
  let controller;
  beforeEach(() => {
    controller = new UserRoleController();
  });

  describe('role config', () => {
    it('should have all config', async () => {
      const keycloakJson = {
        realmAccess: { roles: [] },
        tokenParsed: { group: [] },
      };
      const config = await controller.getConfig(keycloakJson);
      expect(Object.keys(config)).toEqual(
        expect.arrayContaining([
          'vessel',
          'approvalGroups',
          'finalApprover',
          'techGroups',
          'viewInspections',
          'crewAssignment',
          'admin',
          'drills',
          'params',
        ]),
      );
      expect(Object.keys(config.vessel)).toEqual(
        expect.arrayContaining([
          'create',
          'edit',
          'view',
          'approve',
          'moveToActive',
          'moveToHandover',
          'moveToArchival',
          'viewApproval',
          'viewAssigned',
          'requestHandOver',
          'requestArchival',
        ]),
      );
      expect(Object.keys(config.admin)).toEqual(expect.arrayContaining(['view', 'drills']));
      expect(Object.keys(config.drills)).toEqual(expect.arrayContaining(['view', 'assign']));
      expect(Object.keys(config.params)).toEqual(expect.arrayContaining(['view', 'edit']));
    });

    describe('create vessel config', () => {
      it('should be true, when user has roles vessel|approve and vessel|create,', async () => {
        const keycloakJson = {
          realmAccess: { roles: [ROLES.VESSEL.APPROVE, ROLES.VESSEL.CREATE] },
          tokenParsed: { group: [] },
        };
        const config = await controller.getConfig(keycloakJson);
        expect(config.vessel.create).toEqual(true);
      });

      it('should be false, when user does not have vessel|create, but have vessel|approve', async () => {
        const keycloakJson = {
          realmAccess: { roles: [ROLES.VESSEL.APPROVE] },
          tokenParsed: { group: [] },
        };
        const config = await controller.getConfig(keycloakJson);
        expect(config.vessel.create).toEqual(false);
      });
    });

    describe('view vessel config', () => {
      it('should be true, when user has role vessel|view,', async () => {
        const keycloakJson = {
          realmAccess: { roles: [ROLES.VESSEL.VIEW] },
          tokenParsed: { group: [] },
        };
        const config = await controller.getConfig(keycloakJson);
        expect(config.vessel.view).toEqual(true);
      });
    });

    it('finalApprover config', async () => {
      const keycloakJson = {
        realmAccess: { roles: [ROLES.VESSEL.FINAL_APPROVE] },
        tokenParsed: {
          group: [],
        },
      };

      const config = await controller.getConfig(keycloakJson);
      expect(config.finalApprover).toEqual(true);
    });

    it('user group config', async () => {
      const keycloakJson = {
        realmAccess: { roles: [ROLES.VESSEL.APPROVE] },
        tokenParsed: {
          group: [
            '/Department/Tech Group/VesselManager',
            '/Tech Group/techgroup1',
            '/Department/Business/VesselManager',
            '/Department/Accounts/VesselManager',
            '/Department/Fleet Personnel/VesselManager',
            '/Department/Insurance/VesselManager',
          ],
        },
      };

      const config = await controller.getConfig(keycloakJson);
      expect(config.approvalGroups).toEqual([
        'Business',
        'Fleet Personnel',
        'Accounts',
        'Insurance',
        'Tech Group',
      ]);
    });

    describe('approval tech groups', () => {
      it('should have approval techgroups when user has approval role too', async () => {
        const keycloakJson = {
          realmAccess: { roles: [ROLES.VESSEL.APPROVE, ROLES.VESSEL.TECH_GROUP_MANAGE] },
          tokenParsed: {
            group: [
              '/Department/Tech Group/VesselManager',
              '/Tech Group/techgroup1',
              '/Tech Group/techgroup2',
            ],
          },
        };

        const config = await controller.getConfig(keycloakJson);
        expect(config.techGroups).toEqual({
          manage: true,
          techGroups: ['techgroup1', 'techgroup2'],
        });
      });
    });

    describe('edit config', () => {
      it('should have edit true, when user has vessel|edit role', async () => {
        const keycloakJson = {
          realmAccess: { roles: [ROLES.VESSEL.EDIT] },
          tokenParsed: {
            group: [],
          },
        };

        const config = await controller.getConfig(keycloakJson);
        expect(config.vessel.edit).toEqual(true);
      });
    });

    describe('viewApproval config', () => {
      it('should have viewApproval true, when user has vessel|view-approval role', async () => {
        const keycloakJson = {
          realmAccess: { roles: [ROLES.VESSEL.VIEW_APPROVAL] },
          tokenParsed: {
            group: [],
          },
        };

        const config = await controller.getConfig(keycloakJson);
        expect(config.vessel.viewApproval).toEqual(true);
      });
    });

    describe('crewAssignment config', () => {
      it('should have crewAssignment true, when user has crew-assignmnet|view role', async () => {
        const keycloakJson = {
          realmAccess: { roles: [ROLES.VESSEL.VIEW_CREW_ASSIGNMENT] },
          tokenParsed: {
            group: [],
          },
        };

        const config = await controller.getConfig(keycloakJson);
        expect(config.crewAssignment).toBeTruthy();
      });
    });
  });
});
