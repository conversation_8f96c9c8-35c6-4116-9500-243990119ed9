import React from 'react';
import { mount } from 'enzyme';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter } from 'react-router-dom';
import vesselService from '../../service/vessel-service';
import { vesselList } from '../resources/vessel-with-ownership';
import positionReportsListData from '../resources/positionReportListDefaultColumns.json';
import moment from 'moment';
import TechnicalReportsList from '../../component/TechnicalReports/TechnicalReportsList';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';

describe('<TechnicalReports/>', () => {
  let technicalReports;
  const roleConfig = {
    vessel: {
      editReport: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };
  beforeEach(async () => {
    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: vesselList }));
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: positionReportsListData }));
    technicalReports = mount(
      <MemoryRouter initialEntries={['/vessel/report/technical/position']}>
        <TechnicalReportContextProvider roleConfig={roleConfig}>
          <TechnicalReportsList />
        </TechnicalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(technicalReports);
  });

  describe('should check component', () => {
    it(`should render tab component`, async () => {
      expect(technicalReports.find('TabWrapper').exists()).toEqual(true);
    });
    it('should render filter component', async () => {
      expect(
        technicalReports.find('TechnicalReportFilter').find('CustomDatePicker').exists(),
      ).toEqual(true);
      expect(technicalReports.find('VesselDropDown').exists()).toEqual(true);
    });
  });

  describe('technical report date pickers when page loads', () => {
    it('should render yesterday date to current date when navigate from sidebar', async () => {
      const startDate = technicalReports
        .find('TechnicalReportFilter')
        .find('.startDatePicker')
        .at(1)
        .props().children.props.value;

      const endDate = technicalReports
        .find('TechnicalReportFilter')
        .find('.endDatePicker')
        .at(1)
        .props().children.props.value;

      expect(startDate).toEqual(moment().add(-1, 'days').format('YYYY-MM-DD'));
      expect(endDate).toEqual(moment().format('YYYY-MM-DD'));
    });

    it('should render year start date to current date when navigate from details page', async () => {
      const technicalReportsRoute = mount(
        <MemoryRouter
          initialEntries={['/vessel/report/technical/position?vessel_ownership_id=126']}
        >
          <TechnicalReportContextProvider roleConfig={roleConfig}>
            <TechnicalReportsList />
          </TechnicalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(technicalReportsRoute);

      const startDate = technicalReportsRoute
        .find('TechnicalReportFilter')
        .find('.startDatePicker')
        .find('.react-datepicker__input-container input')
        .props().value;

      const endDate = technicalReportsRoute
        .find('TechnicalReportFilter')
        .find('.endDatePicker')
        .at(1)
        .props().children.props.value;

      expect(moment(startDate).format('YYYY-MM-DD')).toEqual(
        moment().startOf('year').format('YYYY-MM-DD'),
      );

      expect(moment(endDate).format('YYYY-MM-DD')).toEqual(moment().format('YYYY-MM-DD'));
    });
  });
});
