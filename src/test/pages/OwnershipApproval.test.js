import React from 'react';
import { mount } from 'enzyme';
import { MemoryRouter, Route } from 'react-router-dom';
import { updateWrapper } from '../../setupTests';
import OwnershipApproval from '../../pages/OwnershipApproval';
import * as ownershipService from '../../service/ownership-service';
import {
  getRecentOwnershipRequestData,
  ownershipApprovedData,
  ownershipChangedData,
} from '../../service/__mocks__/ownership-service';
import moment from 'moment';

import {
  FIRST_APPROVERS_GROUPS,
  BUSINESS,
  FLEET_PERSONNEL,
  ACCOUNTS,
  INSURANCE,
  FINAL_APPROVER,
} from '../../model/constants';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/ownership-service');
jest.mock('../../service/keycloak-service');
jest.mock('../../service/vessel-service');
jest.mock('../../controller/take-over-controller');

let ownershipApproval;

describe('OwnershipApproval', () => {
  const getRoleConfig = (hasRole, group = FIRST_APPROVERS_GROUPS) => {
    return {
      vessel: {
        view: true,
        create: hasRole,
        edit: hasRole,
        viewApproval: hasRole,
        requestHandOver: hasRole,
        requestArchival: hasRole,
      },
      approvalGroups: [...group],
      departments: [FINAL_APPROVER],
      techGroups: [],
      finalApprover: hasRole,
    };
  };
  beforeAll(() => {
    ownershipService.getRecentOwnershipRequestData = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ status: 200, data: getRecentOwnershipRequestData }),
      );
  });

  beforeEach(async () => {
    ownershipApproval = mount(
      <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
        <Route path={'/vessel/ownership/:vesselId/approval'}>
          <OwnershipApproval roleConfig={getRoleConfig(true)} />
        </Route>
      </MemoryRouter>,
    );

    await updateWrapper(ownershipApproval);
  });

  const getFieldByLabel = async (fieldLabel, wrapper = ownershipApproval) => {
    await updateWrapper(wrapper);
    return wrapper
      ? wrapper
          .find('FormGroup')
          .filterWhere(
            (wrp) =>
              wrp.find('label').length > 0 && wrp.find('label').at(0).text().includes(fieldLabel),
          )
      : undefined;
  };

  it('should render page title', async () => {
    expect(ownershipApproval.text().includes('Change Ownership')).toEqual(true);
  });

  it('should render vessel name', async () => {
    expect(ownershipApproval.text().includes('Ultra Bosque')).toEqual(true);
  });

  it('should render approval sections', async () => {
    expect(
      ownershipApproval
        .text()
        .includes('REVIEW', 'FINAL APPROVAL', 'FINAL CONFIRMATION OF OWNERSHIP CHANGE'),
    ).toEqual(true);
  });

  it('should have approve and reject buttons in Final Approval', () => {
    const allButtons = ownershipApproval.find('#test__finalApproval ButtonGroup button');
    console.log(allButtons.debug());
    expect(allButtons.map((btn) => btn.text())).toEqual(['Approve', 'Reject']);
  });

  describe('access error', () => {
    beforeAll(() => {
      ownershipService.getRecentOwnershipRequestData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: ownershipChangedData }));
    });

    it('should render page for ACCOUNTS', async () => {
      const ownershipApproval = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
          <Route path={'/vessel/ownership/:vesselId/approval'}>
            <OwnershipApproval roleConfig={getRoleConfig(false, [ACCOUNTS])} />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(ownershipApproval);

      expect(ownershipApproval.text().includes('Change Ownership')).toEqual(true);
      expect(ownershipApproval.text().includes('403 Access Denied')).toEqual(false);
    });

    it('should render page for Executive Director', async () => {
      const ownershipApproval = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
          <Route path={'/vessel/ownership/:vesselId/approval'}>
            <OwnershipApproval roleConfig={getRoleConfig(true)} />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(ownershipApproval);
      expect(ownershipApproval.text().includes('Change Ownership')).toEqual(true);
      expect(ownershipApproval.text().includes('403 Access Denied')).toEqual(false);
    });

    it('should disable split related fields when the split is no', async () => {
      const splitAccountingBooks = await getFieldByLabel('Split of Accounting Books');
      splitAccountingBooks
        .find('select')
        .simulate('change', { target: { name: 'split_accounting_books', value: 'no' } });
      await updateWrapper(ownershipApproval);

      const vesselShortCode = await getFieldByLabel('Vessel Short Code (NEW)');
      const vesselAccountCode = await getFieldByLabel('Vessel Account Code');
      const vesselTelFacCode = await getFieldByLabel('Vessel Tel FAC Code (NEW)');
      const vesselName = await getFieldByLabel('Vessel Name (NEW)');

      expect(vesselShortCode.at(0).find('FormControl').prop('disabled')).toBe(true);
      expect(vesselAccountCode.at(0).find('FormControl').prop('disabled')).toBe(true);
      expect(vesselTelFacCode.at(0).find('FormControl').prop('disabled')).toBe(true);
      expect(vesselName.at(0).find('FormControl').prop('disabled')).toBe(true);
    });
  });

  describe('first approvers section', () => {
    beforeAll(() => {
      ownershipService.getRecentOwnershipRequestData = jest
        .fn()
        .mockImplementation(() =>
          Promise.resolve({ status: 200, data: getRecentOwnershipRequestData }),
        );
    });
    it('should render vessel first approvers section', () => {
      verifyApproversInTable([BUSINESS, FLEET_PERSONNEL, ACCOUNTS, INSURANCE, 'Tech D7'], 0);
    });

    it('should have only review button when status is pending', () => {
      const allButtons = getButtonsForPendingStatus(ownershipApproval);
      expect(allButtons.map((btn) => btn.text())).toEqual(['Review']);
    });

    it('should not render name, last_update and remarks when status is pending', () => {
      const pendingRowColumns = firstSectionRow(ownershipApproval)
        .find('.pending')
        .first()
        .find('td');
      expect(pendingRowColumns.map((cols) => cols.text())).toEqual(['', '', 'pending', '']);
    });

    it('should have rework button when status is rejected', () => {
      const allButtons = getButtonForRejectedStatus(ownershipApproval);
      expect(allButtons.map((btn) => btn.text())).toEqual(['Rework']);
    });

    it('should not have remarks link, when remarks are not available', () => {
      const rowWithRejectedStatus = firstSectionRow(ownershipApproval)
        .find('.rejected')
        .filterWhere((row) => row.find('Rework').prop('group') == 'Accounts');
      expect(rowWithRejectedStatus.find('Remarks').exists()).toBe(false);
    });

    it('should have remarks link, when remarks are available', () => {
      const rowWithRejectedStatus = firstSectionRow(ownershipApproval).find('.rejected');
      rowWithRejectedStatus.filterWhere((row) => row.find('Rework').prop('group') == 'Insurance');

      expect(rowWithRejectedStatus.find('Remarks').exists()).toBe(true);
    });

    it('should open remarks model, on click of remark link', async () => {
      const rowsWithRejectedStatus = firstSectionRow(ownershipApproval).find('.rejected');
      const rowWithRejectedStatus = rowsWithRejectedStatus.filterWhere(
        (row) => row.find('Rework').prop('group') == 'Insurance',
      );

      const remarksLink = rowWithRejectedStatus.find('Remarks button');
      expect(remarksLink.text()).toBe('Lorem ipsum dolor sit amet, consectetur...');

      remarksLink.simulate('click');
      await updateWrapper(ownershipApproval);

      const remarksModal = ownershipApproval.find('Remarks Modal.action-modal');
      expect(remarksModal.at(0).prop('show')).toBe(true);
      expect(remarksModal.find('ModalBody').text()).toBe(
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec sapien neque, suscipit.',
      );
    });
  });

  describe('Final Confirmation of Ownership Change Section', () => {
    it('should render disabled, when status of final approver are not approved', async () => {
      const confirmOwnershipChangeButton = ownershipApproval
        .find('Button')
        .filterWhere((btn) => btn.text() == 'Confirm Ownership Change');
      expect(confirmOwnershipChangeButton.prop('disabled')).toBe(true);

      const handOverDateButton = ownershipApproval.find('#hand-over-date-picker');
      expect(handOverDateButton.at(0).prop('disabled')).toBe(true);
    });

    it('should have success message when vessel has been changed ownership', async () => {
      ownershipService.getRecentOwnershipRequestData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: ownershipChangedData }));

      const ownershipApproval = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
          <Route path={'/vessel/ownership/:vesselId/approval'}>
            <OwnershipApproval roleConfig={getRoleConfig(true)} />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(ownershipApproval);
      expect(
        ownershipApproval.text().includes('This vessel has been changed ownership successfully.'),
      ).toEqual(true);
    });

    it('should render hand over date as enabled, when final approver approved', async () => {
      ownershipService.getRecentOwnershipRequestData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: ownershipApprovedData }));
      const ownershipApproval = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
          <Route path={'/vessel/ownership/:vesselId/approval'}>
            <OwnershipApproval roleConfig={getRoleConfig(true)} />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(ownershipApproval);

      const handOverDateButton = ownershipApproval.find('#hand-over-date-picker');
      expect(handOverDateButton.at(0).prop('disabled')).toBe(false);
    });

    it('should show status approved and reviewed on approved', async () => {
      ownershipService.getRecentOwnershipRequestData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: ownershipApprovedData }));
      const ownershipApproval = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
          <Route path={'/vessel/ownership/:vesselId/approval'}>
            <OwnershipApproval roleConfig={getRoleConfig(true)} />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(ownershipApproval);
      const finalApprovedRow = ownershipApproval.find(
        'td[data-testid="fml-ownership-approval-final-approver-status"]',
      );
      expect(finalApprovedRow.text().includes('approved')).toEqual(true);
      const firstApprovedRow = ownershipApproval.find(
        'td[data-testid="fml-ownership-approval-reviewer-status"]',
      );
      expect(firstApprovedRow.map((item) => item.text() === 'Reviewed').length).toEqual(5);
    });

    it('should not render save buton, when final approver approved', async () => {
      ownershipService.getRecentOwnershipRequestData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: ownershipApprovedData }));
      const ownershipApproval = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
          <Route path={'/vessel/ownership/:vesselId/approval'}>
            <OwnershipApproval roleConfig={getRoleConfig(true)} />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(ownershipApproval);
      const saveButton = ownershipApproval.find('BottomButton');
      expect(saveButton.exists()).toEqual(false);
    });

    it('should enable final confirmation for BUSINESS', async () => {
      ownershipService.getRecentOwnershipRequestData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: ownershipApprovedData }));
      const ownershipApproval = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
          <Route path={'/vessel/ownership/:vesselId/approval'}>
            <OwnershipApproval roleConfig={getRoleConfig(true, [BUSINESS])} />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(ownershipApproval);

      const handOverDateButton = ownershipApproval.find('#hand-over-date-picker');
      handOverDateButton.at(0).prop('onChange')(new Date());
      await updateWrapper(ownershipApproval);

      expect(
        ownershipApproval.find('#hand-over-date-picker').find('input').at(0).prop('value'),
      ).toBe(moment(new Date()).format('D MMM YYYY'));

      const confirmOwnershipChangeButton = ownershipApproval
        .find('Button')
        .filterWhere((btn) => btn.text() == 'Confirm Ownership Change');
      expect(confirmOwnershipChangeButton.prop('disabled')).toBe(false);
    });

    it('should not enable final confirmation for Non BUSINESS groups', async () => {
      ownershipService.getRecentOwnershipRequestData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: ownershipApprovedData }));
      const ownershipApproval = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
          <Route path={'/vessel/ownership/:vesselId/approval'}>
            <OwnershipApproval roleConfig={getRoleConfig(true, [ACCOUNTS, INSURANCE])} />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(ownershipApproval);

      const confirmOwnershipChangeButton = ownershipApproval
        .find('Button')
        .filterWhere((btn) => btn.text() == 'Confirm Ownership Change');
      expect(confirmOwnershipChangeButton.prop('disabled')).toBe(true);

      const handOverDateButton = ownershipApproval.find('#hand-over-date-picker');
      expect(handOverDateButton.at(0).prop('disabled')).toBe(true);
    });

    it('should not render save button for Non BUSINESS groups', async () => {
      const ownershipApproval = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/123/approval']}>
          <Route path={'/vessel/ownership/:vesselId/approval'}>
            <OwnershipApproval
              roleConfig={getRoleConfig(true, [FLEET_PERSONNEL, ACCOUNTS, INSURANCE, 'Tech D7'])}
            />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(ownershipApproval);

      const saveButton = ownershipApproval.find('BottomButton');
      expect(saveButton.exists()).toEqual(false);
    });
  });

  function getButtonsForPendingStatus(ownershipApproval) {
    const rowsWithPendingStatus = firstSectionRow(ownershipApproval).find('.pending').first();
    const allButtons = rowsWithPendingStatus.find('ButtonGroup button');
    return allButtons;
  }

  const getButtonForRejectedStatus = (ownershipApproval) => {
    const rowWithRejectedStatus = firstSectionRow(ownershipApproval).find('.rejected').first();
    return rowWithRejectedStatus.find('Rework button');
  };

  const firstSectionRow = (ownershipApproval) => {
    return ownershipApproval.find('table').at(0).find('tr');
  };

  function verifyApproversInTable(approvers, tablePosition) {
    const approvalPageRows = ownershipApproval.find('table').at(tablePosition).find('tr').slice(1);

    const actualApprovers = approvalPageRows
      .find('th')
      .map((header) => header.find('span').at(0).text());

    return expect(actualApprovers).toEqual(expect.arrayContaining(approvers));
  }
});
