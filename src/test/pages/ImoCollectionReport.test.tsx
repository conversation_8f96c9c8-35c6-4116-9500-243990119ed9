import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import EnvironmentalReports from '../../component/EnvironmentalReports/EnvironmentalReportsList';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import ImoCollectionData from '../resources/imo-collection-list-data.json';
import vesselList from '../resources/vesselListWithDefaultColumns.json';
import { MemoryRouter, Route } from 'react-router-dom';

describe('<ImoCollectionReports />', () => {
  let imoCollectionReportList;
  const roleConfig = {
    params: {
      view: true,
    },
    vessel: {
      edit: true,
      send: true,
    },
  };
  beforeEach(async () => {
    vesselService.getImoDataReport = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ImoCollectionData }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    imoCollectionReportList = mount(
      <MemoryRouter
        initialEntries={[
          '/vessel/report/environmental/imo-collection?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
        ]}
      >
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/:tab'}>
            <EnvironmentalReports />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(imoCollectionReportList);
  });

  describe('should render imo collection report', () => {
    it('should render columns, when imo collection report is loaded', async () => {
      const customTable = imoCollectionReportList.find('CustomTable');
      const actualImoCollectionReports = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'Vessel',
        'Vessel Type',
        'Ethanol',
        'Methanol',
        'LNG',
        'LPG(Butane)',
        'LPG(Propane)',
        'HFO',
        'LFO',
        'Diesel/Gas Oil',
        'Hours Underway(Hr)',
        'Distance Travelled(nm)',
        'Auxiliary Engine(s)',
        'Main Propulsion Power',
        'Ice Class',
        'EEDI (gCO2/t.nm)',
        'DWT',
        'NT',
        'Gross Tonnage',
        'Imo Number',
        'Method used to measure fuel oil consumption',
        '(cf;....)',
        'Other(....)',
      ];
      expect(actualImoCollectionReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when imo collection Reports is on fetch', async () => {
      vesselService.getImoDataReport = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      imoCollectionReportList = mount(
        <MemoryRouter
          initialEntries={[
            '/vessel/report/environmental/imo-collection?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
          ]}
        >
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/:tab'}>
              <EnvironmentalReports />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(imoCollectionReportList);
      expect(imoCollectionReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('imo report report tab when page loads', () => {
      it('should imo report stream report tab highlighted when page loads', async () => {
        const environmentalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/environmental/imo-collection']}>
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/:tab'}>
                <EnvironmentalReports />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(environmentalReports);
        const testData = environmentalReports.find('a.active');
        expect(testData.text()).toEqual('IMO Data Collection');
      });
    });
  });
});
