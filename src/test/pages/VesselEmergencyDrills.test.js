import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter, Route } from 'react-router-dom';
import VesselEmergencyDrills from '../../component/emergencyDrills/VesselEmergencyDrills';
import Details from '../../pages/Details';
import { editedVessel } from '../resources/vessel';
import vesselEmergencyDrillListData from '../resources/vessel-emergency-drills.json';
import DetailContextProvider from '../../context/DetailContext';

describe('<VesselEmergencyDrills/>', () => {
  let vesselEmergencyDrillList;
  const getRoleConfig = (hasRole, viewRole = true) => {
    return {
      vessel: {
        view: viewRole,
        create: hasRole,
        edit: hasRole,
        viewApproval: hasRole,
        requestHandOver: hasRole,
        requestArchival: hasRole,
        staff: {
          buyer: hasRole,
          accountant: hasRole,
          supdt: hasRole,
          qhse: hasRole,
          operation: hasRole,
          payroll: hasRole,
        },
      },
      techGroups:{
        manage: true,
      },
      drills: {
        view: true,
        assign: true,
      },
      approvalGroups: [],
      ownerReporting: {
        view: false,
        manage: false,
      },
    };
  };

  beforeEach(async () => {
    vesselService.getAssignedEmergencyDrillList = jest
      .fn()
      .mockResolvedValue({ data: vesselEmergencyDrillListData });
    vesselEmergencyDrillList = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <VesselEmergencyDrills setDrillExcelData={jest.fn()} />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(vesselEmergencyDrillList);
  });

  describe('should render copy drill button', () => {
    it('should show copy drill from vessel if the drill list is empty', async () => {
      vesselService.getAssignedEmergencyDrillList = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: [] }));
      vesselService.getAllVessels = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: { results: [] } }));
      const emptyDrillListPage = mount(
        <MemoryRouter>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <VesselEmergencyDrills />
          </DetailContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(emptyDrillListPage);
      expect(
        emptyDrillListPage
          .find('button[data-testid="fml-vesselEmergencyDrills-copyVessel"]')
          .exists(),
      ).toEqual(true);
    });
  });

  describe('should render drill list page', () => {
    it('should render columns, when drill list loads', async () => {
      const customTable = vesselEmergencyDrillList.find('CustomTable');
      const customTableColumnList = customTable.find('.th').map((i) => i.text());
      expect(customTableColumnList).toEqual([
        '',
        'No.',
        'Assigned Drills',
        'Due Date',
        'Description',
        'Last Done Date',
        'Period',
        'Remarks',
      ]);
    });

    it('should render navigation buttons, when drill list page loads', async () => {
      const drillButton = vesselEmergencyDrillList.find('button').map((i) => i.text());
      expect(drillButton).toEqual(['Assign Drills', 'Drill History']);
    });

    describe('emergency drills tab when page loads', () => {
      let vesselDetails;
      it('should emergency drills tab highlighted when page loads', async () => {
        vesselService.getOwnershipVessel = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: editedVessel[2] }));
        vesselService.getEmergencyDrillList = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: [] }));
        vesselService.getAssignedEmergencyDrillList = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: [] }));
        vesselDetails = mount(
          <MemoryRouter initialEntries={['/vessel/ownership/details/2/emergency-drills']}>
            <Route path={'/vessel/ownership/details/:ownershipId/:step?'}>
              <DetailContextProvider roleConfig={getRoleConfig(true)}>
                <Details />
              </DetailContextProvider>
            </Route>
          </MemoryRouter>,
        );
        await updateWrapper(vesselDetails);
        const testData = vesselDetails.find('a.active');
        expect(testData.text()).toEqual('Emergency Drills');
      });

      it('should render Assign drill page on Button click', async () => {
        vesselDetails
          .find('button[data-testid="fml-vesselEmergencyDrills-assignDrills"]')
          .simulate('click');
        await updateWrapper(vesselDetails);
        const customTable = vesselDetails.find('CustomTable');
        const customTableColumnList = customTable.find('.th').map((i) => i.text());
        expect(customTableColumnList).toEqual([
          '',
          'Drills',
          'Applicable To',
          'Description',
          'Drill Period',
        ]);
      });

      it('should not have emergency drills tab with no view role', async () => {
        const roleConfig = getRoleConfig(true);
        roleConfig.drills.view = false;
        vesselService.getOwnershipVessel = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: editedVessel[2] }));
        vesselService.getEmergencyDrillList = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: [] }));
        vesselService.getAssignedEmergencyDrillList = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: [] }));
        vesselDetails = mount(
          <MemoryRouter initialEntries={['/vessel/ownership/details/2/emergency-drills']}>
            <Route path={'/vessel/ownership/details/:ownershipId/:step?'}>
              <DetailContextProvider roleConfig={roleConfig}>
                <Details />
              </DetailContextProvider>
            </Route>
          </MemoryRouter>,
        );
        await updateWrapper(vesselDetails);
        expect(
          vesselDetails
            .find('[data-testid="fml-vessel-details-emergency-drills-tab-link"]')
            .exists(),
        ).toEqual(false);
      });

      it('should not have assign drills button with no assign role', async () => {
        const roleConfig = getRoleConfig(true);
        roleConfig.drills.assign = false;
        vesselService.getOwnershipVessel = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: editedVessel[2] }));
        vesselService.getEmergencyDrillList = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: [] }));
        vesselService.getAssignedEmergencyDrillList = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: [] }));
        vesselDetails = mount(
          <MemoryRouter initialEntries={['/vessel/ownership/details/2/emergency-drills']}>
            <Route path={'/vessel/ownership/details/:ownershipId/:step?'}>
              <DetailContextProvider roleConfig={roleConfig}>
                <Details />
              </DetailContextProvider>
            </Route>
          </MemoryRouter>,
        );
        await updateWrapper(vesselDetails);
        expect(
          vesselDetails
            .find('[data-testid="fml-vesselEmergencyDrills-assignDrills"]')
            .at(0)
            .prop('hidden'),
        ).toEqual(true);
      });
    });
  });
});
