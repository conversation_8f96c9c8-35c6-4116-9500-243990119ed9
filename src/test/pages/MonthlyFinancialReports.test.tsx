import React from 'react';
import { mount } from 'enzyme';
import ownerService from '../../service/owner-service';
import { MemoryRouter, Route } from 'react-router/cjs/react-router';
import MonthlyFinancialReports from '../../pages/MonthlyFinancialReports';
import DetailContextProvider from '../../context/DetailContext';
import moment from 'moment';


jest.mock('../../component/MonthlyFinancialReportTable/MonthlyFinancialReportTable', () => 'MonthlyFinancialReportTable');
jest.mock('../../component/ReopenReportModal', () => 'ReopenReportModal');
jest.mock('../../component/GenerateReportModal.tsx', () => 'GenerateReportModal');


describe('MonthlyFinancialReports', () => {
  let wrapper;
  const reporsList = [
    {
      id: 111,
      frozenOn: null,
      version: 0,
      name: 'September 2024',
      vesselId: 123,
      vesselCode: 1223,
      vesselName: 'ABC123',
      period: moment().format('MMM-YY'),
      registeredOwnerName: 'xyz',
      ownerName: 'xyz',
      status: 'ON_GOING',
      startDate: '2024-09-01',
      endDate: '2024-09-30',
      visibility: 0,
      vesselStatus: 'handed_over',
      handoverDate: '2024-06-01T00:00:00.000Z',
      canDeleteManually: true,
      canCreateNextReport: true,
      accountSplit: null,
      comments: {
        resolved: 0,
        total: 0,
      },
    },
    {
      id: 112,
      frozenOn: null,
      version: 0,
      name: 'August 2024',
      vesselId: 123,
      vesselCode: 1223,
      vesselName: 'ABC123',
      period: moment().subtract(1, 'months').format('MMM-YY'),
      registeredOwnerName: 'xyz',
      ownerName: 'xyz',
      status: 'SUBMITTED',
      startDate: '2024-09-01',
      endDate: '2024-09-30',
      visibility: 0,
      vesselStatus: 'handed_over',
      handoverDate: '2024-06-01T00:00:00.000Z',
      canDeleteManually: true,
      canCreateNextReport: true,
      accountSplit: null,
      comments: {
        resolved: 0,
        total: 0,
      },
    },
    {
      modifiedOn: '2023-06-08T12:31:52.950Z',
      id: 2664,
      name: 'January 2023',
      vesselCode: 1223,
      vesselName: 'test',
      status: 'ON_GOING',
      accountantName: 'test user',
      comments: {
        resolved: 0,
        total: 0,
      },
    },
    {
      modifiedOn: '2023-06-08T12:31:52.950Z',
      id: 2664,
      name: 'January 2023',
      vesselCode: 1223,
      vesselName: 'test',
      status: 'ON_GOING',
      accountantName: 'test user',
      comments: {
        resolved: 0,
        total: 0,
      },
    },
  ];
  const bifercationData = [
    {
      reportId: 1,
      commentsBifurcation: {
        vesselAccountant: {
          resolved: 1,
          total: 6,
        },
        superintendent: {
          resolved: 1,
          total: 2,
        },
        owner: {
          resolved: 0,
          total: 0,
        },
        others: {
          resolved: 0,
          total: 0,
        },
      },
    },
  ];
  const props = {
    vesselOwnership: {
      vessel_account_code_new: 'ABC123',
      fleet_staff: { primary_accountant: '<EMAIL>', secondary_accountant: '<EMAIL>' },
      name: 'test',
      owner: { ship_party_id: '123' },
      owner_end_date: '2023-06-08T12:31:52.950Z',
    },
    vesselId: '123',
  };
  const contextValues = {
    handleError: () => {},
    roleConfig: { ownerReporting: { hasAllVesselAccess: true } },
    userEmail: '<EMAIL>',
    isOwner: true,
  };

  beforeEach(async () => {
    ownerService.getReports = jest.fn().mockImplementation(() => Promise.resolve(reporsList));
    ownerService.getBifercationData = jest
      .fn()
      .mockImplementation(() => Promise.resolve(bifercationData));

    wrapper = mount(
      <MemoryRouter initialEntries={['vessel/ownership/details/1833/monthlyFinancialReports']}>
        <Route path="vessel/ownership/details/123/monthlyFinancialReports" />
        <DetailContextProvider value={contextValues}>
          <MonthlyFinancialReports {...props} />
        </DetailContextProvider>
      </MemoryRouter>,
    );
  });

  it('renders the component', async () => {
    // Assert that the filter header is rendered
    expect(wrapper.find('.filter-header').text()).toBe('Filter Financial Reports');

    // Assert that the year filter is rendered
    const yearSelect = wrapper.find('[dataTestId="fml-monthly-financial-report-filter-year"]');
    expect(yearSelect).toHaveLength(1);

    // Assert that the table component is rendered
    expect(wrapper.find('MonthlyFinancialReportTable')).toHaveLength(1);
  });
  it('handles reopen report correctly', async () => {
    const reopenReportsMock = jest.spyOn(ownerService, 'reopenReports').mockResolvedValue({});
    const getReportsMock = jest.spyOn(ownerService, 'getReports').mockResolvedValue([]);
    wrapper.find('ReopenReportModal').prop('onClose')();
    wrapper.find('ReopenReportModal').prop('onConfirm')();

    expect(reopenReportsMock).toHaveBeenCalled();
    expect(getReportsMock).toHaveBeenCalled();
  });
  it('handles report submission correctly', async () => {
    const generateReportMock = jest
      .spyOn(ownerService, 'generateFinancialReportForNextMonth')
      .mockResolvedValue({});
    const getReportsMock = jest.spyOn(ownerService, 'getReports').mockResolvedValue([]);
        // Simulate the report submission pop up cancellation
        wrapper.find('GenerateReportModal').at(0).prop('onClose')();

    // Simulate the report submission
    wrapper.find('GenerateReportModal').at(0).prop('onConfirm')();

    expect(generateReportMock).toHaveBeenCalled();
    expect(getReportsMock).toHaveBeenCalled();
  });
  it('handles report deletion correctly', async () => {
    const deleteReportMock = jest
      .spyOn(ownerService, 'deleteGeneratedReport')
      .mockResolvedValue({});
    const getReportsMock = jest.spyOn(ownerService, 'getReports').mockResolvedValue([]);

    // Simulate setting the selected report ID and opening the delete modal
    wrapper.find('MonthlyFinancialReportTable').prop('handleDeleteModal')('report-id-123');
    wrapper.update();
    // Simulate the report deletion pop up cancellation
    wrapper.find('GenerateReportModal').at(1).prop('onClose')();
    // Trigger the deletion via the modal's onConfirm prop
    wrapper.find('GenerateReportModal').at(1).prop('onConfirm')();

    expect(deleteReportMock).toHaveBeenCalledWith('report-id-123', '123','123');
    expect(getReportsMock).toHaveBeenCalled();
  });
});
