import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import VoyageReports from '../resources/voyage-report.list.json';
import { MemoryRouter, Route } from 'react-router-dom';
import ControlParametersData from '../resources/control-parameters-data.json';
import TechnicalReportsCompare from '../../pages/TechnicalReportsCompare';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';
import technicalMapperData from '../resources/technical-compare-mapper-data.json';
import VesselContextProvider from '../../context/VesselContext';
import vesselList from '../resources/vesselListWithDefaultColumns.json';

describe('<ReportsCompare />', () => {
  let voyageReportCompare;
  const roleConfig = {
    vessel: {
      view: true,
      create: true,
      edit: true,
      editReport: true,
      send: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };
  beforeEach(async () => {
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: VoyageReports }));
    vesselService.getReportDataMapper = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: technicalMapperData }));

    vesselService.getContolParameters = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ControlParametersData }));

    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: vesselList.data } }));

    voyageReportCompare = mount(
      <MemoryRouter initialEntries={['/vessel/report/technical/voyage/684/compare?gmt=2023-02-24']}>
        <Route path={'/vessel/report/technical/:report/:ownershipId/compare'}>
          <VesselContextProvider roleConfig={roleConfig}>
            <TechnicalReportContextProvider>
              <TechnicalReportsCompare />
            </TechnicalReportContextProvider>
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(voyageReportCompare);
  });
  const openSendEmailModal = async () => {
    voyageReportCompare
      .find('button[data-testid="fml-compare-report-Send-button"]')
      .at(0)
      .simulate('click');
    await updateWrapper(voyageReportCompare);
  };
  it('should render columns, when compare loads', async () => {
    const voyageReportHeaders = voyageReportCompare.find('.vessel-table .th .compare-page-header').map((i) => i.text());
    const expected = [
      'GENERAL INFORMATION',
      'STRATUMFIVE FEED',
      'CONSUMPTION SINCE LAST ARRIVAL/DEPARTURE REPORT (MT)',
      'CONSUMPTION SINCE LAST ARRIVAL/DEPARTURE REPORT (MT) (RESCUE OPERATIONS)',
      'CONSUMPTION SINCE LAST ARRIVAL/DEPARTURE REPORT (MT) (ICE CONDITIONS)',
      'CONSUMPTION SINCE LAST REPORT FOR CARGO DISCHARGING USING ELECTRICAL/ELECTRO-HYDRAULIC CARGO PUMPS',
      'CONSUMPTION SINCE LAST REPORT FOR REFRIGERATED CONTAINERS',
      'CONSUMPTION SINCE LAST REPORT FOR CARGO COOLING/RELIQUEFICATION',
      'FUEL SULPHUR CONTENT',
      'REMAINING ON BOARD',
      'BUNKERING (MT)',
      'CARGO',
      'PASSENGERS (PASSENGER/RO-PAX SHIPS ONLY)',
      'EMISSIONS (MT)',
      'OTHERS',
    ];
    expect(voyageReportHeaders).toEqual(expected);
  });

  it('should render spinner, when compare is on fetch', async () => {
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 100000)));
    voyageReportCompare = mount(
      <MemoryRouter initialEntries={['/vessel/report/technical/voyage/684/compare?gmt=2023-02-24']}>
        <Route path={'/vessel/report/technical/:report/:ownershipId/compare'}>
          <VesselContextProvider roleConfig={roleConfig}>
            <TechnicalReportContextProvider>
              <TechnicalReportsCompare />
            </TechnicalReportContextProvider>
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(voyageReportCompare);
    expect(voyageReportCompare.find('Spinner').exists()).toEqual(true);
  });
  it('should have send button', async () => {
    expect(
      voyageReportCompare.find('button[data-testid="fml-compare-report-Send-button"]').exists(),
    ).toEqual(true);
  });

  it('should open send email modal on send button click', async () => {
    await openSendEmailModal();
    expect(voyageReportCompare.find('div.send-email-modal').exists()).toEqual(true);
  });

  it('should throw error on empty fields', async () => {
    await openSendEmailModal();
    voyageReportCompare
      .find('input[data-testid="fml-sendEmail-subject"]')
      .simulate('change', { target: { value: '' } });
    voyageReportCompare
      .find('textarea[data-testid="fml-sendEmail-recipient"]')
      .simulate('change', { target: { value: '' } });
    voyageReportCompare
      .find('textarea[data-testid="fml-sendEmail-message"]')
      .simulate('change', { target: { value: '' } });
    await updateWrapper(voyageReportCompare);
    voyageReportCompare.find('button[data-testid="fml-sendEmail-send"]').simulate('click');
    await updateWrapper(voyageReportCompare);
    expect(voyageReportCompare.find('div.invalid-feedback').length === 3).toEqual(true);
  });
});
