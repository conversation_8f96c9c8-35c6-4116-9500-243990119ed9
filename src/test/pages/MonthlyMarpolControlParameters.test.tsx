import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter } from 'react-router-dom';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import ControlParametersData from '../resources/control-parameters-data.json';
import { vesselList } from '../resources/vessel-with-ownership';
import MonthlyMarpolControlParameters from '../../component/EnvironmentalReports/ControlParameters/MonthlyMarpolControlParameters';

describe('<MonthlyMarpolControlParameters />', () => {
  let marpolControlParameters;
  beforeEach(async () => {
    vesselService.getContolParameters = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ControlParametersData }));
    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: vesselList }));
    marpolControlParameters = mount(
      <MemoryRouter>
        <EnvironmentalReportContextProvider>
          <MonthlyMarpolControlParameters />
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(marpolControlParameters);
  });

  describe('should render spinner on Clicking vessel option', () => {
    it('should render spinner', async () => {
      vesselService.getContolParameters = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      const marpolControlParameterList = mount(
        <MemoryRouter>
          <EnvironmentalReportContextProvider>
            <MonthlyMarpolControlParameters />
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      marpolControlParameterList
        .find('input[data-testid="fml-marpol-control-parameter-vessel"]')
        .simulate('click');
      await updateWrapper(marpolControlParameterList);
      marpolControlParameterList.find('a').at(1).simulate('click');
      await updateWrapper(marpolControlParameterList);
      expect(marpolControlParameterList.find('Spinner').exists()).toEqual(true);
    });
  });

  describe('should render Monthly Marpol Control Parameters page', () => {
    it('should render vessel Dropdown', async () => {
      expect(
        marpolControlParameters
          .find('[data-testid="fml-marpol-control-parameter-vessel"]')
          .exists(),
      ).toEqual(true);
    });

    it('should render marpol control parameters page with tab', async () => {
      marpolControlParameters
        .find('input[data-testid="fml-marpol-control-parameter-vessel"]')
        .simulate('click');
      await updateWrapper(marpolControlParameters);
      marpolControlParameters.find('a').at(1).simulate('click');
      await updateWrapper(marpolControlParameters);
      expect(marpolControlParameters.find('a.active').text()).toEqual('Monthly Marpol Report');
    });

    it('should render marpol control Parameter list table', async () => {
      marpolControlParameters
        .find('input[data-testid="fml-marpol-control-parameter-vessel"]')
        .simulate('click');
      await updateWrapper(marpolControlParameters);
      marpolControlParameters.find('a').at(1).simulate('click');
      await updateWrapper(marpolControlParameters);
      const parameterTable = marpolControlParameters.find('CustomTable');
      const tableHeaders = parameterTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Parameter',
        'Minimum Value',
        'Maximum Value',
        'Requirement of',
      ];
      expect(tableHeaders).toEqual(expectedHeaders);
    });

    it('should render Edit/ Save button on marpol control Parameter Page ', async () => {
      marpolControlParameters
        .find('input[data-testid="fml-marpol-control-parameter-vessel"]')
        .simulate('click');
      await updateWrapper(marpolControlParameters);
      marpolControlParameters.find('a').at(1).simulate('click');
      await updateWrapper(marpolControlParameters);
      expect(
        marpolControlParameters
          .find('[data-testid="fml-marpol-control-parameter-edit-btn"]')
          .exists(),
      ).toEqual(true);
      marpolControlParameters
        .find('[data-testid="fml-marpol-control-parameter-edit-btn"]')
        .at(0)
        .simulate('click');
      await updateWrapper(marpolControlParameters);
      expect(
        marpolControlParameters
          .find('[data-testid="fml-marpol-controlParameters-save-button"]')
          .exists(),
      ).toEqual(true);
    });
  });
});
