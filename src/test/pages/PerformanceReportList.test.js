import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import PerformanceReportsListData from '../resources/performance-report-list.json';
import { MemoryRouter, Route } from 'react-router-dom';
import PerformanceReports from '../../component/TechnicalReports/ListReports/PerformanceReports';
import TechnicalReportsList from '../../component/TechnicalReports/TechnicalReportsList';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';

describe('<PerformanceReports />', () => {
  let performanceReportList;
  const roleConfig = {
    vessel: {
      editReport: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };

  beforeEach(async () => {
    vesselService.getAllVessels = jest.fn().mockImplementation(() => Promise.resolve({ data: [] }));
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: PerformanceReportsListData }));

    performanceReportList = mount(
      <MemoryRouter>
        <TechnicalReportContextProvider roleConfig={roleConfig}>
          <PerformanceReports />
        </TechnicalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(performanceReportList);
  });

  describe('should render performance report list page', () => {
    it('should render columns, when me report list loads', async () => {
      const meTable = performanceReportList.find('TechnicalReportTable');
      const actualperformanceReports = meTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Vessel',
        'View Report',
        'Report Submit Date',
        'Next ETA',
        'Geo Position',
        'Speed',
        'Remarks',
      ];
      expect(actualperformanceReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when performance Reports list is on fetch', async () => {
      vesselService.getTechnicalReports = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      performanceReportList = mount(
        <MemoryRouter>
          <TechnicalReportContextProvider roleConfig={roleConfig}>
            <PerformanceReports
              filterData={{ startDate: '2022-03-02', endDate: '2022-03-06', vessel: [] }}
              loading={true}
            />
          </TechnicalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(performanceReportList);
      expect(performanceReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('performance report tab when page loads', () => {
      it('should me report tab highlighted when page loads', async () => {
        const technicalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/technical/performance']}>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/technical/:tab'}>
                <TechnicalReportsList roleConfig={roleConfig} />
              </Route>
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(technicalReports);
        const testData = technicalReports.find('a.active');
        expect(testData.text()).toEqual('ME Performance');
      });
    });
    describe('should render data on date pickers when page loads', () => {
      it('should render result count based on selected date', async () => {
        vesselService.getTechnicalReports = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: PerformanceReportsListData }));

        performanceReportList = mount(
          <MemoryRouter initialEntries={['/vessel/report/technical/performance']}>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <PerformanceReports filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(performanceReportList);

        const totalCount = performanceReportList.find('TechnicalReportTable').props().totalCount;
        expect(totalCount).toEqual(15);
      });
    });

    describe('performance reports Table pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const selectPage = async (page, page_num) => {
        page
          .find('div.page-num')
          .find('.page-num-enabled')
          .filterWhere((e) => Number(e.text()) === Number(page_num))
          .at(0)
          .simulate('click');

        await updateWrapper(page);
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        const page = mount(
          <MemoryRouter>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <PerformanceReports filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(page);
        await selectPage(page, 2);

        expect(findSelectedPageNumber(page)).toEqual(2);
        expect(findPageSizeValue(page)).toEqual(10);
      });
    });
  });
});
