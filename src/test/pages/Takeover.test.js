import React from 'react';
import { mount } from 'enzyme';
import Takeover from '../../pages/Takeover';
import { MemoryRouter } from 'react-router-dom';
import { updateWrapper } from '../../setupTests';
import { mockOnLoadPage } from '../../controller/take-over-controller';

jest.mock('../../controller/take-over-controller');

describe('<Takeover />', () => {
  const getRoleConfig = (hasRole) => {
    return {
      vessel: {
        create: hasRole,
        edit: hasRole,
      },
    };
  };

  describe('role based view', () => {
    it('should render error when user do not have role to create vessel', async () => {
      const takeover = mount(
        <MemoryRouter>
          <Takeover username="test.user" roleConfig={getRoleConfig(false)} />
        </MemoryRouter>,
      );

      await updateWrapper(takeover);
      expect(takeover.text().includes('403 Access Denied')).toEqual(true);
      expect(mockOnLoadPage).toHaveBeenCalledTimes(0);
    });

    it('should render create html when user have role true to create a vessel', async () => {
      const takeover = mount(
        <MemoryRouter>
          <Takeover username="test.user" roleConfig={getRoleConfig(true)} />
        </MemoryRouter>,
      );

      await updateWrapper(takeover);
      const breadcrumbtext = takeover
        .find('li')
        .filterWhere((li) => li.prop('className') == 'breadcrumb-item breadcrumb-text active');
      expect(breadcrumbtext.text()).toBe('Create a Vessel');
      expect(mockOnLoadPage).toHaveBeenCalledTimes(1);
    });
  });
});

describe.skip('patch vessel', () => {
  it.skip('should patch handover date in ownership owner_end_date and registered_owner_end_date', async () => {});
  it.skip('should patch date_of_takeover in ownership owner_start_date and registered_owner_start_date', async () => {});
  it.skip('should patch expected_date_of_takeover date in ownership expected_owner_start_date and expected_registered_owner_start_date', async () => {});
});
