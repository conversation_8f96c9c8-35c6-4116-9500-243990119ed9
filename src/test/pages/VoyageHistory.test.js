import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import VoyageHistoryData from '../resources/voyage-history.json';
import { MemoryRouter, Route } from 'react-router-dom';
import TechnicalReportsList from '../../component/TechnicalReports/TechnicalReportsList';
import VoyageHistory from '../../component/TechnicalReports/ListReports/VoyageHistory';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';

describe('<VoyageHistory />', () => {
  let voyageHistoryList;
  const roleConfig = {
    vessel: {
      editReport: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };

  beforeEach(async () => {
    vesselService.getAllVessels = jest.fn().mockImplementation(() => Promise.resolve({ data: [] }));
    vesselService.getVoyageHistory = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: VoyageHistoryData }));
    voyageHistoryList = mount(
      <MemoryRouter initialEntries={['/vessel/report/technical/voyage-history']}>
        <TechnicalReportContextProvider roleConfig={roleConfig}>
          <VoyageHistory />
        </TechnicalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(voyageHistoryList);
  });

  describe('should render voyage history list page', () => {
    it('should render columns, when voyage history list loads', async () => {
      const voyageTable = voyageHistoryList.find('CustomTable');
      const actualVoyageReports = voyageTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'Date Time (SMT)',
        'Vessel',
        'Type',
        'Port',
        'Country',
        'Avg Speed (Knots)',
        'Cargo Loaded & Discharged',
        'Cargo Loaded Qty (MT)',
        'Cargo Discharged Qty (MT)',
        'Reason for port call',
      ];
      expect(actualVoyageReports).toEqual(expectedHeaders);
    });

    describe('voyage history tab for filter/movement', () => {
      it('should render movement button', async () => {
        expect(
          voyageHistoryList.find('button[data-testid="fml-voyage-history-movement"]').exists(),
        ).toEqual(true);
      });
      it('should render filter button', async () => {
        expect(
          voyageHistoryList.find('button[data-testid="fml-voyage-history-add-filter"]').exists(),
        ).toEqual(true);
      });
      it('should render intial filter fields', async () => {
        expect(
          voyageHistoryList.find('input[data-testid="fml-voyage-history-vessel"]').exists(),
        ).toEqual(true);
        expect(
          voyageHistoryList.find('input[data-testid="fml-voyage-history-year"]').exists(),
        ).toEqual(true);
        expect(
          voyageHistoryList.find('select[data-testid="fml-voyage-history-month"]').exists(),
        ).toEqual(true);
      });
    });

    it('should render spinner, when voyage historys list is on fetch', async () => {
      vesselService.getVoyageHistory = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      voyageHistoryList = mount(
        <MemoryRouter>
          <TechnicalReportContextProvider roleConfig={roleConfig}>
            <VoyageHistory />
          </TechnicalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(voyageHistoryList);
      expect(voyageHistoryList.find('Spinner').exists()).toEqual(true);
    });

    describe('voyage history tab when page loads', () => {
      it('should voyage history tab highlighted when page loads', async () => {
        const technicalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/technical/voyage-history']}>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/technical/:tab'}>
                <TechnicalReportsList />
              </Route>
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(technicalReports);
        const testData = technicalReports.find('a.active');
        expect(testData.text()).toEqual('Voyage History');
      });
    });

    describe('voyage historys Table pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const selectPage = async (page, page_num) => {
        page
          .find('div.page-num')
          .find('.page-num-enabled')
          .filterWhere((e) => Number(e.text()) === Number(page_num))
          .at(0)
          .simulate('click');
        await updateWrapper(page);
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        const page = mount(
          <MemoryRouter initialEntries={['/vessel/report/technical/voyage-history']}>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <VoyageHistory />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(page);
        await selectPage(page, 2);

        expect(findSelectedPageNumber(page)).toEqual(2);
        expect(findPageSizeValue(page)).toEqual(10);
      });
    });
  });
});
