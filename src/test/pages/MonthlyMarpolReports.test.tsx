import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter, Route } from 'react-router-dom';
import EnvironmentalReports from '../../component/EnvironmentalReports/EnvironmentalReportsList';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import MonthlyMarpolData from '../resources/monthly-marpol-reports-list.json';
import vesselList from '../resources/vesselListWithDefaultColumns.json';

describe('<MonthlyMarpolReports />', () => {
  let marpolReportList;
  const roleConfig = {
    params: {
      view: true,
    },
    vessel: {
      edit: true,
      send: true,
    },
  };
  beforeEach(async () => {
    vesselService.getMonthlyMarpolList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: MonthlyMarpolData }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    marpolReportList = mount(
      <MemoryRouter initialEntries={['/vessel/report/environmental/marpol']}>
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/:tab'}>
            <EnvironmentalReports />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(marpolReportList);
  });

  describe('should render monthly marpol report', () => {
    it('should render columns, when monthly marpol report is loaded', async () => {
      const customTable = marpolReportList.find('CustomTable');
      const actualMarpolReports = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Vessel',
        'View Report',
        'Report Month',
        'Report Submit Date',
      ];
      expect(actualMarpolReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when monthly marpol report is on fetch', async () => {
      vesselService.getMonthlyMarpolList = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      marpolReportList = mount(
        <MemoryRouter initialEntries={['/vessel/report/environmental/marpol']}>
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/:tab'}>
              <EnvironmentalReports />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(marpolReportList);
      expect(marpolReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('monthly marpol report tab when page loads', () => {
      it('should monthly marpol report tab highlighted when page loads', async () => {
        const testData = marpolReportList.find('a.active');
        expect(testData.text()).toEqual('Monthly MARPOL Report');
      });
    });
  });
});
