import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import MonthlyMarpolData from '../resources/monthly-marpol-reports-list.json';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselList from '../resources/vesselListWithDefaultColumns.json';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import EnvironmentalReportsCompare from '../../pages/EnvironmentalReportsCompare';
import ControlParametersData from '../resources/control-parameters-data.json';
import VesselContextProvider from '../../context/VesselContext';
import technicalMapperData from '../resources/technical-compare-mapper-data.json';

describe('<MonthlyMarpolReportCompare />', () => {
  let marpolReportsCompare;
  const roleConfig = {
    vessel: {
      view: true,
      create: true,
      edit: true,
      editReport: true,
      send: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };

  beforeEach(async () => {
    vesselService.getMonthlyMarpolList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: MonthlyMarpolData }));
    vesselService.getContolParameters = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ControlParametersData }));
    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: vesselList.data } }));

    vesselService.getReportDataMapper = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: technicalMapperData }));

    marpolReportsCompare = mount(
      <MemoryRouter
        initialEntries={['/vessel/report/environmental/marpol/1/compare?report_date=2022-12-31']}
      >
        <VesselContextProvider roleConfig={roleConfig}>
          <EnvironmentalReportContextProvider>
            <Route path={'/vessel/report/environmental/:report/:ownershipId/compare'}>
              <EnvironmentalReportsCompare />
            </Route>
          </EnvironmentalReportContextProvider>
        </VesselContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(marpolReportsCompare);
  });
  const openSendEmailModal = async () => {
    marpolReportsCompare
      .find('button[data-testid="fml-compare-report-Send-button"]')
      .at(0)
      .simulate('click');
    await updateWrapper(marpolReportsCompare);
  };

  it('should render columns, when compare loads', async () => {
    const marpolReportHeaders = marpolReportsCompare
      .find('.vessel-table .compare-page-header')
      .map((i) => i.text());
    const expected = [
      'GENERAL INFORMATION',
      'SLUDGE AND OTHER OIL RESIDUES (AS RECORDED IN OIL RECORD BOOK)(M3)',
      'BILGE / OILY WATER (AS RECORDED IN OIL RECORDED BOOK)(M3)',
      'GARBAGE - GENERAL (M3)',
      'PLASTIC BOTTLED DRINKING WATER (LTRS)',
      'REFRIGERANTS (KG)',
      'MARPOL ANNEX 1 SLOP OIL (TANKERS ONLY) (LTRS)',
      'GRAYWATER (DOMESTIC FRESH WATER CONSUMPTION IN MONTH, LESS ESTIMATED CONSUMPTION IN TOILETS FOR FW FLUSHING SYSTEMS. TOILET FLUSHING CONSUMPTION- FOR NON-VACCUM TOILETS-MAY BE ESTIMATED AT 1/3RD DOMESTIC FW CONSUMPTION)(MT)',
      'SPARES FOR OILY WATER SEPARATOR (SET)',
      'BILGE PUMP SPARES',
      'SPARES FOR OIL CONTENT MONITOR',
      'SPARES FOR INCINERATOR',
      'SPARES FOR SLUDGE PUMP',
      'SEWAGE TREATMENT PLANT',
    ];
    expect(marpolReportHeaders).toEqual(expect.arrayContaining(expected));
  });

  it('should render spinner, when compare is on fetch', async () => {
    vesselService.getMonthlyMarpolList = jest
      .fn()
      .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 100000)));
    marpolReportsCompare = mount(
      <MemoryRouter
        initialEntries={['/vessel/report/environmental/marpol/1/compare?report_date=2022-12-31']}
      >
        <VesselContextProvider roleConfig={roleConfig}>
          <EnvironmentalReportContextProvider>
            <Route path={'/vessel/report/environmental/:report/:ownershipId/compare'}>
              <EnvironmentalReportsCompare />
            </Route>
          </EnvironmentalReportContextProvider>
        </VesselContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(marpolReportsCompare);
    expect(marpolReportsCompare.find('Spinner').exists()).toEqual(true);
  });

  it('should have send button', async () => {
    expect(
      marpolReportsCompare.find('button[data-testid="fml-compare-report-Send-button"]').exists(),
    ).toEqual(true);
  });

  it('should open send email modal on send button click', async () => {
    await openSendEmailModal();
    expect(marpolReportsCompare.find('div.send-email-modal').exists()).toEqual(true);
  });

  it('should throw error on empty fields', async () => {
    await openSendEmailModal();
    marpolReportsCompare
      .find('input[data-testid="fml-sendEmail-subject"]')
      .simulate('change', { target: { value: '' } });
    marpolReportsCompare
      .find('textarea[data-testid="fml-sendEmail-recipient"]')
      .simulate('change', { target: { value: '' } });
    marpolReportsCompare
      .find('textarea[data-testid="fml-sendEmail-message"]')
      .simulate('change', { target: { value: '' } });
    await updateWrapper(marpolReportsCompare);
    marpolReportsCompare.find('button[data-testid="fml-sendEmail-send"]').simulate('click');
    await updateWrapper(marpolReportsCompare);
    expect(marpolReportsCompare.find('div.invalid-feedback').length === 3).toEqual(true);
  });
});
