import React from 'react';
import { mount } from 'enzyme';
import { MemoryRouter, Route } from 'react-router-dom';
import { updateWrapper } from '../../setupTests';
import OwnershipRequest from '../../pages/OwnershipRequest';
import { BUSINESS, ACCOUNTS } from '../../model/constants';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/ownership-service');
jest.mock('../../service/keycloak-service');
jest.mock('../../service/vessel-service');
jest.mock('../../controller/take-over-controller');

let ownershipRequest;

const getRoleConfig = (hasRole, group = BUSINESS) => {
  return {
    vessel: {
      view: true,
      create: hasRole,
      edit: hasRole,
      viewApproval: hasRole,
      requestHandOver: hasRole,
      requestArchival: hasRole,
    },
    approvalGroups: [group],
  };
};

const getFieldByLabel = async (fieldLabel, wrapper = ownershipRequest) => {
  await updateWrapper(wrapper);
  return wrapper
    ? wrapper
        .find('FormGroup')
        .filterWhere(
          (wrp) =>
            wrp.find('label').length > 0 && wrp.find('label').at(0).text().includes(fieldLabel),
        )
    : undefined;
};

describe('OwnershipRequest', () => {
  beforeEach(async () => {
    ownershipRequest = mount(
      <MemoryRouter initialEntries={['/vessel/ownership/1234?ownershipId=100']}>
        <Route path={'/vessel/ownership/:vesselId'}>
          <OwnershipRequest roleConfig={getRoleConfig(true)} setStaffData={() => {}} />
        </Route>
      </MemoryRouter>,
    );

    await updateWrapper(ownershipRequest);
  });

  const submitRequestForm = async (wrapper = ownershipRequest) => {
    await updateWrapper(wrapper);
    const saveButton = wrapper.find('BottomButton');
    saveButton.prop('onClick')();

    await updateWrapper(wrapper);
    const modelButtons = wrapper.find('ModalDialog ModalFooter Button');
    const confirmBtn = modelButtons.filterWhere((btn) => btn.text() === 'Confirm');
    confirmBtn.simulate('click');
    return wrapper;
  };

  it('should render page title', async () => {
    expect(ownershipRequest.text().includes('Change Ownership')).toEqual(true);
  });

  it('should render vessel name', async () => {
    expect(ownershipRequest.text().includes('Ultra Bosque')).toEqual(true);
  });

  it('should render save button', async () => {
    const saveButton = ownershipRequest.find('BottomButton');
    expect(saveButton.exists()).toEqual(true);
    expect(saveButton.text()).toEqual('Save');
  });

  it('should not render save button for Accounts', async () => {
    const ownershipRequest = mount(
      <MemoryRouter>
        <OwnershipRequest roleConfig={getRoleConfig(true, ACCOUNTS)} setStaffData={() => {}} />
      </MemoryRouter>,
    );
    await updateWrapper(ownershipRequest);
    const saveButton = ownershipRequest.find('BottomButton');
    expect(saveButton.exists()).toEqual(false);
  });

  it('should show error on clicking save button without filling request form ', async () => {
    await submitRequestForm();
    await updateWrapper(ownershipRequest);
    expect(
      ownershipRequest
        .text()
        .includes('At lease one input is required from Owner Name or Registered Owner Name'),
    ).toEqual(true);
  });

  it('should have owners in the "New Owner Name" dropdown', async () => {
    const ownerDropdown = ownershipRequest.find('TakeoverDropDownSearchControl');
    ownerDropdown.find('input').at(0).simulate('click');

    await updateWrapper(ownershipRequest);
    expect(ownershipRequest.find('a').at(0).text()).toEqual(
      expect.stringContaining('Blue Forest Shipping Co.'),
    );
    expect(ownershipRequest.find('a').at(1).text()).toEqual(
      expect.stringContaining('Kowa Kaiun Co. Ltd.'),
    );
    expect(ownershipRequest.find('a').at(2).text()).toEqual(
      expect.stringContaining('Saito Shipping Co. Ltd.'),
    );
    expect(ownershipRequest.find('a').at(3).text()).toEqual(
      expect.stringContaining('Scottish Ship Owners & Managers Pty Ltd.'),
    );
  });

  it('should have owners in the "New Registered Owner Name" dropdown', async () => {
    const ownerDropdown = ownershipRequest.find('TakeoverDropDownSearchControl');
    ownerDropdown.find('input').at(2).simulate('click');

    await updateWrapper(ownershipRequest);
    expect(ownershipRequest.find('a').at(0).text()).toEqual(
      expect.stringContaining('East Blue Marlin SA'),
    );
    expect(ownershipRequest.find('a').at(1).text()).toEqual(
      expect.stringContaining('Heroic Hermes Inc.'),
    );
    expect(ownershipRequest.find('a').at(2).text()).toEqual(
      expect.stringContaining('New Fortune Shipping Limited'),
    );
    expect(ownershipRequest.find('a').at(3).text()).toEqual(
      expect.stringContaining('New Golden Shipping Limited'),
    );
  });

  it('should have value "Saito Shipping Co. Ltd." in New Owner Name field when it is selected', async () => {
    const ownerDropdown = ownershipRequest.find('TakeoverDropDownSearchControl');
    ownerDropdown.find('input').at(0).simulate('click');

    await updateWrapper(ownershipRequest);
    ownershipRequest.find('a').at(2).simulate('click');
    expect(
      ownershipRequest.find('TakeoverDropDownSearchControl').find('input').at(0).prop('value'),
    ).toEqual('Saito Shipping Co. Ltd.');
  });

  it('should have value "Heroic Hermes Inc." in New Registered Owner Name field when it is selected', async () => {
    const ownerDropdown = ownershipRequest.find('TakeoverDropDownSearchControl');
    ownerDropdown.find('input').at(2).simulate('click');

    await updateWrapper(ownershipRequest);
    ownershipRequest.find('a').at(1).simulate('click');
    expect(
      ownershipRequest.find('TakeoverDropDownSearchControl').find('input').at(2).prop('value'),
    ).toEqual('Heroic Hermes Inc.');
  });

  it('should redirect to vessel details page after creating ownership change request with split NO', async () => {
    const ownershipRequest = mount(
      <MemoryRouter initialEntries={['/vessel/ownership/1234?ownershipId=100']}>
        <Route path={'/vessel/ownership/:vesselId'}>
          <OwnershipRequest roleConfig={getRoleConfig(true)} setStaffData={() => {}} />
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(ownershipRequest);

    const ownerDropdown = ownershipRequest.find('TakeoverDropDownSearchControl');
    ownerDropdown.find('input').at(0).simulate('click');
    await updateWrapper(ownershipRequest);
    ownershipRequest.find('a').at(2).simulate('click');
    await updateWrapper(ownershipRequest);

    await submitRequestForm(ownershipRequest);

    await updateWrapper(ownershipRequest);
    expect(ownershipRequest.find('Router').prop('history').location.pathname).toEqual(
      '/vessel/ownership/details/100',
    );
  });

  it('should disable split related fields when the split is no', async () => {
    const splitAccountingBooks = await getFieldByLabel('Split of Accounting Books');
    splitAccountingBooks
      .find('select')
      .simulate('change', { target: { name: 'split_accounting_books', value: 'no' } });
    await updateWrapper(ownershipRequest);

    const vesselShortCode = await getFieldByLabel('Vessel Short Code (NEW)');
    const vesselAccountCode = await getFieldByLabel('Vessel Account Code');
    const vesselTelFacCode = await getFieldByLabel('Vessel Tel FAC Code (NEW)');
    const vesselName = await getFieldByLabel('Vessel Name (NEW)');

    expect(vesselShortCode.at(0).find('FormControl').prop('disabled')).toBe(true);
    expect(vesselAccountCode.at(0).find('FormControl').prop('disabled')).toBe(true);
    expect(vesselTelFacCode.at(0).find('FormControl').prop('disabled')).toBe(true);
    expect(vesselName.at(0).find('FormControl').prop('disabled')).toBe(true);
  });

  it('should not disable split related fields when the split is yes', async () => {
    const splitAccountingBooks = await getFieldByLabel('Split of Accounting Books');
    splitAccountingBooks
      .find('select')
      .simulate('change', { target: { name: 'split_accounting_books', value: 'yes' } });
    await updateWrapper(ownershipRequest);

    const vesselShortCode = await getFieldByLabel('Vessel Short Code (NEW)');
    const vesselAccountCode = await getFieldByLabel('Vessel Account Code');
    const vesselTelFacCode = await getFieldByLabel('Vessel Tel FAC Code (NEW)');
    const vesselName = await getFieldByLabel('Vessel Name (NEW)');

    expect(vesselShortCode.at(0).find('FormControl').prop('disabled')).toBe(false);
    expect(vesselAccountCode.at(0).find('FormControl').prop('disabled')).toBe(false);
    expect(vesselTelFacCode.at(0).find('FormControl').prop('disabled')).toBe(false);
    expect(vesselName.at(0).find('FormControl').prop('disabled')).toBe(false);
  });

  it('should show required fields error on creating ownership change request with empty fields', async () => {
    const splitAccountingBooks = await getFieldByLabel('Split of Accounting Books');
    splitAccountingBooks
      .find('select')
      .simulate('change', { target: { name: 'split_accounting_books', value: 'yes' } });
    await updateWrapper(ownershipRequest);

    await submitRequestForm();

    await updateWrapper(ownershipRequest);
    expect(
      ownershipRequest
        .text()
        .includes('At lease one input is required from Owner Name or Registered Owner Name'),
    ).toEqual(true);
    expect(ownershipRequest.text().includes('Vessel Tel FAC Code is required')).toEqual(true);
    expect(ownershipRequest.text().includes('Vessel Account Code New is required')).toEqual(true);
    expect(ownershipRequest.text().includes('Vessel Short Code is required')).toEqual(true);
  });

  it('should show fields validation error on creating ownership change request with invalid field values', async () => {
    const splitAccountingBooks = await getFieldByLabel('Split of Accounting Books');
    splitAccountingBooks
      .find('select')
      .simulate('change', { target: { name: 'split_accounting_books', value: 'yes' } });
    await updateWrapper(ownershipRequest);

    const vesselShortCode = await getFieldByLabel('Vessel Short Code (NEW)');
    vesselShortCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_short_code', value: '1234' } });

    const vesselAccountCode = await getFieldByLabel('Vessel Account Code');
    vesselAccountCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_account_code', value: '12345' } });

    const vesselTelFacCode = await getFieldByLabel('Vessel Tel FAC Code (NEW)');
    vesselTelFacCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_tel_fac_code', value: '123456' } });

    await submitRequestForm();

    await updateWrapper(ownershipRequest);
    expect(
      ownershipRequest
        .text()
        .includes('At lease one input is required from Owner Name or Registered Owner Name'),
    ).toEqual(true);
    expect(
      ownershipRequest.text().includes('vessel_tel_fac_code must be at most 5 characters'),
    ).toEqual(true);
    expect(
      ownershipRequest.text().includes('vessel_account_code must be exactly 4 characters'),
    ).toEqual(true);
    expect(
      ownershipRequest.text().includes('vessel_short_code must be at most 3 characters'),
    ).toEqual(true);
  });

  it('should show duplicate fields input error on creating ownership change request', async () => {
    const splitAccountingBooks = await getFieldByLabel('Split of Accounting Books');
    splitAccountingBooks
      .find('select')
      .simulate('change', { target: { name: 'split_accounting_books', value: 'yes' } });
    await updateWrapper(ownershipRequest);

    const vesselShortCode = await getFieldByLabel('Vessel Short Code (NEW)');
    vesselShortCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_short_code', value: 'PQR' } });

    const vesselAccountCode = await getFieldByLabel('Vessel Account Code');
    vesselAccountCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_account_code', value: '1111' } });

    await submitRequestForm();

    await updateWrapper(ownershipRequest);
    expect(ownershipRequest.text().includes('Duplicate input for Vessel Short Code')).toEqual(true);
    expect(ownershipRequest.text().includes('Duplicate input for Vessel Account Code New')).toEqual(
      true,
    );
  });

  it('should redirect to vessel details page after creating ownership change request with split YES', async () => {
    const ownerDropdown = ownershipRequest.find('TakeoverDropDownSearchControl');
    ownerDropdown.find('input').at(0).simulate('click');
    await updateWrapper(ownershipRequest);
    ownershipRequest.find('a').at(2).simulate('click');

    const splitAccountingBooks = await getFieldByLabel('Split of Accounting Books');
    splitAccountingBooks
      .find('select')
      .simulate('change', { target: { name: 'split_accounting_books', value: 'yes' } });
    await updateWrapper(ownershipRequest);

    const vesselShortCode = await getFieldByLabel('Vessel Short Code (NEW)');
    vesselShortCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_short_code', value: '123' } });

    const vesselAccountCode = await getFieldByLabel('Vessel Account Code');
    vesselAccountCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_account_code', value: '1234' } });

    const vesselTelFacCode = await getFieldByLabel('Vessel Tel FAC Code (NEW)');
    vesselTelFacCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_tel_fac_code', value: '12345' } });

    await submitRequestForm(ownershipRequest);

    await updateWrapper(ownershipRequest);
    expect(ownershipRequest.find('Router').prop('history').location.pathname).toEqual(
      '/vessel/ownership/details/100',
    );
  });

  it('should remove disabled field data When split is changed from Yes to No', async () => {
    let splitAccountingBooks = await getFieldByLabel('Split of Accounting Books');
    splitAccountingBooks
      .find('select')
      .simulate('change', { target: { name: 'split_accounting_books', value: 'yes' } });
    await updateWrapper(ownershipRequest);

    let vesselShortCode = await getFieldByLabel('Vessel Short Code (NEW)');
    vesselShortCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_short_code', value: '123' } });

    let vesselAccountCode = await getFieldByLabel('Vessel Account Code');
    vesselAccountCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_account_code', value: '1234' } });

    let vesselTelFacCode = await getFieldByLabel('Vessel Tel FAC Code (NEW)');
    vesselTelFacCode
      .find('input')
      .simulate('change', { target: { name: 'vessel_tel_fac_code', value: '12345' } });

    let vesselName = await getFieldByLabel('Vessel Name (NEW)');
    vesselName
      .find('input')
      .simulate('change', { target: { name: 'vessel_name', value: 'New vessel' } });

    splitAccountingBooks = await getFieldByLabel('Split of Accounting Books');
    splitAccountingBooks
      .find('select')
      .simulate('change', { target: { name: 'split_accounting_books', value: 'no' } });
    await updateWrapper(ownershipRequest);
    vesselShortCode = await getFieldByLabel('Vessel Short Code (NEW)');
    vesselAccountCode = await getFieldByLabel('Vessel Account Code');
    vesselTelFacCode = await getFieldByLabel('Vessel Tel FAC Code (NEW)');
    vesselName = await getFieldByLabel('Vessel Name (NEW)');

    expect(vesselShortCode.at(0).find('FormControl').prop('value')).toBe('');
    expect(vesselAccountCode.at(0).find('FormControl').prop('value')).toBe('');
    expect(vesselTelFacCode.at(0).find('FormControl').prop('value')).toBe('');
    expect(vesselName.at(0).find('FormControl').prop('value')).toBe('');
  });
});

describe('Ownership Request Details Form', () => {
  const pendingRequestWithNoTechGroupChange = {
    vessel_id: 1234,
    owner_id: 354,
    registered_owner_id: 244,
    ownership_status: 'pending',
    expected_owner_start_date: null,
    expected_registered_owner_start_date: null,
    owner_start_date: null,
    owner_end_date: null,
    registered_owner_start_date: null,
    registered_owner_end_date: null,
    vessel_short_code: 'VSC',
    vessel_account_code_new: '1212',
    vessel_tel_fac_code: '2121',
    name: 'new name',
    tech_group: 'Tech D7',
    ownership_change_request_id: 42,
    ownership_change_request: {
      id: 42,
      request_status: 'pending',
      request_for: 'owner',
      requested_by_user: '<EMAIL>',
      is_split_of_accounting_books_require: true,
      ownership_change_approval: [],
    },
  };

  let ownershipRequestPage;
  beforeAll(async () => {
    ownershipRequestPage = mount(
      <MemoryRouter initialEntries={['/vessel/ownership/123']}>
        <Route path={'/vessel/ownership/:vesselId'}>
          <OwnershipRequest
            roleConfig={getRoleConfig(true)}
            request={pendingRequestWithNoTechGroupChange}
            isFinalApproverApproved={jest.fn().mockImplementation(() => {
              Promise.resolve(true);
            })}
            setStaffData={() => {}}
          />
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(ownershipRequestPage);
  });

  it('should render new name', async () => {
    const name = await getFieldByLabel('Vessel Name (NEW)', ownershipRequestPage);
    const displayedName = name.find('input').prop('value');
    expect(displayedName).toEqual('new name');
  });
});
