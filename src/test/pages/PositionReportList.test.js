import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import positionReportsListData from '../resources/positionReportListDefaultColumns.json';
import { MemoryRouter } from 'react-router-dom';
import PositionReports from '../../component/TechnicalReports/ListReports/PositionReports';
import TechnicalReportsList from '../../component/TechnicalReports/TechnicalReportsList';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';

describe('<PositionReportsList />', () => {
  let positionReportList;
  const roleConfig = {
    vessel: {
      editReport: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };

  beforeEach(async () => {
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: positionReportsListData }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() => Promise.resolve({ data: {} }));
    positionReportList = mount(
      <MemoryRouter>
        <TechnicalReportContextProvider roleConfig={roleConfig}>
          <PositionReports />
        </TechnicalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(positionReportList);
  });

  describe('should render position report list page', () => {
    it('should render columns, when postion report list loads', async () => {
      const PositionTable = positionReportList.find('TechnicalReportTable');
      const actualPositionReports = PositionTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Vessel',
        'View Report',
        'Report Type',
        'Report Date Time(SMT)',
        'Report Date Time(GMT)',
      ];
      expect(actualPositionReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when position Reports list is on fetch', async () => {
      vesselService.getTechnicalReports = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      positionReportList = mount(
        <MemoryRouter>
          <TechnicalReportContextProvider roleConfig={roleConfig}>
            <PositionReports
              filterData={{ startDate: '2022-03-02', endDate: '2022-03-06', vessel: [] }}
              loading={true}
            />
          </TechnicalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(positionReportList);
      expect(positionReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('position report tab when page loads', () => {
      it('should position report tab highlighted when page loads', async () => {
        const technicalReports = mount(
          <MemoryRouter>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <TechnicalReportsList />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(technicalReports);
        const testData = technicalReports.find('a.active');
        expect(testData.text()).toEqual('Position Reports');
      });
    });
    describe('should render data on date pickers when page loads', () => {
      it('should render result count based on selected date', async () => {
        vesselService.getTechnicalReports = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: positionReportsListData }));

        positionReportList = mount(
          <MemoryRouter initialEntries={['/vessel/report/technical/position']}>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <PositionReports filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(positionReportList);

        const totalCount = positionReportList.find('TechnicalReportTable').props().totalCount;
        expect(totalCount).toEqual(15);
      });
    });

    describe('position reports Table pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const selectPage = async (page, page_num) => {
        page
          .find('div.page-num')
          .find('.page-num-enabled')
          .filterWhere((e) => Number(e.text()) === Number(page_num))
          .at(0)
          .simulate('click');

        await updateWrapper(page);
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        const page = mount(
          <MemoryRouter>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <PositionReports filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(page);
        await selectPage(page, 2);

        expect(findSelectedPageNumber(page)).toEqual(2);
        expect(findPageSizeValue(page)).toEqual(10);
      });
    });
  });
});
