import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter, Route } from 'react-router-dom';
import EnvironmentalReports from '../../component/EnvironmentalReports/EnvironmentalReportsList';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import MrvReportData from '../resources/mrv-report-list.json';
import vesselList from '../resources/vesselListWithDefaultColumns.json';

describe('<MrvReport />', () => {
  let mrvReportList;
  const roleConfig = {
    params: {
      view: true,
    },
    vessel: {
      edit: true,
      send: true,
    },
  };
  beforeEach(async () => {
    vesselService.getMrvReport = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: MrvReportData }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    mrvReportList = mount(
      <MemoryRouter
        initialEntries={[
          '/vessel/report/environmental/mrv?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
        ]}
      >
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/:tab'}>
            <EnvironmentalReports />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(mrvReportList);
  });

  describe('should render mrv report', () => {
    it('should render columns, when mrv voyage report is loaded', async () => {
      const customTable = mrvReportList.find('CustomTable');
      const actualMrvVoyageReports = customTable
        .at(0)
        .find('.th')
        .map((i) => i.text());
      const expectedHeaders = [
        'Voyage No.',
        'Port of Departure',
        'EU Port of Departure',
        'Port of Arrival',
        'EU Port of arrival',
        'Leg Start / End Date',
        'Hour of departure (UTC)',
        'Hour of arrival (UTC)',
        'Time spent at sea (hr)',
        'Distance (nm)',
        'Cargo Carried (tons)',
        'Voyage Type',
        'HFO Consumption (tons)',
        'HFO CO\u2082 Emission Factor',
        'HFO CO\u2082 emitted (tons)',
        'LFO Consumption (tons)',
        'LFO CO\u2082 Emission Factor',
        'LFO CO\u2082 emitted (tons)',
        'MGO Consumption (tons)',
        'MGO CO\u2082 Emission Factor',
        'MGO CO\u2082 emitted (tons)',
        'LNG Consumption (tons)',
        'LNG CO\u2082 Emission Factor',
        'LNG CO\u2082 emitted (tons)',
        'LPG (Propane) Consumption (tons)',
        'LPG (Propane) CO\u2082 Emission Factor',
        'LPG (Propane) CO\u2082 emitted (tons)',
        'LPG (Butane) Consumption (tons) ',
        'LPG (Butane) CO\u2082 Emission Factor',
        'LPG (Butane) CO\u2082 emitted (tons) ',
        'Methanol Consumption (tons)',
        'Methanol CO\u2082 Emission Factor',
        'Methanol CO\u2082 emitted (tons)',
        'Ethanol Consumption (tons)',
        'Ethanol CO\u2082 Emission Factor',
        'Ethanol CO\u2082 emitted (tons)',
        'Total CO\u2082 emitted (tons)',
        'CO\u2082 emitted on voyage between EU Ports (tons)',
        'CO\u2082 emitted on voyage departed from EU Port (tons)',
        'CO\u2082 emitted on voyage arrived to EU Port (tons)',
        'Transport Work (tn*nm)',
        'Fuel consumption per distance (tn/nm)',
        'Fuel consumption per transport work (gr/tn*nm)',
        'CO\u2082 emissions per distance (tn/nm)',
        'CO\u2082 emissions per transport work (gCO\u2082/tn*nm)',
        'Boiler consumption for cargo heating HFO',
        'Boiler consumption for cargo heating MGO/MDO',
        'Boiler consumption for cargo heating Fuel 3',
      ];
      expect(actualMrvVoyageReports).toEqual(expectedHeaders);
    });

    it('should render columns, when mrv port report is loaded', async () => {
      const customTable = mrvReportList.find('CustomTable');
      const actualMrvPortReports = customTable
        .at(1)
        .find('.th')
        .map((i) => i.text());
      const expectedHeaders = [
        'Voyage No.',
        'Port of Departure',
        'Arrival in the (EU) port',
        'Departure in the (EU) port',
        'Hour of arrival (UTC)',
        'Hour of departure (UTC)',
        'Time spent on land (hours)',
        'Distance (nm)',
        'Total HFO Consumption (tons)',
        'HFO CO\u2082 Emission Factor',
        'HFO CO\u2082 emitted (tons)',
        'Total LFO Consumption (tons)',
        'LFO CO\u2082 Emission Factor',
        'LFO CO\u2082 emitted (tons)',
        'Total MGO Consumption (tons)',
        'MGO CO\u2082 Emission Factor',
        'MGO CO\u2082 emitted (tons)',
        'Total LNG Consumption (tons)',
        'LNG CO\u2082 Emission Factor',
        'LNG CO\u2082 emitted (tons)',
        'LPG (Propane) Consumption (tons)',
        'LPG (Propane) CO\u2082 Emission Factor',
        'LPG (Propane) CO\u2082 Emitted (tons)',
        'LPG (Butane) Consumption (tons)',
        'LPG (Butane) CO\u2082 Emission Factor',
        'LPG (Butane) CO\u2082 Emitted (tons)',
        'Methanol Consumption (tons)',
        'Methanol CO\u2082 Emission Factor',
        'Methanol CO\u2082 Emitted (tons)',
        'Ethanol Consumption (tons)',
        'Ethanol CO\u2082 Emission Factor',
        'Ethanol CO\u2082 Emitted (tons)',
        'Total CO\u2082 emitted (tons)',
        'Boiler consumption for cargo heating HFO',
        'Boiler consumption for cargo heating MGO/MDO',
        'Boiler consumption for cargo heating Fuel 3',
      ];
      expect(actualMrvPortReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when mrv Reports is on fetch', async () => {
      vesselService.getMrvReport = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      mrvReportList = mount(
        <MemoryRouter
          initialEntries={[
            '/vessel/report/environmental/mrv?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
          ]}
        >
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/:tab'}>
              <EnvironmentalReports />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(mrvReportList);
      expect(mrvReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('mrv report tab when page loads', () => {
      it('should mrv report tab highlighted when page loads', async () => {
        const environmentalReports = mount(
          <MemoryRouter
            initialEntries={[
              '/vessel/report/environmental/mrv?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
            ]}
          >
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/:tab'}>
                <EnvironmentalReports />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(environmentalReports);
        const testData = environmentalReports.find('a.active');
        expect(testData.text()).toEqual('MRV Report');
      });
    });
  });
});