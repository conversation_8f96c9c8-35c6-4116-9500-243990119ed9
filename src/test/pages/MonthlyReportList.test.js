import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import MonthlyReportsData from '../resources/monthlyReportList.json';
import { MemoryRouter, Route } from 'react-router-dom';
import TechnicalReportsList from '../../component/TechnicalReports/TechnicalReportsList';
import MonthlyReports from '../../component/TechnicalReports/ListReports/MonthlyReports';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';

describe('<MonthlyReports />', () => {
  let monthlyReportList;
  const roleConfig = {
    vessel: {
      editReport: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };

  beforeEach(async () => {
    vesselService.getAllVessels = jest.fn().mockImplementation(() => Promise.resolve({ data: [] }));
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: MonthlyReportsData }));
    monthlyReportList = mount(
      <MemoryRouter initialEntries={['/vessel/report/technical/monthly']}>
        <TechnicalReportContextProvider roleConfig={roleConfig}>
          <MonthlyReports filterData={{ startDate: '', endDate: '', vessel: [] }} />
        </TechnicalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(monthlyReportList);
  });

  describe('should render monthly report list page', () => {
    it('should render columns, when monthly report list loads', async () => {
      const monthlyTable = monthlyReportList.find('TechnicalReportTable');
      const actualMonthlyReports = monthlyTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Vessel',
        'View Report',
        'Report Month',
        'Report Submit Date',
      ];
      expect(actualMonthlyReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when monthly Reports list is on fetch', async () => {
      vesselService.getTechnicalReports = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      monthlyReportList = mount(
        <MemoryRouter>
          <TechnicalReportContextProvider roleConfig={roleConfig}>
            <MonthlyReports
              filterData={{ startDate: '2022-03-02', endDate: '2022-03-06', vessel: [] }}
              loading={true}
            />
          </TechnicalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(monthlyReportList);
      expect(monthlyReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('monthly report tab when page loads', () => {
      it('should monthly report tab highlighted when page loads', async () => {
        const technicalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/technical/monthly']}>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/technical/:tab'}>
                <TechnicalReportsList />
              </Route>
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(technicalReports);
        const testData = technicalReports.find('a.active');
        expect(testData.text()).toEqual('Monthly Reports');
      });
    });
    describe('should render data on date pickers when page loads', () => {
      it('should render result count based on selected date', async () => {
        monthlyReportList = mount(
          <MemoryRouter>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <MonthlyReports filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(monthlyReportList);

        const totalCount = monthlyReportList.find('TechnicalReportTable').props().totalCount;
        expect(totalCount).toEqual(15);
      });
    });

    describe('monthly reports Table pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const selectPage = async (page, page_num) => {
        page
          .find('div.page-num')
          .find('.page-num-enabled')
          .filterWhere((e) => Number(e.text()) === Number(page_num))
          .at(0)
          .simulate('click');
        await updateWrapper(page);
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        const page = mount(
          <MemoryRouter>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <MonthlyReports filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(page);
        await selectPage(page, 2);

        expect(findSelectedPageNumber(page)).toEqual(2);
        expect(findPageSizeValue(page)).toEqual(10);
      });
    });
  });
});
