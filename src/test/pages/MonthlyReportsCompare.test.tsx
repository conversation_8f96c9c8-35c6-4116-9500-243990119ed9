import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter, Route } from 'react-router-dom';
import MonthlyReportData from '../resources/monthlyReportList.json';
import ControlParametersData from '../resources/control-parameters-data.json';
import TechnicalReportsCompare from '../../pages/TechnicalReportsCompare';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';
import technicalMapperData from '../resources/technical-compare-mapper-data.json';
import VesselContextProvider from '../../context/VesselContext';
import vesselList from '../resources/vesselListWithDefaultColumns.json';

describe('<MonthlyReportsCompare />', () => {
  let monthlyReportsCompare;
  const roleConfig = {
    vessel: {
      view: true,
      create: true,
      edit: true,
      editReport: true,
      send: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };
  beforeEach(async () => {
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: MonthlyReportData }));

    vesselService.getReportDataMapper = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: technicalMapperData }));

    vesselService.getContolParameters = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ControlParametersData }));

    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: vesselList.data } }));

    monthlyReportsCompare = mount(
      <MemoryRouter
        initialEntries={['/vessel/report/technical/monthly/684/compare?gmt=2023-02-24']}
      >
        <Route path={'/vessel/report/technical/:report/:ownershipId/compare'}>
          <VesselContextProvider roleConfig={roleConfig}>
            <TechnicalReportContextProvider>
              <TechnicalReportsCompare />
            </TechnicalReportContextProvider>
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(monthlyReportsCompare);
  });
  const openSendEmailModal = async () => {
    monthlyReportsCompare
      .find('button[data-testid="fml-compare-report-Send-button"]')
      .at(0)
      .simulate('click');
    await updateWrapper(monthlyReportsCompare);
  };

  it('should render columns, when compare loads', async () => {
    const monthlyReportHeaders = monthlyReportsCompare
      .find('.vessel-table .th')
      .map((i) => i.text());
    const expected = [
      'GENERAL INFORMATION',
      'AUX. ENGINE POWER GENERATION (KWH)',
      'FUEL OIL CONSUMPTION (MT)',
      'FUEL OIL R.O.B. (MT)',
      'EMISSION',
      'LUBE OIL',
      'EXPENSES',
      'REMARKS',
    ];
    expect(monthlyReportHeaders).toEqual(expect.arrayContaining(expected));
  });

  it('should render spinner, when compare is on fetch', async () => {
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 100000)));
    monthlyReportsCompare = mount(
      <MemoryRouter
        initialEntries={['/vessel/report/technical/monthly/684/compare?gmt=2023-02-24']}
      >
        <Route path={'/vessel/report/technical/:report/:ownershipId/compare'}>
          <VesselContextProvider roleConfig={roleConfig}>
            <TechnicalReportContextProvider>
              <TechnicalReportsCompare />
            </TechnicalReportContextProvider>
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(monthlyReportsCompare);
    expect(monthlyReportsCompare.find('Spinner').exists()).toEqual(true);
  });

  it('should have send button', async () => {
    expect(
      monthlyReportsCompare.find('button[data-testid="fml-compare-report-Send-button"]').exists(),
    ).toEqual(true);
  });

  it('should open send email modal on send button click', async () => {
    await openSendEmailModal();
    expect(monthlyReportsCompare.find('div.send-email-modal').exists()).toEqual(true);
  });

  it('should throw error on empty fields', async () => {
    await openSendEmailModal();
    monthlyReportsCompare
      .find('input[data-testid="fml-sendEmail-subject"]')
      .simulate('change', { target: { value: '' } });
    monthlyReportsCompare
      .find('textarea[data-testid="fml-sendEmail-recipient"]')
      .simulate('change', { target: { value: '' } });
    monthlyReportsCompare
      .find('textarea[data-testid="fml-sendEmail-message"]')
      .simulate('change', { target: { value: '' } });
    await updateWrapper(monthlyReportsCompare);
    monthlyReportsCompare.find('button[data-testid="fml-sendEmail-send"]').simulate('click');
    await updateWrapper(monthlyReportsCompare);
    expect(monthlyReportsCompare.find('div.invalid-feedback').length === 3).toEqual(true);
  });
});
