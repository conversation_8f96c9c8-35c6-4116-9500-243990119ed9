import React from 'react';
import { mount } from 'enzyme';
import Details from '../../pages/Details';
import { MemoryRouter } from 'react-router-dom';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { officeData } from '../resources/vessel';
import DetailContextProvider from '../../context/DetailContext';
import VesselContextProvider from '../../context/VesselContext';
import { operationManagerStaffData, qhseStaffData } from '../resources/staff-type';
import { getRoleConfig, mockOtherCalls, mockShipReports } from './Details.test';

describe('<Details />', () => {
  let details;

  beforeAll(async () => {
    vesselService.getOwnershipVessel = jest.fn().mockResolvedValue({ data: officeData[0] });

    mockOtherCalls(false);
    mockShipReports();
    details = mount(
      <MemoryRouter>
        <VesselContextProvider>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Details />
          </DetailContextProvider>
        </VesselContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
  });

  describe('should render vessel details office data section', () => {
    it('should render office data heading', async () => {
      expect(details.find('#officedata').exists()).toEqual(true);
    });

    it('should render fleet staff fields under office data', async () => {
      const fleetStaffFields = details
        .find('#officedata')
        .find('p')
        .map((i) => i.text());

      const expected = [
        expect.stringMatching(/^Tech/),
        expect.stringMatching(/.Director/),
        expect.stringMatching(/.Not assigned/),
      ];

      expect([fleetStaffFields.join(' ')]).toEqual(expect.arrayContaining(expected));
    });

    describe('should render assign dialog when click on Not assigned', () => {
      beforeEach(() => {
        vesselService.getAssignedStaffVessel = jest.fn().mockResolvedValue({ data: qhseStaffData });
      });
      it('should render assign dialog to assign QHSE manager', async () => {
        details.find('#officedata').find('p').at(4).find('span').simulate('click');
        await updateWrapper(details);

        // check assign field dialog heading
        expect(details.find('.modal-title').at(1).text()).toEqual('Assign New QHSE Manager');

        // select option in dropdown and render vessel table in assign field dialog
        details.find('input[data-testid="fml-assign-field-select-user"]').simulate('change', {
          target: {
            id: 'dfa6b9a9-05ec-4cc2-afd2-dc2454b833d2',
            value: `${qhseStaffData[0].full_name}${qhseStaffData[0].email}`,
          },
        });
        await updateWrapper(details);
        details.find('#basic-typeahead-single-item-0').at(1).simulate('click');
        await updateWrapper(details);

        //check vessel information message
        expect(details.find('div.fs-13-label').text()).toEqual(
          ' This person is already responsible for 10 active and 3 new takeover vessels',
        );
        expect(details.find('div.header').text()).toEqual('Vessel');

        // check assign dialog table data
        const vesselTableData = details.find('.td').map((i) => i.text());
        expect(vesselTableData).toEqual(['Unsinkable Queen Test (2023)', 'Xi Hu (1575)']);
      });

      it('should render confirm dialog when clicking next button to assign QHSE manager', async () => {
        details.find('button[data-testid="fml-assign-field-dialog-submit-btn"]').simulate('click');
        await updateWrapper(details);

        // check confirm dialog heading
        expect(details.find('ConfirmModal').find('.modal-title').at(1).text()).toEqual(
          'Confirm Staff Assignment',
        );

        // check confirm dialog table data
        const staffAssignmentData = details
          .find('ConfirmModal')
          .find('Col')
          .map((i) => i.text());
        await updateWrapper(details);

        const expected = [
          expect.stringMatching(/^Curr/),
          expect.stringMatching(/.New/),
          expect.stringMatching(/.fleet/),
        ];

        expect([staffAssignmentData.join(' ')]).toEqual(expect.arrayContaining(expected));

        // check confirm dialog button exists
        expect(
          details.find('button[data-testid="fml-custom-confirm-modal-confirm-button"]').exists(),
        ).toEqual(true);
        expect(
          details.find('button[data-testid="fml-custom-confirm-modal-cancel-button"]').exists(),
        ).toEqual(true);
      });
    });

    describe('should render role assignment replace dialog', () => {
      beforeEach(() => {
        vesselService.getAssignedStaffVessel = jest
          .fn()
          .mockResolvedValue({ data: operationManagerStaffData });
      });

      it('should render replace dialog when click on already assigned operation manager', async () => {
        details.find('#officedata').find('p').at(10).find('span').simulate('click');
        await updateWrapper(details);

        // check modal heading
        expect(details.find('.modal-title').at(1).text()).toEqual('Role Assignment');

        // check opeartion manager role assignment dialog dropdown values
        expect(
          details.find('input[data-testid="fml-assign-role-select-user-type"]').prop('value'),
        ).toEqual('Operation');

        expect(
          details.find('input[data-testid="fml-assign-role-select-user"]').prop('value'),
        ).toEqual('test tanker ops General Manager');

        // check vessel information message
        expect(details.find('div.fs-13-label').text()).toEqual(
          ' This person is already responsible for 0 active and 0 new takeover vessels',
        );

        // check vessel columns in table
        const assignedTableHeaders = details.find('.th').map((i) => i.text());
        expect(assignedTableHeaders).toEqual(['Vessel', 'Tech Group']);
      });

      it('should render assign dialog when clicking next button on role assignment dialog', async () => {
        details.find('button[data-testid="fml-assign-role-dialog-submit-btn"]').simulate('click');
        await updateWrapper(details);

        expect(
          details.find('[data-testid="fml-assign-field-dialog"]').find('.modal-title').at(1).text(),
        ).toEqual('Assign New Operation Manager');

        // check assign dialog dropdown value
        expect(
          details.find('input[data-testid="fml-assign-field-select-user"]').prop('value'),
        ).toEqual('test tanker ops General Manager');

        //check vessel table header
        expect(details.find('.th').at(0).text()).toEqual('Vessel');

        details.find('button[data-testid="fml-assign-field-dialog-submit-btn"]').simulate('click');
        await updateWrapper(details);

        // check modal heading
        expect(
          details.find('[data-testid="fml-confirm-dialog"]').find('.modal-title').at(0).text(),
        ).toEqual('Confirm Staff Assignment');

        // check confirm dialog table data
        const operationManagerAssignment = details
          .find('ConfirmModal')
          .find('Col')
          .map((i) => i.text());
        await updateWrapper(details);

        const expected = [
          expect.stringMatching(/^Curr/),
          expect.stringMatching(/.New/),
          expect.stringMatching(/.General/),
        ];

        expect([operationManagerAssignment.join(' ')]).toEqual(expect.arrayContaining(expected));

        // check confirm dialog button
        expect(
          details.find('button[data-testid="fml-custom-confirm-modal-confirm-button"]').exists(),
        ).toEqual(true);
        expect(
          details.find('button[data-testid="fml-custom-confirm-modal-cancel-button"]').exists(),
        ).toEqual(true);
      });
    });
  });
});
