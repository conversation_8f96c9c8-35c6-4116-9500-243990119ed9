import React from 'react';
import { mount } from 'enzyme';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter } from 'react-router-dom';
import vesselService from '../../service/vessel-service';
import vesselList from '../resources/vesselListWithDefaultColumns.json';
import moment from 'moment';
import EnvironmentalReportsList from '../../component/EnvironmentalReports/EnvironmentalReportsList';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import MonthlyMarpolData from '../resources/monthly-marpol-reports-list.json';

describe('<EnvironmentalReports/>', () => {
  let environmentalReports;
  const roleConfig = {
    params: {
      view: true,
    },
    vessel: {
      edit: true,
      send: true,
    },
  };
  beforeEach(async () => {
    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: vesselList.data } }));
    vesselService.getMonthlyMarpolList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: MonthlyMarpolData }));
    environmentalReports = mount(
      <MemoryRouter initialEntries={['/vessel/report/environmental/marpol']}>
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <EnvironmentalReportsList />
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(environmentalReports);
  });

  describe('should check component', () => {
    it(`should render tab component`, async () => {
      expect(environmentalReports.find('TabWrapper').exists()).toEqual(true);
    });
    it('should render filter component', async () => {
      expect(
        environmentalReports.find('EnvironmentalReportsFilter').find('CustomDatePicker').exists(),
      ).toEqual(true);
      expect(environmentalReports.find('VesselDropDown').exists()).toEqual(true);
    });
  });

  describe('environmental report date pickers when page loads', () => {
    it('should render last month same date to current date when navigate from sidebar', async () => {
      const startDate = environmentalReports
        .find('EnvironmentalReportsFilter')
        .find('.startDatePicker')
        .at(1)
        .props().children[0].props.value;

      const endDate = environmentalReports
        .find('EnvironmentalReportsFilter')
        .find('.endDatePicker')
        .at(1)
        .props().children[0].props.value;

      expect(startDate).toEqual(moment().subtract(1, 'months').format('YYYY-MM-DD'));
      expect(endDate).toEqual(moment().format('YYYY-MM-DD'));
    });

    it('should render year start date to current date when navigate from details page', async () => {
      vesselService.getAllVessels = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: { results: vesselList.data } }));
      vesselService.getMonthlyMarpolList = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: MonthlyMarpolData }));
      const environmentalReportsRoute = mount(
        <MemoryRouter
          initialEntries={['/vessel/report/environmental/marpol?vessel_ownership_id=1']}
        >
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <EnvironmentalReportsList />
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(environmentalReportsRoute);

      const startDate = environmentalReportsRoute
        .find('EnvironmentalReportsFilter')
        .find('.startDatePicker')
        .find('.react-datepicker__input-container input')
        .props().value;

      const endDate = environmentalReportsRoute
        .find('EnvironmentalReportsFilter')
        .find('.endDatePicker')
        .at(1)
        .props().children[0].props.value;

      expect(moment(startDate).format('YYYY-MM-DD')).toEqual(
        moment().startOf('year').format('YYYY-MM-DD'),
      );

      expect(moment(endDate).format('YYYY-MM-DD')).toEqual(moment().format('YYYY-MM-DD'));
    });
  });
});
