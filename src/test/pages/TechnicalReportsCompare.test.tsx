import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import PositionReports from '../resources/position-reports.json';
import technicalMapperData from '../resources/technical-compare-mapper-data.json';
import { MemoryRouter, Route } from 'react-router';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';
import TechnicalReportsCompare from '../../pages/TechnicalReportsCompare';
import ControlParametersData from '../resources/control-parameters-data.json';
import VesselContextProvider from '../../context/VesselContext';
import vesselList from '../resources/vesselListWithDefaultColumns.json';

describe('<TechnicalReportsCompare />', () => {
  let technicalReportsCompare;
  const roleConfig = {
    vessel: {
      view: true,
      create: true,
      edit: true,
      editReport: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };
  beforeEach(async () => {
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: PositionReports }));
    vesselService.getReportDataMapper = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: technicalMapperData }));
    vesselService.getContolParameters = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ControlParametersData }));
    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: vesselList.data } }));

    technicalReportsCompare = mount(
      <MemoryRouter
        initialEntries={['/vessel/report/technical/position/126/compare?gmt=2023-02-24']}
      >
        <Route path={'/vessel/report/technical/:report/:ownershipId/compare'}>
          <VesselContextProvider roleConfig={roleConfig}>
            <TechnicalReportContextProvider>
              <TechnicalReportsCompare />
            </TechnicalReportContextProvider>
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(technicalReportsCompare);
  });

  it('should render edit icon on comparepage', async () => {
    const cellWrapper = technicalReportsCompare.find(
      '[data-testid="fml-compare-general-table-row-1-column-1"]',
    );
    cellWrapper.simulate('mouseover');
    expect(cellWrapper.find('.edit-icon').exists()).toEqual(true);
  });

  it('should render modal on edit icon click', async () => {
    const editIcon = technicalReportsCompare.find('[data-testid="fml-compare-edit-general-1-1"]');
    editIcon.props().onClick();
    await updateWrapper(technicalReportsCompare);
    const editModal = technicalReportsCompare.find('[data-testid="fml-custom-edit-modal"]');
    const saveButton = technicalReportsCompare.find(
      'button[data-testid="fml-custom-info-modal-confirm-button"]',
    );
    const cancelButton = technicalReportsCompare.find(
      'button[data-testid="fml-custom-info-modal-cancel-button"]',
    );
    expect(editModal.exists()).toEqual(true);
    expect(saveButton.exists()).toEqual(true);
    expect(cancelButton.exists()).toEqual(true);
  });

  const renderEditModal = async (id) => {
    const editIcon = technicalReportsCompare.find(`[data-testid="${id}"]`);
    editIcon.props().onClick();
    await updateWrapper(technicalReportsCompare);
    return technicalReportsCompare.find('[data-testid="fml-custom-edit-modal"]');
  };

  it('should render modal of type datetime', async () => {
    const editModal = await renderEditModal('fml-compare-edit-general-1-1');
    const datepicker = editModal.find('.react-datepicker-wrapper');
    expect(datepicker.exists()).toEqual(true);
  });

  it('should render modal of type number', async () => {
    const editModal = await renderEditModal('fml-compare-edit-general-11-1');
    const numberField = editModal.find('input[type="number"]');
    expect(numberField.exists()).toEqual(true);
  });

  it('should render modal of type typeahead', async () => {
    const editModal = await renderEditModal('fml-compare-edit-consumption-1-1');
    const typeAheadInput = editModal.find('input[type="text"]');
    const typeAheadParentClass = editModal.find('.rbt');
    expect(typeAheadInput.exists()).toEqual(true);
    expect(typeAheadParentClass.exists()).toEqual(true);
  });

  it('should render modal of type radio', async () => {
    const editModal = await renderEditModal('fml-compare-edit-other-3-1');
    const radioInput = editModal.find('input[type="radio"]');
    expect(radioInput.exists()).toEqual(true);
    expect(radioInput.length).toEqual(2);
  });

  it('should render modal of type textarea', async () => {
    const editModal = await renderEditModal('fml-compare-edit-other-27-1');
    const textAreaInput = editModal.find('textarea');
    expect(textAreaInput.exists()).toEqual(true);
  });

  it('should render modal of type country port', async () => {
    const editModal = await renderEditModal('fml-compare-edit-general-5-1');
    const countryInput = editModal.find('[data-testid="fml-custom-component-country"]');
    const portInput = editModal.find('[data-testid="fml-custom-component-port"]');
    const viewArea = editModal.find('[data-testid="fml-compare-reports-country-port-info"]');
    expect(countryInput.exists()).toEqual(true);
    expect(portInput.exists()).toEqual(true);
    expect(viewArea.exists()).toEqual(true);
  });

  it('should render modal of type co-ordinate latitude', async () => {
    const editModal = await renderEditModal('fml-compare-edit-general-3-1');
    const latitudeInputOne = editModal.find(
      '[data-testid="fml-custom-co-ordinate-input-latitude-1"]',
    );
    const latitudeInputTwo = editModal.find(
      '[data-testid="fml-custom-co-ordinate-input-latitude-2"]',
    );
    const latitudeDir = editModal.find('label').map((i) => i.text());
    expect(latitudeInputOne.exists()).toEqual(true);
    expect(latitudeInputTwo.exists()).toEqual(true);
    expect(latitudeDir).toEqual(['latitude', 'North', 'South']);
  });

  it('should render modal of type co-ordinate longitude', async () => {
    const editModal = await renderEditModal('fml-compare-edit-general-4-1');
    const longitudeInputOne = editModal.find(
      '[data-testid="fml-custom-co-ordinate-input-longitude-1"]',
    );
    const longitudeInputTwo = editModal.find(
      '[data-testid="fml-custom-co-ordinate-input-longitude-2"]',
    );
    const longitudeDir = editModal.find('label').map((i) => i.text());
    expect(longitudeInputOne.exists()).toEqual(true);
    expect(longitudeInputTwo.exists()).toEqual(true);
    expect(longitudeDir).toEqual(['longitude', 'East', 'West']);
  });

  it('should not render edit icon on comparepage if no edit role', async () => {
    roleConfig.vessel.editReport = false;
    technicalReportsCompare = mount(
      <MemoryRouter
        initialEntries={['/vessel/report/technical/position/126/compare?gmt=2023-02-24']}
      >
        <Route path={'/vessel/report/technical/:report/:ownershipId/compare'}>
          <VesselContextProvider roleConfig={roleConfig}>
            <TechnicalReportContextProvider>
              <TechnicalReportsCompare />
            </TechnicalReportContextProvider>
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(technicalReportsCompare);
    const cellWrapper = technicalReportsCompare.find(
      '[data-testid="fml-compare-general-table-row-1-column-1"]',
    );
    cellWrapper.simulate('mouseover');
    expect(cellWrapper.find('.edit-icon').exists()).toEqual(false);
  });
});
