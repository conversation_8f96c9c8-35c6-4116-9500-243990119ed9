import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper, wait } from '../../setupTests';
import { MemoryRouter, Route } from 'react-router-dom';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import vesselList from '../resources/vesselListWithDefaultColumns.json';
import SealManagement from '../../component/EnvironmentalReports/SealManagement';
import { affixed, logs, sealLists, unused } from '../resources/seal-list-data';
import EnvironmentalReportsList from '../../component/EnvironmentalReports/EnvironmentalReportsList';

const selectPage = async (page, page_num) => {
  page
    .find('div.page-num')
    .find('.page-num-enabled')
    .filterWhere((e) => Number(e.text()) === Number(page_num))
    .at(0)
    .simulate('click');
  await updateWrapper(page);
};

const roleConfig = {
  params: {
    view: true,
  },
  vessel: {
    edit: true,
    send: true,
  },
};
describe('<UnusedSeals/>', () => {
  let unUsedSealList;

  beforeEach(async () => {
    vesselService.getSealGroup = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: unused }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    unUsedSealList = mount(
      <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/unused']}>
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
            <SealManagement />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(unUsedSealList);
  });

  describe('should render unused seal list', () => {
    it('should render columns, when unused seal list is loaded', async () => {
      const customTable = unUsedSealList.find('CustomTable');
      const columnData = customTable.find('.th').map((i) => i.text());
      const actualSealList = columnData.filter((item) => {
        return item.length > 1 || item === '#';
      });
      const expectedHeaders = [
        '#',
        'First Seal#',
        'Last Seal#',
        'Type',
        'Location',
        'Status',
        'Delete Reason',
        'Date',
        'Action',
      ];
      expect(actualSealList).toEqual(expectedHeaders);
    });

    it('should render spinner, when unused seal list is on fetch', async () => {
      vesselService.getSealGroup = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      unUsedSealList = mount(
        <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/unused']}>
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
              <SealManagement />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(unUsedSealList);
      expect(unUsedSealList.find('Spinner').exists()).toEqual(true);
    });

    describe('unused seal list tab when page loads', () => {
      it('should unused seal list tab highlighted when page loads', async () => {
        const environmentalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/unused']}>
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
                <SealManagement />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(environmentalReports);
        const testData = environmentalReports.find('a.active');
        expect(testData.text()).toEqual('Unused Seals');
      });
    });

    describe('unused seal list pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        const page = mount(
          <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/unused']}>
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
                <SealManagement />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(page);
        await selectPage(page, 2);

        expect(findSelectedPageNumber(page)).toEqual(2);
        expect(findPageSizeValue(page)).toEqual(10);
      });
    });
  });
});
describe('<AffixedSeals/>', () => {
  let affixedSealList;

  beforeEach(async () => {
    vesselService.getSealLog = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: affixed }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    affixedSealList = mount(
      <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/affixed']}>
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
            <SealManagement />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(affixedSealList);
  });

  describe('should render affixed seal list', () => {
    it('should render columns, when affixed seal list is loaded', async () => {
      const customTable = affixedSealList.find('CustomTable');
      const columnData = customTable.find('.th').map((i) => i.text());
      const actualSealList = columnData.filter((item) => {
        return item.length > 1 || item === '#';
      });
      const expectedHeaders = [
        'Location#',
        'Sub-location',
        'Seal#',
        'System',
        'Description of Seal Location on System',
        'Component',
        'Component Size',
        'E/R Location (Vertical)',
        'E/R Location (Side)',
        'Seal Type',
        'Affixed Date',
        'Affixed Reason',
        'Approved by Master',
      ];
      expect(actualSealList).toEqual(expectedHeaders);
    });

    it('should render spinner, when affixed seal list is on fetch', async () => {
      vesselService.getSealLog = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      affixedSealList = mount(
        <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/affixed']}>
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
              <SealManagement />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(affixedSealList);
      expect(affixedSealList.find('Spinner').exists()).toEqual(true);
    });

    describe('affixed seal list tab when page loads', () => {
      it('should affixed seal list tab highlighted when page loads', async () => {
        const environmentalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/affixed']}>
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
                <SealManagement />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(environmentalReports);
        const testData = environmentalReports.find('a.active');
        expect(testData.text()).toEqual('Affixed Seals');
      });
      it('should highlight old seal row with light blue backgroud', async () => {
        const environmentalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/affixed']}>
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
                <SealManagement />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(environmentalReports);
        const testData = environmentalReports.find('div.highlight-row-light-blue');
        expect(testData.length).toEqual(2);
      });
    });

    describe('affixed seal list pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        const page = mount(
          <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/affixed']}>
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
                <SealManagement />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(page);
        await selectPage(page, 2);

        expect(findSelectedPageNumber(page)).toEqual(2);
        expect(findPageSizeValue(page)).toEqual(10);
      });
    });
  });
});
describe('<SealLogs/>', () => {
  let sealLogList;

  beforeEach(async () => {
    vesselService.getSealLog = jest.fn().mockImplementation(() => Promise.resolve({ data: logs }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    sealLogList = mount(
      <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/logs']}>
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
            <SealManagement />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(sealLogList);
  });

  describe('should render seal logs list', () => {
    it('should render columns, when seal logs list is loaded', async () => {
      const customTable = sealLogList.find('CustomTable');
      const columnData = customTable.find('.th').map((i) => i.text());
      const actualSealList = columnData.filter((item) => {
        return item.length > 1 || item === '#';
      });
      const expectedHeaders = ['First Seal#', 'Location', 'Status', 'Date'];
      expect(actualSealList).toEqual(expectedHeaders);
    });

    it('should render spinner, when seal logs list is on fetch', async () => {
      vesselService.getSealLog = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      sealLogList = mount(
        <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/logs']}>
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
              <SealManagement />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(sealLogList);
      expect(sealLogList.find('Spinner').exists()).toEqual(true);
    });

    describe('seal logs list tab when page loads', () => {
      it('should seal logs list tab highlighted when page loads', async () => {
        const environmentalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/environmental/seals/1/logs']}>
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/seals/:ownershipId/:tab'}>
                <SealManagement />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(environmentalReports);
        const testData = environmentalReports.find('a.active');
        expect(testData.text()).toEqual('Seal Logs');
      });
    });

    describe('seal logs list pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        await updateWrapper(sealLogList);
        await selectPage(sealLogList, 2);

        expect(findSelectedPageNumber(sealLogList)).toEqual(2);
        expect(findPageSizeValue(sealLogList)).toEqual(10);
      });
    });
  });
});
describe('<SealManagementList/>', () => {
  let sealList;

  beforeEach(async () => {
    vesselService.getSealList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: sealLists }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    sealList = mount(
      <MemoryRouter initialEntries={['/vessel/report/environmental/seal']}>
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/:tab'}>
            <EnvironmentalReportsList />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(sealList);
  });

  describe('should render vessel list in seal page', () => {
    it('should render columns, when vessel list is loaded', async () => {
      const customTable = sealList.find('CustomTable');
      const columnData = customTable.find('.th').map((i) => i.text());
      const actualSealList = columnData.filter((item) => {
        return item.length > 1 || item === '#';
      });
      const expectedHeaders = ['No.', 'Vessel', 'Unused Seals', 'Affixed Seals', 'Seal Log'];
      expect(actualSealList).toEqual(expectedHeaders);
    });

    it('should render spinner, when seal list is on fetch', async () => {
      vesselService.getSealList = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 3000)));
      vesselService.getAllVessels = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 3000)));
      const sealListData = mount(
        <MemoryRouter initialEntries={['/vessel/report/environmental/seal']}>
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/:tab'}>
              <EnvironmentalReportsList />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(sealListData);
      sealListData
        .find('input[data-testid="fml-seal-management-seal-number"]')
        .simulate('change', { target: { value: 'A360907' } });
      await wait(1000);
      await updateWrapper(sealListData);
      expect(sealListData.find('Spinner').exists()).toEqual(true);
    });

    it('should render columns for seals, when seal number is added in the filter', async () => {
      sealList
        .find('input[data-testid="fml-seal-management-seal-number"]')
        .simulate('change', { target: { value: 'A360907' } });
      await wait(1000);
      await updateWrapper(sealList);
      const customTable = sealList.find('CustomTable');
      const columnData = customTable.find('.th').map((i) => i.text());
      const actualSealList = columnData.filter((item) => {
        return item.length > 1 || item === '#';
      });
      const expectedHeaders = [
        'No.',
        'Vessel',
        'Unused Seals',
        'Affixed Seals',
        'Seal Log',
        'Seal #',
        'Type',
        'Location',
        'Status',
      ];
      expect(actualSealList).toEqual(expectedHeaders);
    });

    describe('seal tab when page loads', () => {
      it('should seal list tab highlighted when page loads', async () => {
        const environmentalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/environmental/seal']}>
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/:tab'}>
                <EnvironmentalReportsList />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(environmentalReports);
        const testData = environmentalReports.find('a.active');
        expect(testData.text()).toEqual('Seal Management');
      });
    });
  });
});
