import React from 'react';
import { mount } from 'enzyme';
import Details from '../../pages/Details';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselService from '../../service/vessel-service';
import * as vesselOwnershipService from '../../service/ownership-service';
import vesselApprovalService from '../../service/vessel-approval-service';
import { updateWrapper } from '../../setupTests';
import {
  activeButHandedOverNotStartedResponse,
  activePendingVessel,
  handedOverPendingVessel,
  handedOverButArchivalNotStartedResponse,
  archivalPendingVessel,
  editedVessel,
  activeStatusButInternallyHandedOverVessel,
  withNoOwnerEndDate,
  withOwnerEndDate,
} from '../resources/vessel';
import manualTypes from '../resources/manual-types-data.json';
import { changeLogData } from '../resources/change-log-data';
import { vesselList } from '../resources/vessel-with-ownership';
import { archivedVessel } from '../resources/vessel-approval';
import DetailContextProvider from '../../context/DetailContext';
import HistoryTable from '../../component/changeHistory/HistoryTable';
import ownerService from '../../service/owner-service';

export const getRoleConfig = (hasRole, viewRole = true) => {
  return {
    vessel: {
      view: viewRole,
      create: hasRole,
      edit: hasRole,
      viewApproval: hasRole,
      requestHandOver: hasRole,
      requestArchival: hasRole,
      changeFlag: hasRole,
      staff: {
        buyer: hasRole,
        accountant: hasRole,
        supdt: hasRole,
        qhse: hasRole,
        operation: hasRole,
        payroll: hasRole,
      },
    },
    techGroups: {
      manage: true,
    },
    approvalGroups: [],
    drills: {
      view: true,
      assign: true,
    },
    certificates: {
      manage: true,
      assign: true,
    },
    ownerReporting: {
      view: false,
      manage: false,
    },
  };
};

export const mockOtherCalls = (ownershipPending = false, canDoHandOver = true) => {
  vesselApprovalService.isVesselAllApproved = jest.fn().mockResolvedValue(true);
  vesselService.isAnyInputPending = jest.fn().mockResolvedValue({ data: false });
  vesselService.getAllVessels = jest.fn().mockResolvedValue({ data: vesselList });
  vesselService.getChangeLogs = jest.fn().mockImplementation({ data: changeLogData });
  vesselService.getManualTypes = jest.fn().mockResolvedValue({ data: manualTypes });
  ownerService.getVesselAccountDetails = jest.fn().mockResolvedValue({ data: {} });
  vesselService.canDoHandover = jest.fn().mockResolvedValue({
    status: 200,
    data: {
      canDoHandOver,
    },
  });
  vesselApprovalService.requestApprovalFor = jest.fn().mockResolvedValue({ status: 200, data: [] });
  jest
    .spyOn(vesselOwnershipService, 'isVesselOwnershipPending')
    .mockImplementation(() => Promise.resolve({ status: 200, data: ownershipPending }));
};

export const mockShipReports = () => {
  vesselService.getLastPosition = jest.fn().mockResolvedValue({
    status: 'fulfilled',
    value: {
      data: {
        position: '04959S1035147E',
        time: '2022-09-07T08:20:54',
        lat: '-0.8333333',
        lon: '103.8633333',
      },
    },
    data: {
      position: '04959S1035147E',
      time: '2022-09-07T08:20:54',
      lat: '-0.8333333',
      lon: '103.8633333',
    },
  });
  vesselService.getItinerary = jest.fn().mockResolvedValue({
    status: 'fulfilled',
    value: {
      data: {
        results: [],
        total: 0,
      },
    },
    data: {
      results: [],
      total: 0,
    },
  });
  vesselService.getTechnicalReports = jest.fn().mockResolvedValue({
    status: 'fulfilled',
    value: {
      data: {
        results: [],
        total: 0,
      },
    },
    data: {
      results: [],
      total: 0,
    },
  });
  vesselService.getSignedOnSeafarer = jest
    .fn()
    .mockResolvedValue({ status: 'fulfilled', value: { data: [] }, data: [] });
};

describe('<Details />', () => {
  const draftAlertMessage = 'This vessel page is a draft and pending for approval.';
  const handedOverPendingAlertMessage = 'This vessel page is pending for hand over.';
  const archivePendingAlertMessage = 'This vessel page is pending for archive.';
  const ownershipPendingAlertMessage = 'This vessel is changing ownership.';

  let details;
  beforeAll(async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockResolvedValue({ data: activePendingVessel[0] });
    mockOtherCalls(false);
    mockShipReports();

    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
  });

  describe('404 Error Page', () => {
    it('should render 404 error page on invalid vessel id', async () => {
      vesselService.getOwnershipVessel = jest.fn().mockResolvedValueOnce({ data: {} });
      const details = mount(
        <MemoryRouter>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Details />
          </DetailContextProvider>
        </MemoryRouter>,
      );

      await updateWrapper(details);
      expect(details.text().includes('404 Not Found')).toEqual(true);
    });

    it('should not render 404 error page on valid vessel id', async () => {
      vesselService.getOwnershipVessel = jest
        .fn()
        .mockResolvedValue({ data: activePendingVessel[0] });
      const details = mount(
        <MemoryRouter>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Details />
          </DetailContextProvider>
        </MemoryRouter>,
      );

      await updateWrapper(details);
      expect(details.text().includes('404 Not Found')).toEqual(false);
    });
  });

  it('should render draft message bar, if vessel status is draft', async () => {
    const draftAlert = details.find('Container .alert-info');
    expect(draftAlert.exists()).toEqual(true);
    expect(draftAlert.text()).toEqual(draftAlertMessage);
  });

  it('should not render draft message bar, when vessel pending status is null', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ data: activeButHandedOverNotStartedResponse[0] }),
      );
    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
    expect(details.find('#test__status-alert').exists()).toEqual(false);
  });

  it('should render ownership message bar, when vessel is changing ownership', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockResolvedValueOnce({ data: activeButHandedOverNotStartedResponse[0] });
    mockOtherCalls(true);
    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
    const ownershipAlert = details.find('.details_page__draft-alert .alert-info');
    expect(ownershipAlert.exists()).toEqual(true);
    expect(ownershipAlert.text()).toEqual(ownershipPendingAlertMessage);
  });

  it('should not render ownership message bar for handed over vessel view, when active vessel is changing ownership', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ data: activeStatusButInternallyHandedOverVessel[0] }),
      );
    mockOtherCalls(true);
    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
    const ownershipAlert = details.find('.details_page__draft-alert .alert-info');
    expect(ownershipAlert.exists()).toEqual(false);
  });

  it('should not render change ownership menu for internal handed over vessel', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ data: activeStatusButInternallyHandedOverVessel[0] }),
      );
    mockOtherCalls(true);
    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
    const ownershipAlert = details.find('.details_page__draft-alert .alert-info');
    expect(ownershipAlert.exists()).toEqual(false);
  });

  describe('EditedLabel', () => {
    it.skip('should show created_at if created_by_user_info is not provided', async () => {
      vesselService.getOwnershipVessel = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: editedVessel[0] }));
      mockOtherCalls();
      details = mount(
        <MemoryRouter initialEntries={['/vessel/details/1']}>
          <Route path="/vessel/details/:vesselId">
            <DetailContextProvider roleConfig={getRoleConfig(true)}>
              <Details />
            </DetailContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(details);

      const labels = details.find('EditedLabel');
      expect(labels.length).toEqual(1);

      const createdLabel = labels.at(0);
      expect(createdLabel.text()).toEqual('Created on 10 May 2021');
    });

    it.skip('should show the created_at and updated_at if only created_by_user_info and updated_by_user_info are provided', async () => {
      vesselService.getOwnershipVessel = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: editedVessel[1] }));
      mockOtherCalls();
      details = mount(
        <MemoryRouter initialEntries={['/vessel/details/1']}>
          <Route path="/vessel/details/:vesselId">
            <DetailContextProvider roleConfig={getRoleConfig(true)}>
              <Details />
            </DetailContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(details);

      const labels = details.find('EditedLabel');
      expect(labels.length).toEqual(2);

      const createdLabel = labels.at(0);
      const updatedLabel = labels.at(1);
      expect(createdLabel.text()).toEqual('Created on 10 May 2021');
      expect(updatedLabel.text()).toEqual('Updated on 11 May 2021');
    });

    it.skip('should show the fullnames if created_by_user_info and updated_by_user_info are provided', async () => {
      vesselService.getOwnershipVessel = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: editedVessel[2] }));
      mockOtherCalls();
      details = mount(
        <MemoryRouter initialEntries={['/vessel/details/1']}>
          <Route path="/vessel/details/:vesselId">
            <DetailContextProvider roleConfig={getRoleConfig(true)}>
              <Details />
            </DetailContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(details);

      const labels = details.find('EditedLabel');
      expect(labels.length).toEqual(2);

      const createdLabel = labels.at(0);
      const updatedLabel = labels.at(1);
      expect(createdLabel.text()).toEqual('Created by User A on 10 May 2021');
      expect(updatedLabel.text()).toEqual('Updated by User B on 11 May 2021');
    });
  });

  describe('Hand Over Approval Button', () => {
    beforeEach(async () => {
      vesselService.getOwnershipVessel = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: handedOverPendingVessel[0] }));
      mockOtherCalls();
      details = mount(
        <MemoryRouter initialEntries={['/vessel/details/1']}>
          <Route path="/vessel/details/:vesselId">
            <DetailContextProvider roleConfig={getRoleConfig(true)}>
              <Details />
            </DetailContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(details);
    });

    it('should be rendered, if vessel pending status is handed_over', async () => {
      const button = details
        .find('button')
        .filterWhere((btn) => btn.text() === 'Hand Over Approval');
      expect(button.exists()).toEqual(true);
    });

    it('should call route /vessel/details/:vesselId/approval on click event', async () => {
      const button = details
        .find('button')
        .filterWhere((btn) => btn.text() === 'Hand Over Approval');

      button.simulate('click');
      await updateWrapper(details);
      expect(details.find('Router').prop('history').location.pathname).toBe(
        '/vessel/details/1/approval',
      );
    });
  });

  it('should not render Approval button, if vessel pending status is null', async () => {
    mockOtherCalls();
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockResolvedValue({ data: activeButHandedOverNotStartedResponse });
    jest.spyOn(console, 'error').mockImplementation(() => {});
    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
    const handOverApprovalBtn = details.find('#test__approval-button');
    expect(handOverApprovalBtn.exists()).toEqual(false);
  });

  it('should render handover pending message bar, if vessel pending status is handed_over', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: handedOverPendingVessel[0] }));
    mockOtherCalls();
    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
    const draftAlert = details.find('Container .alert-info');
    expect(draftAlert.exists()).toEqual(true);
    expect(draftAlert.text()).toEqual(handedOverPendingAlertMessage);
  });

  it('should render archival pending message bar, if vessel pending status is archive', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: archivalPendingVessel[0] }));
    mockOtherCalls();
    jest.spyOn(console, 'error').mockImplementation(() => {});
    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
    const draftAlert = details.find('Container .alert-info');
    expect(draftAlert.exists()).toEqual(true);
    expect(draftAlert.text()).toEqual(archivePendingAlertMessage);
  });

  it('should render spinner, when vessel data not is set', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: null }));
    mockOtherCalls();
    jest.spyOn(console, 'error').mockImplementation(() => {});
    const details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );

    await updateWrapper(details);

    const spinner = details.find('Spinner');
    expect(spinner.exists()).toEqual(true);
  });

  it('should render vessel details, when vessel data is set', () => {
    const breadcrumb = details
      .find('li')
      .filterWhere((li) => li.prop('className') === 'breadcrumb-item breadcrumb-text active');
    expect(breadcrumb.text()).toEqual('Big Boat');
  });

  it('should render vessel details with shipReport section', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: withNoOwnerEndDate[0] }));
    mockShipReports();
    mockOtherCalls();
    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
    const shipReport = details.find('ShipReport');
    expect(shipReport.exists()).toEqual(true);
  });

  it('should render vessel details with no shipreport section', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: withOwnerEndDate[0] }));
    mockOtherCalls();
    details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(details);
    const shipReport = details.find('ShipReport');
    expect(shipReport.exists()).toEqual(false);
  });

  it('should render error message, on error recieved', async () => {
    vesselService.getOwnershipVessel = jest.fn().mockRejectedValueOnce(new Error());
    const details = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Details />
        </DetailContextProvider>
      </MemoryRouter>,
    );

    await updateWrapper(details);

    expect(details.find('ErrorAlert Alert').exists()).toEqual(true);
  });

  it('should call route /vessel/:vesselId/:ownershipId/takeover/basic on click of Edit button', async () => {
    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ data: activeButHandedOverNotStartedResponse[0] }),
      );

    mockOtherCalls();

    const details = mount(
      <MemoryRouter initialEntries={['/vessel/ownership/details/11']}>
        <Route path="/vessel/ownership/details/:ownershipId">
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Details />
          </DetailContextProvider>
        </Route>
      </MemoryRouter>,
    );

    await updateWrapper(details);
    const editBtn = details.find('button').filterWhere((btn) => btn.text() === 'Edit');
    editBtn.simulate('click');

    expect(details.find('Router').prop('history').location.pathname).toEqual(
      '/vessel/1/11/takeover/basic',
    );
  });

  describe('Button Toolbar', () => {
    beforeAll(async () => {
      vesselService.getOwnershipVessel = jest
        .fn()
        .mockImplementation(() =>
          Promise.resolve({ data: activeButHandedOverNotStartedResponse[0] }),
        );
      mockShipReports();
      mockOtherCalls();
      details = mount(
        <MemoryRouter>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Details />
          </DetailContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(details);
      clickOnMoreButton(details);
      await updateWrapper(details);
    });

    it('should have button toolbar', () => {
      expect(details.find('ButtonToolbar').exists()).toBe(true);
    });

    it('should have menus in toolbar with hand over', async () => {
      const allLinks = details.find('ButtonToolbar DropdownButton a');

      const links = ['Export to Excel', 'Print', 'Hand Over Vessel', 'Copy as New Vessel'];

      expect(allLinks.map((link) => link.text())).toEqual(expect.arrayContaining(links));
    });

    it('should open handed over model, on click of handed over link', async () => {
      clickOnMenuLink(details, 'Hand Over Vessel');
      await updateWrapper(details);
      const handedOverModal = details.find('Modal.action-modal');
      expect(handedOverModal.at(0).prop('show')).toBe(true);
    });

    it('should call vessel handover approval request, on click of modal confirm button', async () => {
      mockOtherCalls();
      const details = mount(
        <MemoryRouter initialEntries={['/vessel/ownership/details/1']}>
          <Route path="/vessel/ownership/details/:ownershipId">
            <DetailContextProvider roleConfig={getRoleConfig(true)}>
              <Details />
            </DetailContextProvider>
          </Route>
        </MemoryRouter>,
      );

      await updateWrapper(details);
      clickOnMoreButton(details);
      await updateWrapper(details);
      clickOnMenuLink(details, 'Hand Over Vessel');
      await updateWrapper(details);

      const modelButtons = details.find('ModalDialog ModalFooter Button');
      const confirmBtn = modelButtons.filterWhere((btn) => btn.text() === 'Confirm');
      confirmBtn.simulate('click');
      expect(vesselApprovalService.requestApprovalFor).toHaveBeenCalledWith(1, 'handed_over');
    });

    it('should not render Hand Over Vessel menu, when vessel status is active but it is internal handed over view', async () => {
      vesselService.getOwnershipVessel = jest
        .fn()
        .mockImplementation(() =>
          Promise.resolve({ data: activeStatusButInternallyHandedOverVessel[0] }),
        );

      mockOtherCalls();
      details = mount(
        <MemoryRouter>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Details />
          </DetailContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(details);
      clickOnMoreButton(details);
      await updateWrapper(details);

      const allLinks = details.find('ButtonToolbar DropdownButton a');
      expect(allLinks.contains('Hand Over Vessel')).toEqual(false);
    });

    it('should not render Hand Over Vessel menu, when vessel status is active', async () => {
      vesselService.getOwnershipVessel = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: activePendingVessel[0] }));

      mockOtherCalls();
      details = mount(
        <MemoryRouter>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Details />
          </DetailContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(details);
      clickOnMoreButton(details);
      await updateWrapper(details);

      const allLinks = details.find('ButtonToolbar DropdownButton a');
      expect(allLinks.contains('Hand Over Vessel')).toEqual(false);
    });

    it('should have archive vessel menu, when vessel status is handover and pending status is null', async () => {
      mockOtherCalls();
      vesselService.getOwnershipVessel = jest
        .fn()
        .mockResolvedValueOnce({ data: handedOverButArchivalNotStartedResponse[0] });
      details = mount(
        <MemoryRouter>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Details />
          </DetailContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(details);
      clickOnMoreButton(details);
      await updateWrapper(details);
      const allLinks = details.find('ButtonToolbar DropdownButton a');
      expect(allLinks.contains('Archive Vessel')).toEqual(true);
    });

    describe('role based view', () => {
      beforeAll(async () => {
        vesselService.getOwnershipVessel = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: activePendingVessel[0] }));
      });
      mockOtherCalls();
      it('should render error when user do not have role to view vessel', async () => {
        details = mount(
          <MemoryRouter>
            <DetailContextProvider roleConfig={getRoleConfig(false, false)}>
              <Details />
            </DetailContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(details);
        expect(details.text().includes('403 Access Denied')).toEqual(true);
      });

      describe('copy a vessel', () => {
        const copyButton = (page) =>
          page.find('a').filterWhere((anchor) => anchor.text() === 'Copy as New Vessel');
        it('should be displayed, when role config has create config true', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(true)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          clickOnMoreButton(details);
          await updateWrapper(details);
          expect(copyButton(details).exists()).toBe(true);
        });

        it('should not be displayed, when role config has create config false', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(false)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          clickOnMoreButton(details);
          await updateWrapper(details);

          expect(copyButton(details).exists()).toBe(false);
        });
      });
      describe('Edit vessel', () => {
        const editButton = (page) =>
          page.find('ButtonToolbar Button').filterWhere((btn) => btn.text() === 'Edit');

        it('should be displayed, when role config has edit config true', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(true)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          expect(editButton(details).exists()).toBe(true);
        });

        it.skip('should not displayed, when vessel status is handed_over/archived/handed_over vessel after ownership Change', async () => {
          const nonEditableVessels = [
            archivedVessel[0],
            handedOverButArchivalNotStartedResponse[0],
            activeStatusButInternallyHandedOverVessel[0],
          ];

          mockOtherCalls(false);
          const expectations = await Promise.all(
            nonEditableVessels.map(async (vessel) => {
              vesselService.getOwnershipVessel = jest
                .fn()
                .mockImplementation(() => Promise.resolve({ data: vessel }));

              const details = mount(
                <MemoryRouter>
                  <DetailContextProvider roleConfig={getRoleConfig(true)}>
                    <Details />
                  </DetailContextProvider>
                </MemoryRouter>,
              );
              await updateWrapper(details);
              return editButton(details).exists();
            }),
          );

          expect(expectations).toEqual([true, true, true]);
        });

        it('should not displayed, when ownership change request is pending for vessel', async () => {
          mockOtherCalls(true);
          vesselService.getOwnershipVessel = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve({ data: activeButHandedOverNotStartedResponse[0] }),
            );
          const details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(true)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          expect(editButton(details).exists()).toEqual(false);
        });

        it('should not be displayed, when role config has edit config false', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(false)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          expect(editButton(details).exists()).toBe(false);
        });
      });
      describe('Approval Details', () => {
        const approvalDetailsButton = (page) =>
          page.find('ButtonToolbar Button').filterWhere((btn) => btn.text() === 'Approval Details');
        it('should be displayed, when role config has viewApproval config true', async () => {
          vesselService.getOwnershipVessel = jest
            .fn()
            .mockImplementation(() => Promise.resolve({ data: activePendingVessel[0] }));

          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(true)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          expect(approvalDetailsButton(details).exists()).toBe(true);
        });

        it('should not be displayed, when role config has viewApproval config false', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(false)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);

          expect(approvalDetailsButton(details).exists()).toBe(false);
        });
      });
      describe('Archive vessel menu', () => {
        const archiveVesselMenu = (page) =>
          page.find('a').filterWhere((anchor) => anchor.text() === 'Archive Vessel');

        beforeAll(() => {
          vesselService.getOwnershipVessel = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve({ data: handedOverButArchivalNotStartedResponse[0] }),
            );

          mockOtherCalls();
        });
        it('should be displayed, when role config has requestArchival config true', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(true)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          clickOnMoreButton(details);
          await updateWrapper(details);

          expect(archiveVesselMenu(details).exists()).toBe(true);
        });

        it('should not be displayed, when role config has requestArchival config false', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(false)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          clickOnMoreButton(details);
          await updateWrapper(details);

          expect(archiveVesselMenu(details).exists()).toBe(false);
        });
      });
      describe('HandOver vessel menu', () => {
        const handOverVesselMenu = (page) =>
          page.find('a').filterWhere((anchor) => anchor.text() === 'Hand Over Vessel');

        beforeAll(() => {
          vesselService.getOwnershipVessel = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve({ data: activeButHandedOverNotStartedResponse[0] }),
            );

          mockOtherCalls();
        });
        it('should be displayed, when role config has requestHandOver config true', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(true)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          clickOnMoreButton(details);
          await updateWrapper(details);

          expect(handOverVesselMenu(details).exists()).toBe(true);
        });

        it('should not be displayed, when role config has requestHandOver config false', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(false)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          clickOnMoreButton(details);
          await updateWrapper(details);

          expect(handOverVesselMenu(details).exists()).toBe(false);
        });
      });
      describe('Hand Over vessel incase of inspection are pending', () => {
        beforeAll(() => {
          vesselService.getOwnershipVessel = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve({ data: activeButHandedOverNotStartedResponse[0] }),
            );

          mockOtherCalls(false, false);
        });
        it('should open hand over warning model, and display warning message', async () => {
          const vesseldetails = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(true)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(vesseldetails);
          clickOnMoreButton(vesseldetails);
          await updateWrapper(vesseldetails);
          clickOnMenuLink(vesseldetails, 'Hand Over Vessel');
          await updateWrapper(vesseldetails);
          const findByText = (wrapper, text) =>
            wrapper.findWhere((node) => node.type() !== 'Component' && node.text().includes(text));

          expect(
            findByText(
              vesseldetails,
              'The vessel cannot be handed over at the moment due to ongoing inspections',
            ).exists(),
          ).toEqual(true);
        });
      });

      describe('Flag change menu', () => {
        const flagChangeMenu = (page) =>
          page.find('a').filterWhere((anchor) => anchor.text() == 'Change Flag');

        beforeAll(() => {
          vesselService.getOwnershipVessel = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve({ data: activeButHandedOverNotStartedResponse[0] }),
            );

          mockOtherCalls();
        });

        it('should be displayed, when role config has create config true', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(true)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);
          clickOnMoreButton(details);
          await updateWrapper(details);
          expect(flagChangeMenu(details).exists()).toBe(true);
        });

        it('should not be displayed, when role config has create config false', async () => {
          details = mount(
            <MemoryRouter>
              <DetailContextProvider roleConfig={getRoleConfig(false)}>
                <Details />
              </DetailContextProvider>
            </MemoryRouter>,
          );
          await updateWrapper(details);

          clickOnMoreButton(details);
          await updateWrapper(details);

          expect(flagChangeMenu(details).exists()).toBe(false);
        });
      });
    });

    describe('should render change log modal, on button click under more menu', () => {
      beforeEach(async () => {
        details = mount(
          <MemoryRouter>
            <DetailContextProvider roleConfig={getRoleConfig(false)}>
              <Details />
            </DetailContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(details);

        clickOnMoreButton(details);
        await updateWrapper(details);
        clickOnMenuLink(details, 'Activity Log');
        await updateWrapper(details);
      });

      it('should render Activity Log heading, when dialog opens', async () => {
        const titleText = details
          .find('[data-testid="fml-change-history-modal"]')
          .find('.modal-title');
        expect(titleText.at(1).text()).toEqual(`${editedVessel[0].name} Activity Log`);
      });

      it('should render columns, when owner is selected in Activity Log dropdown ', async () => {
        details
          .find('input[data-testid="fml-change-history-dialog-dropdown"]')
          .simulate('change', { target: { type: 'owner', name: 'Ownership' } });
        await updateWrapper(details);

        details = mount(
          <MemoryRouter>
            <HistoryTable entity={{ type: 'owner' }} data={changeLogData} />
          </MemoryRouter>,
        );
        await updateWrapper(details);
        const customTable = details.find('[data-test-id="fml-change-history-custom-table"]');
        const ownerTable = customTable.find('.th').map((i) => i.text());
        const expectedHeaders = [
          'Date',
          'Updated By',
          'Owner',
          'Vessel Name',
          'Vessel Account Code',
        ];
        expect(ownerTable).toEqual(expectedHeaders);
      });

      it('should render columns, when flag is selected in Activity Log dropdown ', async () => {
        details
          .find('input[data-testid="fml-change-history-dialog-dropdown"]')
          .simulate('change', { target: { type: 'flag', name: 'Flag' } });
        await updateWrapper(details);

        details = mount(
          <MemoryRouter>
            <HistoryTable entity={{ type: 'flag' }} data={changeLogData} />
          </MemoryRouter>,
        );
        await updateWrapper(details);
        const customTable = details.find('[data-test-id="fml-change-history-custom-table"]');
        const ownerTable = customTable.find('.th').map((i) => i.text());
        const expectedHeaders = [
          'Date',
          'Updated By',
          'Country',
          'Port of Registry',
          'Flag Office',
          'Call sign',
        ];
        expect(ownerTable).toEqual(expectedHeaders);
      });

      it('should render columns, when register owner is selected in Activity Log dropdown ', async () => {
        details
          .find('input[data-testid="fml-change-history-dialog-dropdown"]')
          .simulate('change', { target: { type: 'register_owner', name: 'Registered Ownership' } });
        await updateWrapper(details);

        details = mount(
          <MemoryRouter>
            <HistoryTable entity={{ type: 'register_owner' }} data={changeLogData} />
          </MemoryRouter>,
        );
        await updateWrapper(details);
        const customTable = details.find('[data-test-id="fml-change-history-custom-table"]');
        const regOwnerTable = customTable.find('.th').map((i) => i.text());
        const expectedHeaders = [
          'Date',
          'Updated By',
          'Registered Owner',
          'Vessel Name',
          'Vessel Account Code',
        ];
        expect(regOwnerTable).toEqual(expectedHeaders);
      });

      it('should render columns, when tech group is selected in Activity Log dropdown ', async () => {
        details
          .find('input[data-testid="fml-change-history-dialog-dropdown"]')
          .simulate('change', { target: { type: 'tech_group', name: 'Tech Group' } });
        await updateWrapper(details);

        details = mount(
          <MemoryRouter>
            <HistoryTable entity={{ type: 'tech_group' }} data={changeLogData} />
          </MemoryRouter>,
        );
        await updateWrapper(details);
        const customTable = details.find('[data-test-id="fml-change-history-custom-table"]');
        const techGroupTable = customTable.find('.th').map((i) => i.text());
        const expectedHeaders = [
          'Date',
          'Updated By',
          'Tech Group',
          'Group Head',
          'Superintendent',
        ];
        expect(techGroupTable).toEqual(expectedHeaders);
      });

      it('should render columns, when buyer is selected in Activity Log dropdown ', async () => {
        details
          .find('input[data-testid="fml-change-history-dialog-dropdown"]')
          .simulate('change', { target: { type: 'buyer', name: 'Buyers' } });
        await updateWrapper(details);

        details = mount(
          <MemoryRouter>
            <HistoryTable entity={{ type: 'buyer' }} data={changeLogData} />
          </MemoryRouter>,
        );
        await updateWrapper(details);
        const customTable = details.find('[data-test-id="fml-change-history-custom-table"]');
        const buyerTable = customTable.find('.th').map((i) => i.text());
        const expectedHeaders = ['Date', 'Updated By', 'Buyer', 'Lead Buyer', 'Senior Lead Buyer'];
        expect(buyerTable).toEqual(expectedHeaders);
      });

      it('should render columns, when accountant is selected in Activity Log dropdown ', async () => {
        details
          .find('input[data-testid="fml-change-history-dialog-dropdown"]')
          .simulate('change', { target: { type: 'accountant', name: 'Vessel Accountant' } });
        await updateWrapper(details);

        details = mount(
          <MemoryRouter>
            <HistoryTable entity={{ type: 'accountant' }} data={changeLogData} />
          </MemoryRouter>,
        );
        await updateWrapper(details);
        const customTable = details.find('[data-test-id="fml-change-history-custom-table"]');
        const accountantTable = customTable.find('.th').map((i) => i.text());
        const expectedHeaders = ['Date', 'Updated By', 'Vessel Name', 'Primary', 'Secondary'];
        expect(accountantTable).toEqual(expectedHeaders);
      });

      it('should render columns, when payroll is selected in Activity Log dropdown ', async () => {
        details
          .find('input[data-testid="fml-change-history-dialog-dropdown"]')
          .simulate('change', { target: { type: 'payroll', name: 'Payroll Accountant' } });
        await updateWrapper(details);

        details = mount(
          <MemoryRouter>
            <HistoryTable entity={{ type: 'payroll' }} data={changeLogData} />
          </MemoryRouter>,
        );
        await updateWrapper(details);
        const customTable = details.find('[data-test-id="fml-change-history-custom-table"]');
        const payrollTable = customTable.find('.th').map((i) => i.text());
        const expectedHeaders = ['Date', 'Updated By', 'Vessel Name', 'Primary', 'Secondary'];
        expect(payrollTable).toEqual(expectedHeaders);
      });

      it('should render columns, when operation is selected in Activity Log dropdown ', async () => {
        details
          .find('input[data-testid="fml-change-history-dialog-dropdown"]')
          .simulate('change', { target: { type: 'operation', name: 'Operation Manager' } });
        await updateWrapper(details);

        details = mount(
          <MemoryRouter>
            <HistoryTable entity={{ type: 'operation' }} data={changeLogData} />
          </MemoryRouter>,
        );
        await updateWrapper(details);
        const customTable = details.find('[data-test-id="fml-change-history-custom-table"]');
        const operationTable = customTable.find('.th').map((i) => i.text());
        const expectedHeaders = ['Date', 'Updated By', 'Operation Manager'];
        expect(operationTable).toEqual(expectedHeaders);
      });

      it('should render columns, when qhse is selected in Activity Log dropdown ', async () => {
        details
          .find('input[data-testid="fml-change-history-dialog-dropdown"]')
          .simulate('change', { target: { type: 'qhse', name: 'QHSE Manager' } });
        await updateWrapper(details);

        details = mount(
          <MemoryRouter>
            <HistoryTable entity={{ type: 'qhse' }} data={changeLogData} />
          </MemoryRouter>,
        );
        await updateWrapper(details);
        const customTable = details.find('[data-test-id="fml-change-history-custom-table"]');
        const qhseTable = customTable.find('.th').map((i) => i.text());
        const expectedHeaders = ['Date', 'Updated By', 'QHSE Manager'];
        expect(qhseTable).toEqual(expectedHeaders);
      });
    });

    function clickOnMoreButton(page) {
      const moreButton = page
        .find('ButtonToolbar button')
        .filterWhere((btn) => btn.text() === 'More');
      moreButton.simulate('click');
    }

    function clickOnMenuLink(page, menuName) {
      const allLinks = page.find('ButtonToolbar DropdownButton a');
      const menuLink = allLinks.filterWhere((link) => link.text() === menuName);
      menuLink.simulate('click');
    }
  });
});
