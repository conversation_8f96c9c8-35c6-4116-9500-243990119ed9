import React from 'react';
import { mount } from 'enzyme';
import VesselItineraryList from '../../pages/VesselItineraryList';
import Details from '../../pages/Details';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import ItineraryList from '../resources/ItineraryListDefaultColumns.json';
import { MemoryRouter, Route } from 'react-router-dom';
import { columns } from '../../component/ItineraryList/ItineraryTable';
import { activePendingVessel } from '../resources/vessel';
import DetailContextProvider from '../../context/DetailContext';

describe('<VesselItineraryList />', () => {
  let vesselItineraryList;

  beforeEach(async () => {
    vesselService.getItinerary = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ItineraryList }));

    vesselItineraryList = mount(
      <MemoryRouter initialEntries={['vessel/ownership/details/1409/itinerary']}>
        <Route path="vessel/ownership/details/:ownershipId">
          <VesselItineraryList ownershipId={1409} />
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(vesselItineraryList);
  });

  describe('should render itinerary list page', () => {
    it('should render columns, when itinerary list loads', async () => {
      const ItineraryTable = vesselItineraryList.find('ItineraryTable');
      const actualItinerary = ItineraryTable.find('.itinerary-table-th').map((i) => i.text());
      expect(actualItinerary).toEqual(columns.map((i) => i.Header));
    });

    it('should render spinner, when itinerary list is on fetch', async () => {
      vesselService.getItinerary = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));

      vesselItineraryList = mount(
        <MemoryRouter initialEntries={['vessel/ownership/details/1409/itinerary']}>
          <Route path="vessel/ownership/details/:ownershipId">
            <VesselItineraryList ownershipId={1409} />
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(vesselItineraryList);
      expect(vesselItineraryList.find('Spinner').exists()).toEqual(true);
    });

    describe('Itinerary Advanced search tests', () => {
      it('should display advanced search overlay component', async () => {
        const searchBtn = vesselItineraryList.find('#dropdown-advanced-search');
        searchBtn.at(0).simulate('click');

        await updateWrapper(vesselItineraryList);
        expect(vesselItineraryList.text().includes('Filter Itinerary')).toEqual(true);
        expect(vesselItineraryList.find('.form-check-input').exists()).toEqual(true);

        vesselItineraryList.find('.advanced_search').find('input').at(0).simulate('click');

        await updateWrapper(vesselItineraryList);
        expect(vesselItineraryList.text().includes('Country')).toEqual(true);
      });

      it('should display results based on searched keyword', async () => {
        vesselItineraryList
          .find('#search-bar')
          .at(0)
          .simulate('change', { target: { value: 'Gemlik' } });
        await updateWrapper(vesselItineraryList);
        expect(vesselItineraryList.text().includes('Gemlik')).toEqual(true);
      });
    });
  });
});

describe('should render itinerary tab when page loads', () => {
  it('when page loads itinerary tab is highlighted', async () => {
    vesselService.isVesselAllApproved = jest.fn().mockImplementation(() => Promise.resolve(true));

    vesselService.isAnyInputPending = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: false }));

    vesselService.getOwnershipVessel = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: activePendingVessel[0] }));

    vesselService.getItinerary = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ItineraryList }));

    const getRoleConfig = (hasRole, viewRole = true) => {
      return {
        vessel: {
          view: viewRole,
          create: hasRole,
          edit: hasRole,
          viewApproval: hasRole,
          requestHandOver: hasRole,
          requestArchival: hasRole,
          staff: {
            buyer: true,
            accountant: true,
            supdt: true,
            qhse: true,
            operation: true,
            payroll: true,
          },
        },
        approvalGroups: [],
        drills: {
          view: true,
          assign: true,
        },
        certificates: {
          manage: true,
          assign: true,
        },
        ownerReporting: {
          view: false,
          manage: false,
        },
        techGroups:{
          manage: true,
        },
      };
    };
    const details = mount(
      <MemoryRouter initialEntries={['/vessel/ownership/details/1409/itinerary']}>
        <Route path="/vessel/ownership/details/:ownershipId/:step?">
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Details />
          </DetailContextProvider>
        </Route>
      </MemoryRouter>,
    );

    await updateWrapper(details);
    const testData = details.find('a.active');
    expect(testData.text()).toEqual('Itinerary');
  });
});
