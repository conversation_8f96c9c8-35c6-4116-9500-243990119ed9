import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import PositionReports from '../resources/position-reports.json';
import technicalMapperData from '../resources/technical-compare-mapper-data.json';
import { MemoryRouter, Route } from 'react-router-dom';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';
import TechnicalReportsCompare from '../../pages/TechnicalReportsCompare';
import ControlParametersData from '../resources/control-parameters-data.json';
import VesselContextProvider from '../../context/VesselContext';
import vesselList from '../resources/vesselListWithDefaultColumns.json';

describe('<PositionReportsCompare />', () => {
  let positionReportsCompare;
  const roleConfig = {
    vessel: {
      view: true,
      create: true,
      edit: true,
      editReport: true,
      send: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };
  beforeEach(async () => {
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: PositionReports }));
    vesselService.getReportDataMapper = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: technicalMapperData }));
    vesselService.getContolParameters = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ControlParametersData }));
    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: vesselList.data } }));

    positionReportsCompare = mount(
      <MemoryRouter
        initialEntries={['/vessel/report/technical/position/126/compare?gmt=2023-02-24']}
      >
        <Route path={'/vessel/report/technical/:report/:ownershipId/compare'}>
          <VesselContextProvider roleConfig={roleConfig}>
            <TechnicalReportContextProvider>
              <TechnicalReportsCompare />
            </TechnicalReportContextProvider>
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(positionReportsCompare);
  });
  const openSendEmailModal = async () => {
    positionReportsCompare
      .find('button[data-testid="fml-compare-report-Send-button"]')
      .at(0)
      .simulate('click');
    await updateWrapper(positionReportsCompare);
  };

  it('should render columns, when compare loads', async () => {
    const PositionReportHeaders = positionReportsCompare
      .find('.vessel-table .th')
      .map((i) => i.text());
    const expected = [
      'GENERAL INFORMATION',
      'CONSUMPTION SINCE LAST REPORT',
      'TOTAL CONSUMPTION SINCE LAST REPORT',
      'WEATHER',
      'OTHERS',
    ];
    expect(PositionReportHeaders).toEqual(expect.arrayContaining(expected));
  });

  it('should render spinner, when compare is on fetch', async () => {
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 100000)));
    positionReportsCompare = mount(
      <MemoryRouter
        initialEntries={['/vessel/report/technical/position/126/compare?gmt=2023-02-24']}
      >
        <Route path={'/vessel/report/technical/:report/:ownershipId/compare'}>
          <VesselContextProvider roleConfig={roleConfig}>
            <TechnicalReportContextProvider>
              <TechnicalReportsCompare />
            </TechnicalReportContextProvider>
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(positionReportsCompare);
    expect(positionReportsCompare.find('Spinner').exists()).toEqual(true);
  });

  it('should have send button', async () => {
    expect(
      positionReportsCompare.find('button[data-testid="fml-compare-report-Send-button"]').exists(),
    ).toEqual(true);
  });

  it('should open send email modal on send button click', async () => {
    await openSendEmailModal();
    expect(positionReportsCompare.find('div.send-email-modal').exists()).toEqual(true);
  });

  it('should throw error on empty fields', async () => {
    await openSendEmailModal();
    positionReportsCompare
      .find('input[data-testid="fml-sendEmail-subject"]')
      .simulate('change', { target: { value: '' } });
    positionReportsCompare
      .find('textarea[data-testid="fml-sendEmail-recipient"]')
      .simulate('change', { target: { value: '' } });
    positionReportsCompare
      .find('textarea[data-testid="fml-sendEmail-message"]')
      .simulate('change', { target: { value: '' } });
    await updateWrapper(positionReportsCompare);
    positionReportsCompare.find('button[data-testid="fml-sendEmail-send"]').simulate('click');
    await updateWrapper(positionReportsCompare);
    expect(positionReportsCompare.find('div.invalid-feedback').length === 3).toEqual(true);
  });
});
