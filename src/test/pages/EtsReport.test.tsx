import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter, Route } from 'react-router-dom';
import EnvironmentalReports from '../../component/EnvironmentalReports/EnvironmentalReportsList';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import EtsReportData from '../resources/ets-report-list.json';
import vesselList from '../resources/vesselListWithDefaultColumns.json';

describe('<ETSReport />', () => {
  let etsReportList;
  const roleConfig = {
    params: {
      view: true,
    },
    vessel: {
      edit: true,
      send: true,
      environmental: {
        editEuEtsReport: true,
      },
    },
  };
  beforeEach(async () => {
    vesselService.getETSReport = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: EtsReportData }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    vesselService.getOwnershipVessel = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data[0] },
      }),
    );
    vesselService.getReportEnabledDatasourceConfigs = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: []
      }),
    );
    etsReportList = mount(
      <MemoryRouter
        initialEntries={[
          '/vessel/report/environmental/ets?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
        ]}
      >
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/:tab'}>
            <EnvironmentalReports />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(etsReportList);
  });

  describe('should render ets report', () => {
    it('should render columns, when ets voyage report is loaded', async () => {
      const customTable = etsReportList.find('CustomTable');
      const actualEtsVoyageReports = customTable
        .at(0)
        .find('.th')
        .map((i) => i.text());
      const expectedHeaders = [
        'Voyage No.',
        'Port of Departure',
        'Port of Arrival',
        'Leg Start / Leg End',
        'Cargo Carried (tons)',
        'Total CO2 Emitted (tons)',
        'Total Energy Consumed (MJ)',
        'Compliance Balance (tCO2eq)',
        'Total Fuel Consumed (tons)',
        'Verifier Name',
        '2022 EU Allowance',
        'Estimate FuelEU Penalty (€)',
        'Verification Status',
        'Action',
      ];
      expect(actualEtsVoyageReports).toEqual(expectedHeaders);
    });

    it('should render columns, when ets pending voyage report is loaded', async () => {
      const customTable = etsReportList.find('CustomTable');
      const actualEtsPortReports = customTable
        .at(1)
        .find('.th')
        .map((i) => i.text());
      const expectedHeaders = [
        'Voyage No.',
        'Port of Departure',
        'Port of Arrival',
        'Leg Start / End Date',
        'Cargo Carried (tons)',
        'Total CO2 Emitted (tons)',
        'Total Energy Consumed (MJ)',
        'Compliance Balance (tCO2eq)',
        'Total Fuel Consumed (tons)',
        'Verifier Name',
        '2022 EU Allowance',
        'Estimate FuelEU Penalty (€)',
        'Action',
      ];
      expect(actualEtsPortReports).toEqual(expectedHeaders);
    });
    it('should render columns, when ets port report is loaded', async () => {
      const customTable = etsReportList.find('CustomTable');
      const actualEtsPortReports = customTable
        .at(2)
        .find('.th')
        .map((i) => i.text());
      const expectedHeaders = [
        'Voyage No.',
        'Port of Departure',
        'Arrival in Port (date)',
        'Departure from Port (date)',
        'Total CO2 Emitted (tons)',
        'Total Energy Consumed (MJ)',
        'Compliance Balance (tCO2eq)',
        'Total Fuel Consumed (tons)',
        'Verifier Name',
        '2022 EU Allowance',
        'Estimate FuelEU Penalty (€)',
      ];
      expect(actualEtsPortReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when ets Reports is on fetch', async () => {
      vesselService.getETSReport = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      etsReportList = mount(
        <MemoryRouter
          initialEntries={[
            '/vessel/report/environmental/ets?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
          ]}
        >
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/:tab'}>
              <EnvironmentalReports />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(etsReportList);
      expect(etsReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('ets report tab when page loads', () => {
      it('should ets report tab highlighted when page loads', async () => {
        const environmentalReports = mount(
          <MemoryRouter
            initialEntries={[
              '/vessel/report/environmental/ets?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
            ]}
          >
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/:tab'}>
                <EnvironmentalReports />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(environmentalReports);
        const testData = environmentalReports.find('a.active');
        expect(testData.text()).toEqual('EU Emission Reports');
      });
    });
  });
});