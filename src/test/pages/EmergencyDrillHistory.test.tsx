import { mount } from 'enzyme';
import moment from 'moment';
import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import DetailContextProvider from '../../context/DetailContext';
import EmergencyDrillHistoryPage from '../../pages/EmergencyDrillHistory';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { drills, history } from '../resources/emergency-drills';

describe('<EmergencyDrillHistory />', () => {
  let emergencyDrillHistory;
  beforeAll(async () => {
    vesselService.getEmergencyDrillList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: drills }));
    vesselService.getDrillHistory = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: history }));
    emergencyDrillHistory = mount(
      <MemoryRouter initialEntries={['vessel/emergency-drills/history/126']}>
        <Route path="vessel/emergency-drills/history/:ownershipId">
          <DetailContextProvider>
            <EmergencyDrillHistoryPage />
          </DetailContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(emergencyDrillHistory);
  });

  it('should render spinner, when drill list is on fetch and filters to be disabled', async () => {
    const list = mount(
      <MemoryRouter>
        <DetailContextProvider>
          <EmergencyDrillHistoryPage />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    expect(list.find('Spinner').exists()).toEqual(true);
    expect(list.find('[data-testid="fml-drillHistory-startDate"]').props()['disabled']).toBe(true);
    expect(list.find('[data-testid="fml-drillHistory-endDate"]').props()['disabled']).toBe(true);
  });

  it('should enable filters after data load', async () => {
    expect(emergencyDrillHistory.find('[data-testid="No.-0-exp"]').exists()).toEqual(true);
    expect(
      emergencyDrillHistory.find('[data-testid="fml-drillHistory-startDate"]').props()['disabled'],
    ).toBe(false);
    expect(
      emergencyDrillHistory.find('[data-testid="fml-drillHistory-endDate"]').props()['disabled'],
    ).toBe(false);
  });

  it('should change active page num on page number click', async () => {
    const page1 = emergencyDrillHistory.find('[data-testid="fml-pagination-1"]');
    expect(page1.props()['className']).toContain('page-num-active');
    const page2 = emergencyDrillHistory.find('[data-testid="fml-pagination-2"]');
    page2.simulate('click');
    await updateWrapper(emergencyDrillHistory);
    expect(
      emergencyDrillHistory.find('[data-testid="fml-pagination-2"]').props()['className'],
    ).toContain('page-num-active');
  });

  it('should contain export and print button', async () => {
    expect(
      emergencyDrillHistory
        .find('[data-testid="fml-emergencyDrillHistory-exportToExcel"]')
        .exists(),
    ).toEqual(true);
    expect(
      emergencyDrillHistory.find('[data-testid="fml-emergencyDrillHistory-print"]').exists(),
    ).toEqual(true);
  });

  it('should have default date selected in filters', async () => {
    expect(
      emergencyDrillHistory.find('input[data-testid="fml-drillHistory-startDate"]').prop('value'),
    ).toEqual(moment().add(-1, 'years').format('D MMM YYYY'));
    expect(
      emergencyDrillHistory.find('input[data-testid="fml-drillHistory-endDate"]').prop('value'),
    ).toEqual(moment().format('D MMM YYYY'));
  });

  it('should reset page to 1 on filter change', async () => {
    emergencyDrillHistory.find('[data-testid="fml-pagination-2"]').simulate('click');
    await updateWrapper(emergencyDrillHistory);
    expect(
      emergencyDrillHistory.find('[data-testid="fml-pagination-2"]').props()['className'],
    ).toContain('page-num-active');
    emergencyDrillHistory
      .find('input[data-testid="fml-drillHistory-startDate"]')
      .simulate('change', { target: { value: '' } });
    await updateWrapper(emergencyDrillHistory);
    expect(
      emergencyDrillHistory.find('[data-testid="fml-pagination-1"]').props()['className'],
    ).toContain('page-num-active');
  });

  it('should have drill selected in filter when query param passed', async () => {
    const drillId = 4;
    const emergencyDrillHistory = mount(
      <MemoryRouter initialEntries={[`vessel/emergency-drills/history/126?drill_id=${drillId}`]}>
        <Route path="vessel/emergency-drills/history/:ownershipId">
          <DetailContextProvider>
            <EmergencyDrillHistoryPage />
          </DetailContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(emergencyDrillHistory);
    expect(
      emergencyDrillHistory.find('input[data-testid="fml-emergencyDrill-dropdown"]').prop('value'),
    ).toEqual(drills.find((drill) => drill.id === drillId).name);
  });
});
