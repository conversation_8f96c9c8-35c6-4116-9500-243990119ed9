import React from 'react';
import { mount } from 'enzyme';
import Takeover from '../../pages/Takeover';
import { MemoryRouter } from 'react-router-dom';
import { updateWrapper } from '../../setupTests';

jest.mock('../../controller/take-over-controller', () => {
  return jest.fn().mockImplementation(() => {
    return {
      onLoadPage: () => {
        return Promise.resolve({
          vessel: { status: 404 },
          dropDownData: {},
          isAllApproved: false,
          userMemberships: [],
          distinctFieldData: { vessel_short_code: [], vessel_account_code_new: [] },
        });
      },
    };
  });
});

describe('<Takeover />', () => {
  const getRoleConfig = (hasRole) => {
    return {
      vessel: {
        create: hasRole,
        edit: hasRole,
      },
    };
  };

  it('should 404 page when server response is 404', async () => {
    const takeover = mount(
      <MemoryRouter>
        <Takeover username="test.user" roleConfig={getRoleConfig(true)} />
      </MemoryRouter>,
    );

    await updateWrapper(takeover);
    expect(takeover.find('ErrorPage').prop('errorCode')).toEqual(404);
  });
});
