import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter, Route } from 'react-router-dom';
import EnvironmentalReports from '../../component/EnvironmentalReports/EnvironmentalReportsList';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import WasteStreamReportData from '../resources/waste-stream-report.list.json';
import vesselList from '../resources/vesselListWithDefaultColumns.json';

describe('<WasteStreamReports />', () => {
  let wasteStreamReportList;
  const roleConfig = {
    params: {
      view: true,
    },
    vessel: {
      edit: true,
      send: true,
    },
  };
  beforeEach(async () => {
    vesselService.getWasteStreamReport = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: WasteStreamReportData }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    wasteStreamReportList = mount(
      <MemoryRouter
        initialEntries={[
          '/vessel/report/environmental/waste-stream?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
        ]}
      >
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/:tab'}>
            <EnvironmentalReports />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(wasteStreamReportList);
  });

  describe('should render waste stream report', () => {
    it('should render columns, when waste stream report is loaded', async () => {
      const customTable = wasteStreamReportList.find('CustomTable');
      const actualWasteStreamReports = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'Vessel',
        'Vessel Type',
        'DWT',
        'Time Spent Underway (hr)',
        'Time Spent In Port (hr)',
        'Distance Travelled(nm)',
        'Number Of Voyages',
        'Percentage of Laden Voyages',
        'Cargo Carried(MT or M3 For LNG)',
        'Transport Work',
        'Total Fuel Consumed At Sea (MT)',
        'Total Fuel Consumed At Port (MT)',
        'Total CO2 Emitted At Sea (MT)',
        'Total CO2 Emitted At Port (MT)',
        'Total SOX Emitted (MT)',
        'Average Sulphur Content Of Fuel Consumed (%)',
        'NOX Emitted By Main Engines (MT)',
        'NOX Emitted By Diesel Generators (MT)',
        'Bilge Generated (M3)',
        'Sludge (%)',
        'Garbage Generation (M3)',
        'Dry Cargo Residues Discharged To Sea (CU.M)',
        'Food Waste Discharged To Sea (CU.M)',
        'Nett Refrigerant Emitted (KGS)',
        'Qty Of Annex 1 Slop oil Discharged To Sea (Ltrs)',
      ];
      expect(actualWasteStreamReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when waste stream Reports is on fetch', async () => {
      vesselService.getWasteStreamReport = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      wasteStreamReportList = mount(
        <MemoryRouter
          initialEntries={[
            '/vessel/report/environmental/waste-stream?&report_date=2022-10-11,2022-11-11&vessel_ownership_id=1',
          ]}
        >
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/:tab'}>
              <EnvironmentalReports />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(wasteStreamReportList);
      expect(wasteStreamReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('waste stream report tab when page loads', () => {
      it('should waste stream report tab highlighted when page loads', async () => {
        const environmentalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/environmental/waste-stream']}>
            <EnvironmentalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/environmental/:tab'}>
                <EnvironmentalReports />
              </Route>
            </EnvironmentalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(environmentalReports);
        const testData = environmentalReports.find('a.active');
        expect(testData.text()).toEqual('Waste Stream Analysis');
      });
    });
  });
});
