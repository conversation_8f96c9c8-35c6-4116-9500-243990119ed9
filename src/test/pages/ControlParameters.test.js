import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter } from 'react-router-dom';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';
import ControlParameters from '../../component/controlParameters/ControlParameter';
import ControlParametersData from '../resources/control-parameters-data.json';
import { vesselList } from '../resources/vessel-with-ownership';

describe('<ContolParameters />', () => {
  let controlParameters;
  const roleConfig = {
    vessel: {
      editReport: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };
  beforeEach(async () => {
    vesselService.getContolParameters = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: ControlParametersData }));
    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: vesselList }));
    controlParameters = mount(
      <MemoryRouter>
        <TechnicalReportContextProvider roleConfig={roleConfig}>
          <ControlParameters />
        </TechnicalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(controlParameters);
  });

  describe('should render spinner on Clicking vessel option', () => {
    it('should render spinner', async () => {
      vesselService.getContolParameters = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      vesselService.getAllVessels = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: vesselList }));
      const controlParameterList = mount(
        <MemoryRouter>
          <TechnicalReportContextProvider roleConfig={roleConfig}>
            <ControlParameters />
          </TechnicalReportContextProvider>
        </MemoryRouter>,
      );
      controlParameterList
        .find('input[data-testid="fml-control-parameter-vessel"]')
        .simulate('click');
      await updateWrapper(controlParameterList);
      controlParameterList.find('a').at(1).simulate('click');
      await updateWrapper(controlParameterList);
      expect(controlParameterList.find('Spinner').exists()).toEqual(true);
    });
  });

  describe('should render Control Parameters page', () => {
    it('should render vessel Dropdown', async () => {
      expect(
        controlParameters.find('[data-testid="fml-control-parameter-vessel"]').exists(),
      ).toEqual(true);
    });

    it('should render controlparameters page with tab', async () => {
      controlParameters.find('input[data-testid="fml-control-parameter-vessel"]').simulate('click');
      await updateWrapper(controlParameters);
      controlParameters.find('a').at(1).simulate('click');
      await updateWrapper(controlParameters);
      const controlParameterTabs = controlParameters.find('Nav NavLink a').map((i) => i.text());
      const expectedTabs = [
        'Position Report',
        'ME Performance',
        'Monthly Report',
        'Quarterly Report',
        'Voyage Report',
      ];
      expect(controlParameters.find('a.active').text()).toEqual('Position Report');
      expect(controlParameterTabs).toEqual(expectedTabs);
    });

    it('should render controlParameter list table', async () => {
      controlParameters.find('input[data-testid="fml-control-parameter-vessel"]').simulate('click');
      await updateWrapper(controlParameters);
      controlParameters.find('a').at(1).simulate('click');
      await updateWrapper(controlParameters);
      const parameterTable = controlParameters.find('CustomTable');
      const tableHeaders = parameterTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Parameter',
        'Minimum Value',
        'Maximum Value',
        'Requirement of',
      ];
      expect(tableHeaders).toEqual(expectedHeaders);
    });
    it('should render Edit/ Save button controlParameter Page ', async () => {
      controlParameters.find('input[data-testid="fml-control-parameter-vessel"]').simulate('click');
      await updateWrapper(controlParameters);
      controlParameters.find('a').at(1).simulate('click');
      await updateWrapper(controlParameters);
      expect(
        controlParameters.find('[data-testid="fml-control-parameter-edit-btn"]').exists(),
      ).toEqual(true);
      controlParameters
        .find('[data-testid="fml-control-parameter-edit-btn"]')
        .at(0)
        .simulate('click');
      await updateWrapper(controlParameters);
      expect(
        controlParameters.find('[data-testid="fml-controlParameters-save-button"]').exists(),
      ).toEqual(true);
    });
    it('should hide edit button on no edit role', async () => {
      const roleConfig = {
        params: {
          view: true,
        },
      };
      const controlParameters = mount(
        <MemoryRouter>
          <TechnicalReportContextProvider roleConfig={roleConfig}>
            <ControlParameters />
          </TechnicalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(controlParameters);
      controlParameters.find('input[data-testid="fml-control-parameter-vessel"]').simulate('click');
      await updateWrapper(controlParameters);
      controlParameters.find('a').at(1).simulate('click');
      await updateWrapper(controlParameters);
      expect(
        controlParameters
          .find('[data-testid="fml-control-parameter-edit-btn"]')
          .at(0)
          .prop('hidden'),
      ).toEqual(true);
    });
  });
});
