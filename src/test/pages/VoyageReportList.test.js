import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter, Route } from 'react-router-dom';
import VoyageReports from '../../component/TechnicalReports/ListReports/VoyageReports';
import TechnicalReportsList from '../../component/TechnicalReports/TechnicalReportsList';
import voyageReportsListData from '../resources/voyage-report.list.json';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';

describe('<VoyageReportsList />', () => {
  let voyageReportList;
  const roleConfig = {
    vessel: {
      editReport: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };

  beforeEach(async () => {
    vesselService.getAllVessels = jest.fn().mockImplementation(() => Promise.resolve({ data: [] }));
    vesselService.getvoyageReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: voyageReportsListData }));
    voyageReportList = mount(
      <MemoryRouter>
        <TechnicalReportContextProvider roleConfig={roleConfig}>
          <VoyageReports />
        </TechnicalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(voyageReportList);
  });

  describe('should render voyage report list page', () => {
    it('should render columns, when postion report list loads', async () => {
      const voyageTable = voyageReportList.find('TechnicalReportTable');
      const actualvoyageReports = voyageTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Vessel',
        'View Report',
        'Report Type',
        'Report Date Time(SMT)',
        'Report Date Time(GMT)',
      ];
      expect(actualvoyageReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when voyage Reports list is on fetch', async () => {
      vesselService.getvoyageReports = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      voyageReportList = mount(
        <MemoryRouter>
          <TechnicalReportContextProvider roleConfig={roleConfig}>
            <VoyageReports
              filterData={{ startDate: '2022-03-02', endDate: '2022-03-06', vessel: [] }}
              loading={true}
            />
          </TechnicalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(voyageReportList);
      expect(voyageReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('voyage report tab when page loads', () => {
      it('should voyage report tab highlighted when page loads', async () => {
        const technicalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/technical/voyage']}>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/technical/:tab'}>
                <TechnicalReportsList />
              </Route>
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(technicalReports);
        const testData = technicalReports.find('a.active');
        expect(testData.text()).toEqual('Voyage Reports');
      });
    });
    describe('should render data on date pickers when page loads', () => {
      it('should render result count based on selected date', async () => {
        vesselService.getTechnicalReports = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: voyageReportsListData }));

        voyageReportList = mount(
          <MemoryRouter initialEntries={['/vessel/report/technical/voyage']}>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <VoyageReports filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(voyageReportList);

        const totalCount = voyageReportList.find('TechnicalReportTable').props().totalCount;
        expect(totalCount).toEqual(15);
      });
    });

    describe('voyage reports Table pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const selectPage = async (page, page_num) => {
        page
          .find('div.page-num')
          .find('.page-num-enabled')
          .filterWhere((e) => Number(e.text()) === Number(page_num))
          .at(0)
          .simulate('click');

        await updateWrapper(page);
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        const page = mount(
          <MemoryRouter>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <VoyageReports filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(page);
        await selectPage(page, 2);

        expect(findSelectedPageNumber(page)).toEqual(2);
        expect(findPageSizeValue(page)).toEqual(10);
      });
    });
  });
});
