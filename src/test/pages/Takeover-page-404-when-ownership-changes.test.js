import React from 'react';
import { mount } from 'enzyme';
import Takeover from '../../pages/Takeover';
import { MemoryRouter, Route } from 'react-router-dom';
import { updateWrapper } from '../../setupTests';

jest.mock('../../controller/take-over-controller', () => {
  return jest.fn().mockImplementation(() => {
    return {
      onLoadPage: () => {
        return Promise.resolve({
          vessel: {
            isOwnershipChangePending: true,
          },
          dropDownData: {},
          isAllApproved: false,
          userMemberships: [],
          distinctFieldData: { vessel_short_code: [], vessel_account_code_new: [] },
        });
      },
    };
  });
});

describe('<Takeover />', () => {
  const getRoleConfig = (hasRole) => {
    return {
      vessel: {
        create: hasRole,
        edit: hasRole,
      },
    };
  };

  it('should 404 page when ownership change request is pending', async () => {
    const takeoverPage = mount(
      <MemoryRouter initialEntries={['/vessel/11/1/takeover/basic']}>
        <Route path="/vessel/:vesselId/:ownershipId/takeover/:step">
          <Takeover username="test.user" roleConfig={getRoleConfig(true)} />
        </Route>
      </MemoryRouter>,
    );

    await updateWrapper(takeoverPage);
    expect(takeoverPage.find('ErrorPage').prop('errorCode')).toEqual(404);
  });
});
