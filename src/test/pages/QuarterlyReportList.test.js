import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import QuarterlyReportsListData from '../resources/quarterlyReportList.json';
import { MemoryRouter, Route } from 'react-router-dom';
import QuarterlyReportsList from '../../component/TechnicalReports/ListReports/QuarterlyReports';
import TechnicalReportsList from '../../component/TechnicalReports/TechnicalReportsList';
import TechnicalReportContextProvider from '../../context/TechnicalReportContext';

describe('<QuarterlyReportsList />', () => {
  let quarterlyReportList;
  const roleConfig = {
    vessel: {
      editReport: true,
    },
    params: {
      view: true,
      edit: true,
    },
  };

  beforeEach(async () => {
    vesselService.getAllVessels = jest.fn().mockImplementation(() => Promise.resolve({ data: [] }));
    vesselService.getTechnicalReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: QuarterlyReportsListData }));
    quarterlyReportList = mount(
      <MemoryRouter>
        <TechnicalReportContextProvider roleConfig={roleConfig}>
          <QuarterlyReportsList filterData={{ startDate: '', endDate: '', vessel: [] }} />
        </TechnicalReportContextProvider>
      </MemoryRouter>,
    );

    await updateWrapper(quarterlyReportList);
  });

  describe('should render quarterly report list page', () => {
    it('should render columns, when quarterly report list loads', async () => {
      const quarterlyTable = quarterlyReportList.find('TechnicalReportTable');
      const actualQuarterlyReports = quarterlyTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Vessel',
        'View Report',
        'Report Quarter',
        'Report Submit Date',
      ];
      expect(actualQuarterlyReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when quarterly Reports list is on fetch', async () => {
      vesselService.getTechnicalReports = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      quarterlyReportList = mount(
        <MemoryRouter>
          <TechnicalReportContextProvider roleConfig={roleConfig}>
            <QuarterlyReportsList
              filterData={{ startDate: '2022-03-02', endDate: '2022-03-06', vessel: [] }}
              loading={true}
            />
          </TechnicalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(quarterlyReportList);
      expect(quarterlyReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('quarterly report tab when page loads', () => {
      it('should quarterly report tab highlighted when page loads', async () => {
        const technicalReports = mount(
          <MemoryRouter initialEntries={['/vessel/report/technical/quarterly']}>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <Route path={'/vessel/report/technical/:tab'}>
                <TechnicalReportsList />
              </Route>
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );
        await updateWrapper(technicalReports);
        const testData = technicalReports.find('a.active').text();
        expect(testData).toEqual('Quarterly Reports');
      });
    });
    describe('should render data on date pickers when page loads', () => {
      it('should render result count based on selected date', async () => {
        quarterlyReportList = mount(
          <MemoryRouter>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <QuarterlyReportsList filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(quarterlyReportList);

        const totalCount = quarterlyReportList.find('TechnicalReportTable').props().totalCount;
        expect(totalCount).toEqual(15);
      });
    });

    describe('quarterly reports Table pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const selectPage = async (page, page_num) => {
        page
          .find('div.page-num')
          .find('.page-num-enabled')
          .filterWhere((e) => Number(e.text()) === Number(page_num))
          .at(0)
          .simulate('click');
        await updateWrapper(page);
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        const page = mount(
          <MemoryRouter>
            <TechnicalReportContextProvider roleConfig={roleConfig}>
              <QuarterlyReportsList filterData={{ startDate: '', endDate: '', vessel: [] }} />
            </TechnicalReportContextProvider>
          </MemoryRouter>,
        );

        await updateWrapper(page);
        await selectPage(page, 2);

        expect(findSelectedPageNumber(page)).toEqual(2);
        expect(findPageSizeValue(page)).toEqual(10);
      });
    });
  });
});
