import React from 'react';
import { mount } from 'enzyme';
import List from '../../pages/List';
import VesselTable from '../../component/vesselList/VesselTable';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import vesselList from '../resources/vesselListWithDefaultColumns.json';
import items from '../../component/vesselList/MenuList';
import { vesselListTabKeys } from '../../model/constants';
import { operationManagerStaffData, qhseStaffData } from '../resources/staff-type';
import VesselContextProvider from '../../context/VesselContext';

const menuItems = items();

describe('<List />', () => {
  let list;
  const getRoleConfig = (canCreate, canView = true, canEdit = true) => {
    return {
      vessel: {
        create: canCreate,
        view: canView,
        edit: canEdit,
        staff: {
          buyer: true,
          accountant: true,
          supdt: true,
          qhse: true,
          operation: true,
          payroll: true,
        },
      },
      techGroups: {
        techGroups: [],
        manage: false,
      },
    };
  };
  beforeEach(async () => {
    vesselService.getDropDownData = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: {
          emailTypes: [],
          flags: [],
          hmUnderwriters: [],
          owners: [],
          phoneTypes: [],
          piClubs: [],
          portOfRegistrys: [],
          vesselClasss: [],
          vesselServiceStatuss: [],
          vesselTypes: [],
        },
      }),
    );
    vesselService.getOwnerships = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ data: { results: vesselList.data, totalCount: 1 } }),
      );
    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: vesselList.data } }));

    list = mount(
      <MemoryRouter initialEntries={['/vessel/active-vessels']}>
        <Route path="/vessel/:tab">
          <VesselContextProvider roleConfig={getRoleConfig(true)} ga4EventTrigger={jest.fn()}>
            <List ga4react={jest.fn()} />
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(list);
  });

  describe('should render vessel headers', () => {
    it('when current active tab is active vessels tab', async () => {
      const vesselHeaders = list.find('div.th');

      const actualVessel = vesselHeaders.map((header) => {
        return header.text();
      });

      expect(actualVessel).toEqual(
        [
          {
            Header: 'No.',
          },
          menuItems[1],
          menuItems[2],
          menuItems[3],
          menuItems[4],
          menuItems[13],
          menuItems[41],
          menuItems[42],
          menuItems[43],
          menuItems[44],
          menuItems[45],
          {
            Header: 'Actions',
          },
        ].map((i) => i.Header),
      );
    });

    it('should render assigned dialog when qhse is not assigned', async () => {
      vesselService.getAssignedStaffVessel = jest.fn().mockResolvedValue({ data: qhseStaffData });
      list
        .find('div[role="cell"].td')
        .find('span.underline-link-text.fw-bolder.d-inline')
        .simulate('click');
      await updateWrapper(list);

      // check assign field dialog heading
      expect(list.find('.modal-title').at(1).text()).toEqual('Assign New QHSE Manager');
    });

    it('should render replace dialog when operation manager value is already assigned', async () => {
      vesselService.getAssignedStaffVessel = jest
        .fn()
        .mockResolvedValue({ data: operationManagerStaffData });

      list
        .find('div[role="cell"].td')
        .find('span.underline-link-text.fw-bolder.d-inline')
        .simulate('click');
      await updateWrapper(list);

      // check replace dialog heading
      expect(list.find('.modal-title').at(1).text()).toEqual('Assign New QHSE Manager');
    });

    it('when current active tab is new takeovers tab', async () => {
      const newTakeoverLink = list.find('Nav NavLink a').at(1);
      newTakeoverLink.simulate('click');
      await updateWrapper(list);
      const vesselHeaders = list.find('.th');
      const actualVessel = vesselHeaders.map((header) => {
        return header.text();
      });

      expect(actualVessel).toEqual(
        [
          {
            Header: 'No.',
          },
          menuItems[1],
          menuItems[2],
          menuItems[3],
          menuItems[4],
          menuItems[9],
          menuItems[13],
          menuItems[41],
          menuItems[44],
          menuItems[45],
          {
            Header: 'Actions',
          },
        ].map((i) => i.Header),
      );
    });
    it('when current active tab is handed over tab', async () => {
      const handedOverLink = list.find('Nav NavLink a').at(2);
      handedOverLink.simulate('click');
      await updateWrapper(list);
      const vesselHeaders = list.find('.th');
      const actualVessel = vesselHeaders.map((header) => {
        return header.text();
      });

      expect(actualVessel).toEqual(
        [
          {
            Header: 'No.',
          },
          menuItems[1],
          menuItems[2],
          menuItems[3],
          menuItems[4],
          menuItems[11],
          menuItems[13],
          menuItems[41],
          menuItems[42],
          menuItems[43],
          menuItems[44],
          menuItems[45],
          {
            Header: 'Actions',
          },
        ].map((i) => i.Header),
      );
    });
  });

  it('should navigate to tab content on click of tab header archived', async () => {
    const tabUrl = '/vessel/archived';
    vesselService.getOwnerships = jest.fn().mockImplementation(() => Promise.resolve(vesselList));
    const list = mount(
      <MemoryRouter initialEntries={[tabUrl]}>
        <Route path="/vessel/:tab">
          <VesselContextProvider roleConfig={getRoleConfig(true)} ga4EventTrigger={jest.fn()}>
            <List ga4react={jest.fn()} />
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(list);

    list
      .find('NavLink')
      .filterWhere((link) => link.text() == 'Archived')
      .simulate('click');
    await updateWrapper(list);

    expect(list.find('Router').prop('history').location.pathname).toEqual('/vessel/archived');
  });

  it('should render spinner, when vessel list is on fetch', async () => {
    vesselService.getDropDownData = jest
      .fn()
      .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
    vesselService.getOwnerships = jest
      .fn()
      .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
    const list = mount(
      <MemoryRouter initialEntries={['/vessel/active-vessels']}>
        <Route path="/vessel/:tab">
          <VesselContextProvider roleConfig={getRoleConfig(true)} ga4EventTrigger={jest.fn()}>
            <List ga4react={jest.fn()} />
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(list);

    const vesselsTable = list.find('RenderTableRow');
    expect(vesselsTable.exists()).toEqual(false);
    expect(list.find('Spinner').exists()).toEqual(true);
  });

  it('should call route /vessel/ownership/details/:ownershipId on click of particular ownership vessel', async () => {
    list.find('div.tr').at(1).simulate('click');
    const ownershipId = list.find('div.td').first();
    expect(list.find('Router').prop('history').location.pathname).toEqual(
      `/vessel/ownership/details/${ownershipId.text()}`,
    );
  });

  it('should call route /vessel/search on click of Advanced Search button', async () => {
    const searchBtn = list.find('button').filterWhere((btn) => btn.text() == 'Advanced Search');
    searchBtn.simulate('click');
    expect(list.find('Router').prop('history').location.pathname).toEqual('/vessel/search');
  });

  it('should call route /vessel/takeover/basic/ on click of Add a vessel', async () => {
    clickOnMore(list);

    list
      .find('a')
      .filterWhere((anchor) => anchor.text() == 'Add a Vessel')
      .simulate('click');

    expect(list.find('Router').prop('history').location.pathname).toEqual(
      '/vessel/takeover/basic/',
    );
  });

  describe('role based view', () => {
    describe('view vessel', () => {
      it('should render error when user do not have role to view vessel', async () => {
        list = mount(
          <MemoryRouter initialEntries={['/vessel/active-vessels']}>
            <Route path="/vessel/:tab">
              <VesselContextProvider
                roleConfig={getRoleConfig(false, false)}
                ga4EventTrigger={jest.fn()}
              >
                <List ga4react={jest.fn()} />
              </VesselContextProvider>
            </Route>
          </MemoryRouter>,
        );

        await updateWrapper(list);
        expect(list.text().includes('403 Access Denied')).toEqual(true);
      });
    });

    describe('create a vessel', () => {
      it('should be displayed, when logged in user role config has create config true', async () => {
        list = mount(
          <MemoryRouter initialEntries={['/vessel/active-vessels']}>
            <Route path="/vessel/:tab">
              <VesselContextProvider roleConfig={getRoleConfig(true)} ga4EventTrigger={jest.fn()}>
                <List ga4react={jest.fn()} />
              </VesselContextProvider>
            </Route>
          </MemoryRouter>,
        );
        await updateWrapper(list);

        clickOnMore(list);
        await updateWrapper(list);
        const addVessel = list.find('a').filterWhere((anchor) => anchor.text() == 'Add a Vessel');

        expect(addVessel.exists()).toBe(true);
      });

      it('should not be displayed, when logged in user role config has create config false', async () => {
        list = mount(
          <MemoryRouter initialEntries={['/vessel/active-vessels']}>
            <Route path="/vessel/:tab">
              <VesselContextProvider roleConfig={getRoleConfig(false)} ga4EventTrigger={jest.fn()}>
                <List ga4react={jest.fn()} />
              </VesselContextProvider>
            </Route>
          </MemoryRouter>,
        );
        await updateWrapper(list);

        clickOnMore(list);
        await updateWrapper(list);
        const addVessel = list.find('a').filterWhere((anchor) => anchor.text() == 'Add a Vessel');

        expect(addVessel.exists()).toBe(false);
      });
    });
  });

  const clickOnMore = async (page) => {
    page.find('#dropdown-more').at(2).simulate('click');
  };
  describe('Vessel Table tests', () => {
    const getSelectedColumns = (page) => {
      const header_elements = page.find('.vessel-table').find('.header').find('.tr').children();
      const header_names = header_elements.map((head) => head.text());
      return header_names;
    };

    const findSelectedPageNumber = (page) => {
      return Number(page.find('.page-number-border').find('.page-num-active').text());
    };

    const selectPage = async (page, page_num) => {
      page
        .find('.page-number-border')
        .children()
        .filterWhere((page) => Number(page.text()) == String(page_num))
        .simulate('click');
      await updateWrapper(page);
    };

    const selectColumn = async (page, col_name) => {
      page
        .find('button')
        .filterWhere((btn) => btn.text() == 'Table Columns')
        .simulate('click');
      await updateWrapper(page);
      page
        .find('.dropdown-item')
        .filterWhere((item) => item.text() == col_name)
        .find('a')
        .simulate('click');
      await updateWrapper(page);
    };

    const switchTab = async (page, tabName) => {
      page
        .find('NavLink')
        .filterWhere((link) => link.text() == tabName)
        .simulate('click');
      await updateWrapper(page);
    };

    const selectSize = async (page, size) => {
      page
        .find('.vessel-table')
        .find('select')
        .simulate('change', { target: { value: Number(size) } });
      await updateWrapper(page);
    };

    const findPageSizeValue = (page) => page.find('.vessel-table').find('select').props().value;

    it('Should hide columns(Actions,No) during load', async () => {
      const selected_columns = [
        {
          Header: 'No.',
        },
        menuItems[1],
        menuItems[4],
        {
          Header: 'Actions',
        },
      ];
      const list = mount(
        <VesselTable
          tabName={vesselListTabKeys.ACTIVE_VESSEL}
          vessels={[]}
          selectedColumns={selected_columns}
          isLoading={true}
          fetchData={() => {}}
          initSort={[]}
        />,
      );
      await updateWrapper(list);
      const header_elements = list.find('.vessel-table').find('.header').find('.tr').children();
      const header_names = header_elements.map((head) => head.text());
      expect(header_names).toEqual(expect.not.arrayContaining(['Actions']));
      expect(header_names).toEqual(expect.not.arrayContaining(['No.']));
    });

    it('Should load spinner positioning between table header and empty table body', async () => {
      vesselService.getDropDownData = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      vesselService.getOwnerships = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      const list = mount(
        <MemoryRouter initialEntries={['/vessel/active-vessels']}>
          <Route path="/vessel/:tab">
            <VesselContextProvider roleConfig={getRoleConfig(true)} ga4EventTrigger={jest.fn()}>
              <List ga4react={jest.fn()} />
            </VesselContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(list);
      const vesselsTable = list.find('.vessel-table');
      const vessel_children = vesselsTable.find('.table.sticky').children();
      expect(vessel_children.at(0).hasClass('header')).toBe(true);
      expect(vessel_children.at(1).name()).toEqual('Spinner');
      expect(vessel_children.at(2).hasClass('body')).toBe(true);
    });

    it('should retain page number and page size for each tab on refresh', async () => {
      const data_list = [];
      //Replicate mock data 52 times(needed for pagination tests)
      for (let y = 0; y < 52; y++) data_list.push(vesselList.data[0]);
      const data_mock = { data: { vessels: data_list, totalCount: 52 } };

      vesselService.getOwnerships = jest.fn().mockImplementation(() => Promise.resolve(data_mock));

      const page = mount(
        <MemoryRouter initialEntries={['/vessel/active-vessels']}>
          <Route path="/vessel/:tab">
            <VesselContextProvider roleConfig={getRoleConfig(true)} ga4EventTrigger={jest.fn()}>
              <List ga4react={jest.fn()} />
            </VesselContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(page);

      //Set page size and number in tab 1
      await switchTab(page, 'Archived');
      await selectSize(page, 20);
      await selectPage(page, 2);
      await selectColumn(page, ' Depth');
      await selectColumn(page, ' Service Speed');

      //Set a different page size and number in tab 2
      await switchTab(page, 'New Takeovers');
      await selectSize(page, 10);
      await selectPage(page, 4);
      await selectColumn(page, ' Length O.A.');

      //Refresh page
      const page_refreshed = mount(
        <MemoryRouter initialEntries={['/vessel/active-vessels']}>
          <Route path="/vessel/:tab">
            <VesselContextProvider roleConfig={getRoleConfig(true)} ga4EventTrigger={jest.fn()}>
              <List ga4react={jest.fn()} />
            </VesselContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(page_refreshed);
      //Verify persistence
      await switchTab(page_refreshed, 'Archived');
      expect(findSelectedPageNumber(page_refreshed)).toEqual(2);
      expect(findPageSizeValue(page_refreshed)).toEqual(20);
      //console.log(page_refreshed.find('.vessel-table').find('.header').debug())
      expect(getSelectedColumns(page_refreshed)).toEqual(
        expect.arrayContaining(['Depth', 'Service Speed']),
      );

      await switchTab(page_refreshed, 'New Takeovers');
      expect(findSelectedPageNumber(page_refreshed)).toEqual(4);
      expect(findPageSizeValue(page_refreshed)).toEqual(10);
      expect(getSelectedColumns(page_refreshed)).toEqual(
        expect.arrayContaining(['Length O.A.', 'Tech Group']),
      );
    }, 30000);

    describe('Edit Vessel Menu', () => {
      beforeEach(async () => {
        vesselService.getDropDownData = jest.fn().mockImplementation(() =>
          Promise.resolve({
            data: {
              emailTypes: [],
              flags: [],
              hmUnderwriters: [],
              owners: [],
              phoneTypes: [],
              piClubs: [],
              portOfRegistrys: [],
              vesselClasss: [],
              vesselServiceStatuss: [],
              vesselTypes: [],
            },
          }),
        );
        vesselService.getOwnerships = jest
          .fn()
          .mockImplementation(() =>
            Promise.resolve({ data: { results: vesselList.data, totalCount: 1 } }),
          );
      });

      it('should show Edit actions when current tab is active/new-takeover but hide when current tab is handed-over/archived', async () => {
        const tabOptions = ['active-vessels', 'new-takeovers', 'handed-over', 'archived'];
        const expectations = await Promise.all(
          tabOptions.map(async (currentTab) => {
            vesselService.getOwnerships = jest
              .fn()
              .mockImplementation(() =>
                Promise.resolve({ data: { results: vesselList.data, totalCount: 1 } }),
              );
            const list = mount(
              <MemoryRouter initialEntries={[`/vessel/${currentTab}`]}>
                <Route path="/vessel/:tab">
                  <VesselContextProvider
                    roleConfig={getRoleConfig(true)}
                    ga4EventTrigger={jest.fn()}
                  >
                    <List ga4react={jest.fn()} />
                  </VesselContextProvider>
                </Route>
              </MemoryRouter>,
            );

            await updateWrapper(list);
            const moreOption = list.find('.vessel-table .body OverlayTrigger Icon');
            return moreOption.exists();
          }),
        );

        expect(expectations).toEqual([true, true, false, false]);
      });
    });

    it('should display results based on searched keyword', async () => {
      const list = mount(
        <MemoryRouter initialEntries={['/vessel/active-vessels']}>
          <Route path="/vessel/:tab">
            <VesselContextProvider roleConfig={getRoleConfig(true)} ga4EventTrigger={jest.fn()}>
              <List ga4react={jest.fn()} />
            </VesselContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(list);
      list
        .find('#search-bar')
        .at(0)
        .simulate('change', { target: { value: 'Boat' } });
      await updateWrapper(list);
      expect(list.text().includes('Boat')).toEqual(true);
    });

    it('should display advanced search overlay component', async () => {
      vesselService.getOwnerships = jest.fn().mockImplementation(() => Promise.resolve(vesselList));
      const list = mount(
        <MemoryRouter initialEntries={['/vessel/active-vessels']}>
          <Route path="/vessel/:tab">
            <VesselContextProvider roleConfig={getRoleConfig(true)} ga4EventTrigger={jest.fn()}>
              <List ga4react={jest.fn()} />
            </VesselContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(list);

      const searchBtn = list.find('#dropdown-advanced-search');
      searchBtn.at(0).simulate('click');
      await updateWrapper(list);
      expect(list.text().includes('Category')).toEqual(true);
      list.find('.advanced_search').find('input').at(0).simulate('click');

      await updateWrapper(list);
      expect(list.text().includes('Vessel Name')).toEqual(true);
      list
        .find('.advanced_search')
        .find('.dropdown-item')
        .filterWhere((item) => item.text() == 'Vessel Name')
        .find('a')
        .simulate('click');

      await updateWrapper(list);
      expect(list.find('.advanced_search').find('input').at(0).props().value).toEqual(
        'Vessel Name',
      );

      list
        .find('.advanced_search')
        .find('.form-group')
        .find('input')
        .at(2)
        .simulate('change', { target: { value: 'Boat' } });
      await updateWrapper(list);
      expect(
        list.find('.advanced_search').find('.form-group').find('input').at(2).props().value,
      ).toEqual('Boat');
    });
  });
});
