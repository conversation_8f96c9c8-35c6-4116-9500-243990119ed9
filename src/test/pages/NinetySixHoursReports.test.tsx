import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import { MemoryRouter, Route } from 'react-router-dom';
import EnvironmentalReports from '../../component/EnvironmentalReports/EnvironmentalReportsList';
import EnvironmentalReportContextProvider from '../../context/EnvironmentalReportContext';
import NinetySixHoursData from '../resources/ninety-six-hours-reports-list.json';
import vesselList from '../resources/vesselListWithDefaultColumns.json';

describe('<NinetySixHoursReports />', () => {
  let ninetySixHoursReportList;
  const roleConfig = {
    params: {
      view: true,
    },
    vessel: {
      edit: true,
      send: true,
    },
  };
  beforeEach(async () => {
    vesselService.get96HoursReportList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: NinetySixHoursData }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    ninetySixHoursReportList = mount(
      <MemoryRouter initialEntries={['/vessel/report/environmental/ninety-six-hours']}>
        <EnvironmentalReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/environmental/:tab'}>
            <EnvironmentalReports />
          </Route>
        </EnvironmentalReportContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(ninetySixHoursReportList);
  });

  describe('should render 96 hours report', () => {
    it('should render columns, when 96 hours report is loaded', async () => {
      const customTable = ninetySixHoursReportList.find('CustomTable');
      const actual96HoursReports = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Vessel',
        'View Report',
        'Report Submit Date',
      ];
      expect(actual96HoursReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when 96 Hours report is on fetch', async () => {
      vesselService.get96HoursReportList = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      ninetySixHoursReportList = mount(
        <MemoryRouter initialEntries={['/vessel/report/environmental/ninety-six-hours']}>
          <EnvironmentalReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/environmental/:tab'}>
              <EnvironmentalReports />
            </Route>
          </EnvironmentalReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(ninetySixHoursReportList);
      expect(ninetySixHoursReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('96 hours report tab when page loads', () => {
      it('should 96 hours report tab highlighted when page loads', async () => {
        const testData = ninetySixHoursReportList.find('a.active');
        expect(testData.text()).toEqual('96 Hours');
      });
    });
  });
});
