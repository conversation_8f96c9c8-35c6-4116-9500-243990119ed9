import React from 'react';
import { mount } from 'enzyme';
import Approval from '../../pages/Approval';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselService from '../../service/vessel-service';
import { updateWrapper } from '../../setupTests';
import vesselApprovalService from '../../service/vessel-approval-service';
import vesselApprovalJson from '../resources/get-approval.json';
import { getDataWithFinalApproverStatus } from '../resources/get-all-fisrt-approved.js';
import {
  activePendingVessel,
  handedOverPendingVessel,
  activeButHandedOverNotStartedResponse,
} from '../resources/vessel-approval';
import {
  FIRST_APPROVERS_GROUPS,
  FINAL_APPROVER,
  BUSINESS,
  FLEET_PERSONNEL,
  TECH_GROUP,
  ACCOUNTS,
  INSURANCE,
} from '../../model/constants';
import moment from 'moment';

describe('<Approval />', () => {
  let approvalPage;

  const getRoleConfig = (canMove) => {
    return {
      vessel: {
        moveToActive: canMove,
        moveToHandover: true,
        moveToArchival: true,
      },
      approvalGroups: FIRST_APPROVERS_GROUPS,
      techGroups: [{ manage: false }],
      finalApprover: true,
    };
  };

  function mockVesselDataCallFor() {
    vesselService.getVessel = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ status: 200, data: activePendingVessel[0] }));
  }

  function mockVesselApprovalDataCall() {
    vesselApprovalService.getVesselApprovalData = jest
      .fn()
      .mockImplementation(() => Promise.resolve(vesselApprovalJson));
  }

  beforeEach(async () => {
    global.scrollTo = jest.fn();
    mockVesselDataCallFor();
    mockVesselApprovalDataCall();

    approvalPage = mount(
      <MemoryRouter>
        <Approval roleConfig={getRoleConfig(true)} />
      </MemoryRouter>,
    );
    await updateWrapper(approvalPage);
  });

  it('should render vessel name', async () => {
    const breadcrumbtext = approvalPage
      .find('li')
      .filterWhere((li) => li.prop('className') == 'breadcrumb-item breadcrumb-text active');
    expect(breadcrumbtext.text()).toEqual('Approval: Takeover a vessel');
  });

  it('should render error message, when error is recieved', async () => {
    vesselApprovalService.getVesselApprovalData = jest
      .fn()
      .mockImplementation(() => Promise.reject(new Error()));

    const approvalPage = mount(
      <MemoryRouter>
        <Approval roleConfig={getRoleConfig(true)} />
      </MemoryRouter>,
    );

    await updateWrapper(approvalPage);

    expect(approvalPage.find('ErrorAlert Alert').exists()).toEqual(true);
  });

  describe('first approvers section', () => {
    it('should render vessel first approvers section', () => {
      verifyApproversInTable([BUSINESS, FLEET_PERSONNEL, ACCOUNTS, INSURANCE, 'techgroup1'], 0);
    });

    it('should have only review button when status is pending', () => {
      const allButtons = getButtonsForPendingStatus(approvalPage);

      expect(allButtons.map((btn) => btn.text())).toEqual(['Review']);
    });

    it('should not render name, last_update and remarks when status is pending', () => {
      const pendingRowColumns = firstSectionRow(approvalPage).find('.pending').first().find('td');
      expect(pendingRowColumns.map((cols) => cols.text())).toEqual(['', '', 'pending', '']);
    });

    it('should have rework button when status is rejected', () => {
      const allButtons = getButtonForRejectedStatus(approvalPage);

      expect(allButtons.map((btn) => btn.text())).toEqual(['Rework']);
    });

    it('should not have remarks link, when remarks are not available', () => {
      const rowWithRejectedStatus = firstSectionRow(approvalPage)
        .find('.rejected')
        .filterWhere((row) => row.prop('group') == 'Accounts');

      expect(rowWithRejectedStatus.find('Remarks').exists()).toBe(false);
    });

    it('should have remarks link, when remarks are available', () => {
      const rowWithRejectedStatus = firstSectionRow(approvalPage).find('.rejected');
      rowWithRejectedStatus.filterWhere((row) => row.find('Rework').prop('group') == 'Insurance');

      expect(rowWithRejectedStatus.find('Remarks').exists()).toBe(true);
    });

    it('should open remarks model, on click of remark link', async () => {
      const rowsWithRejectedStatus = firstSectionRow(approvalPage).find('.rejected');
      const rowWithRejectedStatus = rowsWithRejectedStatus.filterWhere(
        (row) => row.find('Rework').prop('group') == 'Insurance',
      );

      const remarksLink = rowWithRejectedStatus.find('Remarks button');
      expect(remarksLink.text()).toBe('Lorem ipsum dolor sit amet, consectetur...');

      remarksLink.simulate('click');
      await updateWrapper(approvalPage);

      const remarksModal = approvalPage.find('Remarks Modal.action-modal');
      expect(remarksModal.at(0).prop('show')).toBe(true);
      expect(remarksModal.find('ModalBody').text()).toBe(
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec sapien neque, suscipit.',
      );
    });
  });

  describe('Final Approval Section', () => {
    it('should render disabled, when all statuses of first approvers are not approved', () => {
      const finalApproverRow = approvalPage.find('#test__finalApproval tr').at(1);
      expect(finalApproverRow.find('th span').at(0).text()).toEqual(FINAL_APPROVER);
    });
    it('should have approve and reject buttons', () => {
      const allButtons = approvalPage.find('#test__finalApproval ButtonGroup button');
      expect(allButtons.map((btn) => btn.text())).toEqual(['Approve', 'Reject']);
    });
  });

  describe('Missing fields error section', () => {
    it('should render missing fields section', async () => {
      vesselApprovalService.getVesselApprovalData = jest
        .fn()
        .mockImplementation(() => Promise.resolve(getDataWithFinalApproverStatus('approved')));

      approvalPage = mount(
        <MemoryRouter>
          <Approval roleConfig={getRoleConfig(true)} />
        </MemoryRouter>,
      );
      await updateWrapper(approvalPage);
      const missingFieldsAlert = approvalPage.find('.approval_error_list');
      expect(missingFieldsAlert.exists()).toEqual(true);
    });

    it('should render missing fields inside alert', async () => {
      vesselApprovalService.getVesselApprovalData = jest
        .fn()
        .mockImplementation(() => Promise.resolve(getDataWithFinalApproverStatus('approved')));

      approvalPage = mount(
        <MemoryRouter>
          <Approval roleConfig={getRoleConfig(true)} />
        </MemoryRouter>,
      );
      await updateWrapper(approvalPage);
      const missingFieldsError = approvalPage.find('li');
      expect(missingFieldsError.map((field) => field.text()).filter(Boolean)).toEqual(
        expect.arrayContaining([
          'Vessel',
          'Approval: Takeover a vessel',
          'Please enter IMO Number',
          'Please enter Vessel Hull Number',
          'Please enter Life Boat Capacity',
          'Please enter Length O.A.',
          'Please enter Length B.P.',
          'Please enter Depth',
          'Please enter Breadth (Extreme)',
          'Please enter Summer Draft',
          'Please enter Summer DWT',
          'Please enter International GRT',
          'Please enter International NRT',
          'Please enter Service Speed',
          'Please select H & M Underwriter',
          'Please select P & I Club',
          'Please select Shipyard',
          'Please enter Vessel Type',
          'Please enter Owners',
          'Please enter Engine',
          'Please enter DWT',
          'Please enter Power(kW)',
          'Please select Operator',
          'Please select Manager',
          'Please select Currency',
          'Please select Is Manning Manager',
          'Please select Flag (ISPS)',
          'Please select Classification',
          'Please select Classification Society',
          'Please select QI',
          'Please select OSRO',
          'Please select Salvage',
          'Please select Media Response',
          'Please select Management Type',
          'Please select Other Contacts',
          'Please select US Visa Required',
          'Please select Portage bill module',
          'Please enter Vessel Account Code New',
          'Please select Registered Owner',
          'Please enter Vessel Short Code',
          'Please enter Vessel Tel FAC Code',
          'Please enter Expected Date of Takeover',
          'Please select Year Built / Date of Delivery',
          'Please enter Phone',
          'Please enter Email',
        ]),
      );
    });

    it('should not render missing fields section', async () => {
      vesselApprovalService.getVesselApprovalData = jest
        .fn()
        .mockImplementation(() => Promise.resolve(getDataWithFinalApproverStatus('pending')));

      approvalPage = mount(
        <MemoryRouter>
          <Approval roleConfig={getRoleConfig(true)} />
        </MemoryRouter>,
      );
      await updateWrapper(approvalPage);

      const missingFieldsAlert = approvalPage.find('.approval_error_list');
      expect(missingFieldsAlert.exists()).toEqual(false);
    });
  });

  describe('Move Vessel to Active List Section', () => {
    it('should render disabled, when status of final approver are not approved', async () => {
      vesselApprovalService.getVesselApprovalData = jest
        .fn()
        .mockImplementation(() => Promise.resolve(getDataWithFinalApproverStatus('pending')));

      approvalPage = mount(
        <MemoryRouter>
          <Approval roleConfig={getRoleConfig(true)} />
        </MemoryRouter>,
      );
      await updateWrapper(approvalPage);

      const moveToActiveButton = approvalPage.find(
        '[data-testid="fml-Approval-moveToNewStatusSection"]',
      );
      expect(moveToActiveButton.at(0).prop('className')).toContain('disabled');
      expect(moveToActiveButton.at(0).prop('disabled')).toBe('disabled');
    });

    it('should have tick mark when vessel is active', async () => {
      vesselService.getVessel = jest
        .fn()
        .mockImplementation(() =>
          Promise.resolve({ status: 200, data: activeButHandedOverNotStartedResponse[0] }),
        );

      const approvalPage = mount(
        <MemoryRouter>
          <Approval roleConfig={getRoleConfig(true)} />
        </MemoryRouter>,
      );
      await updateWrapper(approvalPage);

      const moveToActiveSection = approvalPage.find('AlreadyMovedView');

      expect(moveToActiveSection.text()).toEqual(
        'This vessel has been moved to Active Vessel List.',
      );
      expect(moveToActiveSection.find('.vessel_approval__moved-tick').exists()).toBe(true);
    });

    it('should show status approved and reviewed on approved', async () => {
      mockVesselDataCallFor();
      vesselApprovalService.getVesselApprovalData = jest
        .fn()
        .mockImplementation(() => Promise.resolve(getDataWithFinalApproverStatus('approved')));

      vesselService.updateVessel = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: handedOverPendingVessel }));

      vesselService.patchVessel = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: handedOverPendingVessel }));
      approvalPage = mount(
        <MemoryRouter initialEntries={['/vessel/details/11/approval']}>
          <Route path="/vessel/details/:vesselId/approval">
            <Approval roleConfig={getRoleConfig(true)} />
          </Route>
        </MemoryRouter>,
      );

      await updateWrapper(approvalPage);
      const finalApprovedRow = approvalPage.find(
        'td[data-testid="fml-approval-final-approver-status"]',
      );
      expect(finalApprovedRow.text().includes('approved')).toEqual(true);
      const firstApprovedRow = approvalPage.find('td[data-testid="fml-approval-reviewer-status"]');
      expect(firstApprovedRow.map((item) => item.text() === 'Reviewed').length).toEqual(5);
    });

    it('should render as enabled, when all status of final approver are approved', async () => {
      mockVesselDataCallFor();
      vesselApprovalService.getVesselApprovalData = jest
        .fn()
        .mockImplementation(() => Promise.resolve(getDataWithFinalApproverStatus('approved')));

      vesselService.updateVessel = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: handedOverPendingVessel }));

      vesselService.patchVessel = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ status: 200, data: handedOverPendingVessel }));
      approvalPage = mount(
        <MemoryRouter initialEntries={['/vessel/details/11/approval']}>
          <Route path="/vessel/details/:vesselId/approval">
            <Approval roleConfig={getRoleConfig(true)} />
          </Route>
        </MemoryRouter>,
      );

      await updateWrapper(approvalPage);
      approvalPage
        .find('[data-testid="fml-move-to-new-status-date"]')
        .simulate('change', { target: { value: moment().format('DD MMM yyyy') } });
      await updateWrapper(approvalPage);
      const moveToActiveButton = approvalPage.find(
        '[data-testid="fml-Approval-moveToNewStatusSection"]',
      );
      expect(moveToActiveButton.at(1).prop('className')).not.toContain('disabled');
    });
  });

  describe('role based view', () => {
    describe('move to active', () => {
      it('should be displayed, when logged in user role config has moveToActive config true', async () => {
        approvalPage = mount(
          <MemoryRouter>
            <Approval roleConfig={getRoleConfig(true)} />
          </MemoryRouter>,
        );
        await updateWrapper(approvalPage);

        expect(approvalPage.find('MoveToNewStatusSection').exists()).toBe(true);
      });

      it('should not be displayed, when logged in user role config has moveToActive config false', async () => {
        approvalPage = mount(
          <MemoryRouter>
            <Approval roleConfig={getRoleConfig(false)} />
          </MemoryRouter>,
        );
        await updateWrapper(approvalPage);

        expect(approvalPage.find('MoveToNewStatusSection').exists()).toBe(false);
      });
    });

    describe('approve|reject buttons', () => {
      it(' should have approve and reject buttons for row group, if approval groups config contains row group', async () => {
        const roleConfig = {
          vessel: {
            moveToActive: true,
            moveToHandover: true,
            moveToArchival: true,
            approve: true,
          },
          approvalGroups: [BUSINESS, ACCOUNTS, TECH_GROUP],
          finalApprover: false,
          techGroups: {
            manage: false,
            techGroups: ['techgroup1', 'techgroup2'],
          },
        };
        const page = mount(
          <MemoryRouter>
            <Approval roleConfig={roleConfig} />
          </MemoryRouter>,
        );
        await updateWrapper(page);

        verifyApproveRejectButtonsForGroup(page, BUSINESS, true);
        verifyApproveRejectButtonsForGroup(page, FLEET_PERSONNEL, false);
        verifyApproveRejectButtonsForGroup(page, ACCOUNTS, true);
        verifyApproveRejectButtonsForGroup(page, INSURANCE, false);
        verifyApproveRejectButtonsForGroup(page, 'techgroup1', true);
        verifyApproveRejectButtonsForGroup(page, FINAL_APPROVER, false);
      });

      it(' should not have approve,reject buttons for tech group, when vessel tech group is different than user tech group', async () => {
        const roleConfig = {
          vessel: {
            moveToActive: true,
            moveToHandover: true,
            moveToArchival: true,
            approve: true,
          },
          approvalGroups: [BUSINESS, ACCOUNTS, TECH_GROUP],
          finalApprover: false,
          techGroups:{
            manage: false,
            techGroups: ['techgroup2'],
          },
        };
        const page = mount(
          <MemoryRouter>
            <Approval roleConfig={roleConfig} />
          </MemoryRouter>,
        );
        await updateWrapper(page);

        verifyApproveRejectButtonsForGroup(page, 'techgroup1', false);
      });
    });
  });

  function verifyApproveRejectButtonsForGroup(page, group, shouldRender) {
    const groupRow = firstSectionRow(page).filterWhere((row) => row.text().includes(group));
    expect(groupRow.find('ButtonsOnStatus').exists()).toBe(shouldRender);
  }

  function verifyApproversInTable(approvers, tablePosition) {
    const approvalPageRows = approvalPage.find('table').at(tablePosition).find('tr').slice(1);

    const actualApprovers = approvalPageRows
      .find('th')
      .map((header) => header.find('span').at(0).text());

    return expect(actualApprovers).toEqual(expect.arrayContaining(approvers));
  }

  function getButtonsForPendingStatus(approvalPage) {
    const rowsWithPendingStatus = firstSectionRow(approvalPage).find('.pending').first();
    const allButtons = rowsWithPendingStatus.find('ButtonGroup button');
    return allButtons;
  }

  const getButtonForRejectedStatus = (approvalPage) => {
    const rowWithRejectedStatus = firstSectionRow(approvalPage).find('.rejected').first();
    return rowWithRejectedStatus.find('Rework button');
  };

  const firstSectionRow = (approvalPage) => {
    return approvalPage.find('table').at(0).find('tr');
  };
});
