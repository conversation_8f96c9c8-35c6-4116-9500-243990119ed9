import React from 'react';
import { mount } from 'enzyme';
import DropdownSearchControl from '../../../component/TechnicalReports/DropdownSearchControl';
import { Typeahead } from 'react-bootstrap-typeahead';

jest.mock('react-bootstrap-typeahead', () => ({
  Typeahead: jest.fn(() => <div data-testid="typeahead-component"></div>),
}));

describe('DropdownSearchControl', () => {
  const mockOnChange = jest.fn();

  const defaultProps = {
    selectedVessel: { id: 1, value: 'Vessel One' },
    onChange: mockOnChange,
    vesselsDropdownData: [
      { id: 1, value: 'Vessel One' },
      { id: 2, value: 'Vessel Two' },
      { id: 3, value: 'Vessel Three' },
    ],
    labelKey: 'value',
    placeholder: 'Select a vessel',
    dropdownLoading: false,
    dataTestId: 'vessel-dropdown',
  };

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('should render the typeahead component', () => {
    const wrapper = mount(<DropdownSearchControl {...defaultProps} />);
    expect(wrapper.find('[data-testid="typeahead-component"]').exists()).toBe(true);
  });

  it('should call onChange when a vessel is selected', () => {
    Typeahead.mockImplementation(({ onChange }) => (
      <div
        data-testid="typeahead-item"
        role="button"
        tabIndex={0}
        onClick={() => onChange([{ id: 2, value: 'Vessel Two' }])}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            onChange([{ id: 2, value: 'Vessel Two' }]);
          }
        }}
      >
        Vessel Two
      </div>
    ));

    const wrapper = mount(<DropdownSearchControl {...defaultProps} />);
    wrapper.find('[data-testid="typeahead-item"]').simulate('click');

    expect(mockOnChange).toHaveBeenCalledTimes(1);
    expect(mockOnChange).toHaveBeenCalledWith({ id: 2, value: 'Vessel Two' });
  });

  it('should not call onChange if no vessel is selected', () => {
    Typeahead.mockImplementation(({ onChange }) => (
      <div
        data-testid="typeahead-item"
        role="button"
        tabIndex={0}
        onClick={() => onChange([])}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            onChange([]);
          }
        }}
      >
        Empty Selection
      </div>
    ));

    const wrapper = mount(<DropdownSearchControl {...defaultProps} />);
    wrapper.find('[data-testid="typeahead-item"]').simulate('click');

    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('should clear the search input when a vessel is selected', async () => {
    Typeahead.mockImplementation(({ onChange }) => (
      <div
        data-testid="typeahead-item"
        role="button"
        tabIndex={0}
        onClick={() => onChange([{ id: 3, value: 'Vessel Three' }])}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            onChange([{ id: 3, value: 'Vessel Three' }]);
          }
        }}
      >
        Vessel Three
      </div>
    ));

    const wrapper = mount(<DropdownSearchControl {...defaultProps} />);
    wrapper.find('[data-testid="typeahead-item"]').simulate('click');

    expect(mockOnChange).toHaveBeenCalledWith({ id: 3, value: 'Vessel Three' });
  });

  it('should use the filterBy function to filter vessels', () => {
    const filterProps = {
      ...defaultProps,
      selectedVessel: null,
    };

    Typeahead.mockImplementation(({ filterBy }) => {
      const result = filterBy({ value: 'Vessel One' }, { text: 'Vessel' });
      return <div data-testid="filtered-item">{result ? 'Item Found' : 'Item Not Found'}</div>;
    });

    const wrapper = mount(<DropdownSearchControl {...filterProps} />);
    expect(wrapper.find('[data-testid="filtered-item"]').text()).toBe('Item Found');
  });

  it('should clear the selected vessel if not found in search', async () => {
    Typeahead.mockImplementation(({ onChange }) => (
      <div
        data-testid="typeahead-item"
        role="button"
        tabIndex={0}
        onClick={() => onChange([])}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            onChange([]);
          }
        }}
      >
        No Vessel Found
      </div>
    ));

    const wrapper = mount(<DropdownSearchControl {...defaultProps} />);
    wrapper.find('[data-testid="typeahead-item"]').simulate('click');

    expect(mockOnChange).not.toHaveBeenCalled();
  });
});
