import React from 'react';
import { mount } from 'enzyme';
import TakeoverParticularsForm from '../../../component/takeover/TakeoverParticularsForm.js';
import { MemoryRouter } from 'react-router-dom';
import dropdowndata from '../../resources/drop-down-data.json';
import { Formik } from 'formik';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');

let wrapper;

beforeAll(() => {
  wrapper = mount(
    <MemoryRouter>
      <Formik>
        <TakeoverParticularsForm
          vessel={{ vessel_service_status_id: 2 }}
          dropDownData={dropdowndata.data}
          errors={{}}
          onInputChange={jest.fn()}
          handleBlur={jest.fn()}
          onDateChange={jest.fn()}
          isAllApproved={false}
        />
      </Formik>
    </MemoryRouter>,
  );
});

describe('TakeoverParticularsForm', () => {
  it('should have Header', () => {
    const title = wrapper.find('h6');
    expect(title.text()).toEqual('PARTICULARS');
  });

  it('should have form inputs', () => {
    const expectedInputs = [
      'Class',
      'Notation',
      'Flag (ISPS)',
      'Life Boat Capacity',
      'Length O.A.',
      'Length B.P.',
      'Depth',
      'Breadth (Extreme)',
      'Summer Draft',
      'Summer DWT',
      'International GRT',
      'International NRT',
      'Service Speed',
    ];
    const inputs = wrapper.find('FormLabel');
    expect(inputs.map((input) => input.text())).toEqual(expectedInputs);
  });
});
