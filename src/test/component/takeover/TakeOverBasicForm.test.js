import React from 'react';
import { mount } from 'enzyme';
import TakeoverBasicForm from '../../../component/takeover/TakeoverBasicForm';
import dropdowndata from '../../resources/drop-down-data.json';
import keycloakService from '../../../service/keycloak-service';
import { updateWrapper } from '../../../setupTests';
import { Formik } from 'formik';
import {
  activePendingVessel,
  activeButHandedOverNotStartedResponse,
  handedOverButArchivalNotStartedResponse,
  archivedVessel,
} from '../../resources/vessel-with-ownership';

let wrapper;

const memberShips = [
  {
    departmentGroup: 'Tech Group',
    positionGroup: 'Bunker Group',
    username: 'bunker-group.user2',
  },
  {
    departmentGroup: 'Accounts',
    positionGroup: 'Superintendent',
    username: 'bunker-group.user2',
  },
];

const techSupdtResponse = [
  {
    username: 'bunker-group.user1',
    full_name: 'Bunker-group User1',
    email: '<EMAIL>',
    position: 'Superintendent',
    subgroup_name: 'Bunker Group',
  },
  {
    username: 'bunker-group.user2',
    full_name: 'Bunker-group User2',
    email: '<EMAIL>',
    position: 'Superintendent',
    subgroup_name: 'Bunker Group',
  },
  {
    username: 'celcius-tech.user',
    full_name: 'Celcius Tech User',
    email: '<EMAIL>',
    position: 'Superintendent',
    subgroup_name: 'Celsius Tech',
  },
];

beforeEach(async () => {
  keycloakService.getSuperintendentsInTechGroup = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ data: techSupdtResponse }));
  wrapper = mount(
    <Formik>
      <TakeoverBasicForm
        vessel={{ vessel_service_status_id: 2 }}
        dropDownData={dropdowndata.data}
        errors={{}}
        onInputChange={jest.fn()}
        handleBlur={jest.fn()}
        onDateChange={jest.fn()}
        isAllApproved={false}
        userMemberships={memberShips}
        onVesselChange={jest.fn()}
      />
    </Formik>,
  );
  await updateWrapper(wrapper);
});

describe('BasicForm', () => {
  it('should have Header', () => {
    const title = wrapper.find('h6').at(0);

    expect(title.text()).toEqual('BASIC');
  });

  describe('vessel service status option', () => {
    it('should be enabled on add a vessel', () => {
      const serviceStatusOptions = wrapper.find('FormCheck');
      expect(serviceStatusOptions.map((option) => option.prop('label'))).toEqual([
        'New Building',
        'In Service',
      ]);
      expect(serviceStatusOptions.map((option) => option.prop('disabled'))).toEqual([false, false]);
    });

    it('should be disabled when vessel has id (on edit)', async () => {
      wrapper = mount(
        <Formik>
          <TakeoverBasicForm
            vessel={{ id: 1, vessel_service_status_id: 2 }}
            dropDownData={dropdowndata.data}
            errors={{}}
            onInputChange={jest.fn()}
            handleBlur={jest.fn()}
            onDateChange={jest.fn()}
            isAllApproved={false}
            userMemberships={memberShips}
            onVesselChange={jest.fn()}
          />
        </Formik>,
      );
      await updateWrapper(wrapper);
      const serviceStatusOptions = wrapper.find('FormCheck');
      expect(serviceStatusOptions.map((option) => option.prop('disabled'))).toEqual([true, true]);
    });
  });

  it('should have form inputs', () => {
    const expectedInputs = [
      'Vessel Name',
      'IMO Number',
      'Owners',
      'Vessel Type',
      'Emission Type',
      'Shipyard',
      'Vessel Hull Number',
      'Expected Date of Takeover',
      'Date of Takeover',
      'Date of Handover',
      'Tech Group',
      'Year Built/ Date of Delivery',
    ];
    const inputs = wrapper.find('FormLabel');

    expect(inputs.map((input) => input.text())).toEqual(expectedInputs);
  });

  describe('Tech Group and Supdt fields', () => {
    describe('when user belongs to some tech group for add a vessel', () => {
      it('should have user tech group [Bunker Group] filled', () => {
        const techGroupField = wrapper
          .find('FormControl')
          .filterWhere((control) => control.prop('name') == 'techgroup');

        expect(techGroupField.prop('value')).toEqual('Bunker Group');
      });
    });

    describe('when user select Tech Group for add a vessel', () => {
      let wrapper;
      const mockOnInputChange = jest.fn();

      beforeAll(async () => {
        wrapper = mount(
          <Formik>
            <TakeoverBasicForm
              vessel={{ vessel_service_status_id: 2 }}
              dropDownData={dropdowndata.data}
              errors={{}}
              onInputChange={mockOnInputChange}
              handleBlur={jest.fn()}
              onDateChange={jest.fn()}
              isAllApproved={false}
              userMemberships={[]}
              onVesselChange={jest.fn()}
            />
          </Formik>,
        );
        await updateWrapper(wrapper);
      });

      it('should have tech group field enabled and visible to select', async () => {
        const techGroupField = wrapper
          .find('FormControl')
          .filterWhere((control) => control.prop('name') == 'techgroup');
        expect(techGroupField.prop('value')).toEqual(undefined);
        techGroupField.find('select').simulate('change', { target: { value: 'Celsius Tech' } });
        await updateWrapper(wrapper);

        expect(mockOnInputChange).toHaveBeenCalled();
        expect(techGroupField.prop('hidden')).toEqual(undefined);
      });
    });
  });

  describe('handover date field', () => {
    const getHandoverDateInput = (wrapper) =>
      wrapper
        .find('FormGroup')
        .filterWhere(
          (wrp) =>
            wrp.find('label').length > 0 && wrp.find('label').at(0).text() === 'Date of Handover',
        )
        .find('input');

    it('should be disabled when vessel is being created', async () => {
      wrapper = mount(
        <Formik>
          <TakeoverBasicForm
            vessel={{ vessel_service_status_id: 2 }}
            dropDownData={dropdowndata.data}
            errors={{}}
            onInputChange={jest.fn()}
            handleBlur={jest.fn()}
            onDateChange={jest.fn()}
            isAllApproved={false}
            userMemberships={memberShips}
            onVesselChange={jest.fn()}
          />
        </Formik>,
      );
      await updateWrapper(wrapper);

      const handoverDateInput = getHandoverDateInput(wrapper);
      expect(handoverDateInput.prop('disabled')).toEqual(true);
    });
    it('should be disabled when vessel status is draft', async () => {
      wrapper = mount(
        <Formik>
          <TakeoverBasicForm
            vessel={activePendingVessel[0]}
            dropDownData={dropdowndata.data}
            errors={{}}
            onInputChange={jest.fn()}
            handleBlur={jest.fn()}
            onDateChange={jest.fn()}
            isAllApproved={false}
            userMemberships={memberShips}
            onVesselChange={jest.fn()}
          />
        </Formik>,
      );
      await updateWrapper(wrapper);

      const handoverDateInput = getHandoverDateInput(wrapper);
      expect(handoverDateInput.prop('disabled')).toEqual(true);
    });

    it('should be disabled when vessel status is active', async () => {
      wrapper = mount(
        <Formik>
          <TakeoverBasicForm
            vessel={activeButHandedOverNotStartedResponse}
            dropDownData={dropdowndata.data}
            errors={{}}
            onInputChange={jest.fn()}
            handleBlur={jest.fn()}
            onDateChange={jest.fn()}
            isAllApproved={false}
            userMemberships={memberShips}
            onVesselChange={jest.fn()}
          />
        </Formik>,
      );
      await updateWrapper(wrapper);

      const handoverDateInput = getHandoverDateInput(wrapper);
      expect(handoverDateInput.prop('disabled')).toEqual(true);
    });

    it('should be enabled when vessel status is handed_over ', async () => {
      let wrapper = mount(
        <Formik>
          <TakeoverBasicForm
            vessel={handedOverButArchivalNotStartedResponse[0]}
            dropDownData={dropdowndata.data}
            errors={{}}
            onInputChange={jest.fn()}
            handleBlur={jest.fn()}
            onDateChange={jest.fn()}
            isAllApproved={false}
            userMemberships={memberShips}
            onVesselChange={jest.fn()}
          />
        </Formik>,
      );
      await updateWrapper(wrapper);

      const handoverDateInput = getHandoverDateInput(wrapper);
      expect(handoverDateInput.prop('disabled')).toEqual(true);
    });

    it('should be enabled when vessel status is archived ', async () => {
      let wrapper = mount(
        <Formik>
          <TakeoverBasicForm
            vessel={archivedVessel[0]}
            dropDownData={dropdowndata.data}
            errors={{}}
            onInputChange={jest.fn()}
            handleBlur={jest.fn()}
            onDateChange={jest.fn()}
            isAllApproved={false}
            userMemberships={memberShips}
            onVesselChange={jest.fn()}
          />
        </Formik>,
      );
      await updateWrapper(wrapper);

      const handoverDateInput = getHandoverDateInput(wrapper);
      expect(handoverDateInput.prop('disabled')).toEqual(true);
    });
  });
});
