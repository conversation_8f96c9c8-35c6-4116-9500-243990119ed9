import React from 'react';
import { mount } from 'enzyme';
import TakeoverMiscellaneousForm from '../../../component/takeover/TakeoverMiscellaneousForm.js';
import { MemoryRouter } from 'react-router-dom';
import dropdowndata from '../../resources/drop-down-data.json';
import { updateWrapper } from '../../../setupTests';

jest.mock('../../../service/user-service');
let wrapper;

beforeAll(() => {
  wrapper = mount(
    <MemoryRouter>
      <TakeoverMiscellaneousForm
        vessel={{ vessel_service_status_id: 2 }}
        dropDownData={dropdowndata.data}
        errors={{}}
        onInputChange={jest.fn()}
        handleBlur={jest.fn()}
        onDateChange={jest.fn()}
        isAllApproved={false}
      />
    </MemoryRouter>,
  );
});

describe('TakeoverMiscellaneousForm', () => {
  it('should have Header', () => {
    const title = wrapper.find('h6').at(0);
    expect(title.text()).toEqual('MISCELLANEOUS');
  });

  it('should have form inputs', () => {
    const expectedInputs = [
      'Engine',
      'DWT',
      'Power(kW)',
      'Engine Consumes LNG?',
      'LNG Engine Category (Main Engine)',
      'LNG Engine Category (Diesel Generator)',
      'Registered Owner',
      'Operator',
      'Manager',
      'Is Manning Manager',
      'Classification',
      'Classification Society (Emergency Response Assistance)',
      'EU Verifier',
      'QI',
      'OSRO',
      'Salvage',
      'Media Response',
      'Other Contacts',
      'Management Type',
      'US Visa Required',
      'P & I Club',
      'H & M Underwriter',
      'IHM Provider',
      'IHM Initial Inspection Date',
    ];
    const inputs = wrapper.find('FormLabel');
    expect(inputs.map((input) => input.text())).toEqual(expectedInputs);
  });
});

describe('Disable Registered Owner when vessel is active ', () => {
  it('should render a change ownership process message', async () => {
    const takeover = mount(
      <MemoryRouter>
        <TakeoverMiscellaneousForm
          vessel={{ vessel_service_status_id: 2, status: 'active', isOwnershipChangePending: true }}
          dropDownData={dropdowndata.data}
          errors={{}}
          onInputChange={jest.fn()}
          handleBlur={jest.fn()}
          onDateChange={jest.fn()}
          isAllApproved={false}
        />
      </MemoryRouter>,
    );

    await updateWrapper(takeover);

    const ownershipText = takeover
      .find('FormText')
      .filterWhere(
        (input) =>
          input.text() ===
          'To change this field, it needs to be done by Business Team through the change ownership process.',
      );
    expect(ownershipText.exists()).toEqual(true);
  });

  it('should render a change ownership process with link to ownership request page', async () => {
    const takeover = mount(
      <MemoryRouter>
        <TakeoverMiscellaneousForm
          vessel={{
            vessel_service_status_id: 2,
            status: 'active',
            isOwnershipChangePending: false,
          }}
          dropDownData={dropdowndata.data}
          errors={{}}
          onInputChange={jest.fn()}
          handleBlur={jest.fn()}
          onDateChange={jest.fn()}
          isAllApproved={false}
        />
      </MemoryRouter>,
    );

    await updateWrapper(takeover);

    const ownershipLink = takeover
      .find('.change-ownership-link')
      .filterWhere((input) => input.text() === 'change ownership');
    expect(ownershipLink.exists()).toEqual(true);
  });
});
