import React from 'react';
import { mount } from 'enzyme';
import TakeoverFinancialForm from '../../../component/takeover/TakeoverFinancialForm';
import dropdowndata from '../../resources/drop-down-data.json';
import keycloakService from '../../../service/keycloak-service';
import { updateWrapper } from '../../../setupTests';
import { Formik } from 'formik';

jest.setTimeout(30000);

const techSupdtResponse = [
  {
    username: 'bunker-group.user1',
    full_name: 'Bunker-group User1',
    email: '<EMAIL>',
    position: 'Superintendent',
    subgroup_name: 'Bunker Group',
  },
  {
    username: 'bunker-group.user2',
    full_name: 'Bunker-group User2',
    email: '<EMAIL>',
    position: 'Superintendent',
    subgroup_name: 'Bunker Group',
  },
  {
    username: 'celcius-tech.user',
    full_name: '<PERSON><PERSON><PERSON> Tech User',
    email: '<EMAIL>',
    position: 'Superintendent',
    subgroup_name: 'Celsius Tech',
  },
];

describe('test on standalone rending TakeOverFinancialForm component', () => {
  let wrapper;
  const mockOnInputChange = jest.fn();

  beforeAll(async () => {
    keycloakService.getSuperintendentsInTechGroup = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: techSupdtResponse }));

    // Note: need to wrap <TakeoverFinancialForm> with <Formik> to avoid error: TypeError: formik.getFieldProps is not a function
    wrapper = mount(
      <Formik>
        <TakeoverFinancialForm
          vessel={{ vessel_service_status_id: 2 }}
          dropDownData={dropdowndata.data}
          errors={{}}
          onInputChange={mockOnInputChange}
          handleBlur={jest.fn()}
          onInputArrayChange={jest.fn()}
          onInputArrayRemoveRow={jest.fn()}
        />
      </Formik>,
    );
    await updateWrapper(wrapper);
  });
  describe('TakeoverFinancialForm', () => {
    it('should have Header', () => {
      const title = wrapper.find('h6');
      expect(title.text()).toEqual('PORTAGE BILL RELATED');
    });

    it('should have form inputs', () => {
      const expectedInputs = [
        'Wages Treatment',
        'Accumulation Wages',
        'Portage bill module',
        'Vessel Account Code',
        'Accounting Currency',
        'Pay Calculation Method'
      ];
      const inputs = wrapper.find('FormLabel');
      expect(inputs.map((input) => input.text())).toEqual(expectedInputs);
    });
  });
});
