import React from 'react';
import { mount } from 'enzyme';
import { MemoryRouter } from 'react-router-dom';
import dropdowndata from '../../resources/drop-down-data.json';
import { updateWrapper } from '../../../setupTests';
import TakeoverTrialForm from '../../../component/takeover/TakeoverTrialForm.js';

let wrapper;

beforeAll(() => {
  wrapper = mount(
    <MemoryRouter>
      <TakeoverTrialForm
        vessel={{ vessel_service_status_id: 2 }}
        dropDownData={dropdowndata.data}
        errors={{}}
        onInputChange={jest.fn()}
        handleBlur={jest.fn()}
        onDateChange={jest.fn()}
        isAllApproved={false}
      />
    </MemoryRouter>,
  );
});

describe('TakeoverTrialForm', () => {
  it('should render a change ownership process message', async () => {
    const takeover = mount(
      <MemoryRouter>
        <TakeoverTrialForm
          vessel={{ vessel_service_status_id: 2 }}
          dropDownData={dropdowndata.data}
          errors={{}}
          onInputChange={jest.fn()}
          handleBlur={jest.fn()}
          onDateChange={jest.fn()}
          isAllApproved={false}
        />
      </MemoryRouter>,
    );

    await updateWrapper(wrapper);

    const headerText = takeover.find('h6').at(0);
    expect(headerText.text()).toEqual('Maximum continuous rating (MCR) of engine');
  });
});
