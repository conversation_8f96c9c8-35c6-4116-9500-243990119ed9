import React from 'react';
import { mount } from 'enzyme';
import TakeoverOfficeDataForm from '../../../component/takeover/TakeoverOfficeDataForm';
import { MemoryRouter } from 'react-router-dom';
import dropdowndata from '../../resources/drop-down-data.json';

let wrapper;

beforeAll(() => {
  wrapper = mount(
    <MemoryRouter>
      <TakeoverOfficeDataForm
        vessel={{ vessel_service_status_id: 2 }}
        dropDownData={dropdowndata.data}
        errors={{}}
        onInputChange={jest.fn()}
        handleBlur={jest.fn()}
        onDateChange={jest.fn()}
        isAllApproved={false}
      />
    </MemoryRouter>,
  );
});

describe('TakeoverOfficeDataForm', () => {
  it('should have Header', () => {
    const title = wrapper.find('h6').at(0);
    expect(title.text()).toEqual('OFFICE DATA');
  });

  it('should have form inputs', () => {
    const expectedInputs = [
      'Vessel Short Code',
      'Vessel Tel FAC Code',
      'Vessel Account Code (OLD)',
    ];
    const inputs = wrapper.find('FormLabel');
    expect(inputs.map((input) => input.text())).toEqual(expectedInputs);
  });
});
