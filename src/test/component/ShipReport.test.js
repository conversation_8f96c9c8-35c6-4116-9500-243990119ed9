import React from 'react';
import { mount } from 'enzyme';
import { MemoryRouter } from 'react-router-dom';
import { updateWrapper } from '../../setupTests';
import { signedOnSeafarer } from '../resources/signedOnSeafarer';
import { itineraryData } from '../resources/itineraryData';
import { handedOverPendingVessel } from '../resources/vessel-with-ownership';
import ShipReport from '../../component/ShipReport';
import { positionReportData } from '../resources/positionReportData';
import { CHIEF_ENGINEER, MASTER } from '../../model/constants';
import DetailContextProvider from '../../context/DetailContext';

const locationData = {
  position: '222019N114723E',
  time: '2022-05-12T07:00:29',
  lat: '22.3387083',
  lon: '114.1231633',
};
const master = signedOnSeafarer.filter((data) => data.rank_value === MASTER)[0];
const chiefEngineer = signedOnSeafarer.filter((data) => data.rank_value === CHIEF_ENGINEER)[0];
const lastReport = positionReportData.results;
const lastItinerary = itineraryData.results;
const vesselData = {
  certificate_due: '0',
  certificate_due_30: '0',
  certificate_due_60: '0',
};
const getRoleConfig = (hasRole) => {
  return {
    vessel: { view: true },
    seafarerViewCrewList: hasRole,
    seafarerGeneralView: hasRole,
  };
};

const renderShipReport = async (vesselId, ownershipId, hasRole) => {
  const shipReport = mount(
    <MemoryRouter>
      <DetailContextProvider roleConfig={getRoleConfig(hasRole)}>
        <ShipReport
          lastPosition={locationData}
          lastItinerary={lastItinerary}
          lastReport={lastReport}
          master={master}
          chiefEngineer={chiefEngineer}
          vesselId={vesselId}
          ownershipId={ownershipId}
          vesselData={vesselData}
        />
      </DetailContextProvider>
    </MemoryRouter>,
  );
  await updateWrapper(shipReport);
  return shipReport;
};

describe('<ShipReport />', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render Crew List link', async () => {
    const renderedView = await renderShipReport(
      handedOverPendingVessel[0].id,
      handedOverPendingVessel[0].ownerships[0].id,
      true,
    );

    const links = renderedView
      .find('Button div.ship-report-wrapper__link')
      .map((e) => e.props().children);
    const crewList = 'Crew List';
    expect(links).toContain(crewList);
  });

  it('should render master & cheif engg name', async () => {
    const renderedView = await renderShipReport(
      handedOverPendingVessel[0].id,
      handedOverPendingVessel[0].ownerships[0].id,
      true,
    );

    const crewAssignmentValues = renderedView
      .find('div.ship-report-wrapper__link')
      .map((e) => e.props().children);

    let master = signedOnSeafarer.filter((e) => e.rank_value == 'MASTER')[0];
    let chiefEngineer = signedOnSeafarer.filter((e) => e.rank_value == 'CHIEF ENGINEER')[0];
    const expectedCrewAssignmentValues = [
      master.first_name + ' ' + master.last_name,
      chiefEngineer.first_name + ' ' + chiefEngineer.last_name,
    ];

    expect(expectedCrewAssignmentValues).toContain(...crewAssignmentValues);
  });

  it('should not render Crew List link if no role', async () => {
    const renderedView = await renderShipReport(
      handedOverPendingVessel[0].id,
      handedOverPendingVessel[0].ownerships[0].id,
      false,
    );

    const links = renderedView
      .find('Button div.ship-report-wrapper__link')
      .map((e) => e.props().children);
    expect(links).toEqual([]);
  });

  it('should not render master & cheif engg name if no role', async () => {
    const renderedView = await renderShipReport(
      handedOverPendingVessel[0].id,
      handedOverPendingVessel[0].ownerships[0].id,
      false,
    );

    const crewAssignmentValues = renderedView
      .find('div.ship-report-wrapper__link')
      .map((e) => e.props().children);
    expect(crewAssignmentValues).toEqual([]);
  });
});
