import React from 'react';
import { mount } from 'enzyme';
import ClearAllModalView from '../../../component/advanced_search/ClearAllModalView';
import Modal from 'react-bootstrap/Modal';
import Button from 'react-bootstrap/Button';

describe('ClearAllModalView', () => {
  const onCloseMock = jest.fn();
  const onConfirmMock = jest.fn();

  const defaultProps = {
    onClose: onCloseMock,
    onConfirm: onConfirmMock,
    show: true,
  };

  beforeEach(() => {
    onCloseMock.mockClear();
    onConfirmMock.mockClear();
  });

  it('should render the modal correctly', () => {
    const wrapper = mount(<ClearAllModalView {...defaultProps} />);

    expect(wrapper.find(Modal.Title).text()).toBe('Confirm Clearing All?');
    expect(wrapper.find(Modal.Body).text()).toBe('Are you sure to clear all search criteria?');
    expect(wrapper.find(Button).at(0).text()).toBe('Cancel');
    expect(wrapper.find(Button).at(1).text()).toBe('Confirm');
  });

  it('should call onClose when Cancel button is clicked', () => {
    const wrapper = mount(<ClearAllModalView {...defaultProps} />);
    wrapper.find(Button).at(0).simulate('click');

    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it('should call onConfirm when Confirm button is clicked', () => {
    const wrapper = mount(<ClearAllModalView {...defaultProps} />);
    wrapper.find(Button).at(1).simulate('click');

    expect(onConfirmMock).toHaveBeenCalledTimes(1);
  });

  it('should not render the modal when show prop is false', () => {
    const wrapper = mount(<ClearAllModalView {...defaultProps} show={false} />);
    expect(wrapper.find(Modal).prop('show')).toBe(false);
    expect(wrapper.isEmptyRender()).toBe(true);
  });

  it('should have the modal centered with aria-labelledby', () => {
    const wrapper = mount(<ClearAllModalView {...defaultProps} />);
    const modal = wrapper.find(Modal);
    expect(modal.prop('aria-labelledby')).toBe('contained-modal-title-vcenter');
  });

  it('should not render confirm button if the modal is closed', () => {
    const wrapper = mount(<ClearAllModalView {...defaultProps} show={false} />);
    expect(wrapper.find(Button).at(1).exists()).toBe(false);
  });

  it('should render without crashing even if onClose or onConfirm is not provided', () => {
    const wrapper = mount(<ClearAllModalView show={true} />);
    expect(wrapper.exists()).toBe(true);
  });
});
