import { mount } from 'enzyme';
import React from 'react';
import { MemoryRouter } from 'react-router-dom';
import ManualsIndividualPage from '../../../component/drawingsAndManuals/ManualsIndividualPage';
import DetailContextProvider from '../../../context/DetailContext';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import assignedVesselsToManualsData from '../../resources/assigned-vessel-to-manuals-data.json';

describe('<ManualsIndividualPage />', () => {
  let assignedVesselToManualsList;

  beforeEach(async () => {
    vesselService.getAssignedVesselsListToManuals = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: assignedVesselsToManualsData }));

    assignedVesselToManualsList = mount(
      <MemoryRouter>
        <DetailContextProvider>
          <ManualsIndividualPage />
        </DetailContextProvider>
      </MemoryRouter>,
    );

    await updateWrapper(assignedVesselToManualsList);
  });

  describe('should render manual individual page', () => {
    it('should render columns, when manual individual page list loads', async () => {
      const customTable = assignedVesselToManualsList.find('CustomTable');
      const customTableColumns = customTable.find('.th').map((i) => i.text());
      expect(customTableColumns).toEqual([
        'No.',
        'Vessel',
        'Date Uploaded',
        'Remarks',
        'Document',
        'Action',
      ]);
    });

    it('should render spinner, when manuals list is on fetch', async () => {
      vesselService.getAssignedVesselsListToManuals = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      const assignedVesselToManualsList = mount(
        <MemoryRouter>
          <DetailContextProvider>
            <ManualsIndividualPage />
          </DetailContextProvider>
        </MemoryRouter>,
      );
      expect(assignedVesselToManualsList.find('Spinner').exists()).toEqual(true);
    });

    describe('manual individual page Table pagination tests', () => {
      it('should change active page num on page number click', async () => {
        const page1 = assignedVesselToManualsList.find('[data-testid="fml-pagination-1"]');
        expect(page1.props()['className']).toContain('page-num-active');

        const page2 = assignedVesselToManualsList.find('[data-testid="fml-pagination-2"]');
        page2.simulate('click');
        await updateWrapper(assignedVesselToManualsList);
        expect(
          assignedVesselToManualsList.find('[data-testid="fml-pagination-2"]').props()['className'],
        ).toContain('page-num-active');
      });

      it('should render result count when page loads', async () => {
        const totalCount = assignedVesselToManualsList.find('CustomTable').props().totalCount;
        expect(totalCount).toEqual(12);
      });
    });
  });
});
