import { mount } from 'enzyme';
import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import ManualsList from '../../../component/drawingsAndManuals/ManualsList';
import DetailContextProvider from '../../../context/DetailContext';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import manualListData from '../../resources/manual-list-data.json';

const getRoleConfig = (hasRole) => {
  return {
    vessel: {
      edit: hasRole,
    },
  };
};

describe('<ManualsList />', () => {
  let manualsList;

  beforeEach(async () => {
    vesselService.getManualsList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: manualListData }));

    manualsList = mount(
      <MemoryRouter initialEntries={['vessel/ownership/details/1833/manual']}>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Route path="vessel/ownership/details/:ownershipId">
            <ManualsList />
          </Route>
        </DetailContextProvider>
      </MemoryRouter>,
    );

    await updateWrapper(manualsList);
  });

  describe('should render manual list', () => {
    it('should render columns, when manuals list is loaded', async () => {
      const customTable = manualsList.find('CustomTable');
      const actualManualList = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Manual Type',
        'Date Uploaded',
        'Remarks',
        'Document',
        'Action',
      ];
      expect(actualManualList).toEqual(expectedHeaders);
    });

    it('should render spinner, when manuals list is on fetch', async () => {
      vesselService.getManualsList = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      manualsList = mount(
        <MemoryRouter initialEntries={['vessel/ownership/details/1833/manual']}>
          <DetailContextProvider  roleConfig={getRoleConfig(true)}>
            <Route path="vessel/ownership/details/:ownershipId">
              <ManualsList />
            </Route>
          </DetailContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(manualsList);
      expect(manualsList.find('Spinner').exists()).toEqual(true);
    });

    describe('manuals list tab when page loads', () => {
      it('should add report button render and open modal on button click when manual report page loads', async () => {
        const addButton = manualsList.find('button[data-testid="fml-manual-list-add-Button"]');
        addButton.simulate('click');
        await updateWrapper(manualsList);

        const manualDialog = manualsList.find('[data-testid="fml-manual-dialog"]');
        const saveButton = manualDialog.find('button[data-testid="fml-manual-dialog-save"]');
        const cancelButton = manualDialog.find('button[data-testid="fml-manual-dialog-cancel"]');

        const formLabel = manualDialog.find('.form-label').map((i) => i.text());
        const expectedFormLabel = ['Ship*', 'Manual Type*', 'Date*', 'Remark', 'Document*'];
        expect(formLabel).toEqual(expectedFormLabel);
        expect(addButton.exists()).toEqual(true);
        expect(manualDialog.exists()).toEqual(true);
        expect(saveButton.exists()).toEqual(true);
        expect(cancelButton.exists()).toEqual(true);
      });
    });

    describe('manuals list Table pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const selectPage = async (page, page_num) => {
        page
          .find('div.page-num')
          .find('.page-num-enabled')
          .filterWhere((e) => Number(e.text()) === Number(page_num))
          .at(0)
          .simulate('click');

        await updateWrapper(page);
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        await updateWrapper(manualsList);
        await selectPage(manualsList, 2);

        expect(findSelectedPageNumber(manualsList)).toEqual(2);
        expect(findPageSizeValue(manualsList)).toEqual(10);
      });
    });

    it('should render columns without action, if not edit role', async () => {
      const manualsList = mount(
        <MemoryRouter initialEntries={['vessel/ownership/details/1833/manual']}>
          <DetailContextProvider roleConfig={getRoleConfig(false)}>
            <Route path="vessel/ownership/details/:ownershipId">
              <ManualsList />
            </Route>
          </DetailContextProvider>
        </MemoryRouter>,
      );
  
      await updateWrapper(manualsList);
      const customTable = manualsList.find('CustomTable');
      const actualManualList = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Manual Type',
        'Date Uploaded',
        'Remarks',
        'Document',
      ];
      expect(actualManualList).toEqual(expectedHeaders);
    });
  });
});
