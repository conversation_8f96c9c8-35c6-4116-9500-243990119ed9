import React from 'react';
import { mount } from 'enzyme';
import ReopenReportModal from '../../component/ReopenReportModal';
import { updateWrapper } from '../../setupTests';
describe('ReopenReportModal', () => {
  let wrapper;
  const onConfirm = jest.fn();
  const onClose = jest.fn();
  const reportId = '123';
  beforeEach(async () => {
    wrapper = mount(
        <ReopenReportModal
          onConfirm={onConfirm}
          reopenId={reportId}
          onClose={onClose}
        />
    );
    await updateWrapper(wrapper);
  });
  it('should render reopen report pop up', () => {
    // Check reopen report modal
    const reopenModalCancel = wrapper.find('[dataTestId="reopen-rpt-modal-cancel"]');
    const reopenModalConfirm = wrapper.find('[dataTestId="reopen-rpt-modal-confirm"]');
    expect(reopenModalCancel).toHaveLength(2);
    expect(reopenModalConfirm).toHaveLength(2);
    expect(reopenModalCancel.exists()).toEqual(true);
    expect(reopenModalConfirm.exists()).toEqual(true);
    // Simulate closing reopen report modal
    reopenModalCancel.at(1).simulate('click');
    expect(onClose).toHaveBeenCalled();
    // Simulate confirming reopen report
    reopenModalConfirm.at(1).simulate('click');
    expect(onConfirm).toHaveBeenCalled();
  });
});