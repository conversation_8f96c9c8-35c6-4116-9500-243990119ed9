import React from 'react';
import { mount } from 'enzyme';
import Approval from '../../../pages/Approval';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import vesselApprovalService from '../../../service/vessel-approval-service';
import vesselApprovalJson from '../../resources/get-approval.json';
import { getDataWithFinalApproverStatus } from '../../resources/get-all-fisrt-approved.js';
import { activePendingVessel, handedOverPendingVessel } from '../../resources/vessel-approval';
import { FIRST_APPROVERS_GROUPS } from '../../../model/constants';
import moment from 'moment';

const roleConfig = {
  vessel: {
    moveToActive: true,
    moveToHandover: true,
    moveToArchival: true,
  },
  techGroups: [],
  approvalGroups: FIRST_APPROVERS_GROUPS,
  finalApprover: true,
};

function mockVesselDataCallFor(status) {
  const vesselData = status == 'draft' ? activePendingVessel : handedOverPendingVessel;
  vesselService.getVessel = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ status: 200, data: vesselData[0] }));
}

function mockVesselApprovalDataCall() {
  vesselApprovalService.getVesselApprovalData = jest
    .fn()
    .mockImplementation(() => Promise.resolve(vesselApprovalJson));
}

describe('<MoveToNewStatusSection />', () => {
  beforeEach(async () => {
    mockVesselDataCallFor('draft');
    vesselApprovalService.getVesselApprovalData = jest
      .fn()
      .mockImplementation(() => Promise.resolve(getDataWithFinalApproverStatus('approved')));

    vesselService.applyPendingStatus = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ status: 200, data: activePendingVessel }));
  });

  it('should call update vessel with status active, when no input pending for vessel', async () => {
    vesselService.isAnyInputPending = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: false }));

    const approvalPage = mount(
      <MemoryRouter initialEntries={['/vessel/details/11/approval']}>
        <Route path="/vessel/details/:vesselId/approval">
          <Approval roleConfig={roleConfig} />
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(approvalPage);
    approvalPage
      .find('[data-testid="fml-move-to-new-status-date"]')
      .simulate('change', { target: { value: moment().format('DD MMM yyyy') } });
    await updateWrapper(approvalPage);
    const moveToActiveButton = approvalPage.find(
      '[data-testid="fml-Approval-moveToNewStatusSection"]',
    );
    moveToActiveButton.at(0).simulate('click');
    await updateWrapper(approvalPage);

    clickOnModelActionButton(approvalPage, 'Confirm');
    await updateWrapper(approvalPage);

    expect(vesselService.applyPendingStatus).toHaveBeenCalledWith({
      date_of_takeover: moment().format('DD MMM yyyy'),
      vessel_id: 11,
    });
    expect(vesselApprovalService.getVesselApprovalData).toHaveBeenCalledTimes(2);
  });

  it('should show missing input model, when any input is pending for vessel', async () => {
    vesselService.isAnyInputPending = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: true }));

    const approvalPage = mount(
      <MemoryRouter initialEntries={['/vessel/details/11/approval']}>
        <Route path="/vessel/details/:vesselId/approval">
          <Approval roleConfig={roleConfig} />
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(approvalPage);
    approvalPage
      .find('[data-testid="fml-move-to-new-status-date"]')
      .simulate('change', { target: { value: moment().format('d MMM yyyy') } });
    await updateWrapper(approvalPage);
    const moveToActiveButton = approvalPage.find(
      '[data-testid="fml-Approval-moveToNewStatusSection"]',
    );
    moveToActiveButton.at(0).simulate('click');
    await updateWrapper(approvalPage, 1000);

    expect(vesselApprovalService.getVesselApprovalData).toHaveBeenCalledTimes(1);
  });
});

const clickOnModelActionButton = (approvalPage, actionName) => {
  const modelButtons = approvalPage.find('ModalDialog ModalFooter Button');
  const actionButton = modelButtons.filterWhere((btn) => btn.text() == actionName);
  actionButton.simulate('click');
};
