import React from 'react';
import { mount } from 'enzyme';
import Approval from '../../../pages/Approval';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import vesselApprovalService from '../../../service/vessel-approval-service';
import vesselApprovalJson from '../../resources/get-approval.json';
import { activePendingVessel, handedOverPendingVessel } from '../../resources/vessel-approval';
import { FIRST_APPROVERS_GROUPS } from '../../../model/constants';

let approvalPage;
const roleConfig = {
  vessel: {
    moveToActive: true,
    moveToHandover: true,
    moveToArchival: true,
  },
  approvalTechGroups: [],
  approvalGroups: FIRST_APPROVERS_GROUPS,
  finalApprover: true,
};

function mockVesselDataCallFor(status) {
  const vesselData = status == 'draft' ? activePendingVessel : handedOverPendingVessel;
  vesselService.getVessel = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ status: 200, data: vesselData[0] }));
}

function mockVesselApprovalDataCall() {
  vesselApprovalService.getVesselApprovalData = jest
    .fn()
    .mockImplementation(() => Promise.resolve(vesselApprovalJson));
}

describe('<Reject />', () => {
  beforeEach(async () => {
    mockVesselDataCallFor('draft');
    mockVesselApprovalDataCall();
    vesselApprovalService.updateVesselApprovalStatus = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ status: 200, data: 1 }));
    approvalPage = mount(
      <MemoryRouter initialEntries={['/vessel/details/11/approval']}>
        <Route path="/vessel/details/:vesselId/approval">
          <Approval roleConfig={roleConfig} />
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(approvalPage);
    const allButtons = getButtonsForPendingStatus(approvalPage);
    const rejectBtn = allButtons.filterWhere((btn) => btn.text() == 'Reject');
    rejectBtn.simulate('click');
    await updateWrapper(approvalPage);
  });

  it('should call api for vessel update with rejected status, when remarks are given', async () => {
    approvalPage.find('textarea').simulate('change', { target: { value: 'some remarks' } });
    clickOnModelActionButton(approvalPage, 'Confirm');
    await updateWrapper(approvalPage);

    expect(vesselApprovalService.updateVesselApprovalStatus).toHaveBeenCalledWith(
      6,
      'rejected',
      'some remarks',
    );
    expect(vesselApprovalService.getVesselApprovalData).toHaveBeenCalledTimes(2);
  });

  it('should not call api for vessel update with rejected status, when remarks are not given', async () => {
    clickOnModelActionButton(approvalPage, 'Confirm');
    await updateWrapper(approvalPage);

    expect(vesselApprovalService.updateVesselApprovalStatus).toHaveBeenCalledTimes(0);

    expect(vesselApprovalService.getVesselApprovalData).toHaveBeenCalledTimes(1);
  });
});

function getButtonsForPendingStatus(approvalPage) {
  const rowsWithPendingStatus = approvalPage.find('table').at(1).find('tr.pending').first();
  const allButtons = rowsWithPendingStatus.find('ButtonGroup button');
  return allButtons;
}

const clickOnModelActionButton = (approvalPage, actionName) => {
  const modelButtons = approvalPage.find('ModalDialog ModalFooter Button');
  const actionButton = modelButtons.filterWhere((btn) => btn.text() == actionName);
  actionButton.simulate('click');
};
