import React from 'react';
import { mount } from 'enzyme';
import Approval from '../../../pages/Approval';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import vesselApprovalService from '../../../service/vessel-approval-service';
import vesselApprovalJson from '../../resources/get-approval.json';
import { activePendingVessel, handedOverPendingVessel } from '../../resources/vessel-approval';
import { FIRST_APPROVERS_GROUPS } from '../../../model/constants';

const roleConfig = {
  vessel: {
    moveToActive: true,
    moveToHandover: true,
    moveToArchival: true,
  },
  techGroups: [],
  approvalGroups: FIRST_APPROVERS_GROUPS,
  finalApprover: true,
};

function mockVesselDataCallFor(status) {
  const vesselData = status == 'draft' ? activePendingVessel : handedOverPendingVessel;
  vesselService.getVessel = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ status: 200, data: vesselData[0] }));
}

function mockVesselApprovalDataCall() {
  vesselApprovalService.getVesselApprovalData = jest
    .fn()
    .mockImplementation(() => Promise.resolve(vesselApprovalJson));
}

describe('<Rework />', () => {
  it('should call api for vessel update with pending status, on click of button', async () => {
    mockVesselDataCallFor('draft');
    mockVesselApprovalDataCall();
    vesselApprovalService.updateVesselApprovalStatus = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ status: 200, data: 1 }));
    const approvalPage = mount(
      <MemoryRouter initialEntries={['/vessel/details/11/approval']}>
        <Route path="/vessel/details/:vesselId/approval">
          <Approval roleConfig={roleConfig} />
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(approvalPage);

    const rejectButton = getButtonForRejectedStatus(approvalPage);

    rejectButton.simulate('click');
    await updateWrapper(approvalPage);
    clickOnModelActionButton(approvalPage, 'Confirm');
    await updateWrapper(approvalPage);

    expect(vesselApprovalService.updateVesselApprovalStatus).toHaveBeenCalledWith(
      4,
      'pending',
      null,
    );
    expect(vesselApprovalService.getVesselApprovalData).toHaveBeenCalledTimes(2);
  });
});

const getButtonForRejectedStatus = (approvalPage) => {
  const rowsWithRejectedStatus = firstSectionRow(approvalPage).find('.rejected Rework');
  const reworkRow = rowsWithRejectedStatus.filterWhere((row) => row.prop('group') == 'Insurance');
  return reworkRow.find('button');
};

const firstSectionRow = (approvalPage) => {
  return approvalPage.find('table').at(0).find('tr');
};

const clickOnModelActionButton = (approvalPage, actionName) => {
  const modelButtons = approvalPage.find('ModalDialog ModalFooter Button');
  const actionButton = modelButtons.filterWhere((btn) => btn.text() == actionName);
  actionButton.simulate('click');
};
