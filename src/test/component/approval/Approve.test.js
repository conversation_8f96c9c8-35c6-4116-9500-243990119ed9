import React from 'react';
import { mount } from 'enzyme';
import Approval from '../../../pages/Approval';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import vesselApprovalService from '../../../service/vessel-approval-service';
import vesselApprovalJson from '../../resources/get-approval.json';
import { activePendingVessel } from '../../resources/vessel-approval';
import { FIRST_APPROVERS_GROUPS } from '../../../model/constants';

function mockVesselDataCallFor() {
  vesselService.getVessel = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ status: 200, data: activePendingVessel[0] }));
}

function mockVesselApprovalDataCall() {
  vesselApprovalService.getVesselApprovalData = jest
    .fn()
    .mockImplementation(() => Promise.resolve(vesselApprovalJson));
}

const roleConfig = {
  vessel: {
    moveToActive: true,
    moveToHandover: true,
    moveToArchival: true,
  },
  techGroups: [],
  approvalGroups: FIRST_APPROVERS_GROUPS,
  finalApprover: true,
};

describe('<Approve />', () => {
  it('should call api for vessel update with approved status, on click of button', async () => {
    mockVesselDataCallFor();
    mockVesselApprovalDataCall();
    vesselApprovalService.updateVesselApprovalStatus = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ status: 200, data: 1 }));
    const approvalPage = mount(
      <MemoryRouter initialEntries={['vessel/details/11/approval']}>
        <Route path="vessel/details/:vesselId/approval">
          <Approval roleConfig={roleConfig} />
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(approvalPage);

    const allButtons = getButtonsForPendingStatus(approvalPage);
    const approveBtn = allButtons.filterWhere((btn) => btn.text() == 'Approve');
    approveBtn.simulate('click');
    await updateWrapper(approvalPage);
    clickOnModelActionButton(approvalPage, 'Confirm');
    await updateWrapper(approvalPage);

    expect(vesselApprovalService.updateVesselApprovalStatus).toHaveBeenCalledWith(
      6,
      'approved',
      null,
    );
    expect(vesselApprovalService.getVesselApprovalData).toHaveBeenCalledTimes(2);
  });
});

function getButtonsForPendingStatus(approvalPage) {
  const rowsWithPendingStatus = approvalPage.find('table').at(1).find('tr.pending').first();
  const allButtons = rowsWithPendingStatus.find('ButtonGroup button');
  return allButtons;
}
const clickOnModelActionButton = (approvalPage, actionName) => {
  const modelButtons = approvalPage.find('ModalDialog ModalFooter Button');
  const actionButton = modelButtons.filterWhere((btn) => btn.text() == actionName);
  actionButton.simulate('click');
};
