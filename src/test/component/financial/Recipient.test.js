import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../../service/vessel-service';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselList from '../../resources/vesselListWithDefaultColumns.json';
import FinancialReportContextProvider from '../../../context/FinancialReportContext';
import { updateWrapper } from '../../../setupTests';
import { recipientListData } from '../../resources/recipient.list.data';
import FinancialReportTypes from '../../resources/financial-report-types.json';
import CashCallCurrencyList from '../../resources/cash-call-currency-list-data.json';
import Recipients from '../../../component/FinancialReports/ListReports/Recipients';

describe('<RecipientList />', () => {
  let recipientList;
  const roleConfig = {
    departments: [],
    vessel: {
      view: true,
      create: true,
      edit: true,
      viewApproval: true,
      requestHandOver: true,
      requestArchival: true,
    },
    cashCall: {
      view: true,
      manage: true,
    },
  };

  beforeEach(async () => {
    vesselService.getRecipientList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: recipientListData }));
    vesselService.getFinancialReportTypes = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: FinancialReportTypes }));

    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );

    vesselService.getCashCallDropDownData = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: CashCallCurrencyList }));

    recipientList = mount(
      <MemoryRouter initialEntries={['/vessel/report/financial/recipients']}>
        <FinancialReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/financial/recipients'}>
            <Recipients />
          </Route>
        </FinancialReportContextProvider>
      </MemoryRouter>,
    );

    await updateWrapper(recipientList);
  });

  describe('should render recipient list', () => {
    it('should render columns, when recipient list is loaded', async () => {
      const customTable = recipientList.find('CustomTable');
      const actualRecipientReports = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = ['No', 'Vessel', 'Subject', 'Vessel Accountant', 'Email', 'Action'];
      expect(actualRecipientReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when recipient list is on fetch', async () => {
      vesselService.getRecipientList = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      recipientList = mount(
        <MemoryRouter initialEntries={['/vessel/report/financial/recipients']}>
          <FinancialReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/financial/recipients'}>
              <Recipients />
            </Route>
          </FinancialReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(recipientList);
      expect(recipientList.find('Spinner').exists()).toEqual(true);
    });

    describe('recipient list filter', () => {
      it('should render filter of vessel and recipient list', async () => {
        const vesselInput = recipientList.find('input[data-testid="fml-recipient-filter-vessel"]');
        const subjectInput = recipientList.find(
          'input[data-testid="fml-recipient-filter-subject"]',
        );
        expect(vesselInput.exists()).toEqual(true);
        expect(subjectInput.exists()).toEqual(true);
      });
    });

    describe('should render recipient dialog', () => {
      it('should render add modal fields when recipient dialog opens', async () => {
        const addButton = recipientList.find('button[data-testid="fml-recipient-list-add-button"]');
        addButton.simulate('click');
        await updateWrapper(recipientList);
        const recipientModal = recipientList.find('[data-testid="fml-recipient-list-dialog"]');
        const primaryAccountant = recipientModal.find(
          'input[data-testid="fml-recipient-dialog-primary-vessel-accountant"]',
        );
        const secondaryAccountant = recipientModal.find(
          'input[data-testid="fml-recipient-dialog-secondary-vessel-accountant"]',
        );
        const saveButton = recipientModal.find('button[data-testid="fml-recipient-dialog-save"]');
        const closeButton = recipientModal.find('button[data-testid="fml-recipient-dialog-close"]');

        const formLabel = recipientModal.find('.form-label').map((i) => i.text());
        const expectedFormLabel = [
          'Vessel*',
          'Subject',
          'Vessel Accountant',
          'To Email Address*',
          'CC Email Address',
        ];
        expect(formLabel).toEqual(expectedFormLabel);
        expect(addButton.exists()).toEqual(true);
        expect(recipientModal.exists()).toEqual(true);
        expect(primaryAccountant).toBeTruthy();
        expect(secondaryAccountant).toBeTruthy();
        expect(saveButton.exists()).toEqual(true);
        expect(closeButton.exists()).toEqual(true);
      });

      it('should render edit modal with field values', async () => {
        recipientList.find('[data-testid="fml-recipients-list-edit-0"]').props().onClick();
        await updateWrapper(recipientList);
        const recipientModal = recipientList.find('[data-testid="fml-recipient-list-dialog"]');

        expect(
          recipientModal.find('input[data-testid="fml-recipient-dialog-vessel"]').prop('value'),
        ).toEqual(recipientListData.results[0].vessel_name);

        expect(
          recipientModal
            .find('input[data-testid="fml-recipient-dialog-primary-vessel-accountant"]')
            .prop('value'),
        ).toEqual(recipientListData.results[0].vessel_accountants.primary.email);

        expect(
          recipientModal
            .find('input[data-testid="fml-recipient-dialog-secondary-vessel-accountant"]')
            .prop('value'),
        ).toEqual(recipientListData.results[0].vessel_accountants.secondary.email);
      });
    });

    describe('recipient list Table pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const selectPage = async (page, page_num) => {
        page
          .find('div.page-num')
          .find('.page-num-enabled')
          .filterWhere((e) => Number(e.text()) === Number(page_num))
          .at(0)
          .simulate('click');

        await updateWrapper(page);
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        await updateWrapper(recipientList);
        await selectPage(recipientList, 2);

        expect(findSelectedPageNumber(recipientList)).toEqual(2);
        expect(findPageSizeValue(recipientList)).toEqual(10);
      });
    });
  });
});
