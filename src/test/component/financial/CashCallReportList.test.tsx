import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../../service/vessel-service';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselList from '../../resources/vesselListWithDefaultColumns.json';
import FinancialReportContextProvider from '../../../context/FinancialReportContext';
import { updateWrapper } from '../../../setupTests';
import CashCallListData from '../../resources/cash-call-report-list-data.json';
import FinancialReportTypes from '../../resources/financial-report-types.json';
import CashCallCurrencyList from '../../resources/cash-call-currency-list-data.json';
import CashCallReceipientData from '../../resources/cash-call-send-email-receipient-list.json';
import FinancialList from '../../../component/FinancialReports/FinancialList';

describe('<CashCallReportList />', () => {
  let cashCallReportList;
  const roleConfig = {
    departments: [],
    vessel: {
      view: true,
      create: true,
      edit: true,
      viewApproval: true,
      requestHandOver: true,
      requestArchival: true,
    },
    cashCall: {
      view: true,
      manage: true,
    },
    financial: {
      view: true,
      manage: true,
    },
  };
  beforeEach(async () => {
    vesselService.getCashCallReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: CashCallListData }));
    vesselService.getFinancialReportTypes = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: FinancialReportTypes }));
    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );
    vesselService.getCashCallDropDownData = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: CashCallCurrencyList }));
    vesselService.getRecipientList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: CashCallReceipientData }));

    vesselService.getOwnershipVessel = jest.fn().mockResolvedValue({ data: vesselList.data[0] });

    cashCallReportList = mount(
      <MemoryRouter initialEntries={['/vessel/report/financial/cash-call']}>
        <FinancialReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/financial/:tab'}>
            <FinancialList />
          </Route>
        </FinancialReportContextProvider>
      </MemoryRouter>,
    );

    await updateWrapper(cashCallReportList);
  });

  const openSendEmailModal = async () => {
    cashCallReportList.find('[data-testid="fml-cash-call-list-send-button-0"]').simulate('click');
    await updateWrapper(cashCallReportList);
  };

  describe('should render cash call  report', () => {
    it('should render columns, when cash call report is loaded', async () => {
      const customTable = cashCallReportList.find('CustomTable');
      const actualfinancialReports = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Vessel',
        'Booking Month',
        'Type of Fund',
        'Report Submit Date',
        'Fund Request Number',
        'Reference Number',
        'Subject',
        'Fund Amount Currency',
        'Fund Amount Requested',
        'Fund Receipt Status',
        'Fund Receipt Date',
        'Apply to other ships of the same owner?',
        'Document',
        'Action',
      ];
      expect(actualfinancialReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when cash call Reports is on fetch', async () => {
      vesselService.getCashCallReports = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      cashCallReportList = mount(
        <MemoryRouter initialEntries={['/vessel/report/financial/cash-call']}>
          <FinancialReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/financial/:tab'}>
              <FinancialList />
            </Route>
          </FinancialReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(cashCallReportList);
      expect(cashCallReportList.find('Spinner').exists()).toEqual(true);
    });
  });

  describe('cash call report filter', () => {
    it('should render vessel filter', async () => {
      const vesselInput = cashCallReportList.find(
        'input[data-testid="fml-financial-report-filter-vessel"]',
      );
      expect(vesselInput.exists()).toEqual(true);
    });
  });

  describe('cash call report tab when page loads', () => {
    it('should add report button render and open modal on button click when cash call report page loads', async () => {
      const addButton = cashCallReportList.find(
        'button[data-testid="fml-cash-call-list-add-report-Button"]',
      );
      try {
        addButton.simulate('click');
      } catch (e) {
        console.log(e);
      }
      await updateWrapper(cashCallReportList);
      const cashCallModal = cashCallReportList.find('[data-testid="fml-cash-call-report-dialog"]');
      const saveButton = cashCallModal.find('button[data-testid="fml-cash-call-report-save"]');
      const closeButton = cashCallModal.find('button[data-testid="fml-cash-call-report-close"]');

      const formLabel = cashCallModal.find('.form-label').map((i) => i.text());
      const expectedFormLabel = [
        'Vessel*',
        'Booking Month*',
        'Report Submit Date',
        'Fund Request Number (between 0 - 100)*',
        'Fund Amount Requested*',
        'Fund Amount Currency*',
        'Document*',
        'Vessels Of The Same Owner Included In This Report',
        'Booking Year*',
        'Type of Fund*',
        'Reference Number',
        'Subject*',
        'Fund Receipt Date',
      ];
      expect(formLabel).toEqual(expectedFormLabel);
      expect(addButton.exists()).toEqual(true);
      expect(cashCallModal.exists()).toEqual(true);
      expect(saveButton.exists()).toEqual(true);
      expect(closeButton.exists()).toEqual(true);
    });

    it('should Receipient button render when cash call report page loads', async () => {
      const recipientButton = cashCallReportList.find(
        'button[data-testid="fml-cash-call-list-recipient-Button"]',
      );
      await updateWrapper(cashCallReportList);
      expect(recipientButton.exists()).toEqual(true);
    });

    it('should render buttons when click more button', async () => {
      cashCallReportList.find('ButtonToolbar button').simulate('click');

      await updateWrapper(cashCallReportList);

      const moreButton = cashCallReportList.find('ButtonToolbar button').props()['aria-expanded'];
      expect(moreButton).toEqual(true);

      const exportToExcel = cashCallReportList
        .find('[data-testid="fml-cash-call-list-export-excel"]')
        .at(0);
      expect(exportToExcel.text()).toEqual('Export to Excel');

      const print = cashCallReportList.find('[data-testid="fml-cash-call-list-print"]').at(0);
      expect(print.text()).toEqual('Print');
    });
  });

  describe('cash call report Table pagination tests', () => {
    const findSelectedPageNumber = (page) => {
      return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
    };

    const selectPage = async (page, page_num) => {
      page
        .find('div.page-num')
        .find('.page-num-enabled')
        .filterWhere((e) => Number(e.text()) === Number(page_num))
        .at(0)
        .simulate('click');

      await updateWrapper(page);
    };

    const findPageSizeValue = (page) =>
      page.find('.vessel-table').find('select').at(0).props().value;
    it('should retain page number and page size for each tab on refresh', async () => {
      await updateWrapper(cashCallReportList);
      await selectPage(cashCallReportList, 2);

      expect(findSelectedPageNumber(cashCallReportList)).toEqual(2);
      expect(findPageSizeValue(cashCallReportList)).toEqual(10);
    });
  });

  describe('cash call report send email', () => {
    it('should render send button when cash call page loads', async () => {
      expect(
        cashCallReportList.find('[data-testid="fml-cash-call-list-send-button-0"]').exists(),
      ).toEqual(true);
    });

    it('should open send email modal on send button click', async () => {
      await openSendEmailModal();
      expect(cashCallReportList.find('div.send-email-modal').exists()).toEqual(true);
    });

    it('should throw error on empty fields', async () => {
      await openSendEmailModal();
      cashCallReportList
        .find('input[data-testid="fml-sendEmail-subject"]')
        .simulate('change', { target: { value: '' } });
      cashCallReportList
        .find('textarea[data-testid="fml-sendEmail-recipient"]')
        .simulate('change', { target: { value: '' } });
      cashCallReportList
        .find('textarea[data-testid="fml-sendEmail-message"]')
        .simulate('change', { target: { value: '' } });
      cashCallReportList.find('button[data-testid="fml-sendEmail-send"]').simulate('click');
      await updateWrapper(cashCallReportList);
      expect(cashCallReportList.find('div.invalid-feedback').length === 3).toEqual(true);
    });
    it('should hide send button when cash call page loads if receipt date is provided', async () => {
      expect(
        cashCallReportList.find('[data-testid="fml-cash-call-list-send-button-2"]').exists(),
      ).toEqual(false);
    });
    it('should have error message and disabled submit button if the primary accountant is missing', async () => {
      let dataWithDeletedPrimaryAccountant = vesselList.data[0]
      _.unset(dataWithDeletedPrimaryAccountant, 'fleet_staff.primary_accountant')
      _.unset(dataWithDeletedPrimaryAccountant, 'fleet_staff.secondary_accountant')
      vesselService.getOwnershipVessel = jest.fn().mockResolvedValue({ data: dataWithDeletedPrimaryAccountant });
      cashCallReportList = mount(
        <MemoryRouter initialEntries={['/vessel/report/financial/cash-call']}>
          <FinancialReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/financial/:tab'}>
              <FinancialList />
            </Route>
          </FinancialReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(cashCallReportList);
      await openSendEmailModal();
      expect(cashCallReportList.find('[data-testid="fml-sendEmail-error"]').exists()).toBeTruthy();
      expect(cashCallReportList.find('[data-testid="fml-sendEmail-error"]').text()).toEqual("Email can not be send. Vessel does not have any assigned vessel accountant.");
      expect(cashCallReportList.find('button[data-testid="fml-sendEmail-send"]').props()['disabled']).toBe(true);
    });
  });
});
