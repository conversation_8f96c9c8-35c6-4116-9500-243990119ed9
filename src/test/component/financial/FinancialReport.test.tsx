import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../../service/vessel-service';
import { MemoryRouter, Route } from 'react-router-dom';
import vesselList from '../../resources/vesselListWithDefaultColumns.json';
import FinancialReportContextProvider from '../../../context/FinancialReportContext';
import { updateWrapper } from '../../../setupTests';
import FinancialList from '../../../component/FinancialReports/FinancialList';
import FinancialListData from '../../resources/financial-report-list-data.json';
import FinancialReportTypes from '../../resources/financial-report-types.json';
import CashCallCurrencyList from '../../resources/cash-call-currency-list-data.json';

describe('<FinancialReport />', () => {
  let financialReportList;
  const roleConfig = {
    departments: [],
    vessel: {
      view: true,
      create: true,
      edit: true,
      viewApproval: true,
      requestHandOver: true,
      requestArchival: true,
    },
    financial: {
      view: true,
      manage: true,
    },
    cashCall: {
      view: true,
      manage: true,
    },
    owner: {
      view: true,
      manage: true,
    },
  };
  beforeEach(async () => {
    vesselService.getFinancialReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: FinancialListData }));
    vesselService.getFinancialReportTypes = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: FinancialReportTypes }));

    vesselService.getAllVessels = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: { results: vesselList.data },
      }),
    );

    vesselService.getCashCallDropDownData = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: CashCallCurrencyList }));

    financialReportList = mount(
      <MemoryRouter initialEntries={['/vessel/report/financial/accounts']}>
        <FinancialReportContextProvider roleConfig={roleConfig}>
          <Route path={'/vessel/report/financial/:tab'}>
            <FinancialList />
          </Route>
        </FinancialReportContextProvider>
      </MemoryRouter>,
    );

    await updateWrapper(financialReportList);
  });

  describe('should render financial  report', () => {
    it('should render columns, when financial report is loaded', async () => {
      const customTable = financialReportList.find('CustomTable');
      const actualfinancialReports = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = [
        'No.',
        'Vessel',
        'Booking Month',
        'Name of Report',
        'Report Submit Date',
        'Remarks',
        'Document',
        'Action',
      ];
      expect(actualfinancialReports).toEqual(expectedHeaders);
    });

    it('should render spinner, when financial Reports is on fetch', async () => {
      vesselService.getFinancialReports = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      financialReportList = mount(
        <MemoryRouter initialEntries={['/vessel/report/financial/accounts']}>
          <FinancialReportContextProvider roleConfig={roleConfig}>
            <Route path={'/vessel/report/financial/:tab'}>
              <FinancialList />
            </Route>
          </FinancialReportContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(financialReportList);
      expect(financialReportList.find('Spinner').exists()).toEqual(true);
    });

    describe('financial report filter', () => {
      it('should render filter of vessel and report ty', async () => {
        const vesselInput = financialReportList.find(
          'input[data-testid="fml-financial-report-filter-vessel"]',
        );
        const typeInput = financialReportList.find(
          'input[data-testid="fml-financial-report-filter-report-type"]',
        );
        expect(vesselInput.exists()).toEqual(true);
        expect(typeInput.exists()).toEqual(true);
      });
    });

    describe('financial report tab when page loads', () => {
      it('should add report button render and open modal on button click when financial report page loads', async () => {
        const addButton = financialReportList.find(
          'button[data-testid="fml-financial-report-list-add-report-Button"]',
        );
        addButton.simulate('click');
        await updateWrapper(financialReportList);
        const financialModal = financialReportList.find(
          '[data-testid="fml-financial-report-dialog"]',
        );
        const saveButton = financialModal.find(
          'button[data-testid="fml-financial-report-list-save"]',
        );
        const closeButton = financialModal.find(
          'button[data-testid="fml-financial-report-list-close"]',
        );

        const formLabel = financialModal.find('.form-label').map((i) => i.text());
        const expectedFormLabel = [
          'Vessel*',
          'Booking Month*',
          'Report Submit Date*',
          'Document*',
          'Name of Report*',
          'Booking Year*',
          'Remarks',
        ];
        expect(formLabel).toEqual(expectedFormLabel);
        expect(addButton.exists()).toEqual(true);
        expect(financialModal.exists()).toEqual(true);
        expect(saveButton.exists()).toEqual(true);
        expect(closeButton.exists()).toEqual(true);
      });
    });

    describe('financial report Table pagination tests', () => {
      const findSelectedPageNumber = (page) => {
        return Number(page.find('.page-number-border').find('.page-num-active').at(1).text());
      };

      const selectPage = async (page, page_num) => {
        page
          .find('div.page-num')
          .find('.page-num-enabled')
          .filterWhere((e) => Number(e.text()) === Number(page_num))
          .at(0)
          .simulate('click');

        await updateWrapper(page);
      };

      const findPageSizeValue = (page) =>
        page.find('.vessel-table').find('select').at(0).props().value;

      it('should retain page number and page size for each tab on refresh', async () => {
        await updateWrapper(financialReportList);
        await selectPage(financialReportList, 2);

        expect(findSelectedPageNumber(financialReportList)).toEqual(2);
        expect(findPageSizeValue(financialReportList)).toEqual(10);
      });
    });
  });
});
