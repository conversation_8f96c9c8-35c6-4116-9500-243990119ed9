import React from 'react';
import { mount } from 'enzyme';
import { MemoryRouter } from 'react-router-dom';
import { updateWrapper } from '../../setupTests';
import { SendEmailModal } from '../../component/SendEmailModal';

describe('<SendEmailModal />', () => {
  let sendEmailModal;
  beforeAll(async () => {
    const emailContent = {
      subject: 'test subject',
      recipient: '<EMAIL>',
      cc: '',
      message: 'test email',
    };
    sendEmailModal = mount(
      <MemoryRouter>
        <SendEmailModal isVisible={true} onClose={() => {}} emailContent={emailContent} />
      </MemoryRouter>,
    );
    await updateWrapper(sendEmailModal);
  });

  it('should have the send email modal visible', async () => {
    expect(sendEmailModal.find('div.send-email-modal').exists()).toEqual(true);
  });

  it('should have all required fields', async () => {
    const inputFields = sendEmailModal.find('input').map((e) => e.props()['data-testid']);
    const inputIdList = ['fml-sendEmail-subject'];
    expect(inputFields).toEqual(expect.arrayContaining(inputIdList));
    const textAreaFields = sendEmailModal.find('textarea').map((e) => e.props()['data-testid']);
    const textAreaIdList = ['fml-sendEmail-message', 'fml-sendEmail-recipient', 'fml-sendEmail-cc'];
    expect(textAreaFields).toEqual(expect.arrayContaining(textAreaIdList));
    const buttons = sendEmailModal.find('button').map((e) => e.props()['data-testid']);
    const buttonIdList = ['fml-sendEmail-cancel', 'fml-sendEmail-send'];
    expect(buttons).toEqual(expect.arrayContaining(buttonIdList));
  });

  it('should throw error for required fields on send button click', async () => {
    sendEmailModal
      .find('input[data-testid="fml-sendEmail-subject"]')
      .simulate('change', { target: { value: '' } });
    sendEmailModal
      .find('textarea[data-testid="fml-sendEmail-recipient"]')
      .simulate('change', { target: { value: '' } });
    sendEmailModal
      .find('textarea[data-testid="fml-sendEmail-message"]')
      .simulate('change', { target: { value: '' } });
    await updateWrapper(sendEmailModal);
    sendEmailModal.find('button[data-testid="fml-sendEmail-send"]').simulate('click');
    await updateWrapper(sendEmailModal);
    expect(sendEmailModal.find('div.invalid-feedback').length === 3).toEqual(true);
  });

  it('should have the send email modal closed', async () => {
    sendEmailModal = mount(
      <MemoryRouter>
        <SendEmailModal isVisible={false} onClose={() => {}} emailContent={{}} />
      </MemoryRouter>,
    );
    await updateWrapper(sendEmailModal);
    expect(sendEmailModal.find('div.send-email-modal').exists()).toEqual(false);
  });
});
