import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../../service/vessel-service';
import { MemoryRouter, Route } from 'react-router-dom';
import { updateWrapper } from '../../../setupTests';
import AgentsCharterer from '../../../component/agentsAndCharterer/AgentsCharterer';
import DetailContextProvider from '../../../context/DetailContext';
import AgentsList from '../../resources/agents-data.json';
import chartererList from '../../resources/charterers-data.json';

const getRoleConfig = (hasRole) => {
  return {
    vessel: {
      edit: hasRole,
    },
  };
};

describe('<AgentsCharterer />', () => {
  let agentsList;

  beforeEach(async () => {
    vesselService.getAgentsList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: AgentsList }));
    vesselService.getChartererList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: chartererList }));

    agentsList = mount(
      <MemoryRouter initialEntries={['vessel/ownership/details/1833/agents']}>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Route path="vessel/ownership/details/:ownershipId">
            <AgentsCharterer vesselId={1833} type="Agent" />
          </Route>
        </DetailContextProvider>
      </MemoryRouter>,
    );

    await updateWrapper(agentsList);
  });

  const findAgentsChartererDialogContent = async (page) => {
    const addButton = page.find('button[data-testid="fml-agents-charterer-list-add-Button"]');
    addButton.simulate('click');
    await updateWrapper(page);

    const agentChartererDialog = page.find('[data-testid="fml-agents-charterer-dialog"]');
    const saveButton = agentChartererDialog.find(
      'button[data-testid="fml-agents-charterer-dialog-save"]',
    );
    const cancelButton = agentChartererDialog.find(
      'button[data-testid="fml-agents-charterer-dialog-cancel"]',
    );
    const formLabel = agentChartererDialog.find('.form-label').map((i) => i.text());

    return { addButton, agentChartererDialog, saveButton, cancelButton, formLabel };
  };

  describe('should render agents page', () => {
    it('should render columns, when agents are loaded', async () => {
      const customTable = agentsList.find('CustomTable');
      const actualAgentsList = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = ['No.', 'Agent', 'Port', 'Last Modified', 'Action'];
      expect(actualAgentsList).toEqual(expectedHeaders);
    });

    it('should render spinner, when agents are on fetch', async () => {
      vesselService.getAgentsList = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      agentsList = mount(
        <MemoryRouter initialEntries={['vessel/ownership/details/1833/agents']}>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Route path="vessel/ownership/details/:ownershipId">
              <AgentsCharterer vesselId={1833} type="Agent" />
            </Route>
          </DetailContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(agentsList);
      expect(agentsList.find('Spinner').exists()).toEqual(true);
    });

    it('should add button render and open modal on button click when agents page loads', async () => {
      const { addButton, agentChartererDialog, saveButton, cancelButton, formLabel } =
        await findAgentsChartererDialogContent(agentsList);

      expect(formLabel).toEqual(['Country*', 'Port*', 'Agent*']);
      expect(saveButton.exists()).toEqual(true);
      expect(cancelButton.exists()).toEqual(true);
      expect(addButton.exists()).toEqual(true);
      expect(agentChartererDialog.exists()).toEqual(true);
    });

    it('should render columns without action if no edit rolw', async () => {
      agentsList = mount(
        <MemoryRouter initialEntries={['vessel/ownership/details/1833/agents']}>
          <DetailContextProvider roleConfig={getRoleConfig(false)}>
            <Route path="vessel/ownership/details/:ownershipId">
              <AgentsCharterer vesselId={1833} type="Agent" />
            </Route>
          </DetailContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(agentsList);
      const customTable = agentsList.find('CustomTable');
      const actualAgentsList = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = ['No.', 'Agent', 'Port', 'Last Modified'];
      expect(actualAgentsList).toEqual(expectedHeaders);
    });
  });

  describe('should render charterer page', () => {
    let charterersList = mount(
      <MemoryRouter initialEntries={['vessel/ownership/details/1833/charterer']}>
        <DetailContextProvider roleConfig={getRoleConfig(true)}>
          <Route path="vessel/ownership/details/:ownershipId">
            <AgentsCharterer vesselId={1833} type="Charterer" />
          </Route>
        </DetailContextProvider>
      </MemoryRouter>,
    );

    it('should render columns, when charterers are loaded', async () => {
      const customTable = charterersList.find('CustomTable');
      const actualAgentsList = customTable.find('.th').map((i) => i.text());
      const expectedHeaders = ['No.', 'Charterer', 'Last Modified', 'Action'];
      expect(actualAgentsList).toEqual(expectedHeaders);
    });

    it('should render spinner, when charterers are on fetch', async () => {
      vesselService.getChartererList = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      const charterersList = mount(
        <MemoryRouter initialEntries={['vessel/ownership/details/1833/charterer']}>
          <DetailContextProvider roleConfig={getRoleConfig(true)}>
            <Route path="vessel/ownership/details/:ownershipId">
              <AgentsCharterer vesselId={1833} type="Charterer" />
            </Route>
          </DetailContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(charterersList);
      expect(charterersList.find('Spinner').exists()).toEqual(true);
    });

    it('should add button render and open modal on button click when charterers page loads', async () => {
      const { addButton, agentChartererDialog, saveButton, cancelButton, formLabel } =
        await findAgentsChartererDialogContent(charterersList);

      expect(formLabel).toEqual(['Charterer*']);
      expect(saveButton.exists()).toEqual(true);
      expect(cancelButton.exists()).toEqual(true);
      expect(addButton.exists()).toEqual(true);
      expect(agentChartererDialog.exists()).toEqual(true);
    });
  });
});
