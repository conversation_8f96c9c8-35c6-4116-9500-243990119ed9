import React from 'react';
import { mount } from 'enzyme';
import GenerateReportModal from '../../component/GenerateReportModal';
import { updateWrapper } from '../../setupTests';
describe('GenerateReportModal', () => {
  let wrapper;
  const onConfirm = jest.fn();
  const onClose = jest.fn();
  const reportId = '123';
  beforeEach(async () => {
    wrapper = mount(
        <GenerateReportModal
          onConfirm={onConfirm}
          reopenId={reportId}
          onClose={onClose}
          open={true}
          headingText={"XYZ"}
          headingDescription={"XYZ!@#"}
          loading={false}
        />
    );
    await updateWrapper(wrapper);
  });
  it('should render generate new report pop up', () => {
    // Check generate new  report modal
    const generateReportModalCancel = wrapper.find('[dataTestId="genrate-rpt-modal-cancel"]');
    const generateReportModalConfirm = wrapper.find('[dataTestId="genrate-rpt-modal-confirm"]');
    expect(generateReportModalCancel).toHaveLength(2);
    expect(generateReportModalConfirm).toHaveLength(2);
    expect(generateReportModalCancel.exists()).toEqual(true);
    expect(generateReportModalConfirm.exists()).toEqual(true);
    // Simulate closing generate new  report modal
    generateReportModalCancel.at(1).simulate('click');
    expect(onClose).toHaveBeenCalled();
    // Simulate confirming generate new  report
    generateReportModalConfirm.at(1).simulate('click');
    expect(onConfirm).toHaveBeenCalled();
  });
});