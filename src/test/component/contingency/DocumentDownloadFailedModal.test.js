import React from 'react';
import { mount } from 'enzyme';
import DocumentDownloadFailedModal from '../../../component/contingency/DocumentDownloadFailedModal';
import Modal from 'react-bootstrap/Modal';
import Button from 'react-bootstrap/Button';

describe('DocumentDownloadFailedModal', () => {
  const mockOnClose = jest.fn();

  const defaultProps = {
    onClose: mockOnClose,
    title: 'Download Failed',
    children: 'The document could not be downloaded. Please try again later.',
    show: true,
  };

  beforeEach(() => {
    mockOnClose.mockClear();
  });

  it('should render the modal with provided title and children', () => {
    const wrapper = mount(<DocumentDownloadFailedModal {...defaultProps} />);

    expect(wrapper.find(Modal.Title).text()).toBe('Download Failed');
    expect(wrapper.find(Modal.Body).text()).toBe(
      'The document could not be downloaded. Please try again later.',
    );
    expect(wrapper.find(Button).text()).toBe('Ok');
  });

  it('should call onClose when Ok button is clicked', () => {
    const wrapper = mount(<DocumentDownloadFailedModal {...defaultProps} />);
    wrapper.find(Button).simulate('click');

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('should render the modal in centered position', () => {
    const wrapper = mount(<DocumentDownloadFailedModal {...defaultProps} />);
    expect(wrapper.find('.modal-dialog-centered').exists()).toBe(true);
  });

  it('should render without crashing if no children are provided', () => {
    const wrapper = mount(
      <DocumentDownloadFailedModal onClose={mockOnClose} title="Test Title" show />,
    );
    expect(wrapper.exists()).toBe(true);
  });

  it('should call onClose when the modal is closed via backdrop click', () => {
    const wrapper = mount(<DocumentDownloadFailedModal {...defaultProps} />);

    wrapper.find('.modal-backdrop').simulate('click');
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });
});
