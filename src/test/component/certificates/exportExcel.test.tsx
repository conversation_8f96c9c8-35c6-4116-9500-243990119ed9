import * as XLSX from 'sheetjs-style';
import { exportTableToExcel2, formatCertificateData } from '../../../util/certificates-export/excel-export-v2';
import { certificates_coloum, vessel_list_column } from '../../../util/certificates-export/certifictes-columns';

jest.mock('sheetjs-style', () => {
    const actual = jest.requireActual('sheetjs-style');
  
    return {
      ...actual,
      utils: {
        ...actual.utils,
        book_new: jest.fn(() => ({})),
        book_append_sheet: jest.fn(),
        encode_cell: jest.fn(({ r, c }) => `C${c}_R${r}`),
        encode_range: jest.fn(() => 'A1:B10'),
        json_to_sheet: jest.fn(() => ({})),
  
        // ✅ Fixed mock that creates cells in worksheet
        sheet_add_aoa: jest.fn((worksheet, data, options) => {
          const origin = options?.origin || { r: 0, c: 0 };
  
          // Handle both object and string origin
          let originR, originC;
          if (typeof origin === 'string') {
            const match = origin.match(/C(\d+)_R(\d+)/);
            originC = parseInt(match?.[1] || '0', 10);
            originR = parseInt(match?.[2] || '0', 10);
          } else {
            originR = origin.r || 0;
            originC = origin.c || 0;
          }
  
          data.forEach((row, rowIndex) => {
            row.forEach((value, colIndex) => {
              const r = originR + rowIndex;
              const c = originC + colIndex;
              const cellRef = `C${c}_R${r}`;
              worksheet[cellRef] = { v: value }; // Creates the cell so `.s` can be set
            });
          });
  
          return worksheet;
        }),
      },
      writeFile: jest.fn(),
    };
  });
  

describe('exportTableToExcel2', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockCertData = [
    {
      name: "Safety Cert",
      type: "Annual",
      group: "Group A",
      department: "Dept X",
      audit_date: "2025-01-01",
      audit_due_date: "2025-12-31",
      underway_audit_date: "2025-06-01",
      underway_audit_due_date: "2025-07-01",
      status: "Valid",
      planned_date: "2025-05-01",
      port: "Port A",
      remark: "All OK",
      grace: 30,
      grace_unit: "Days",
    }
  ];

  it('should export certificate data to Excel without crashing', () => {
    const formattedData = formatCertificateData(mockCertData);
    const testData = {
      CertificateSheet: {
        title: "Certificate Export",
        columns: certificates_coloum,
        jsonData: formattedData,
      }
    };

    exportTableToExcel2(testData, "TestCertificateExport");

    expect(XLSX.utils.book_new).toHaveBeenCalled();
    expect(XLSX.utils.book_append_sheet).toHaveBeenCalled();
    expect(XLSX.writeFile).toHaveBeenCalledWith(expect.any(Object), expect.stringContaining("TestCertificateExport.xlsx"));
  });

  it('should handle vessel list export with nested columns', () => {
    const vesselData = [{
      vessel_name: "MV Test",
      tech_group: "TechX",
      group: "Group B",
      department: "Dept Y",
      audit_date: "2025-02-01",
      audit_due_date: "2025-11-30",
      underway_audit_date: "2025-03-15",
      underway_audit_due_date: "2025-04-15",
      place: "Shipyard Z",
      status: "Pending",
      planned_date: "2025-06-20",
      port: "Port B",
      remark: "Requires Review",
      grace: 15,
      grace_unit: "Days",
    }];

    const formattedVesselData = formatCertificateData(vesselData);

    const testData = {
      VesselSheet: {
        title: "Vessel Export",
        columns: vessel_list_column,
        jsonData: formattedVesselData,
      }
    };

    exportTableToExcel2(testData, "TestVesselExport");

    expect(XLSX.writeFile).toHaveBeenCalledWith(expect.any(Object), expect.stringContaining("TestVesselExport.xlsx"));
  });
});
