import { mount } from 'enzyme';
import React from 'react';
import { MemoryRouter } from 'react-router-dom';
import AssignedCertificatesToVessel from '../../../component/certificates/AssignedCertificatesToVessel';
import DetailContextProvider from '../../../context/DetailContext';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import assignedCertificatesData from '../../resources/assigned-certificates-to-vessel-data.json';
import keycloakService from '../../../service/keycloak-service';

describe('<AssignedCertificates />', () => {
  let assignedCertificates;
  const roleConfig = {
    departments: [],
    vessel: {
      view: true,
      create: true,
      edit: true,
      viewApproval: true,
      requestHandOver: true,
      requestArchival: true,
    },
    drills: {
      view: true,
      assign: true,
    },
    certificates: {
      all: true,
      manage: true,
      assign: true,
      create: true,
    },
  };
  beforeEach(async () => {
    vesselService.getAssignedCertificates = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: assignedCertificatesData }));
    keycloakService.getTechgroup = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: {
          response: {
            tech_group: [
              'Bunker Tech',
              'Celsius Tech',
              'CY Tech D1',
              'CY Tech D2',
              'CY Tech D3',
              'CY Tech T1',
              'KR Tech D1',
              'MMPL',
              'MMPL TECH D1',
              'MMPL TECH D2',
              'NSM',
              'SG Tech D1',
              'SG Tech D2',
              'SG Tech D3',
              'SG Tech D4',
              'SG Tech D5',
              'SG Tech D6',
              'SG Tech D7',
              'SG Tech T1',
              'Superintendent',
              'SuperintendentOB',
              'SupportStaff',
              'Tech D1',
              'Tech D10',
              'Tech D11',
              'Tech D12',
              'Tech D15',
              'Tech D2',
              'Tech D3',
              'Tech D4',
              'Tech D5',
              'Tech D6',
              'Tech D7',
              'Tech D8',
              'Tech D9',
              'Tech DSMPL',
              'Tech FMEL',
              'Tech FMIPL',
              'Technical',
              'Tech Shandong',
              'Tech T1',
              'Tech T10',
              'Tech T11',
              'Tech T12',
              'Tech T15',
              'Tech T2',
              'Tech T3',
              'Tech T4',
              'Tech T5',
              'Tech T6',
              'Tech T7',
              'Tech T8',
              'Tech T9',
              'TFMIPL Tech D1',
              'TFMIPL Tech D2',
              'TWFLEETDMCC',
              'VesselManager',
            ],
          },
        },
      }),
    );
    assignedCertificates = mount(
      <MemoryRouter>
        <DetailContextProvider roleConfig={roleConfig}>
          <AssignedCertificatesToVessel ga4EventTrigger={() => {}}/>
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(assignedCertificates);
  });

  describe('should render assgined certificates to vessel page', () => {
    it('should render columns, when assigned certificates list loads', async () => {
      const customTable = assignedCertificates.find('CustomTable');
      const customTableColumns = customTable.find('.th').map((i) => i.text());
      const drillColumns = customTableColumns.filter((item) => {
        return item.length > 1;
      });
      expect(drillColumns).toEqual([
        'Annual Audit/Inspection',
        'Underway Audit/Inspection',
        'No.',
        'Vessel',
        'Tech Group',
        'Certificate Group',
        'Survey department',
        'Date of Survey',
        'Due Date',
        'Date of Survey',
        'Due Date',
        'Place of Survey',
        'Grace Period',
        'Status',
        'Planned Date of Inspection',
        'Port',
        'Remark',
        'Document',
        'Action',
      ]);
    });

    it('should render buttons when click more button', async () => {
      assignedCertificates.find('ButtonToolbar button').simulate('click');

      await updateWrapper(assignedCertificates);

      const moreButton = assignedCertificates.find('ButtonToolbar button').props()['aria-expanded'];
      expect(moreButton).toEqual(true);

      const exportToExcel = assignedCertificates
        .find('[data-testid="fml-assigned-certificatest-export-excel"]')
        .at(0);
      expect(exportToExcel.text()).toEqual('Export to Excel');

      const print = assignedCertificates
        .find('[data-testid="fml-assigned-certificates-print"]')
        .at(0);
      expect(print.text()).toEqual('Print');
    });

    describe('Assigned Certificates Table pagination tests', () => {
      it('should render result count when page loads', async () => {
        const totalCount = assignedCertificates.find('CustomTable').props().totalCount;
        expect(totalCount).toEqual(0);
      });
    });
  });
});
