import { mount } from 'enzyme';
import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import VesselCertificates from '../../../component/certificates/VesselCertificates';
import DetailContextProvider from '../../../context/DetailContext';
import Details from '../../../pages/Details';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import { editedVessel } from '../../resources/vessel';
import vesselCertificatesData from '../../resources/vessel-certificates-data.json';
import VesselContextProvider from '../../../context/VesselContext';
import vesselList from '../../resources/vesselListWithDefaultColumns.json';
import manualTypes from '../../resources/manual-types-data.json';

describe('<VesselCertificates />', () => {
  let vesselCertificates;
  const roleConfig = {
    departments: [],
    admin: {
      view: true,
      certificates: {
        manage: true,
      },
    },
    vessel: {
      view: true,
      create: true,
      edit: true,
      send: true,
      viewApproval: true,
      requestHandOver: true,
      requestArchival: true,
      staff: {
        buyer: true,
        accountant: true,
        supdt: true,
        qhse: true,
        operation: true,
        payroll: true,
      },
    },
    drills: {
      view: true,
      assign: true,
    },
    certificates: {
      all: true,
      manage: true,
      assign: true,
    },
    techGroups:{
      manage: true,
    },
    ownerReporting: {
      view: false,
      manage: false,
    },
  };
  const mockShipReports = () => {
    vesselService.getLastPosition = jest.fn().mockResolvedValue({
      status: 'fulfilled',
      value: {
        data: {
          position: '04959S1035147E',
          time: '2022-09-07T08:20:54',
          lat: '-0.8333333',
          lon: '103.8633333',
        },
      },
      data: {
        position: '04959S1035147E',
        time: '2022-09-07T08:20:54',
        lat: '-0.8333333',
        lon: '103.8633333',
      },
    });
    vesselService.getItinerary = jest.fn().mockResolvedValue({
      status: 'fulfilled',
      value: {
        data: {
          results: [],
          total: 0,
        },
      },
      data: {
        results: [],
        total: 0,
      },
    });
    vesselService.getTechnicalReports = jest.fn().mockResolvedValue({
      status: 'fulfilled',
      value: {
        data: {
          results: [],
          total: 0,
        },
      },
      data: {
        results: [],
        total: 0,
      },
    });
    vesselService.getSignedOnSeafarer = jest
      .fn()
      .mockResolvedValue({ status: 'fulfilled', value: { data: [] }, data: [] });
  };
  beforeEach(async () => {
    vesselService.getVesselCertificates = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: vesselCertificatesData }));

    vesselService.getAllVessels = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: vesselList.data } }));

    vesselService.getManualTypes = jest.fn().mockResolvedValue({ data: manualTypes });
    mockShipReports();

    vesselCertificates = mount(
      <MemoryRouter initialEntries={['vessel/ownership/details/1833/certificates']}>
        <Route path="vessel/ownership/details/:ownershipId">
          <VesselContextProvider>
            <DetailContextProvider roleConfig={roleConfig}>
              <VesselCertificates vesselId={1833} />
            </DetailContextProvider>
          </VesselContextProvider>
        </Route>
      </MemoryRouter>,
    );
    await updateWrapper(vesselCertificates);
  });

  describe('should render vessel certificates page', () => {
    it('should render columns, when vessel certificates list loads', async () => {
      const customTable = vesselCertificates.find('CustomTable');
      const customTableColumns = customTable.find('.th').map((i) => i.text());
      const tableColumns = customTableColumns.filter((item) => {
        return item.length > 1;
      });
      expect(tableColumns.sort()).toEqual(
        [
          'Action',
      'Annual Audit/Inspection',
      'Certificate Group',
      'Date of Survey',
      'Date of Survey',
      'Document',
      'Due Date',
      'Due Date',
      'Grace Period',
      'No.',
      'Place of Survey',
      'Planned Date of Inspection',
      'Port',
      'Remark',
      'Status',
      'Survey Department',
      'Surveys and Certificate',
      'Type',
      'Underway Audit/Inspection'
        ].sort((a, b) => a.localeCompare(b)),
      );
    });

    it('should render spinner, when certificates list is on fetch', async () => {
      vesselService.getVesselCertificates = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));

      vesselCertificates = mount(
        <MemoryRouter initialEntries={['vessel/ownership/details/1833/certificates']}>
          <Route path="vessel/ownership/details/:ownershipId">
            <VesselContextProvider>
              <DetailContextProvider roleConfig={roleConfig}>
                <VesselCertificates vesselId={1833} />
              </DetailContextProvider>
            </VesselContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(vesselCertificates);
      expect(vesselCertificates.find('div[data-testid="fml-spinner"]').exists()).toEqual(false);
    });

    it('should contain headings when page loads', async () => {
      const heading = vesselCertificates.find('div').find('.emergency-drills');
      expect(heading.text()).toEqual('Assigned Surveys And Certificates');
    });

    it('should render filter component', async () => {
      expect(
        vesselCertificates.find('input[placeholder="Search Surveys and Certificates"]')
          .exists()
      ).toEqual(true);
    });

    describe('should render navigation buttons, when certificate list page loads', () => {
      let vesselDetails;
      it('should Surveys and certificates tab highlighted when page loads', async () => {
        vesselService.getOwnershipVessel = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: editedVessel[2] }));

        vesselService.getVesselCertificates = jest
          .fn()
          .mockImplementation(() => Promise.resolve({ data: [] }));

        const getRoleConfig = (hasRole, viewRole = true) => {
          return {
            vessel: {
              view: viewRole,
              create: hasRole,
              edit: hasRole,
              viewApproval: hasRole,
              requestHandOver: hasRole,
              requestArchival: hasRole,
            },
            techGroups:{
              manage: true,
            },
            approvalGroups: [],
          };
        };

        vesselDetails = mount(
          <MemoryRouter initialEntries={['/vessel/ownership/details/2/certificates']}>
            <Route path={'/vessel/ownership/details/:ownershipId/:step?'}>
              <VesselContextProvider>
                <DetailContextProvider roleConfig={roleConfig}>
                  <Details roleConfig={getRoleConfig(true)} />
                </DetailContextProvider>
              </VesselContextProvider>
            </Route>
          </MemoryRouter>,
        );
        await updateWrapper(vesselDetails);
        const testData = vesselDetails.find('a.active');
        expect(testData.text()).toEqual('Surveys and Certificates');
      });
    });

    describe('should render navigation buttons, when certificate list page loads', () => {
      it('should render navigation buttons when page loads ', async () => {
        const certificateButtons = vesselCertificates.find('button').map((i) => i.text());
        expect(certificateButtons.sort()).toEqual(
          [
            'Assign Survey/Certificate',
            'Email to Vessel',
            'Send Survey Docs',
            'All 210',
            'Statutory 0',
            'Important 0',
            'Ancillary 12',
            'Certificate Groups',
            'Survey Department',
            'All 12',
            'Overdue 7',
            'Due Within 30 Days 0',
            'Due Within 60 Days 0'
          ].sort((a, b) => a.localeCompare(b)),
        );
      });
      //vesselCertificates.find('button[data-testid="fml-vessel-certificates-email-Button"]').text("Email to Vessel")
      it('should render Modal for email to vessel button ', async () => {
        vesselCertificates
          .find('button[data-testid="fml-vessel-certificates-email-Button"]')
          .simulate('click');
        //await updateWrapper(vesselCertificates);
        const titleText = vesselCertificates.find('.modal-title');
        expect(titleText.at(1).text()).toEqual('Email List to Vessel');
      });

      it('should render Modal for Assign Survey/Certificate button ', async () => {
        vesselCertificates
          .find('button[data-testid="fml-vessel-certificates-assign-survey-Button"]')
          .simulate('click');
        await updateWrapper(vesselCertificates);
        const titleText = vesselCertificates.find('.modal-title');
        const formLabel = vesselCertificates.find('.form-label').map((i) => i.text());
        expect(titleText.at(1).text()).toEqual('Assign Survey/Certificate to ');
        expect(formLabel).toEqual([
          'Survey / Certificate*',
          'Place of Survey/Certificate',
          'Annual Audit/Inspection Date*',
          'Annual Audit/Inspection Due Date*',
          'Underway Audit/Inspection Date',
          'Planned Date of Inspection',
          'Country',
          'Port',
          'Status',
          '',
          '',
          'Underway Audit/Inspection Due Date',
          'Grace Period*',
          'Remark',
          'Document'
        ]);
      });
      it('should not have Assign Survey/Certificate button on no assign role', async () => {
        roleConfig.certificates.assign = false;
        const vesselCertificate = mount(
          <MemoryRouter initialEntries={['vessel/ownership/details/1833/certificates']}>
            <Route path="vessel/ownership/details/:ownershipId">
              <VesselContextProvider>
                <DetailContextProvider roleConfig={roleConfig}>
                  <VesselCertificates vesselId={1833} />
                </DetailContextProvider>
              </VesselContextProvider>
            </Route>
          </MemoryRouter>,
        );
        await updateWrapper(vesselCertificate);
        expect(
          vesselCertificate
            .find('button[data-testid="fml-vessel-certificates-assign-survey-Button"]')
            .prop('hidden'),
        ).toEqual(true);
      });
    });

    it('should not render email buttons', async () => {
      roleConfig.vessel.send = false;
      const vesselCertificate = mount(
        <MemoryRouter initialEntries={['vessel/ownership/details/1833/certificates']}>
          <Route path="vessel/ownership/details/:ownershipId">
            <VesselContextProvider>
              <DetailContextProvider roleConfig={roleConfig}>
                <VesselCertificates vesselId={1833} />
              </DetailContextProvider>
            </VesselContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(vesselCertificate);
      expect(vesselCertificate
        .find('button[data-testid="fml-vessel-certificates-email-Button"]')
        .exists()).toEqual(false);
    });
  });
});
