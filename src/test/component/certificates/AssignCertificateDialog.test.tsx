import React from 'react';
import { mount } from 'enzyme';
import { MemoryRouter } from 'react-router-dom';
import { DetailContext } from '../../../context/DetailContext';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import AssignCertificateDialog from '../../../component/certificates/AssignCertificateDialog';

jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const mockData = {
  id: 1,
  name: 'Test Certificate',
  status: 'ACTIVE',
  audit_date: '2023-10-01',
  audit_due_date: '2023-12-01',
  underway_audit_date: '2023-11-01',
  underway_audit_due_date: '2023-11-15',
  planned_date: '2023-10-15',
  grace: 5,
  grace_unit: 'DAY',
  port_info: [{ id: 1, value: 'Port 1' }],
  country: [{ id: 1, value: 'Country 1' }],
  port: 'Country 1, Port 1',
  remark: 'Test Remark',
  place: 'Test Place',
  url: 'test-url',
  updated_by: 'Admin',
  updated_at: '2023-10-01T12:00:00Z',
};

const roleConfig = {
  departments: [],
  certificates: {
    all: true,
  },
  admin: {
    view: true,
    drills: {
      view: false,
    },
    certificates: {
      manage: true,
      assign: true,
      create: true,
    },
  },
};

const mockContext = {
  ga4EventTrigger: jest.fn(),
  roleConfig,
  vesselName: 'Test Vessel',
  setVesselName: jest.fn(),
};

describe('<AssignCertificateDialog />', () => {
  let wrapper;
  const setShowModal = jest.fn();
  const handleModalCancel = jest.fn();
  const handleModalSubmit = jest.fn();
  const handleModalClear = jest.fn();
  const setListLoading = jest.fn();
  const setPageError = jest.fn();

  beforeEach(async () => {
    vesselService.getAdminCertificatesList = jest.fn().mockResolvedValue({
      data: {
        results: [
          { id: 1, name: 'Test Certificate 1', department: 'DEPT1' },
          { id: 2, name: 'Test Certificate 2', department: 'DEPT2' },
        ],
      },
    });

    vesselService.createAssignCertificate = jest.fn().mockResolvedValue({});
    vesselService.updateAssignCertificate = jest.fn().mockResolvedValue({});
    vesselService.getPreSignedUploadLink = jest.fn().mockResolvedValue({
      data: { url: 'test-url', pre_signed_link: 'test-presigned-url' },
    });
    vesselService.uploadPresignedDocument = jest.fn().mockResolvedValue({});

    wrapper = mount(
      <MemoryRouter>
        <DetailContext.Provider value={mockContext}>
          <AssignCertificateDialog
            showModal={true}
            setShowModal={setShowModal}
            edit={false}
            handleModalCancel={handleModalCancel}
            handleModalSubmit={handleModalSubmit}
            handleModalClear={handleModalClear}
            data={mockData}
            vesselId="1"
            setListLoading={setListLoading}
            setPageError={setPageError}
            pageType="CERTIFICATE"
          />
        </DetailContext.Provider>
      </MemoryRouter>,
    );
    await updateWrapper(wrapper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render the modal with the correct title', () => {
    const modalTitle = wrapper.find('.modal-title').first().text();
    expect(modalTitle).toBe('Assign Survey/Certificate to Test Vessel');
  });

  it('should render the form fields correctly', () => {
    const formLabels = wrapper.find('.form-label').map((label) => label.text());
    expect(formLabels).toContain('Survey / Certificate*');
    expect(formLabels).toContain('Place of Survey/Certificate');
    expect(formLabels).toContain('Annual Audit/Inspection Date*');
    expect(formLabels).toContain('Annual Audit/Inspection Due Date*');
    expect(formLabels).toContain('Underway Audit/Inspection Date');
    expect(formLabels).toContain('Planned Date of Inspection');
    expect(formLabels).toContain('Port');
    expect(formLabels).toContain('Status');
    expect(formLabels).toContain('Grace Period*');
    expect(formLabels).toContain('Remark');
    expect(formLabels).toContain('Document');
  });

  it('should handle file upload and removal', () => {
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    const fileInput = wrapper.find('input[type="file"]');
    fileInput.simulate('change', { target: { files: [file] } });
    wrapper.update();
    expect(wrapper.find('UploadItem').exists()).toBe(true);
  
    const removeButton = wrapper.find('[data-testid="uploadItemRemoveBtn"]').first();
    removeButton.simulate('click');
    wrapper.update();
    expect(wrapper.find('UploadItem').exists()).toBe(false);
  });

  it('should handle form cancellation', () => {
    const cancelButton = wrapper.find('[data-testid="fml-assign-certificate-close"]').first();
    cancelButton.simulate('click');
    expect(handleModalCancel).toHaveBeenCalled();
  });
});