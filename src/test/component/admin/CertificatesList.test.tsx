import AdminContextProvider from '../../../context/AdminContext';
import { mount } from 'enzyme';
import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import CertificatesList from '../../../component/admin/certificates/CertificatesList';
import VesselAdmin from '../../../pages/VesselAdmin';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import adminCertificatesListData from '../../resources/admin-certificates-list-data.json';

const roleConfig = {
  departments: [],
  certificates: {
    all: true,
  },
  admin: {
    view: true,
    drills: {
      view: false,
    },
    certificates: {
      manage: true,
      assign: true,
      create: true
    },
  },
};

describe('<CertificatesList />', () => {
  let certificatesList;
  beforeEach(async () => {
    vesselService.getAdminCertificatesList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: adminCertificatesListData }));
    certificatesList = mount(
      <MemoryRouter>
        <AdminContextProvider roleConfig={roleConfig}>
          <CertificatesList />
        </AdminContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(certificatesList);
  });

  describe('should render surveys and certificates list page', () => {
    it('should render columns, when surveys and certificates list loads', async () => {
      const customTable = certificatesList.find('CustomTable');
      const customTableColumns = customTable.find('.th').map((i) => i.text());
      expect(customTableColumns).toEqual([
        'Survey/Certificate',
        'Certificate Group',
        'Survey Department',
        'Type',
        'Visible to Owners',
        'Compulsory for All Vessels',
        'Mark as Important',
        'Status',
        'Action',
      ]);
    });

    it('should render spinner, when surveys and certificates list is on fetch', async () => {
      vesselService.getAdminCertificatesList = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      const certificatesList = mount(
        <MemoryRouter>
          <AdminContextProvider roleConfig={roleConfig}>
            <CertificatesList />
          </AdminContextProvider>
        </MemoryRouter>,
      );
      expect(certificatesList.find('Spinner').exists()).toEqual(true);
    });

    it('should surveys and certificates tab highlighted when page loads', async () => {
      const vesselAdmin = mount(
        <MemoryRouter initialEntries={['/vessel/admin/certificates']}>
          <Route path={'/vessel/admin/:tab'}>
            <AdminContextProvider roleConfig={roleConfig}>
              <VesselAdmin />
            </AdminContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(vesselAdmin);
      const testData = vesselAdmin.find('a.active');
      expect(testData.text()).toEqual('Surveys and Certificates');
    });
  });

  describe('Define New Survey/Certificate button', () => {
    it('should contain new Survey/Certificate button', async () => {
      expect(
        certificatesList
          .find('[data-testid="fml-certificates-list-defineNewCertificate-button"]')
          .exists(),
      ).toEqual(true);
    });

    it('should display results based on searched keyword', async () => {
      certificatesList
        .find('#search-bar')
        .at(0)
        .simulate('change', { target: { value: 'AERO' } });
      await updateWrapper(certificatesList);
      expect(certificatesList.text().includes('AERO')).toEqual(true);
    });

    it('should change active page num on page number click', async () => {
      const page1 = certificatesList.find('[data-testid="fml-pagination-1"]');
      expect(page1.props()['className']).toContain('page-num-active');

      const page2 = certificatesList.find('[data-testid="fml-pagination-2"]');
      page2.simulate('click');
      await updateWrapper(certificatesList);
      expect(
        certificatesList.find('[data-testid="fml-pagination-2"]').props()['className'],
      ).toContain('page-num-active');
    });

    it('should render result count based on selected date', async () => {
      const totalCount = certificatesList.find('CustomTable').props().totalCount;
      expect(totalCount).toEqual(210);
    });
  });

  describe('Define New/Edit Survey/Certificate ', () => {
    it('should render Modal for add survey/certificate ', async () => {
      certificatesList
        .find('button[data-testid="fml-certificates-list-defineNewCertificate-button"]')
        .simulate('click');
      await updateWrapper(certificatesList);
      const titleText = certificatesList.find('.modal-title');
      const formLabel = certificatesList.find('.form-label').map((i) => i.text());
      expect(titleText.at(1).text()).toEqual('Define New Survey/Certificate');
      expect(formLabel).toEqual([
        'Survey / Certificate*',
        'Survey / Certificate*',
        'Certificate Group*',
        'Certificate Group*',
        'Survey Department*',
        'Survey Department*',
        'Tech Group',
      ]);
    });

    it('should contain save/cancel Survey/Certificate button', async () => {
      certificatesList
        .find('button[data-testid="fml-certificates-list-defineNewCertificate-button"]')
        .simulate('click');
      await updateWrapper(certificatesList);
      expect(
        certificatesList.find('[data-testid="fml-admin-certificate-cancel"]').exists(),
      ).toEqual(true);
      expect(certificatesList.find('[data-testid="fml-admin-certificate-save"]').exists()).toEqual(
        true,
      );
    });
    it('should render Modal for edit survey/certificate ', async () => {
      certificatesList
        .find('[data-testid="fml-certificates-list-edit-button-0"]')
        .props()
        .onClick();

      await updateWrapper(certificatesList);
      const titleText = certificatesList.find('.modal-title');
      const formLabel = certificatesList.find('.form-label').map((i) => i.text());
      expect(titleText.at(1).text()).toEqual('Edit Survey/Certificate');
      expect(
        formLabel.includes(
          'Survey / Certificate*',
          'Certificate Group*',
          'Survey Department*',
          'Tech Group',
          'Visible to Owners',
          'Compulsory for All vessels',
          'Mark as Important',
          'Mark as Inactive',
          'Select Type*'
        ),
      ).toEqual(true);
    });
  });
});
