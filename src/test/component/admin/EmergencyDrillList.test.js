import React from 'react';
import { mount } from 'enzyme';
import vesselService from '../../../service/vessel-service';
import { updateWrapper } from '../../../setupTests';
import EmergencyDrillListData from '../../resources/emergency-drill-list.json';
import { MemoryRouter, Route } from 'react-router-dom';
import VesselAdmin from '../../../pages/VesselAdmin';
import AdminContextProvider from '../../../context/AdminContext';

describe('<EmergencyDrillList />', () => {
  let emergencyDrillList;

  const roleConfig = {
    departments: [],
    admin: {
      view: true,
      drills: {
        view: true,
        create: true,
        edit: true,
      },
      certificates: {
        manage: true,
        assign: true,
      },
    },
  };

  beforeAll(async () => {
    vesselService.getEmergencyDrillList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: EmergencyDrillListData }));
    emergencyDrillList = mount(
      <MemoryRouter initialEntries={['/vessel/admin/emergency-drills']}>
        <Route path={'/vessel/admin/:tab'}>
          <AdminContextProvider roleConfig={roleConfig}>
            <VesselAdmin />
          </AdminContextProvider>
        </Route>
      </MemoryRouter>,
    );

    await updateWrapper(emergencyDrillList);
  });

  describe('should render emergency drill list page', () => {
    it('should render columns, when  emergency drill list loads', async () => {
      const customTable = emergencyDrillList.find('CustomTable');
      const customTableColumns = customTable.find('.th').map((i) => i.text());
      expect(customTableColumns).toEqual([
        'Drills',
        'Applicable To',
        'Description',
        'Drill Period',
        'Status',
        'Action',
      ]);
    });

    it('should render spinner, when emergency drill list is on fetch', async () => {
      vesselService.getEmergencyDrillList = jest
        .fn()
        .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 1000)));
      const list = mount(
        <MemoryRouter initialEntries={['/vessel/admin/emergency-drills']}>
          <Route path={'/vessel/admin/:tab'}>
            <AdminContextProvider roleConfig={roleConfig}>
              <VesselAdmin />
            </AdminContextProvider>
          </Route>
        </MemoryRouter>,
      );

      expect(list.find('Spinner').exists()).toEqual(true);
    });

    it('should contain Admin heading when page loads', async () => {
      const vesselAdmin = mount(
        <MemoryRouter initialEntries={['/vessel/admin/emergency-drills']}>
          <Route path={'/vessel/admin/:tab'}>
            <AdminContextProvider roleConfig={roleConfig}>
              <VesselAdmin />
            </AdminContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(vesselAdmin);
      const mainHeading = vesselAdmin.find('label').find('b');
      expect(mainHeading.text()).toEqual('Admin');
    });

    it('should Emergency Drills tab highlighted when page loads', async () => {
      const vesselAdmin = mount(
        <MemoryRouter initialEntries={['/vessel/admin/emergency-drills']}>
          <Route path={'/vessel/admin/:tab'}>
            <AdminContextProvider roleConfig={roleConfig}>
              <VesselAdmin />
            </AdminContextProvider>
          </Route>
        </MemoryRouter>,
      );
      await updateWrapper(vesselAdmin);
      const testData = vesselAdmin.find('a.active');
      expect(testData.text()).toEqual('Emergency Drills');
    });

    it('should not contain emergency drills tab on no view role', async () => {
      const roleConfig = {
        departments: [],
        admin: {
          view: true,
          drills: {
            create: true,
            edit: true,
          },
          certificates: {
            manage: true,
            assign: true,
          },
        },
      };
      const vesselAdmin = mount(
        <MemoryRouter>
          <AdminContextProvider roleConfig={roleConfig}>
            <VesselAdmin />
          </AdminContextProvider>
        </MemoryRouter>,
      );
      await updateWrapper(vesselAdmin);
      expect(
        vesselAdmin.find('[data-testid="fml-vessel-details-emergency-drills-tab-link"]').exists(),
      ).toEqual(false);
    });

    describe('Define New Drill button', () => {
      it('should contain new drill button', async () => {
        expect(
          emergencyDrillList.find('[data-testid="fml-emergencyDrillList-defineNewDrill"]').exists(),
        ).toEqual(true);
      });

      it('click on Define New Drill button, should open model popup', async () => {
        const newDrillButton = emergencyDrillList
          .find('[data-testid="fml-emergencyDrillList-defineNewDrill"]')
          .filterWhere((btn) => btn.text() === 'Define New Drill');
        newDrillButton.at(0).simulate('click');
        await updateWrapper(emergencyDrillList);
        const drillModal = emergencyDrillList.find('.modal-content');
        expect(drillModal).toHaveLength(1);
      });

      it('should not contain new drill button on no create role', async () => {
        const roleConfig = {
          admin: {
            view: true,
            drills: {
              view: true,
              edit: true,
            },
            certificates: {
              manage: true,
              assign: true,
            },
          },
        };
        const vesselAdmin = mount(
          <MemoryRouter initialEntries={['/vessel/admin/emergency-drills']}>
            <Route path={'/vessel/admin/:tab'}>
              <AdminContextProvider roleConfig={roleConfig}>
                <VesselAdmin />
              </AdminContextProvider>
            </Route>
          </MemoryRouter>,
        );
        await updateWrapper(vesselAdmin);
        expect(
          vesselAdmin
            .find('[data-testid="fml-emergencyDrillList-defineNewDrill"]')
            .at(0)
            .prop('hidden'),
        ).toEqual(true);
      });

      it('should not contain edit drill icon on no edit role', async () => {
        const roleConfig = {
          admin: {
            view: true,
            drills: {
              view: true,
              create: true,
            },
            certificates: {
              manage: true,
              assign: true,
            },
          },
        };
        const vesselAdmin = mount(
          <MemoryRouter initialEntries={['/vessel/admin/emergency-drills']}>
            <Route path={'/vessel/admin/:tab'}>
              <AdminContextProvider roleConfig={roleConfig}>
                <VesselAdmin />
              </AdminContextProvider>
            </Route>
          </MemoryRouter>,
        );
        await updateWrapper(vesselAdmin);
        expect(vesselAdmin.find('.edit-icon').exists()).toEqual(false);
      });
    });
  });
});
