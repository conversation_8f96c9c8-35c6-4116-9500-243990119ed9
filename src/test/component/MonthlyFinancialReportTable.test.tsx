import React from 'react';
import { mount } from 'enzyme';
import MonthlyFinancialReportTable from '../../component/MonthlyFinancialReportTable/MonthlyFinancialReportTable';
import { MemoryRouter, Route } from 'react-router/cjs/react-router';
import { updateWrapper } from '../../setupTests';
import moment from 'moment';
import DetailContextProvider from '../../../src/context/DetailContext';
describe('MonthlyFinancialReportTable', () => {
  let wrapper;
  const data = [
    {
      id: 165403,
      frozenOn: null,
      version: 0,
      name: `${moment().format('MMMM YYYY')}`,
      vesselId: 123,
      vesselCode: 1223,
      vesselName: 'ABC123',
      period: moment().format('MMM-YY'),
      registeredOwnerName: 'xyz',
      ownerName: 'xyz',
      status: 'SUBMITTED',
      startDate: '2024-09-01',
      endDate: '2024-09-30',
      visibility: 0,
      vesselStatus: 'active',
      handoverDate: '2024-06-01T00:00:00.000Z',
      canDeleteManually: true,
      canCreateNextReport: true,
      canGenrateNewReport: true,
      accountSplit: null,
      canReopen: false,
      comments: {
        resolved: 0,
        total: 0,
      },
    },
    {
      id: 16542,
      frozenOn: null,
      version: 0,
      name: `${moment().subtract(1, 'months').format('MMMM YYYY')}`,
      vesselId: 123,
      vesselCode: 1223,
      vesselName: 'ABC123',
      period: moment().subtract(1, 'months').format('MMM-YY'),
      registeredOwnerName: 'xyz',
      ownerName: 'xyz',
      status: 'SUBMITTED',
      startDate: '2024-09-01',
      endDate: '2024-09-30',
      visibility: 0,
      vesselStatus: 'active',
      handoverDate: '2024-06-01T00:00:00.000Z',
      canGenrateNewReport: true,
      accountSplit: null,
      canReopen: true,
      comments: {
        resolved: 0,
        total: 0,
      },
    },
    {
      id: 2,
      name: 'Feb-23',
      status: 'UNDER_REVIEW',
      accountantName: 'John Doe',
      modifiedOn: '2023-06-08T12:31:52.950Z',
      canDeleteManually: true,
      version: 0,
      comments: {
        total: 15,
        resolved: 13,
      },
    },
    {
      id: 3,
      name: 'March-23',
      status: 'FROZEN',
      accountantName: 'John Doe',
      modifiedOn: '2023-06-08T12:31:52.950Z',
      version: '1',
      comments: {
        total: 1,
        resolved: 1,
      },
    },
  ];
  const bifercationData = [
    {
      reportId: 165403,
      commentsBifurcation: {
        vesselAccountant: {
          resolved: 1,
          total: 6,
        },
        superintendent: {
          resolved: 1,
          total: 2,
        },
        owner: {
          resolved: 0,
          total: 0,
        },
        others: {
          resolved: 0,
          total: 0,
        },
      },
    },
  ];
  const isLoading = false;
  const setData = jest.fn();
  const vesselId = '123';
  const setReopenId = jest.fn();
  beforeEach(async () => {
    wrapper = mount(
      <MemoryRouter initialEntries={['vessel/ownership/details/123']}>
        <Route path="vessel/ownership/details/123/monthlyFinancialReports" />
        <DetailContextProvider roleConfig={{ownerReporting: {
      view: true,
      manage: true,
      candeleteReport: true,
    },}}>
        <MonthlyFinancialReportTable
          data={data}
          isLoading={isLoading}
          setData={setData}
          vesselId={vesselId}
          setReopenId={setReopenId}
          isCurrentVa={true}
          bifercationData={bifercationData}
        />
        </DetailContextProvider>
      </MemoryRouter>,
    );
    await updateWrapper(wrapper);
  });
  it('should render columns, when monthly financial report is loaded', async () => {
    // Assert that table renders with correct headers
    const customTable = wrapper.find('CustomTable');
    const actualMarpolReports = customTable.find('.th').map((i) => i.text());
    const expectedHeaders = [
      'Month',
      'Status',
      'Format',
      'Accountant',
      'Updated On',
      '# of Comments',
      'Actions',
    ];
    expect(actualMarpolReports).toEqual(expectedHeaders);
  });
  it('Simulate click on a report comments and validate navigation', () => {
    const reportcomments = wrapper.find('[dataTestId="fml-monthly-financial-report-comments-2"]');
    expect(reportcomments).toHaveLength(1);
    reportcomments.simulate('click');
    expect(wrapper.find('Router').prop('history').location.pathname).toEqual(
      '/owner-reporting/123/reports/2',
    );
  });
  it('renders the component, check table functionality', () => {
    // Simulate click on report name and validate navigation
    const report = wrapper.find('[dataTestId="fml-monthly-financial-report-Feb-23"]');
    expect(report).toHaveLength(1);
    report.simulate('click');
    expect(wrapper.find('Router').prop('history').location.pathname).toEqual(
      '/owner-reporting/123/reports/2',
    );
  });
  it('Simulate table sorting functionality', () => {
    const report2 = wrapper.find('[data-testid="fml-custom-table-sort"]');
    expect(report2).toHaveLength(1);
    report2.at(0).simulate('click');
  });
});
