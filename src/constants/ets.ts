export const VERIFICATION_STATUS_TYPE = Object.freeze({
  DATA_NOT_SENT: 'data-not-sent',
  DATA_SENT: 'data-sent',
  VERIFICATION_TRIGGERED: 'verification-triggered',
  VERIFIED: 'verified',
  NEED_EVIDENCE: 'need-evidence',
  DATA_VALIDATED: 'data-validated',
});

export const VERIFICATION_STATUS_DESC = Object.freeze({
  'data-not-sent': 'Pending',
  'data-sent': 'Pending',
  'verification-triggered': 'Pending',
  verified: 'Verified',
  'need-evidence': 'Correction',
  'data-validated': 'Pending',
});
