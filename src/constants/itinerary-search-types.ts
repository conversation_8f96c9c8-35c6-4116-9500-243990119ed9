export const QUERY_TYPE_LIKE = 'like';
export const QUERY_TYPE_MATCH = 'match';
export const QUERY_TYPE_RANGE = 'range';

export const ITINERARY_SEARCH_TYPES = [
  {
    type: 'countries',
    name: 'Country',
    section: '',
    inputType: 'multiselect',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'country',
  },
  {
    type: 'berth',
    name: 'Berth',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'berth',
  },
  {
    type: 'ports',
    name: 'Port',
    section: '',
    inputType: 'multiselect',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'port',
  },
  {
    type: 'estimated_arrival',
    name: 'ETA',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'estimated_arrival',
  },
  {
    type: 'estimated_berth',
    name: 'ETB',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'estimated_berth',
  },
  {
    type: 'estimated_departure',
    name: 'ETD',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'estimated_departure',
  },
  {
    type: 'vessel_itinerary_reasons',
    name: 'Reason for Portcall',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'vessel_itinerary_reasons',
  },
];

export const FILTER_MENU_ITEMS = ['Country', 'Port', 'ETA', 'ETB', 'ETD'];

export default ITINERARY_SEARCH_TYPES;
