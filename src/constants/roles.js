const VESSEL = {
  APPROVE: 'vessel|approve',
  CREATE: 'vessel|create',
  EDIT: 'vessel|edit',
  EXPORT_TO_EXCEL: 'vessel|list|export',
  FINAL_APPROVE: 'vessel|final-approve',
  MOVE_TO_ARCHIVAL: 'vessel|move-to-archival',
  MOVE_TO_ACTIVE: 'vessel|move-to-active',
  MOVE_TO_HANDOVER: 'vessel|move-to-handover',
  REQUEST_HANDOVER: 'vessel|request-handover',
  REQUEST_ARCHIVAL: 'vessel|request-archival',
  CHANGE_FLAG: 'vessel|edit|flag',
  VIEW: 'vessel|view',
  SEND: 'vessel|send',
  VIEW_HISTORY: 'vessel|view|history',
  VIEW_TANKER: 'vessel|view|tanker',
  VIEW_DRY: 'vessel|view|dry',
  VIEW_APPROVAL: 'vessel|view-approval',
  VIEW_ASSIGNED: 'vessel|view|assigned',
  ADMIN: 'vessel|admin',
  ADMIN_DRILLS_VIEW: 'vessel|admin|emergency-drills|view',
  ADMIN_DRILLS_CREATE: 'vessel|admin|emergency-drills|create',
  ADMIN_DRILLS_EDIT: 'vessel|admin|emergency-drills|edit',
  DRILLS_VIEW: 'vessel|emergency-drills|view',
  DRILLS_ASSIGN: 'vessel|emergency-drills|assign',
  CONTROL_PARAMS_VIEW: 'vessel|control-params|view',
  CONTROL_PARAMS_EDIT: 'vessel|control-params|edit',
  VIEW_CREW_ASSIGNMENT: 'crew-assignment|view',
  CERTIFICATE_VIEW_ALL: 'vessel|certificate|view|all',
  CERTIFICATE_MANAGE: 'vessel|admin|certificate|manage',
  CERTIFICATE_ASSIGN: 'vessel|certificate|assign',
  CERTIFICATE_CREATE: 'vsl|crt|c',
  FINANCIAL_ACCOUNTS_MANAGE: 'vessel|financial|manage',
  FINANCIAL_ACCOUNTS_VIEW: 'vessel|financial|view',
  FINANCIAL_TECHNICAL_VIEW: 'vessel|ownerreport|view',
  FINANCIAL_TECHNICAL_MANAGE: 'vessel|ownerreport|manage',
  FINANCIAL_CASH_CALL_VIEW: 'vessel|cashcall|view',
  FINANCIAL_CASH_CALL_MANAGE: 'vessel|cashcall|manage',
  VESSEL_PRODUCT_EORB: 'vessel|isea|view',
  FINANCIAL_EDIT: 'vessel|edit|financial',
  REPORT_EDIT: 'vessel|report|edit',
  FINANCIAL_PAY_CALCULATION: 'vessel|edit|financial|payroll',
  BUYER_MANAGE: 'vessel|buyer|manage',
  ACCOUNTANT_MANAGE: 'vessel|accountant|manage',
  SUPDT_MANAGE: 'vessel|supdt|manage',
  QHSE_MANAGE: 'vessel|qhse|manage',
  OPERATION_MANAGE: 'vessel|operation|manage',
  PAYROLL_MANAGE: 'vessel|payrollacc|manage',
  PARIS1_VIEW: 'paris1links|view',
  EUETS_REPORT_EDIT: 'vessel|environmental|report|eu-ets|edit',
  MANUAL_SYNC_REPORT: 'vessel|eu|sync',
  TECH_GROUP_MANAGE: 'vessel|techgroup|manage',
  POD_MANAGER: 'vessel|pod|manage',
};
const VIR_ROLES = {
  GENERAL: 'inspection|vir|general',
  HEAD: 'inspection|vir|head',
  SENIOR: 'inspection|vir|senior',
  SUPERINTENDENT: 'inspection|vir|superintendent',
};

const SEAFARER_ROLES = {
  SEAFARER_REPORT_MODELLER_VIEW: 'sf|rt|md|v',
  SEAFARER_VIEW_GENERAL: 'seafarer|view|general',
  SEAFARER_VIEW_CREW_LIST: 'seafarer|view|crew-list',
};

const SHIP_PARTY_ROLES = {
  SHIP_PARTY_VIEW_GENERAL: 'ship-party|view|general',
};

const OWNER_REPORTING_ROLES = {
  VIEW: 'vessel|financial|view',
  MANAGE: 'vessel|financial|manage',
  HAS_ALL_VESSEL_ACCESS: 'vessel|financial|report|list-full-access',
  CAN_DELETE_REPORT: 'vsl|fin|rpt|man|del',
};

export default { VESSEL, VIR_ROLES, SEAFARER_ROLES, SHIP_PARTY_ROLES, OWNER_REPORTING_ROLES };
