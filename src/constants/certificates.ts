import { QUERY_TYPE_MATCH } from './itinerary-search-types';

export const CERTIFICATE_GROUP = [
  { id: 'flag', value: 'Flag' },
  { id: 'class', value: 'Class' },
  { id: 'other', value: 'Other' },
  { id: 'new_reg', value: 'New Regs' },
];

export const SURVEY_DEPARTMENT = [
  { id: 'Tech Group', value: 'Tech Group' },
  { id: 'Fleet Personnel', value: 'Fleet Personnel' },
  { id: 'QMS Management', value: 'QMS Management' },
  { id: 'Insurance', value: 'Insurance' },
  { id: 'Tanker Operations', value: 'Marine Operations'}
];

export const STATUS = [
  { id: 'to_request', value: 'To Request' },
  { id: 'requested', value: 'Requested Awaiting Confirmation' },
  { id: 'cancelled', value: 'Inspection Cancelled by us' },
  { id: 'screen', value: 'Will Screen basis SIRE/CDI' },
  { id: 'decline', value: 'Decline to Inspect' },
  { id: 'confirmed', value: 'Inspection Confirmed' },
];

export const VISIBLE_TO_OWNERS = [
  { id: 'true', value: 'Is Visible to Owners' },
  { id: 'false', value: 'Is Not Visible to Owners' },
];

export const COMPULSORY_TO_ALL_VESSELS = [
  { id: 'true', value: 'Is Compulsory for All Vessels' },
  { id: 'false', value: 'Is Not Compulsory for All Vessels' },
];

export const MARK_AS_IMPORTANT = [
  { id: 'true', value: 'Marked as Important' },
  { id: 'false', value: 'Not Marked as Important' },
];

export const CERTIFICATE_SEARCH_TYPES = [
  {
    type: 'group',
    name: 'Certificate Group',
    section: 'select',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'group',
  },
  {
    type: 'department',
    name: 'Survey Department',
    section: 'select',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'department',
  },
  {
    type: 'applicable_vessel_type',
    name: 'Type',
    section: 'select',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'applicable_vessel_type',
  },
  {
    type: 'is_visible',
    name: 'Visible to Owners',
    section: 'checkbox',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'is_visible',
  },
  {
    type: 'is_compulsory',
    name: 'Compulsory for All Vessels',
    section: 'checkbox',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'is_compulsory',
  },
  {
    type: 'is_important',
    name: 'Mark as Important',
    section: 'checkbox',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'is_important',
  },
];

export const SUPERINTENDENT_ONBOARDING_GROUP = '/Department/Tech Group/SuperintendentOB';

export const TECH_GROUPS = 'tech_group'

export const QMS_DEPT = 'QMS Management'