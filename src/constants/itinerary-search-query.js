import itinerarySearchTypes, {
  QUERY_TYPE_LIKE,
  QUERY_TYPE_MATCH,
  QUERY_TYPE_RANGE,
} from './itinerary-search-types';
import moment from 'moment';
import { string } from 'prop-types';
import { formatDate } from '../util/view-utils';

const getValueFromCriteriaItem = (item) => {
  let value = string;
  switch (item.type.inputType) {
    case 'dropdown':
      value = item.subtype.value;
      break;
    case 'multiselect':
      value = item.subtype ? item.subtype.map((selected) => selected.value) : [];
      break;
    case 'text':
      value = item.subtype;
      break;
    case 'number_range': {
      const min = item.subtype.min;
      const max = item.subtype.max;
      value = { min, max };
      break;
    }
    case 'date': {
      let min = formatDate(item.subtype.startDate, 'YYYY-MM-DD', 'Invalid date');
      let max = formatDate(item.subtype.endDate, 'YYYY-MM-DD', 'Invalid date');
      if (max === 'Invalid date' && min === 'Invalid date') break;
      if (max === 'Invalid date') max = '';
      if (min === 'Invalid date') min = '';
      value = { min, max };
      break;
    }
    case 'year': {
      const year = formatDate(item.subtype, 'YYYY', '---');
      if (year !== 'Invalid date') value = { min: `${year}-01-01`, max: `${year}-12-31` };
      break;
    }
    default:
      value = item.subtype ?? '';
  }
  return value;
};

const putValueToCriteriaItem = (item, value) => {
  switch (item.type.inputType) {
    case 'dropdown':
      item.subtype = { value };
      break;
    case 'multiselect':
      item.subtype = { value };
      break;
    case 'text':
      item.subtype = value;
      break;
    case 'number_range':
      item.subtype = {
        ...value,
      };
      break;
    case 'date': {
      let startDate = moment(value.min).toDate();
      let endDate = moment(value.max).toDate();
      if (startDate.toString() === 'Invalid Date' && endDate.toString() === 'Invalid Date') break;
      else if (startDate.toString() === 'Invalid Date') item.subtype = { endDate };
      else if (endDate.toString() === 'Invalid Date') item.subtype = { startDate };
      else item.subtype = { startDate, endDate };
      break;
    }
    case 'year':
      item.subtype = moment(value.min).toDate() == 'Invalid Date' ? '' : moment(value.min).toDate();
      break;
    default:
      item.subtype = value;
  }
  return item;
};

export const mapSearchCriteriaToVesselQuery = (searchCriteria) => {
  const query = searchCriteria
    .reduce((arr, item) => {
      const {
        type: { queryType, queryKey, inputType },
      } = item;
      const queryItems = [];

      // get value
      let value = getValueFromCriteriaItem(item);

      // push query items
      switch (queryType) {
        case QUERY_TYPE_LIKE:
          queryItems.push({
            key: queryKey,
            value: value ?? '',
          });
          break;
        case QUERY_TYPE_RANGE:
          if (value) {
            queryItems.push({
              key: `${queryKey}`,
              value: `${value.min},${value.max}`,
            });
          }
          break;
        case QUERY_TYPE_MATCH:
          if (inputType === 'multiselect') {
            value.map((selected) => {
              queryItems.push({
                key: queryKey,
                value: queryKey === 'port' ? selected.toString().substring(0, 4) : selected,
              });
            });
            break;
          }
          queryItems.push({
            key: queryKey,
            value,
          });
          break;
        default:
          queryItems.push({
            key: queryKey,
            value,
          });
      }
      return [...arr, ...queryItems];
    }, [])
    .map((item) => {
      const { key, value } = item;
      return `${key}=${encodeURIComponent(value)}`;
    })
    .join('&');

  return query;
};

const createRangeItem = ({ value, searchType, key }) => {
  const valuePair = value.split(',');
  return {
    searchType,
    key,
    value: {
      min: valuePair[0],
      max: valuePair[1],
    },
  };
};

export const mapVesselQueryToSearchCriteria = (vesselQuery, dropDownData) => {
  const criteria = decodeURIComponent(vesselQuery)
    .split('&')
    .map((param) => param.split('='))
    .reduce((arr, [key, value]) => {
      if (!key) return arr;
      const searchType = itinerarySearchTypes.find((type) => type.queryKey === key);
      if (!searchType) return arr;
      const { queryType } = searchType;
      if (queryType === QUERY_TYPE_RANGE) {
        return [...arr, createRangeItem({ value, searchType, key })];
      }
      return [...arr, { searchType, key, value }];
    }, [])
    .map(({ searchType, value }) => {
      const item = {
        type: searchType,
      };
      putValueToCriteriaItem(item, value);
      if (searchType.inputType === 'dropdown' && dropDownData) {
        const dropDownOptions = dropDownData[searchType.type];
        if (!dropDownOptions) return item;
        const data = dropDownOptions.find(({ value }) => value === item.subtype.value);
        if (!data) return item;
        item.subtype.id = data.id;
      }
      return item;
    });
  return criteria;
};

export const modifyJSONData = (data) => {
  let result;
  if (data?.countries) {
    const result = data.countries.map((item) => {
      return {
        id: item.alpha2_code,
        value: item.value,
      };
    });
    return { countries: result };
  }
  if (data?.ports) {
    result = data.ports.map((item) => {
      return {
        id: item.city_code,
        value: item.name,
      };
    });
    return { ports: result };
  }
};
