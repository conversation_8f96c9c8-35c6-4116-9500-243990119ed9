export const LOWER_CALORIFIC_VALUES = Object.freeze({
  HFO: 0.0405,
  LFO: 0.041,
  MGO: 0.0427,
  LNG: 0.0491,
  LPG_PROPANE: 0.046,
  LPG_BUTANE: 0.046,
  METHANOL_NG: 0.0199,
});

export const WELL_TO_WAKE_GHG_INTENSITY_VALUES = Object.freeze({
  HFO: 91.74,
  LFO: 91.39,
  MGO: 90.77,
  LNG: 82.88,
  LPG_PROPANE: 74.21,
  LPG_BUTANE: 74.86,
  METHANOL_NG: 103.15,
});

export const GHG_INTENSITY_TARGET_VALUES = Object.freeze({
  2025: 89.3368,
  2026: 89.3368,
  2027: 89.3368,
  2028: 89.3368,
  2029: 89.3368,
  2030: 85.6904,
  2031: 85.6904,
  2032: 85.6904,
  2033: 85.6904,
  2034: 85.6904,
  2035: 77.9418,
  2036: 77.9418,
  2037: 77.9418,
  2038: 77.9418,
  2039: 77.9418,
  2040: 62.9044,
  2041: 62.9044,
  2042: 62.9044,
  2043: 62.9044,
  2044: 62.9044,
  2045: 34.6408,
  2046: 34.6408,
  2047: 34.6408,
  2048: 34.6408,
  2049: 34.6408,
  2050: 18.232,
});
