export const STATUS = {
  ON_GOING: {
    label: 'Ongoing',
    className: 'yellow',
  },
  APPROVED: {
    label: 'Approved',
    className: 'blue',
  },
  NOT_APPROVED: {
    label: 'Rejected',
    className: 'red',
  },
  SUBMITTED: {
    label: 'Submitted',
    className: 'green',
  },
  FROZEN: {
    label: 'Submitted',
    className: 'green',
  },
  RE_OPENED: {
    label: 'Reopened',
    className: 'peach',
  },
  VIEW_ONLY: {
    label: 'Closed',
    className: 'grey',
  },
  UNDER_REVIEW: {
    label: 'Under Review',
    className: 'orange',
  },
};

export const ONGOING_AND_FINALIZED_UNDER_REVIEW_RE_OPENED_APPROVED_STATUSES = ['ON_GOING', 'FINALIZE', 'UNDER_REVIEW', 'RE_OPENED','APPROVED'];
export const SUBMITTED_AND_RE_OPENED_STATUSES = ['SUBMITTED', 'RE_OPENED'];
export const DELETE_REPORT_BODY_TEXT = 'Are you sure you want to delete this report?';
export const GENERATE_REPORT_BODY_TEXT = 'Are you sure you want to Generate financial report for the';
export const VESSEL_STATUS_OPTIONS = {
  ACTIVE: 'active',
  HANDED_OVER: 'handed_over',
  PENDING_HANDOVER: 'pending_handover',
};
export const VESSEL_STATUS_PENDING_HANDED_OVER = [
  'handed_over',
  'pending_handover',
];
