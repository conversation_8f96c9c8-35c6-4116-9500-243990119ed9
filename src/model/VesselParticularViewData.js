import { valueOrDash, Dash } from './utils.js';
const VesselParticularViewData = (data) => [
  {
    label: 'Life Boat Capacity',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['life_boat_capacity'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['life_boat_capacity'])),
  },
  {
    label: 'Length O.A.',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['length_oa'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['length_oa'])),
    unit: 'm',
  },
  {
    label: 'Length BP',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['length_bp'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['length_bp'])),
    unit: 'm',
  },
  {
    label: 'Depth',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['depth'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['depth'])),
    unit: 'm',
  },
  {
    label: 'Breadth (extreme)',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['breadth_extreme'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['breadth_extreme'])),
    unit: 'm',
  },
  {
    label: 'Summer Draft',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['summer_draft'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['summer_draft'])),
    unit: 'm',
  },
  {
    label: 'Summer DWT',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['summer_dwt'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['summer_dwt'])),
    unit: 'mt',
  },
  {
    label: 'International GRT',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['international_grt'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['international_grt'])),

    unit: 'mt',
  },
  {
    label: 'International NRT',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['international_nrt'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['international_nrt'])),
    unit: 'mt',
  },
  {
    label: 'Service Speed',
    value: isNaN(parseFloat(valueOrDash(data.vessel, ['service_speed'])))
      ? Dash
      : parseFloat(valueOrDash(data.vessel, ['service_speed'])),
    unit: 'knots',
  },
];

export default VesselParticularViewData;
