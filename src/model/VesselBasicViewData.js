import * as R from 'ramda';
import { valueOrDash, dateOrDash } from './utils.js';

const VesselBasicViewData = (data) => [
  {
    label: 'This vessel is',
    value: valueOrDash(data.vessel, ['service_status', 'value']),
  },
  {
    label: 'IMO Number',
    value: valueOrDash(data.vessel, ['imo_number']),
  },
  {
    label: 'Owners',
    value: valueOrDash(data, ['owner', 'value']),
  },
  {
    label: 'Vessel Type',
    value: valueOrDash(data, ['vessel_type', 'value']),
  },
  {
    label: 'Emission Type',
    value: valueOrDash(data.vessel, ['emission_type', 'emission_type']),
  },
  {
    label: 'Shipyard',
    value: valueOrDash(data.vessel, ['shipyard_text']),
  },
  {
    label: 'Vessel Hull Number',
    value: valueOrDash(data.vessel, ['vessel_hull_number']),
  },
  {
    label: 'Year Built / Date of Delivery',
    value: vesselDeliveryDate(data.vessel),
  },
  {
    label: 'Expected Date of Takeover',
    value: dateOrDash(data, ['expected_owner_start_date']),
  },
  {
    label: 'Date of Takeover',
    value: dateOrDash(data, ['owner_start_date']),
  },
  {
    label: 'Date of Handover',
    value: dateOrDash(data, ['owner_end_date']),
  },
  {
    label: 'Class',
    value: R.pluck('notation', valueOrDash(data, ['class_notations'])).join(', '),
  },
  {
    label: 'H & M Underwriter',
    value: valueOrDash(data.vessel, ['h_m_underwriter', 'value']),
  },
  {
    label: 'P & I Club',
    value: valueOrDash(data.vessel, ['p_i_club', 'value']),
  },
  {
    label: 'Flag - Office',
    value: valueOrDash(_.first(data.flags), ['office', 'value']),
  },
  {
    label: 'Flag - Country',
    value: valueOrDash(_.first(data.flags), ['country_desc']),
  },
  {
    label: 'Port of Registry',
    value: valueOrDash(_.first(data.flags), ['port_desc']),
  },
  {
    label: 'Call Sign',
    value: valueOrDash(_.first(data.flags), ['call_sign']),
  },
];

export default VesselBasicViewData;

const vesselDeliveryDate = (vessel) => {
  const deliveryDate = vessel['date_of_delivery'];
  return deliveryDate
    ? dateOrDash(vessel, ['date_of_delivery'])
    : dateOrDash(vessel, ['year_of_delivery'], 'YYYY');
};
