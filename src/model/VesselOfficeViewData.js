import React from 'react';
import _ from 'lodash';
import { assignReplaceUser } from '../util/view-utils.js';
import { Dash } from './utils.js';
import { POD_MANAGER_SUB_ROLES } from '../constants/assign-user.ts';

const VesselOfficeViewData = (
  {
    fleet_staff,
    id: ownershipId,
    vessel_short_code = Dash,
    vessel_account_code = Dash,
    vessel_account_code_new = Dash,
    vessel_tel_fac_code = Dash,
    temp_ref_id,
    vessel,
  },
  setAssignUserActionStatus,
  roleConfig,
) => {
  const fetchData = (labelData) => (
    <>
      {labelData.map((item) => (
        <p key={item.label}>
          {item.label} - {item.label_value}
        </p>
      ))}
    </>
  );

  return [
    {
      label: fetchData([
        {
          label: 'Tech Group',
          label_value: roleConfig.techGroups.manage && temp_ref_id
            ? assignReplaceUser(
                {
                  subrole: 'tech_group',
                  ownershipid: ownershipId,
                  full_name: fleet_staff?.tech_group,
                  osc_pilot: vessel?.osc_pilot,
                  tech_group_modal: true,
                },
                setAssignUserActionStatus,
                'tech_group',
                'tech group',
              )
            : _.get(fleet_staff, 'tech_group', 'Not assigned'),
        },
      ]),
      value: fetchData([
        {
          label: 'Group Head',
          label_value: _.get(fleet_staff, 'tech_group_group_head.full_name', 'Not assigned'),
        },
        {
          label: 'Superintendent',
          label_value: roleConfig.vessel.staff.supdt
            ? assignReplaceUser(
                {
                  subrole: 'superintendent',
                  ownershipid: ownershipId,
                  tech_group: fleet_staff?.tech_group,
                  ...fleet_staff?.superintendent,
                },
                setAssignUserActionStatus,
                'superintendent',
                'superintendent',
              )
            : _.get(fleet_staff, 'superintendent.full_name', 'Not assigned'),
        },
      ]),
    },
    {
      label: 'QHSE',
      value: fetchData([
        {
          label: 'Deputy General Manager',
          label_value: _.get(fleet_staff, 'qhse_deputy_general_manager.full_name', 'Not assigned'),
        },
        {
          label: 'QHSE Manager',
          label_value: roleConfig.vessel.staff.qhse
            ? assignReplaceUser(
                { subrole: 'qhse', ownershipid: ownershipId, ...fleet_staff?.qhse_manager },
                setAssignUserActionStatus,
                'qhse',
                'QHSE Manager',
              )
            : _.get(fleet_staff, 'qhse_manager.full_name', 'Not assigned'),
        },
      ]),
    },
    {
      label: 'POD Manager (Ranks)',
      value: fetchData([
        {
          label: 'Primary',
          label_value: roleConfig.vessel.staff.pod_manager
            ? assignReplaceUser(
                {
                  subrole: POD_MANAGER_SUB_ROLES[0],
                  ownershipid: ownershipId,
                  ...fleet_staff?.primary_pod_manager_rank,
                },
                setAssignUserActionStatus,
                'pod_manager_rank',
                'Primary POD Manager Rank',
              )
            : _.get(fleet_staff, 'primary_pod_manager_rank.full_name', 'Not assigned')
        },
        {
          label: 'Secondary',
          label_value: roleConfig.vessel.staff.pod_manager
            ? assignReplaceUser(
                {
                  subrole: POD_MANAGER_SUB_ROLES[1],
                  ownershipid: ownershipId,
                  ...fleet_staff?.secondary_pod_manager_rank,
                },
                setAssignUserActionStatus,
                'pod_manager_rank',
                'Secondary POD Manager Rank',
              )
            : _.get(fleet_staff, 'secondary_pod_manager_rank.full_name', 'Not assigned'),
        },
      ]),
    },
    {
      label: 'POD Manager (Ratings)',
      value: fetchData([
        {
          label: 'Primary',
          label_value: roleConfig.vessel.staff.pod_manager
            ? assignReplaceUser(
                {
                  subrole: POD_MANAGER_SUB_ROLES[2],
                  ownershipid: ownershipId,
                  ...fleet_staff?.primary_pod_manager_ratings,
                },
                setAssignUserActionStatus,
                'pod_manager_ratings',
                'Primary POD Manager Ratings',
              )
            : _.get(fleet_staff, 'primary_pod_manager_ratings.full_name', 'Not assigned')
        },
        {
          label: 'Secondary',
          label_value: roleConfig.vessel.staff.pod_manager
            ? assignReplaceUser(
                {
                  subrole: POD_MANAGER_SUB_ROLES[3],
                  ownershipid: ownershipId,
                  ...fleet_staff?.secondary_pod_manager_ratings,
                },
                setAssignUserActionStatus,
                'pod_manager_ratings',
                'Secondary POD Manager Ratings',
              )
            : _.get(fleet_staff, 'secondary_pod_manager_ratings.full_name', 'Not assigned'),
        },
      ]),
    },
    {
      label: 'Operations',
      value: fetchData([
        {
          label: 'Operations Director',
          label_value: _.get(fleet_staff, 'operation_director.full_name', 'Not assigned'),
        },
        {
          label: 'Operations Manager',
          label_value: roleConfig.vessel.staff.operation
            ? assignReplaceUser(
                {
                  subrole: 'operation',
                  ownershipid: ownershipId,
                  ...fleet_staff?.operation_manager,
                },
                setAssignUserActionStatus,
                'operation',
                'operation manager',
              )
            : _.get(fleet_staff, 'operation_manager.full_name', 'Not assigned'),
        },
      ]),
    },
    {
      label: 'Vessel Accountants',
      value: fetchData([
        {
          label: 'Primary',
          label_value: roleConfig.vessel.staff.accountant
            ? assignReplaceUser(
                {
                  subrole: 'accountant1',
                  ownershipid: ownershipId,
                  ...fleet_staff?.primary_accountant,
                },
                setAssignUserActionStatus,
                'accountant',
                'primary accountant',
              )
            : _.get(fleet_staff, 'primary_accountant.full_name', 'Not assigned'),
        },
        {
          label: 'Secondary',
          label_value: roleConfig.vessel.staff.accountant
            ? assignReplaceUser(
                {
                  subrole: 'accountant2',
                  ownershipid: ownershipId,
                  ...fleet_staff?.secondary_accountant,
                },
                setAssignUserActionStatus,
                'accountant',
                'Secondary accountant',
              )
            : _.get(fleet_staff, 'secondary_accountant.full_name', 'Not assigned'),
        },
      ]),
    },
    {
      label: 'Payroll Accountants',
      value: fetchData([
        {
          label: 'Primary',
          label_value: roleConfig.vessel.staff.payroll
            ? assignReplaceUser(
                { subrole: 'payroll1', ownershipid: ownershipId, ...fleet_staff?.primary_payroll },
                setAssignUserActionStatus,
                'payroll',
                'primary accountant',
              )
            : _.get(fleet_staff, 'primary_payroll.full_name', 'Not assigned'),
        },
        {
          label: 'Secondary',
          label_value: roleConfig.vessel.staff.payroll
            ? assignReplaceUser(
                {
                  subrole: 'payroll2',
                  ownershipid: ownershipId,
                  ...fleet_staff?.secondary_payroll,
                },
                setAssignUserActionStatus,
                'payroll',
                'Secondary accountant',
              )
            : _.get(fleet_staff, 'secondary_payroll.full_name', 'Not assigned'),
        },
      ]),
    },
    {
      label: 'Procurement',
      value: fetchData([
        {
          label: 'Senior Lead Buyer',
          label_value: roleConfig.vessel.staff.buyer
            ? assignReplaceUser(
                {
                  subrole: 'buyer_senior_lead',
                  ownershipid: ownershipId,
                  ...fleet_staff?.buyer_senior_lead,
                },
                setAssignUserActionStatus,
                'buyer_senior_lead',
                'senior lead buyer',
              )
            : _.get(fleet_staff, 'buyer_senior_lead.full_name', 'Not assigned'),
        },
        {
          label: 'Lead Buyer',
          label_value: roleConfig.vessel.staff.buyer
            ? assignReplaceUser(
                { subrole: 'buyer_lead', ownershipid: ownershipId, ...fleet_staff?.buyer_lead },
                setAssignUserActionStatus,
                'buyer_lead',
                'lead buyer',
              )
            : _.get(fleet_staff, 'buyer_lead.full_name', 'Not assigned'),
        },
        {
          label: 'Buyer',
          label_value: roleConfig.vessel.staff.buyer
            ? assignReplaceUser(
                { subrole: 'buyer', ownershipid: ownershipId, ...fleet_staff?.buyer },
                setAssignUserActionStatus,
                'buyer',
                'buyer',
              )
            : _.get(fleet_staff, 'buyer.full_name', 'Not assigned'),
        },
      ]),
    },
    {
      label: 'Vessel Short Code',
      value: vessel_short_code,
    },
    {
      label: 'Vessel Account Code (OLD)',
      value: vessel_account_code,
    },
    {
      label: 'Vessel Account Code',
      value: vessel_account_code_new,
    },
    {
      label: 'Vessel Tel FAC Code',
      value: vessel_tel_fac_code,
    },
  ];
};

export default VesselOfficeViewData;
