import { valueOrDash, Dash } from './utils.js';

const VesselContactViewData = (data) => {
  const phones = valueOrDash(data, ['phones']);
  const emails = valueOrDash(data, ['emails']);

  const phoneRows =
    phones == Dash
      ? []
      : phones.map(function (phone) {
          return {
            label: valueOrDash(phone, ['phone_type', 'value']),
            value: valueOrDash(phone, ['phone_number']),
          };
        });

  const emailRows =
    emails == Dash
      ? []
      : emails.map(function (email) {
          return {
            label: valueOrDash(email, ['email_type', 'value']),
            value: valueOrDash(email, ['email']),
          };
        });

  return phoneRows.concat(emailRows);
};

export default VesselContactViewData;
