import { valueOrDash } from './utils.js';
import { PAY_CALCULATION_DROPDOWN } from './constants.js';
import LNG_ENGINE_CATEGORY from '../constants/lng-type';

const VesselMiscellaneousViewData = (data) => [
  {
    label: 'Engine',
    value: valueOrDash(data.vessel, ['misc_engine', 'value']),
  },
  {
    label: 'DWT',
    value: valueOrDash(data.vessel, ['dwt']),
  },
  {
    label: 'Power(kW)',
    value: valueOrDash(data.vessel, ['bhp']),
  },
  {
    label: 'LNG Engine Category (Main Engine)',
    value:
      LNG_ENGINE_CATEGORY.find(
        (category) => category.id === valueOrDash(data.vessel, ['lng_engine_category_main_engine']),
      )?.value ?? '- - -',
  },
  {
    label: 'LNG Engine Category (Diesel Generator)',
    value:
      LNG_ENGINE_CATEGORY.find(
        (category) =>
          category.id === valueOrDash(data.vessel, ['lng_engine_category_diesel_generator']),
      )?.value ?? '- - -',
  },
  {
    label: 'Registered Owner',
    value: valueOrDash(data, ['registered_owner', 'value']),
  },
  {
    label: 'Operator',
    value: valueOrDash(data.vessel, ['misc_operator', 'value']),
  },
  {
    label: 'Manager',
    value: valueOrDash(data.vessel, ['misc_manager', 'value']),
  },
  {
    label: 'Currency',
    value: valueOrDash(data.vessel, ['misc_currency', 'value']),
  },
  {
    label: 'Is Manning Manager',
    value: valueOrDash(data.vessel, ['is_manning_manager']),
  },
  {
    label: 'Flag (ISPS)',
    value: valueOrDash(data, ['flag_isps', 'value']),
  },
  {
    label: 'Classification',
    value: valueOrDash(data.vessel, ['misc_classification', 'value']),
  },
  {
    label: 'Classification Society (Emergency Response Assistance)',
    value: valueOrDash(data.vessel, ['misc_classification_society', 'value']),
  },
  {
    label: 'EU Verifier',
    value: data.vessel_class_regulation?.length
      ? valueOrDash(data.vessel_class_regulation[0], ['vessel_class', 'value'])
      : '- - -',
  },
  {
    label: 'QI',
    value: valueOrDash(data.vessel, ['misc_qi', 'value']),
  },
  {
    label: 'OSRO',
    value: valueOrDash(data.vessel, ['misc_osro', 'value']),
  },
  {
    label: 'Salvage',
    value: valueOrDash(data.vessel, ['misc_salvage', 'value']),
  },
  {
    label: 'Media Response',
    value: valueOrDash(data.vessel, ['misc_media_response', 'value']),
  },
  {
    label: 'Other Contacts',
    value: valueOrDash(data.vessel, ['misc_other_contacts', 'value']),
  },
  {
    label: 'IHM Provider',
    value: valueOrDash(data.vessel, ['ihm_provider', 'value']),
  },
  {
    label: 'Management Type',
    value: valueOrDash(data.vessel, ['misc_management_type', 'value']),
  },
  {
    label: 'US Visa Required',
    value: valueOrDash(data.vessel, ['us_visa_required']),
  },
  {
    label: 'Wages Treatment',
    value: valueOrDash(data, ['wages_treatment']),
  },
  {
    label: 'Accumulation Wages',
    value: valueOrDash(data, ['wages_accumulation']),
  },
  {
    label: 'Portage bill module',
    value: valueOrDash(data, ['has_portage_bill']),
  },
  {
    label: 'Pay calculation method',
    value: PAY_CALCULATION_DROPDOWN[valueOrDash(data, ['pay_calculation_method'])],
  },
];

export default VesselMiscellaneousViewData;
