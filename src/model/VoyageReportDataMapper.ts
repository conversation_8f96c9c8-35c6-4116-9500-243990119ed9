import _ from 'lodash';
import { formatDate, formatNumber, formatValue, getHighlightColor } from '../util/view-utils';
import { Dash } from './utils';

export const GeneralInfoData = (voyageReportData, parameter) => [
  {
    order: 1,
    label: 'Type',
    value: formatValue(_.get(voyageReportData, 'report_type'), Dash),
  },
  {
    order: 2,
    label: 'Date Time (SMT) of Arrival at/ Departure From, location below',
    value: formatDate(_.get(voyageReportData, 'smt'), 'DD MMM YYYY HH:mm'),
  },
  {
    order: 3,
    label: 'Date Time (GMT) of Arrival at/ Departure From, location below',
    value: formatDate(_.get(voyageReportData, 'gmt'), 'DD MMM YYYY HH:mm'),
  },
  {
    order: 4,
    label: 'Location',
    value: formatValue(_.get(voyageReportData, 'report_json.current.location_desc'), Dash),
  },
  {
    order: 5,
    label: 'ETB',
    value: formatValue(_.get(voyageReportData, 'report_json.current.etd'), Dash),
  },
  {
    order: 6,
    label: 'Time Spent at sea (Hr)',
    text_color: getHighlightColor(_.get(voyageReportData, 'hours', 0), parameter, 'hours'),
    value: formatValue(_.get(voyageReportData, 'hours'), Dash),
  },
  {
    order: 7,
    label: 'Berth/ Anchorage/ Lighterage',
    value: '',
    header: 'Berth/ Anchorage/ Lighterage',
  },
  {
    order: 8,
    label: 'Location Name',
    value: formatValue(_.get(voyageReportData, 'report_json.current.berth'), Dash),
  },
  {
    order: 9,
    label: 'Position (Latitude)',
    value: formatValue(_.get(voyageReportData, 'report_json.current.berth_lat'), Dash),
  },
  {
    order: 10,
    label: 'Position (Longitude)',
    value: formatValue(_.get(voyageReportData, 'report_json.current.berth_lon'), Dash),
  },
  {
    order: 11,
    label: 'Country',
    value: formatValue(_.get(voyageReportData, 'report_json.current.country_desc'), Dash),
  },
  {
    order: 12,
    label: 'Port',
    value: formatValue(_.get(voyageReportData, 'report_json.current.port_name'), Dash),
  },
  {
    order: 13,
    label: 'Next Location',
    value: '',
    header: 'Next Location',
  },
  {
    order: 14,
    label: 'Next Country',
    value: formatValue(_.get(voyageReportData, 'report_json.next.country_desc'), Dash),
  },
  {
    order: 15,
    label: 'Next Port',
    value: formatValue(_.get(voyageReportData, 'report_json.next.port_name'), Dash),
  },
  {
    order: 16,
    label: 'ETA (SMT)',
    value: formatDate(_.get(voyageReportData, 'report_json.next.eta'), 'DD MMM YYYY HH:mm'),
  },
  {
    order: 17,
    label: 'Last Location',
    value: '',
    header: 'Last Location',
  },
  {
    order: 18,
    label: 'Last Country',
    value: formatValue(_.get(voyageReportData, 'report_json.last.country_desc'), Dash),
  },
  {
    order: 19,
    label: 'Last Port',
    value: formatValue(_.get(voyageReportData, 'report_json.last.port_name'), Dash),
  },
  {
    order: 20,
    label: 'Last Location',
    value: formatValue(_.get(voyageReportData, 'report_json.last.location_desc'), Dash),
  },
  {
    order: 21,
    label: 'Name of Last Berth/ Anchorage/ Lighterage (Location Name) ',
    value: formatValue(_.get(voyageReportData, 'report_json.last.berth'), Dash),
  },
];

export const ConsumptionData = (voyageReportData, parameter) => [
  {
    order: 1,
    label: 'Main Engines',
    value: '',
    header: 'Main Engines',
  },
  {
    order: 2,
    label: 'HFO',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.cons.me.hfo', 0),
      parameter,
      'report_json.cons.me.hfo',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.me.hfo')),
  },
  {
    order: 3,
    label: 'MGO/MDO',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.cons.me.mgo', 0),
      parameter,
      'report_json.cons.me.mgo',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.me.mgo')),
  },
  {
    order: 4,
    label: 'Fuel3 Type',
    value: formatValue(_.get(voyageReportData, 'report_json.cons.me.fuel3_desc'), Dash),
  },
  {
    order: 5,
    label: 'Fuel3',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.me.fuel3')),
  },
  {
    order: 6,
    label: 'Average Consumption Per Day',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.me.avg')),
  },
  {
    order: 7,
    label: 'Diesel Generator Engines',
    value: '',
    header: 'Diesel Generator Engines',
  },
  {
    order: 8,
    label: 'HFO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.ge.hfo')),
  },
  {
    order: 9,
    label: 'MGO/MDO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.ge.mgo')),
  },
  {
    order: 10,
    label: 'Fuel3 Type',
    value: formatValue(_.get(voyageReportData, 'report_json.cons.ge.fuel3_desc')),
  },
  {
    order: 11,
    label: 'Fuel3',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.ge.fuel3')),
  },
  {
    order: 12,
    label: 'Average Consumption Per Day',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.cons.ge.avg', 0),
      parameter,
      'report_json.cons.ge.avg',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.ge.avg')),
  },

  {
    order: 13,
    label: 'Boilers',
    value: '',
    header: 'Boilers',
  },
  {
    order: 14,
    label: 'HFO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.boiler.hfo')),
  },
  {
    order: 15,
    label: 'MGO/MDO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.boiler.mgo')),
  },
  {
    order: 16,
    label: 'Fuel3 Type',
    value: formatValue(_.get(voyageReportData, 'report_json.cons.boiler.fuel3_desc')),
  },
  {
    order: 17,
    label: 'Fuel3',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.boiler.fuel3')),
  },
  {
    order: 18,
    label: 'Average Consumption Per Day',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.cons.boiler.avg', 0),
      parameter,
      'report_json.cons.boiler.avg',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.boiler.avg')),
  },
  {
    order: 19,
    label: 'Boiler Cargo Heating',
    value: '',
    header: 'Boiler Cargo Heating',
  },
  {
    order: 20,
    label: 'HFO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.ch.hfo')),
  },
  {
    order: 21,
    label: 'MGO/MDO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.ch.mgo')),
  },

  {
    order: 22,
    label: 'Fuel3',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.ch.fuel3')),
  },

  {
    order: 23,
    label: 'Cargo Engines',
    value: '',
    header: 'Cargo Engines',
  },

  {
    order: 24,
    label: 'MGO/MDO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.ce.mgo')),
  },
  {
    order: 25,
    label: 'Insert Gas Engines',
    value: '',
    header: 'Insert Gas Engines',
  },

  {
    order: 26,
    label: 'MGO/MDO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.igg.mgo')),
  },
  {
    order: 27,
    label: 'Total HFO',
    value: '',
    header: 'Total HFO',
  },

  {
    order: 28,
    label: 'HFO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.hfo.total')),
  },
  {
    order: 29,
    label: 'Weighted Average Sulphur Content(%)',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.hfo.weighted_avg')),
  },

  {
    order: 30,
    label: 'Average Consumption per day',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.hfo.avg')),
  },
  {
    order: 31,
    label: 'Total MGO/MDO',
    value: '',
    header: 'Total MGO/MDO',
  },
  {
    order: 32,
    label: 'MGO/MDO',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.mgo.total')),
  },
  {
    order: 33,
    label: 'Weighted Average Sulphur Content(%)',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.mgo.weighted_avg')),
  },
  {
    order: 34,
    label: 'Average Consumption per day',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.mgo.avg')),
  },

  {
    order: 35,
    label: 'Total Fuel3',
    value: '',
    header: 'Total Fuel3',
  },
  {
    order: 36,
    label: 'Fuel3',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.fuel.total')),
  },
  {
    order: 37,
    label: 'Weighted Average Sulphur Content(%)',
    value: formatNumber(_.get(voyageReportData, 'report_json.cons.fuel.weighted_avg')),
  },
];

export const RemainingBoardData = (voyageReportData, parameter) => [
  {
    order: 1,
    label: 'Low Sulphur HFO on ARR/DEP (MT)',
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.low_hfo')),
  },
  {
    order: 2,
    label: 'High Sulphur HFO on ARR/DEP (MT)',
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.high_hfo')),
  },
  {
    order: 3,
    label: 'HFO (MT)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.rob.hfo', 0),
      parameter,
      'report_json.rob.hfo',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.hfo')),
  },
  {
    order: 4,
    label: 'HFO Consumed Basis (MT)',
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.hfo_basis')),
  },
  {
    order: 5,
    label: 'Low Sulphur MGO/MDO on ARR/DEP (MT)',
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.low_mgo')),
  },
  {
    order: 6,
    label: 'High Sulphur MGO/MDO on ARR/DEP (MT)',
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.high_mgo')),
  },
  {
    order: 7,
    label: 'MGO/MDO (MT)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.rob.mgo', 0),
      parameter,
      'report_json.rob.mgo',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.mgo')),
  },
  {
    order: 8,
    label: 'MGO/MDO Consumed Basis (MT)',
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.mgo_basis')),
  },
  {
    order: 9,
    label: 'Fuel3 on DEP/ARR (MT)',
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.fuel3_mt')),
  },
  {
    order: 10,
    label: 'Fuel3 Consumed Basis (MT)',
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.fuel_basis')),
  },
  {
    order: 11,
    label: 'FW (MT)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.rob.fw', 0),
      parameter,
      'report_json.rob.fw',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.fw')),
  },
  {
    order: 12,
    label: 'MECC (Ltrs)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.rob.mecc', 0),
      parameter,
      'report_json.rob.mecc',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.mecc')),
  },
  {
    order: 14,
    label: 'MECYL (Ltrs)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.rob.mecyl', 0),
      parameter,
      'report_json.rob.mecyl',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.mecyl')),
  },
  {
    order: 15,
    label: 'MECYL (Low TBN) (Ltrs)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.rob.mecyl_low', 0),
      parameter,
      'report_json.rob.mecyl_low',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.mecyl_low')),
  },
  {
    order: 16,
    label: 'AECC (Ltrs)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.rob.aecc', 0),
      parameter,
      'report_json.rob.aecc',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.aecc')),
  },
  {
    order: 17,
    label: 'FREON (KG)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.rob.freon', 0),
      parameter,
      'report_json.rob.freon',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.freon')),
  },
];

export const BunkeringData = (voyageReportData, parameter) => [
  {
    order: 1,
    label: 'Bunkered',
    value: '',
    header: 'Bunkered',
  },
  {
    order: 2,
    label: 'Low Sulphur HFO',
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.low_hfo')),
  },
  {
    order: 3,
    label: 'High Sulphur HFO',
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.high_hfo')),
  },
  {
    order: 4,
    label: 'HFO',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.hfo', 0),
      parameter,
      'report_json.bunker.hfo',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.hfo')),
  },

  {
    order: 5,
    label: 'Low Sulphur MGO/MDO',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.low_mgo', 0),
      parameter,
      'report_json.bunker.low_mgo',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.low_mgo')),
  },
  {
    order: 6,
    label: 'High Sulphur MGO/MDO',
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.high_mgo')),
  },
  {
    order: 7,
    label: 'MGO',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.mgo', 0),
      parameter,
      'report_json.bunker.mgo',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.mgo')),
  },

  {
    order: 8,
    label: 'Fuel3',
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.fuel3')),
  },
  {
    order: 9,
    label: 'Received',
    value: '',
    header: 'Received',
  },
  {
    order: 10,
    label: 'FW (MT)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.receive.fw', 0),
      parameter,
      'report_json.bunker.receive.fw',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.receive.fw')),
  },
  {
    order: 11,
    label: 'MECC (Ltrs)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.receive.mecc', 0),
      parameter,
      'report_json.bunker.receive.mecc',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.receive.mecc')),
  },
  {
    order: 12,
    label: 'MECYL (Ltrs)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.receive.mecyl', 0),
      parameter,
      'report_json.bunker.receive.mecyl',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.receive.mecyl')),
  },
  {
    order: 13,
    label: 'MECYL (Low TBN) (Ltrs)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.receive.mecyl_low', 0),
      parameter,
      'report_json.bunker.receive.mecyl_low',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.receive.mecyl_low')),
  },
  {
    order: 14,
    label: 'AECC (Ltrs)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.receive.aecc', 0),
      parameter,
      'report_json.bunker.receive.aecc',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.receive.aecc')),
  },
  {
    order: 15,
    label: 'FREON (KG)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.receive.freon', 0),
      parameter,
      'report_json.bunker.receive.freon',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.receive.freon')),
  },
];

export const DistanceData = (voyageReportData, parameter) => [
  {
    order: 1,
    label: 'Travelled Over Ground (NM)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.dist.ground', 0),
      parameter,
      'report_json.dist.ground',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.dist.ground')),
  },
  {
    order: 2,
    label: 'Travelled Through Water (Log Distance) (NM)',
    value: formatNumber(_.get(voyageReportData, 'report_json.dist.water')),
  },
];

export const CargoData = (voyageReportData, parameter) => [
  {
    order: 1,
    label: 'Name',
    value: formatValue(_.get(voyageReportData, 'report_json.cargo.name'), Dash),
  },
  {
    order: 2,
    label: 'Loaded (MT or M3 FOR LNG)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.cargo.load', 0),
      parameter,
      'report_json.cargo.load',
    ),

    value: formatNumber(_.get(voyageReportData, 'report_json.cargo.load')),
  },
  {
    order: 3,
    label: 'Discharged (MT or M3 FOR LNG)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.cargo.dis', 0),
      parameter,
      'report_json.cargo.dis',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.cargo.dis')),
  },
  {
    order: 4,
    label: 'Total on Board  on Departure (MT  or M3 FOR LNG)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.cargo.total', 0),
      parameter,
      'report_json.cargo.total',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.cargo.total')),
  },
];
export const PassengerData = (voyageReportData) => [
  {
    order: 1,
    label: 'Embarked (Number)',
    value: formatNumber(_.get(voyageReportData, 'report_json.pass.emb')),
  },
  {
    order: 2,
    label: 'Disembarked(Number)',
    value: formatNumber(_.get(voyageReportData, 'report_json.pass.dis')),
  },
  {
    order: 3,
    label: 'On Board on Departure (Number)',
    value: formatNumber(_.get(voyageReportData, 'report_json.pass.on')),
  },
  {
    order: 4,
    label: 'Transportation Work (T-NM or M3-NM for LNG or p-NM for PASSENGER/RO-PAX)',
    value: formatNumber(_.get(voyageReportData, 'report_json.pass.total')),
  },
];

export const EmissionData = (voyageReportData) => [
  {
    order: 1,
    label: 'HFO CO2',
    value: formatNumber(_.get(voyageReportData, 'report_json.emission.hfo')),
  },
  {
    order: 2,
    label: 'MGO/MDO CO2',
    value: formatNumber(_.get(voyageReportData, 'report_json.emission.mgo')),
  },
  {
    order: 3,
    label: 'Fuel3 CO2',
    value: formatNumber(_.get(voyageReportData, 'report_json.emission.fuel3')),
  },
  {
    order: 4,
    label: 'SOx',
    value: formatNumber(_.get(voyageReportData, 'report_json.emission.sox')),
  },
];

export const OtherData = (voyageReportData, parameter) => [
  {
    order: 1,
    label: 'Engine',
    value: '',
    header: 'Engine',
  },
  {
    order: 2,
    label: 'Time Spent drifting(Hr)',
    value: formatNumber(_.get(voyageReportData, 'report_json.emission.drift')),
  },
  {
    order: 3,
    label: 'Average Speed (Knots)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.drift', 0),
      parameter,
      'report_json.other.drift',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.drift')),
  },
  {
    order: 4,
    label: 'Total ME Revolutions',
    value: formatNumber(_.get(voyageReportData, 'report_json.other.revolution')),
  },
  {
    order: 5,
    label: 'Average RPM',
    value: formatNumber(_.get(voyageReportData, 'report_json.other.rpm')),
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.rpm', 0),
      parameter,
      'report_json.other.rpm',
    ),
  },
  {
    order: 6,
    label: 'Main Engine Propellor Pitch(M)',
    value: formatValue(_.get(voyageReportData, 'report_json.cons.me.prop')),
  },
  {
    order: 7,
    label: 'Main Engine Distance (NM)',
    value: formatNumber(_.get(voyageReportData, 'report_json.other.me_distance')),
  },
  {
    order: 8,
    label: 'Average Slip (%) ',
    value: formatNumber(_.get(voyageReportData, 'report_json.other.average_slip')),
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.average_slip', 0),
      parameter,
      'report_json.other.average_slip',
    ),
  },

  {
    order: 9,
    label: 'Average Consumption',
    value: '',
    header: 'Average Consumption',
  },
  {
    order: 10,
    label: 'Average FW Cons (MT/Day) ',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.avg.fw_cons', 0),
      parameter,
      'report_json.other.avg.fw_cons',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.avg.fw_cons')),
  },
  {
    order: 11,
    label: 'Average MECC Cons (Ltrs/Day)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.avg.mecc', 0),
      parameter,
      'report_json.other.avg.mecc',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.avg.mecc')),
  },
  {
    order: 12,
    label: 'Average MECYL Cons (Ltrs/Day)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.avg.mecyl', 0),
      parameter,
      'report_json.other.avg.mecyl',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.avg.mecyl')),
  },
  {
    order: 13,
    label: 'Average MECYL Cons (Low TBN) (Ltrs)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.avg.mecyl_low', 0),
      parameter,
      'report_json.other.avg.mecyl_low',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.avg.mecyl_low')),
  },
  {
    order: 14,
    label: 'Average AECC Cons(Ltrs/day))',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.avg.aecc', 0),
      parameter,
      'report_json.other.avg.aecc',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.avg.aecc')),
  },
  {
    order: 15,
    label: 'Average Production',
    value: '',
    header: 'Average Production',
  },
  {
    order: 16,
    label: 'Average FW prod (MT/Day)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.avg.fw_prod', 0),
      parameter,
      'report_json.other.avg.fw_prod',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.avg.fw_prod')),
  },
  {
    order: 17,
    label: 'Weather',
    value: '',
    header: 'Weather',
  },
  {
    order: 18,
    label: 'Good Weather days',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.weather.gd', 0),
      parameter,
      'report_json.other.weather.gd',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.weather.gd')),
  },
  {
    order: 19,
    label: 'Average Speed in good WX (knots)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.weather.gd_sp', 0),
      parameter,
      'report_json.other.weather.gd_sp',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.weather.gd_sp')),
  },
  {
    order: 20,
    label: 'Number of Bad Weather Days',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.weather.bd', 0),
      parameter,
      'report_json.other.weather.bd',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.weather.bd')),
  },
  {
    order: 21,
    label: 'Average Speed in bad WX (Knots)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.weather.bd_sp', 0),
      parameter,
      'report_json.other.weather.bd_sp',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.weather.bd_sp')),
  },
  {
    order: 22,
    label: 'Others',
    value: '',
    header: 'Others',
  },
  {
    order: 23,
    label: 'Cash Received (US$)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.bunker.receive.cash', 0),
      parameter,
      'report_json.bunker.receive.cash',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.bunker.receive.cash')),
  },
  {
    order: 24,
    label: 'Cash ROB (US$)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.rob.cash', 0),
      parameter,
      'report_json.rob.cash',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.rob.cash')),
  },
  {
    order: 25,
    label: 'Lo Sample Details',
    value: formatValue(_.get(voyageReportData, 'report_json.other.lo')),
  },
  {
    order: 26,
    label: 'Port Rotation',
    value: formatValue(_.get(voyageReportData, 'report_json.other.port_rn'), 'Nill'),
  },
  {
    order: 27,
    label: 'Recap of Stores/Spares',
    value: formatValue(_.get(voyageReportData, 'report_json.other.recap'), 'Nill'),
  },
  {
    order: 28,
    label: 'Doctors Visit Details',
    value: formatValue(_.get(voyageReportData, 'report_json.other.dr'), 'Nill'),
  },
  {
    order: 29,
    label: 'Result of Stowaway Search',
    value: formatValue(_.get(voyageReportData, 'report_json.other.stw'), 'Nill'),
  },
  {
    order: 30,
    label: 'Total Stoppage if any',
    value: formatNumber(_.get(voyageReportData, 'report_json.other.stop')),
  },
  {
    order: 31,
    label: 'Arrival Draft F/A (M)',
    text_color_1: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.a_draft_f', 0),
      parameter,
      'report_json.other.a_draft_f',
    ),
    text_color_2: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.a_draft_a', 0),
      parameter,
      'report_json.other.a_draft_a',
    ),
    value_1: `${formatNumber(_.get(voyageReportData, 'report_json.other.a_draft_f'))}`,
    value_2: `${formatNumber(_.get(voyageReportData, 'report_json.other.a_draft_a'))}`,
    value: `${formatNumber(_.get(voyageReportData, 'report_json.other.a_draft_f'))}/${formatNumber(
      _.get(voyageReportData, 'report_json.other.a_draft_a'),
    )}`,
  },
  {
    order: 32,
    label: 'Departure Draft F/A (M)',
    text_color_1: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.d_draft_f', 0),
      parameter,
      'report_json.other.d_draft_f',
    ),
    text_color_2: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.d_draft_a', 0),
      parameter,
      'report_json.other.d_draft_a',
    ),
    value_1: `${formatNumber(_.get(voyageReportData, 'report_json.other.d_draft_f'))}`,
    value_2: `${formatNumber(_.get(voyageReportData, 'report_json.other.d_draft_a'))}`,
    value: `${formatNumber(_.get(voyageReportData, 'report_json.other.d_draft_f'))}/${formatNumber(
      _.get(voyageReportData, 'report_json.other.d_draft_a'),
    )}`,
  },
  {
    order: 33,
    label: 'Confirm Compliance  with Applicable Loadline for entire Passage',
    value: formatValue(_.get(voyageReportData, 'report_json.other.compliance')),
  },
  {
    order: 34,
    label: 'Vessel Condition',
    value: formatValue(_.get(voyageReportData, 'report_json.other.cond', Dash)),
  },
  {
    order: 35,
    label: 'Displacement (MT)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.dispm', 0),
      parameter,
      'report_json.other.dispm',
    ),
    value: formatNumber(_.get(voyageReportData, 'report_json.other.dispm')),
  },
  {
    order: 36,
    label: 'Shippers Declaration of Cargo Received',
    value: formatValue(_.get(voyageReportData, 'report_json.other.declaration')),
  },
  {
    order: 37,
    label: 'AMVER Report has been send?',
    value: formatValue(_.get(voyageReportData, 'report_json.other.is_amver', 'false')),
  },
  {
    order: 38,
    label: 'Misc',
    value: formatValue(_.get(voyageReportData, 'report_json.other.misc', Dash)),
  },
  {
    order: 39,
    label: 'Remarks',
    value: formatValue(_.get(voyageReportData, 'report_json.other.remark', Dash)),
  },
  {
    order: 40,
    label: 'Off Hire (Hrs:Mins)',
    text_color: getHighlightColor(
      _.get(voyageReportData, 'report_json.other.offhr', 0),
      parameter,
      'report_json.other.offhr',
    ),
    value: `${_.get(voyageReportData, 'report_json.other.offhr', '00')}:${_.get(
      voyageReportData,
      'report_json.other.offmin',
      '00',
    )}`,
  },
  {
    order: 41,
    label: 'Off Hire Reason',
    value: formatValue(_.get(voyageReportData, 'report_json.other.reason', Dash)),
  },
];

export default {
  GeneralInfoData,
  ConsumptionData,
  EmissionData,
  OtherData,
  PassengerData,
  CargoData,
  DistanceData,
  BunkeringData,
  RemainingBoardData,
};
