export const TechnicalReportCompareMapperData = {
  position: [
    { name: 'general', label: 'general information' },
    { name: 'stratumfive-speed', label: 'STRATUMFIVE FEED' },
    {
      name: 'consumption',
      label: 'consumption since last report',
    },
    {
      name: 'consumption_rescue_operations',
      label: 'consumption since last report (rescue operations)',
      tooltip: 'Consumption Since Last Report during Voyage adjustment period (Rescue Operations)'
    },
    {
      name: 'consumption_ice_conditions',
      label: 'consumption since last report (ice conditions)',
      tooltip: 'Consumption Since Last Report during Voyage adjustment period (Ice Conditions)'
    },
    {
      name: 'consumption_cargo_pumps',
      label: 'CONSUMPTION SINCE LAST REPORT FOR CARGO DISCHARGING USING ELECTRICAL/ELECTRO-HYDRAULIC CARGO PUMPS',
      tooltip: 'Ensure calculation is made as shown in ‘Daily Fuel Consumption Record’ Form S 10.40 F'
    },
    {
      name: 'consumption_refrigerated_containers',
      label: 'CONSUMPTION SINCE LAST REPORT FOR REFRIGERATED CONTAINERS',
      tooltip: 'Ensure calculation is made as shown in ‘Daily Fuel Consumption Record’ Form S 10.40 F'
    },
    {
      name: 'consumption_cargo_cooling',
      label: 'CONSUMPTION SINCE LAST REPORT FOR CARGO COOLING/RELIQUEFICATION',
      tooltip: 'Ensure calculation is made as shown in ‘Daily Fuel Consumption Record’ Form S 10.40 F'
    },
    { name: 'total-consumption', label: 'total consumption since last report' },
    { name: 'weather', label: 'weather' },
    { name: 'other', label: 'others' },   
  ],
  voyage: [
    { name: 'general', label: 'general information' },
    { name: 'stratumfive', label: 'STRATUMFIVE FEED' },
    {
      name: 'consumption',
      label: 'CONSUMPTION SINCE LAST ARRIVAL/DEPARTURE REPORT (MT)',
    },
    {
      name: 'consumption_rescue_operations',
      label: 'CONSUMPTION SINCE LAST ARRIVAL/DEPARTURE REPORT (MT) (RESCUE OPERATIONS)',
      tooltip: 'Consumption Since Last Report during Voyage adjustment period (Rescue Operations)'
    },
    {
      name: 'consumption_ice_conditions',
      label: 'CONSUMPTION SINCE LAST ARRIVAL/DEPARTURE REPORT (MT) (ICE CONDITIONS)',
      tooltip: 'Consumption Since Last Report during Voyage adjustment period (Ice Conditions)'
    },
    {
      name: 'consumption_cargo_pumps',
      label: 'CONSUMPTION SINCE LAST REPORT FOR CARGO DISCHARGING USING ELECTRICAL/ELECTRO-HYDRAULIC CARGO PUMPS',
      tooltip: 'Ensure calculation is made as shown in ‘Daily Fuel Consumption Record’ Form S 10.40 F'
    },
    {
      name: 'consumption_refrigerated_containers',
      label: 'CONSUMPTION SINCE LAST REPORT FOR REFRIGERATED CONTAINERS',
      tooltip: 'Ensure calculation is made as shown in ‘Daily Fuel Consumption Record’ Form S 10.40 F'
    },
    {
      name: 'consumption_cargo_cooling',
      label: 'CONSUMPTION SINCE LAST REPORT FOR CARGO COOLING/RELIQUEFICATION',
      tooltip: 'Ensure calculation is made as shown in ‘Daily Fuel Consumption Record’ Form S 10.40 F'
    },
    {
      name: 'fuel_sulphur_content',
      label: 'FUEL SULPHUR CONTENT',   
      tooltip: '1) For ships using EGCS, Sulphur content entered should be the equivalent value after scrubbing. 2) Sulphur content in BDN to be used' 
    },
    { name: 'rob', label: 'REMAINING ON BOARD' },
    { name: 'bunkering', label: 'Bunkering (MT)' },
    { 
      name: 'cargo', 
      label: 'CARGO', 
      tooltip: '1) B/L quantities to be used for cargo loaded/discharged. 2) If ALL cargo is discharged in this port, then the amount entered in the cargo discharged field must be exactly the same as the cargo on board in the previous Departure report.'
    },
    { name: 'passenger', label: 'PASSENGERS (PASSENGER/RO-PAX SHIPS ONLY)' },
    { name: 'emission', label: 'EMISSIONS (MT)' },
    { name: 'other', label: 'OTHERS' },
  ],
  performance: [
    { name: 'general', label: 'general information' },
    {
      name: 'consumption',
      label: 'CONSUMPTION/PRODUCTION',
    },
    {
      name: 'exhaust',
      label: 'EXHAUST TC TEMPERATURE (Deg C)',
    },
    {
      name: 'acinlet',
      label: 'AIR COOLER INLET TEMPERATURE (Deg C)',
    },
    {
      name: 'compandpeak',
      label: 'COMPRESSION/PEAK PRESSURE (Bar)',
    },
    {
      name: 'weather',
      label: 'WEATHER INFORMATION',
    },
    {
      name: 'remark',
      label: 'REMARKS',
    },
  ],
  quarterly: [
    { name: 'general', label: 'GENERAL INFORMATION' },
    { name: 'ballast', label: 'BALLAST AVERAGE' },
    { name: 'loaded', label: 'LOADED AVERAGE' },
    { name: 'consumption', label: 'CONSUMPTION AVERAGE' },
    { name: 'steaming', label: 'STEAMING TIME' },
    { name: 'stoppage', label: 'MAIN ENGINE STOPPAGE' },
    { name: 'remark', label: 'REMARKS' },
  ],
  monthly: [
    { name: 'general', label: 'GENERAL INFORMATION' },
    { name: 'aepower', label: 'AUX. ENGINE POWER GENERATION (kWh)' },
    { name: 'consumption', label: 'FUEL OIL CONSUMPTION (MT)' },
    { name: 'rob', label: 'FUEL OIL R.O.B. (MT)' },
    { name: 'emission', label: 'EMISSION' },
    { name: 'lubeoil', label: 'LUBE OIL' },
    { name: 'expense', label: 'EXPENSES' },
    { name: 'remark', label: 'REMARKS' },
  ],
  marpol: [
    { name: 'general', label: 'GENERAL INFORMATION' },
    {
      name: 'residues',
      label: 'SLUDGE AND OTHER OIL RESIDUES (AS RECORDED IN OIL RECORD BOOK)(M3)',
    },
    { name: 'bilgeoilywater', label: 'BILGE / OILY WATER (AS RECORDED IN OIL RECORDED BOOK)(M3)' },
    { name: 'garbage', label: 'GARBAGE - GENERAL (M3)' },
    { name: 'bottledWater', label: 'PLASTIC BOTTLED DRINKING WATER (LTRS)' },
    { name: 'refrigerants', label: 'REFRIGERANTS (KG)' },
    { name: 'marpolannexslopoil', label: 'MARPOL ANNEX 1 SLOP OIL (TANKERS ONLY) (LTRS)' },
    {
      name: 'graywater',
      label:
        'GRAYWATER (Domestic fresh water consumption in month, less estimated consumption in toilets for FW flushing systems. Toilet flushing consumption- for non-vaccum toilets-may be estimated at 1/3rd domestic FW consumption)(MT)',
    },
    { name: 'oilywaterseparator', label: 'SPARES FOR OILY WATER SEPARATOR (SET)' },
    { name: 'bilgepumpspares', label: 'BILGE PUMP SPARES' },
    { name: 'oilcontentmonitor', label: 'SPARES FOR OIL CONTENT MONITOR' },
    { name: 'incinerator', label: 'SPARES FOR INCINERATOR' },
    { name: 'sludgepump', label: 'SPARES FOR SLUDGE PUMP' },
    { name: 'sewagetreatmentplant', label: 'SEWAGE TREATMENT PLANT' },
  ],
  'ninety-six-hours':[
    { name: 'general', label: 'GENERAL INFORMATION' },
    { name: 'oil_record_book', label: '1. OIL RECORD BOOK' },
    { name: 'garbage_disposal', label: '2. GARBAGE DISPOSAL' },
    { name: 'retention', label: '3. RENENTION' },
    { name: 'equipment', label: '4. EQUIPMENT' },
    { name: 'us_call_certificates_documents', label: '5. CERTIFICATED / DOCUMENTS FOR US CALL' },
  ]
};
