import moment from 'moment';

export const vesselListTabKeys = {
  ACTIVE_VESSEL: 'active-vessels',
  NEW_TAKEOVERS: 'new-takeovers',
  HANDED_OVER: 'handed-over',
  ARCHIVED: 'archived',
};

export const BUSINESS = 'Business';
export const FLEET_PERSONNEL = 'Fleet Personnel';
export const ACCOUNTS = 'Accounts';
export const INSURANCE = 'Insurance';
export const TECH_GROUP = 'Tech Group';
export const MASTER = 'MASTER';
export const CHIEF_ENGINEER = 'CHIEF ENGINEER';
export const OWNERSHIP_PENDING = 'ownership_pending';
export const OWNERSHIP = 'ownership';

export const FIRST_APPROVERS_GROUPS = [BUSINESS, FLEET_PERSONNEL, ACCOUNTS, INSURANCE, TECH_GROUP];
export const FINAL_APPROVER = 'Executive Director';
export const vesselStatuses = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  HANDED_OVER: 'handed_over',
  ARCHIVED: 'archived',
};

export const approvalStatuses = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
};

export const VESSEL_OWNERSHIP_STATUS = {
  PENDING: 'pending',
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  REJECTED: 'rejected',
};

export const OWNERSHIP_CHANGE_REQUEST_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
};

export const CUSTOM_USER_GROUPS = ['VesselManager', 'Superintendent'];

export const REQUIRED_FIELDS = [
  'imo_number',
  'vessel_hull_number',
  'life_boat_capacity',
  'length_oa',
  'length_bp',
  'depth',
  'breadth_extreme',
  'summer_draft',
  'summer_dwt',
  'international_grt',
  'international_nrt',
  'service_speed',
  'h_m_underwriter_id',
  'p_i_club_id',
  'ihm_provider_id',
  'shipyard_text',
  'vessel_type_id',
  'owner_id',
  'misc_engine_id',
  'dwt',
  'bhp',
  'misc_operator_id',
  'misc_manager_id',
  'misc_currency_id',
  'is_manning_manager',
  'misc_classification_id',
  'misc_classification_society_id',
  'misc_classification_euets_verifier_id',
  'misc_qi_id',
  'misc_osro_id',
  'misc_salvage_id',
  'misc_media_response_id',
  'misc_management_type_id',
  'misc_other_contacts_id',
  'us_visa_required',
  'has_portage_bill',
  'is_engine_consume_lng',
];

export const REQUIRED_OWNERSHIP_FIELDS = [
  'vessel_account_code_new',
  'registered_owner_id',
  'vessel_short_code',
  'vessel_tel_fac_code',
  'fleet_staff[0].tech_group',
  'name',
  'expected_owner_start_date',
  'flag_isps_id',
  'flags[0].call_sign',
];

export const MISSING_FIELD_DESCRIPTIONS = {
  name: 'Please enter Vessel Name',
  imo_number: 'Please enter IMO Number',
  vessel_hull_number: 'Please enter Vessel Hull Number',
  expected_owner_start_date: 'Please enter Expected Date of Takeover',
  shipyard_text: 'Please select Shipyard',
  techgroup: 'Please enter Tech Group',
  owner_id: 'Please enter Owners',
  vessel_type_id: 'Please enter Vessel Type',
  year_built_date_of_delivery: 'Please select Year Built / Date of Delivery',
  life_boat_capacity: 'Please enter Life Boat Capacity',
  length_oa: 'Please enter Length O.A.',
  length_bp: 'Please enter Length B.P.',
  depth: 'Please enter Depth',
  breadth_extreme: 'Please enter Breadth (Extreme)',
  summer_draft: 'Please enter Summer Draft',
  summer_dwt: 'Please enter Summer DWT',
  international_grt: 'Please enter International GRT',
  international_nrt: 'Please enter International NRT',
  service_speed: 'Please enter Service Speed',
  vessel_short_code: 'Please enter Vessel Short Code',
  vessel_tel_fac_code: 'Please enter Vessel Tel FAC Code',
  vessel_account_code_new: 'Please enter Vessel Account Code New',
  emails: 'Please enter Email',
  phones: 'Please enter Phone',
  h_m_underwriter_id: 'Please select H & M Underwriter',
  p_i_club_id: 'Please select P & I Club',
  ihm_provider_id: 'Please select IHM Provider',
  misc_engine_id: 'Please enter Engine',
  dwt: 'Please enter DWT',
  bhp: 'Please enter Power(kW)',
  registered_owner_id: 'Please select Registered Owner',
  misc_operator_id: 'Please select Operator',
  misc_manager_id: 'Please select Manager',
  misc_currency_id: 'Please select Currency',
  is_manning_manager: 'Please select Is Manning Manager',
  flag_isps_id: 'Please select Flag (ISPS)',
  misc_classification_id: 'Please select Classification',
  misc_classification_society_id: 'Please select Classification Society',
  misc_classification_euets_verifier_id: 'Please select EU Verifier',
  misc_qi_id: 'Please select QI',
  misc_osro_id: 'Please select OSRO',
  misc_salvage_id: 'Please select Salvage',
  misc_media_response_id: 'Please select Media Response',
  misc_management_type_id: 'Please select Management Type',
  misc_other_contacts_id: 'Please select Other Contacts',
  us_visa_required: 'Please select US Visa Required',
  has_portage_bill: 'Please select Portage bill module',
  'flags[0].call_sign': 'Please enter Call sign',
  is_engine_consume_lng: 'Please select Engine Consumes LNG?',
};

export const LOCAL_STORAGE_FIELDS = {
  masterKey: 'vessel-table-details',
  tablePageSize: 'vessel-table-page-size',
  tableSelectedColumns: 'vessel-table-selected-columns',
  tablePageIndex: 'vessel-table-page-index',
  tablePageSort: 'vessel-table-page-sort',
  advancedSearchParams: 'vessel-search-criteria',
  itineraryKey: 'itinerary-table-details',
  itineraryDataKey: 'itinerary-data',
  contingencyContactDataKey: 'contingency-contact-data',
  contingencyPositionDataKey: 'contingency-position-data',
  showFutureItineraryKey: 'show-future-itinerary',
  tableSortKey: 'table-sort-key',
};

export const REQUIREMENT_OPTIONS = [
  {
    id: 'maker',
    value: 'Maker',
  },
  {
    id: 'charter',
    value: 'Charter',
  },
  {
    id: 'owner',
    value: 'Owner',
  },
  {
    id: 'manager',
    value: 'Manager',
  },
];

export const minValue = -999;

export const maxValue = 1000000000000;

export const errorMessageList = {
  invalidRangeMessage: `value must be between ${minValue} to ${maxValue - 1}`,
  invalidMessge: 'Minimum value must be less than maximum value',
  invalidEqualityMessage: 'Minimum and maximum value must not be equal',
  invalidBlankMessage: 'value must not be null',
};

export const mrvVoyageReportSelectedIds = [
  'voyage_no',
  'port_departure',
  'eu_port_departure',
  'port_arrival',
  'eu_port_arrival',
  'leg_date',
  'departure_hour',
  'arrival_hour',
  'time_spent_sea',
  'distance',
  'cargo_carried',
  'voyage_type',
  'hfo_cons',
  'hfo_emission_factor',
  'hfo_co2_emitted',
  'lfo_cons',
  'lfo_emission_factor',
  'lfo_co2_emitted',
  'mgo_cons',
  'mgo_emission_factor',
  'mgo_co2_emitted',
  'lng_cons',
  'lng_emission_factor',
  'lng_co2_emitted',
  'total_co2_emitted',
  'co2_emitted_voyage_between_eu_port',
  'co2_emitted_voyage_from_eu_port',
  'co2_emitted_voyage_to_eu_port',
  'transport_work',
  'fuel_cons_per_distance',
  'fuel_cons_per_transport_work',
  'co2_emitted_per_distance',
  'co2_emitted_per_transport_work',
  'boiler_hfo_cons',
  'boiler_mgo_cons',
  'boiler_fuel3_cons',
  'lpg_propane_cons',
  'lpg_propane_emission_factor',
  'lpg_propane_co2_emitted',
  'lpg_butane_cons',
  'lpg_butane_emission_factor',
  'lpg_butane_co2_emitted',
  'methanol_cons',
  'methanol_emission_factor',
  'methanol_co2_emitted',
  'ethanol_cons',
  'ethanol_emission_factor',
  'ethanol_co2_emitted',
];

export const etsVoyageReportSelectedIds = [
  'voyage_no',
  'port_departure',
  'port_arrival',
  'leg_date',
  'cargo_carried',
  'total_fuel_consumed',
  'total_co2_emitted',
  'total_energy_consumed',
  'compliance_balance',
  'class_name',
  'eua',
  'estimate_fuel_eu_penalty',
  'verification_status',
  'action',
];

export const etsPendingVoyageReportSelectedIds = [
  'voyage_no',
  'port_departure',
  'port_arrival',
  'leg_date',
  'cargo_carried',
  'total_fuel_consumed',
  'total_co2_emitted',
  'total_energy_consumed',
  'compliance_balance',
  'class_name',
  'eua',
  'estimate_fuel_eu_penalty',
  'verification_status',
  'action',
];

export const mrvPortReportSelectedIds = [
  'voyage_no',
  'port',
  'eu_port_departure_date',
  'eu_port_arrival_date',
  'arrival_hour',
  'departure_hour',
  'time_spent_land',
  'distance',
  'hfo_cons',
  'hfo_emission_factor',
  'hfo_co2_emitted',
  'lfo_cons',
  'lfo_emission_factor',
  'lfo_co2_emitted',
  'mgo_cons',
  'mgo_emission_factor',
  'mgo_co2_emitted',
  'lng_cons',
  'lng_emission_factor',
  'lng_co2_emitted',
  'total_co2_emitted',
  'boiler_hfo_cons',
  'boiler_mgo_cons',
  'boiler_fuel3_cons',
  'lpg_propane_cons',
  'lpg_propane_emission_factor',
  'lpg_propane_co2_emitted',
  'lpg_butane_cons',
  'lpg_butane_emission_factor',
  'lpg_butane_co2_emitted',
  'methanol_cons',
  'methanol_emission_factor',
  'methanol_co2_emitted',
  'ethanol_cons',
  'ethanol_emission_factor',
  'ethanol_co2_emitted',
];
export const etsPortReportSelectedIds = [
  'voyage_no',
  'port',
  'eu_port_departure_date',
  'eu_port_arrival_date',
  'total_fuel_consumed',
  'total_co2_emitted',
  'total_energy_consumed',
  'compliance_balance',
  'class_name',
  'eua',
  'estimate_fuel_eu_penalty',
  'verification_status',
  'action',
];

export const wasteStreamSelectedIds = [
  'vessel_name',
  'vessel_type',
  'dwt',
  'time_spent_underway',
  'time_spent_port',
  'distance_travelled',
  'voyage_number',
  'laden_voyage_percentage',
  'cargo_carried',
  'transport_work',
  'fuel_sea_cons',
  'fuel_port_cons',
  'co2_sea_emitted',
  'co2_port_emiited',
  'co2_emitted_per_unit_distance',
  'co2_emitted_per_unit_transport_work',
  'sox_emitted',
  'avg_shulphur_fuel_cons',
  'nox_emitted_me',
  'nox_emitted_dg',
  'bilge_generated',
  'sludge',
  'garbage_generation',
  'cargo_discharged_sea',
  'food_waste_dsicharged_sea',
  'refrigerant_emitted',
  'qtx_annex_discharge_sea',
];

export const imoSelectedIds = [
  'vessel_name',
  'vessel_type',
  'ethanol',
  'auxiliary_engine',
  'cf',
  'diesel_gas',
  'distance_travelled',
  'dwt',
  'eedi',
  'gross_tonnage',
  'hfo',
  'ice_class',
  'imo_number',
  'lfo',
  'lng',
  'lpg_butane',
  'lpg_propane',
  'main_propulsion_pw',
  'methanol',
  'method_fuel_oil_cons',
  'nt',
  'other',
  'underway_hour',
];

export const MONTHS = Array.apply(0, Array(12)).map((_, i) => ({
  id: i + 1,
  value: moment().month(i).format('MMMM'),
}));

export const FUNDTYPES = [
  { value: 'Opex', id: 'Opex' },
  { value: 'Opex + Predel + Upgrade', id: 'Opex + Predel + Upgrade' },
  { value: 'Dock', id: 'Dock' },
  { value: 'Opex + Predel', id: 'Opex + Predel' },
  { value: 'Opex + Dock', id: 'Opex + Dock' },
  { value: 'Opex + Non Budget', id: 'Opex + Non-Budget' },
  { value: 'Non-Budget', id: 'Non-Budget' },
  { value: 'Predel', id: 'Predel' },
  { value: 'Predel + Upgrade', id: 'Predel + Upgrade' },
  { value: 'OPEX - Recon', id: 'Opex - Recon' },
  { value: 'Security Deposit', id: 'Security Deposit' },
  { value: 'Projects', id: 'Projects' },
  { value: 'EU-ETS', id: 'EU-ETS' },
];

export const CASHCALLSUBJECT = [
  { value: 'Fund Request', id: 'default' },
  { value: 'Non-budget Fund Request', id: 'nonbudget' },
  { value: 'Pre-delivery Fund Request', id: 'predelivery' },
  { value: 'Drydock Fund Request', id: 'drydock' },
  { value: 'Charterers Fund Request', id: 'charterer' },
  { value: 'Security Fund Request', id: 'security' },
  { value: 'Projects Fund Request', id: 'project' },
  { value: 'EU-ETS Fund Request', id: 'eu-ets' },
];

export const EORB_STATUSES = {
  ACTIVE: 'active',
  PENDING: 'pending',
  NOT_INSTALLED: 'not_installed',
};

export const EORB_PRODUCT_NAME = 'eorb';

export const DROPDOWN_EORB_STATUS_MAPPING = {
  Open: 'active',
  'Pending Installation': 'pending',
  'Not Installed': 'not_installed',
};

export const PAY_CALCULATION_DROPDOWN = {
  standard: 'Standard Wages for 30 days',
  actual: 'Wages basis actual working days in the month',
  day: 'Wages on per day basis',
};

export const EU_BANNER_CHECK = {
  BANNER_DATE: '2025-01-01',
  BANNER_COUNTRY: ['Norway','Iceland','Liechtenstein']
}
