import { Dash } from './utils.js';

export const MaximumContinuousRatingEngineData = ({
  maximum_continuous_rating_kw,
  maximum_continuous_rating_rpm,
}) => [
  {
    label: 'MCR KW',
    value: isNaN(parseFloat(maximum_continuous_rating_kw))
      ? Dash
      : parseFloat(maximum_continuous_rating_kw),
  },
  {
    label: 'MCR RPM',
    value: isNaN(parseFloat(maximum_continuous_rating_rpm))
      ? Dash
      : parseFloat(maximum_continuous_rating_rpm),
  },
];
