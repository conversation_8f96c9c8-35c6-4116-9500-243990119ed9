import React, { createContext, useEffect, useMemo, useState } from 'react';
import { onLoadPage } from '../controller/itinerary-controller';
import _ from 'lodash';

export const AdminContext = createContext({});

export const Provider = AdminContext.Provider;

const AdminContextProvider = (props) => {
  const providerValue = useMemo(() => {
    return {
      ga4EventTrigger: props.ga4EventTrigger,
      roleConfig: props.roleConfig,
    };
  }, [props.ga4EventTrigger, props.roleConfig]);

  return <Provider value={providerValue}>{props.children} </Provider>;
};

export default AdminContextProvider;
