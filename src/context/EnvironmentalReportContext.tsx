import React, { createContext, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import vesselService from '../service/vessel-service';
import { formatDate, formatValue } from '../util/view-utils';
import moment from 'moment';
import getURLParams from '../util/getURLParams';

export const EnvironmentalReportContext = createContext({});

export const Provider = EnvironmentalReportContext.Provider;

const EnvironmentalReportContextProvider = (props) => {
  const history = useHistory();
  const ownershipId = getURLParams('vessel_ownership_id', history.location.search);
  const report_date = getURLParams('report_date', history.location.search)?.toString().split(',');
  const datasourceParam = getURLParams('datasource', history.location.search);
  const [vesselList, setVesselList] = useState([]);
  const [activeVesselList, setActiveVesselList] = useState([]);
  const [excelData, setExcelData] = useState({});
  const [vendorDataSourceOptions, setVendorDataSourceOptions] = useState([]);
  let defaultStartDate = ownershipId
    ? moment().startOf('year').format('YYYY-MM-DD')
    : moment().subtract(1, 'months').format('YYYY-MM-DD');
  const [filterData, setFilterData] = useState({
    startDate: !_.isEmpty(report_date) ? report_date[0] : defaultStartDate,
    endDate: !_.isEmpty(report_date) ? report_date[1] : formatDate(new Date(), 'YYYY-MM-DD', ''),
    vessel: [],
    eu: false,
    dataSource: [],
  });
  const [bannerInfo, setBannerInfo] = useState(false);
  const options = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  };
  // Cache vendor datasource configs
  const [cachedConfigs, setCachedConfigs] = useState({});

  useEffect(() => {
    const fetchVendorDataSourceOptions = async () => {
      const startDate = new Date(filterData.startDate);
      const comparisonYear = new Date('2025-01-01'); // Start of the year 2025
      const in2025orLater = startDate >= comparisonYear;

      if (ownershipId && in2025orLater) {
        try {
          // Check cache first
          const cacheKey = `${ownershipId}_${filterData.startDate}`;
          if (cachedConfigs[cacheKey]) {
            setVendorDataSourceOptions(cachedConfigs[cacheKey]);
            return;
          }

          const { data: vendor_data_source_configs } =
            await vesselService.getReportEnabledDatasourceConfigs(ownershipId);
          if (vendor_data_source_configs.length > 0) {
            const vendorDataSources = vendor_data_source_configs.map((item) => {
              const date = new Date(item.last_sync_at);
              const formattedDate = date.toLocaleString('en-GB', options).replace(',', '');
              return {
                value: item.datasource_code,
                name: item.datasource_code,
                isMainSource: item.is_main_source,
                lastSyncAt: formattedDate,
              };
            });

            const newOptions = [
              ...vendorDataSources,
              {
                value: 'PARIS',
                label: 'PARIS',
                isMainSource: false,
              },
            ];

            // Cache the results
            setCachedConfigs((prev) => ({
              ...prev,
              [cacheKey]: newOptions,
            }));
            setVendorDataSourceOptions(newOptions);
            setFilterData((prev) => ({
              ...prev,
              dataSource: newOptions.filter(
                (item) => item.value === datasourceParam || item.isMainSource === true,
              ),
            }));
          }
        } catch (error) {
          console.error(error);
        }
      } else {
        setVendorDataSourceOptions([]);
      }
    };

    fetchVendorDataSourceOptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ownershipId, filterData.startDate]);
  const providerValue = useMemo(() => {
    return {
      vesselList: vesselList,
      filterData: filterData,
      setFilterData: setFilterData,
      excelData: excelData,
      setExcelData: setExcelData,
      userEmail: props.userEmail,
      ga4EventTrigger: props.ga4EventTrigger,
      activeVesselList: activeVesselList,
      roleConfig: props.roleConfig,
      bannerInfo: bannerInfo,
      setBannerInfo: setBannerInfo,
      vendorDataSourceOptions: vendorDataSourceOptions,
      setVendorDataSourceOptions: setVendorDataSourceOptions,
    };
  }, [
    vesselList,
    filterData,
    excelData,
    props.userEmail,
    props.ga4EventTrigger,
    activeVesselList,
    props.roleConfig,
    bannerInfo,
    vendorDataSourceOptions,
  ]);

  useEffect(() => {
    (async () => {
      try {
        const response = await vesselService.getAllVessels();
        const data = response.data.results.map((vessel) => {
          return {
            id: vessel.id,
            value: formatValue(vessel.name) + `(${vessel.id})`,
            name: formatValue(vessel.name),
          };
        });
        const vesselData = data.find((i) => i.id === Number(ownershipId));
        if (vesselData) {
          setFilterData({ ...filterData, vessel: [vesselData] });
        }
        setVesselList(data);
      } catch (error) {
        console.error(error);
      }

      try {
        const response = await vesselService.getAllVessels('f=name&f=id', true);
        const data = response.data.results.map((vessel) => {
          return {
            id: vessel.id,
            value: formatValue(vessel.name) + `(${vessel.id})`,
            name: formatValue(vessel.name),
          };
        });
        setActiveVesselList(data);
      } catch (error) {
        console.error(error);
      }
    })();
  }, []);
  return <Provider value={providerValue}>{props.children}</Provider>;
};

export default EnvironmentalReportContextProvider;
