import React, { createContext, useEffect, useMemo, useState } from 'react';
import vesselService from '../service/vessel-service';
import { formatValue } from '../util/view-utils';

export const FinancialReportContext = createContext({});

export const Provider = FinancialReportContext.Provider;

const FinancialReportContextProvider = (props) => {
  const [vesselList, setVesselList] = useState([]);
  const [dropdownData, setDropdownData] = useState([]);
  const [financialReportTypes, setFinancialReportTypes] = useState([]);

  const providerValue = useMemo(() => {
    return {
      vesselList: vesselList,
      financialReportTypes: financialReportTypes,
      dropdownData: dropdownData,
      userEmail: props.userEmail,
      roleConfig: props.roleConfig,
      ga4EventTrigger: props.ga4EventTrigger,
    };
  }, [
    vesselList,
    financialReportTypes,
    dropdownData,
    props.userEmail,
    props.roleConfig,
    props.ga4EventTrigger,
  ]);

  useEffect(() => {
    (async () => {
      try {
        const response = await vesselService.getAllVessels('f=name&f=id&f=owner.id&flatten=true');
        const data = response.data.results.map((vessel) => {
          return {
            id: vessel.id,
            value: formatValue(vessel.name) + `(${vessel.id})`,
            ownerId: vessel.owner?.id,
          };
        });
        setVesselList(data);
      } catch (error) {
        console.error(error);
      }

      try {
        const responseFinancialType = await vesselService.getFinancialReportTypes();
        const data = responseFinancialType.data.results.map((type) => {
          return { id: type.id, value: formatValue(type.name), type: type.type };
        });
        setFinancialReportTypes(data);
      } catch (error) {
        console.error(error);
      }
      try {
        const dropdownData = await vesselService.getCashCallDropDownData();
        setDropdownData(dropdownData);
      } catch (error) {
        console.error(error);
      }
    })();
  }, []);
  return <Provider value={providerValue}>{props.children}</Provider>;
};

export default FinancialReportContextProvider;
