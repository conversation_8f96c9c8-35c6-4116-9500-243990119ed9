import React, { createContext, useEffect, useMemo, useState } from 'react';
import vesselService from '../service/vessel-service';
import { formatValue } from '../util/view-utils';

export const DetailContext = createContext({});

export const Provider = DetailContext.Provider;

const DetailContextProvider = (props) => {
  const [showFutureItinerary, setShowFutureItinerary] = useState(false);
  const [vesselName, setVesselName] = useState('');
  const [ownershipId, setOwnershipId] = useState('');
  const [vesselEmail, setVesselEmail] = useState('');
  const [excelColumns, setExcelColumns] = useState([]);
  const [isCurrentOwner, setIsCurrentOwner] = useState('');
  const [vesselList, setVesselList] = useState([]);
  const [manualTypes, setManualTypes] = useState([]);
  const [isAccessDenied, setIsAccessDenied] = useState(false);
  const [invalidVessel, setInvalidVessel] = useState(false);
  const [error, setError] = useState(null);
  const handleError = (response, message = 'Oops, something went wrong. Please try again.') => {
    if (response?.status === 403) {
      setIsAccessDenied(true);
    } else if (response?.status === 404) {
      setInvalidVessel(true);
    } else {
      setError(message);
    }
  };

  const providerValue = useMemo(() => {
    return {
      showFutureItinerary: showFutureItinerary,
      setShowFutureItinerary: setShowFutureItinerary,
      vesselName: vesselName,
      setVesselName: setVesselName,
      vesselEmail: vesselEmail,
      setVesselEmail: setVesselEmail,
      setOwnershipId: setOwnershipId,
      ownershipId: ownershipId,
      ga4EventTrigger: props.ga4EventTrigger,
      excelColumns: excelColumns,
      setExcelColumns: setExcelColumns,
      roleConfig: props.roleConfig,
      isCurrentOwner: isCurrentOwner,
      setIsCurrentOwner: setIsCurrentOwner,
      userEmail: props.userEmail,
      vesselList: vesselList,
      manualTypes: manualTypes,
      isAccessDenied: isAccessDenied,
      invalidVessel: invalidVessel,
      handleError: handleError,
      setInvalidVessel: setInvalidVessel,
      error: error,
      setError: setError,
      isOwner: props.isOwner,
    };
  }, [
    showFutureItinerary,
    vesselName,
    vesselEmail,
    ownershipId,
    excelColumns,
    props.ga4EventTrigger,
    props.roleConfig,
    isCurrentOwner,
    props.userEmail,
    vesselList,
    manualTypes,
    isAccessDenied,
    invalidVessel,
    error,
    
  ]);

  useEffect(() => {
    (async () => {
      try {
        const vesselResponse = await vesselService.getAllVessels(
          'f=name&f=id&f=owner.id&flatten=true',
        );
        const data = vesselResponse.data.results.map((vessel) => {
          return { id: vessel.id, value: formatValue(vessel.name) + `(${vessel.id})` };
        });
        setVesselList(data);

        const responseManualType = await vesselService.getManualTypes();
        const manualData = responseManualType.data.map((type) => {
          return { id: type.id, value: formatValue(type.name), type: type.type };
        });
        setManualTypes(manualData);
      } catch (error) {
        console.error(error);
      }
    })();
  }, []);

  return <Provider value={providerValue}>{props.children} </Provider>;
};

export default DetailContextProvider;
