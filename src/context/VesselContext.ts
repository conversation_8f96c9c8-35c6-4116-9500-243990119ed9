import React, { createContext, useEffect, useMemo, useState } from 'react';
import { getCountries } from '../service/reference-service';
import vesselService from '../service/vessel-service';

export const VesselContext = createContext({});

export const Provider = VesselContext.Provider;

const VesselContextProvider = (props) => {
  const [countries, setCountries] = useState([]);
  const [compareReportData, setCompareReportData] = useState({});
  const [vesselName, setVesselName] = useState(null);
  const [editData, setEditData] = useState(null);
  const [showEditModal, setShowEditModal] = useState(null);
  const [vesselList, setVesselList] = useState([]);
  const [assignUserActionStatus, setAssignUserActionStatus] = useState();
  const [reportVersion, setReportVersion] = useState(null);

  const providerValue = useMemo(() => {
    return {
      countries: countries,
      vesselName: vesselName,
      setVesselName: setVesselName,
      ga4EventTrigger: props.ga4EventTrigger,
      compareReportData: compareReportData,
      setCompareReportData: setCompareReportData,
      editData: editData,
      setEditData: setEditData,
      showEditModal: showEditModal,
      setShowEditModal: setShowEditModal,
      roleConfig: props.roleConfig,
      vesselList: vesselList,
      assignUserActionStatus: assignUserActionStatus,
      setAssignUserActionStatus: setAssignUserActionStatus,
      reportVersion: reportVersion,
      setReportVersion: setReportVersion,
    };
  }, [
    countries,
    vesselName,
    compareReportData,
    editData,
    showEditModal,
    props.ga4EventTrigger,
    props.roleConfig,
    vesselList,
    assignUserActionStatus,
    reportVersion,
  ]);

  const fetchCountries = async () => {
    try {
      const countryResponse = await getCountries();
      const countryData = countryResponse.data.countries?.map((country) => {
        return { id: country.alpha2_code, value: country.value };
      });
      setCountries(countryData);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchVesselList = async () => {
    try {
      const response = await vesselService.getAllVessels();
      setVesselList(response.data.results);
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    fetchCountries();
    fetchVesselList();
  }, []);

  return <Provider value={providerValue}>{props.children} </Provider>;
};

export default VesselContextProvider;
