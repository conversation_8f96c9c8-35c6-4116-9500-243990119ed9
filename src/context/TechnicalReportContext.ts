import React, { createContext, useEffect, useMemo, useState } from 'react';
import vesselService from '../service/vessel-service';
import { formatDate, formatValue } from '../util/view-utils';
import moment from 'moment';
import _ from 'lodash';
import getURLParams from '../util/getURLParams';
import { useHistory } from 'react-router-dom';
import { getCountries } from '../service/reference-service';

export const TechnicalReportContext = createContext({});

export const Provider = TechnicalReportContext.Provider;

const TechnicalReportContextProvider = (props) => {
  const history = useHistory();
  const ownershipId = getURLParams('vessel_ownership_id', history.location.search);
  const gmt = getURLParams('gmt', history.location.search)?.toString().split(',');
  const [vesselList, setVesselList] = useState([]);
  const [countryList, setCountryList] = useState([]);
  const [tableSort, setTableSort] = useState([]);
  const [filterVoyageData, setFilterVoyageData] = useState({
    year: new Date(),
    month: moment().month() + 1,
  });
  const [excelDownloadedData, setExcelDownloadedData] = useState([]);
  const [excelColumns, setExcelColumns] = useState([]);
  const [filterData, setFilterData] = useState({
    startDate: !_.isEmpty(gmt)
      ? gmt[0]
      : ownershipId
      ? moment().startOf('year').format('YYYY-MM-DD')
      : moment().add(-1, 'days').format('YYYY-MM-DD'),
    endDate: !_.isEmpty(gmt) ? gmt[1] : formatDate(new Date(), 'YYYY-MM-DD', ''),
    vessel: [],
  });
  const [loadingOptions, setLoadingOptions] = useState(true);
  const [elements, setElements] = useState(['vessel', 'year', 'month']);
  const [vesselName, setVesselName] = useState(null);
  const [editData, setEditData] = useState(null);
  const [showEditModal, setShowEditModal] = useState(null);
  const [error, setError] = useState(null);
  const [compareReportData, setCompareReportData] = useState({});
  const providerValue = useMemo(() => {
    return {
      vesselList: vesselList,
      filterData: filterData,
      setFilterData: setFilterData,
      tableSort: tableSort,
      setTableSort: setTableSort,
      roleConfig: props.roleConfig,
      ga4EventTrigger: props.ga4EventTrigger,
      filterVoyageData: filterVoyageData,
      setFilterVoyageData: setFilterVoyageData,
      filterVoyageOptions: {
        vessel: vesselList,
        country: countryList,
      },
      excelDownloadedData: excelDownloadedData,
      setExcelDownloadedData: setExcelDownloadedData,
      excelColumns: excelColumns,
      setExcelColumns: setExcelColumns,
      loadingOptions: loadingOptions,
      elements: elements,
      setElements: setElements,
      userEmail: props.userEmail,
      vesselName: vesselName,
      setVesselName: setVesselName,
      editData: editData,
      setEditData: setEditData,
      showEditModal: showEditModal,
      setShowEditModal: setShowEditModal,
      error: error,
      setError: setError,
      compareReportData: compareReportData,
      setCompareReportData: setCompareReportData,
    };
  }, [
    vesselList,
    filterData,
    tableSort,
    props.roleConfig,
    props.ga4EventTrigger,
    countryList,
    filterVoyageData,
    excelDownloadedData,
    excelColumns,
    loadingOptions,
    elements,
    props.userEmail,
    vesselName,
    editData,
    showEditModal,
    error,
    compareReportData,
  ]);

  const intialLoadData = async () => {
    try {
      setLoadingOptions(true);
      const response = await vesselService.getAllVessels();
      const data = response.data.results?.map((vessel) => {
        return {
          id: vessel.id,
          value: formatValue(vessel.name) + `(${vessel.id})`,
          name: formatValue(vessel.name),
          vesselId: vessel.vessel.id,
        };
      });
      const vesselData = data?.find((i) => i.id === Number(ownershipId));
      if (vesselData) {
        setFilterData({ ...filterData, vessel: [vesselData] });
        setFilterVoyageData({ ...filterVoyageData, vessel: [vesselData] });
      }
      setVesselList(data);
      setLoadingOptions(false);
      const countryResponse = await getCountries();
      const countryData = countryResponse.data.countries?.map((country) => {
        return { id: country.value, value: country.value, label: country.alpha2_code };
      });
      setCountryList(countryData);
    } catch (error) {
      console.error(error);
      setLoadingOptions(false);
    }
  };

  useEffect(() => {
    intialLoadData();
  }, []);

  return <Provider value={providerValue}>{props.children}</Provider>;
};

export default TechnicalReportContextProvider;
